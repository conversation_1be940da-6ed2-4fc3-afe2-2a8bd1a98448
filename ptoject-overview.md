# 🏫 Project Overview: Teaching Center Management SaaS (Multi-Tenant, Subdomain-Based)

This is a **Software-as-a-Service (SaaS)** platform designed to help **coaching centers, schools, and private teaching institutions** in Bangladesh manage their entire operations digitally. Each institution gets a **dedicated subdomain** (e.g., `sunriseacademy.yourdomain.com`) and access based on their **subscription plan** (weekly/monthly).

The platform enables centers to **digitize** their operations, **improve efficiency**, and **enhance student experience**, all while offering premium features for paid tiers to increase recurring revenue.

---

## 🔑 Core Features (Available Based on Subscription Plan)

### 🔹 Multi-Tenant Subdomain Architecture
- Each center gets its own subdomain (e.g., `abcacademy.domain.com`)
- Isolated data per tenant (branch, staff, students, financials)
- Branded experience (logo, theme, center name)

### 🔹 Subscription & Plan Management
- Weekly/Monthly Subscription Tiers
- Free, Basic, and Premium plans
- Auto-renewal & manual renewal
- Plan upgrade/downgrade with feature toggles
- bKash, Nagad, Rocket & Card payment integration

---

## 🧩 Feature Modules (Premium Features Marked ⭐)

### 1. 🏢 Branch Management
- Add/manage multiple branches under one institution
- Assign admins/staff per branch
- Geo-mapping of branches

### 2. 📚 Course Management
- Create/manage unlimited courses
- Define syllabus, pricing, duration, batch size
- Link with teachers and schedules

### 3. 👩‍🏫 Teacher Management
- Add/manage teacher profiles, roles, and assignments
- Track attendance and performance
- ⭐ Performance analytics and salary breakdown

### 4. 👨‍🎓 Student Management
- Student registration with documents
- Guardian info, payment history, attendance
- ⭐ Student performance analytics

### 5. 🗓️ Class Scheduling Management
- Weekly/monthly schedule creation
- Conflict checker (same teacher, same time)
- ⭐ Automatic schedule suggestion engine

### 6. 📝 Attendance Management
- Teacher and student attendance
- Manual and biometric import support
- ⭐ SMS notification to guardians on absence

### 7. 🧑‍💼 HRM + Payroll Management
- Manage all staff roles
- Define salary structures, overtime, deductions
- ⭐ Auto-payroll with leave & attendance integration

### 8. 💰 Accounting Module
- Track income/expenses, fees, salary, purchases
- Monthly financial reports and balance sheets
- ⭐ VAT, tax tracking, export to Excel

### 9. 📦 Inventory Module
- Track classroom assets (projectors, whiteboards, etc.)
- Purchase history & quantity control
- ⭐ Expiry alerts for consumables (e.g., markers, books)

### 10. 📝 Requisition Module
- Teachers/staff can request materials
- Approve, reject, and track requests
- ⭐ Auto-link with inventory for availability check

### 11. 📊 Result Management
- Marks entry for MCQ, written, practical
- Automated report cards
- ⭐ Online access for students/guardians

---

## 🧠 Bonus Features to Increase Sales in Bangladesh 🇧🇩

### 📲 SMS & WhatsApp Notification Integration ⭐
- Absence alerts
- Exam reminders
- Fee due notices
- Admission campaigns

### 🌐 Online Admission & Payment Gateway ⭐
- Admission form on subdomain
- Payment via bKash/Nagad/SSLCommerz

### 🖼️ Digital ID Card & Certificate Generator ⭐
- Auto-generate ID cards for students/staff
- Generate digital and printable certificates

### 🗂️ Document Management ⭐
- Upload and manage files for students/teachers
- NID, certificates, resumes, etc.

### 🔐 Role-Based Access Control
- Fine-grained permissions per role (admin, teacher, HR, accountant)

### 📈 Admin Dashboard with Reports
- Branch-wise summary
- Graphs for earnings, attendance, performance

### 🌓 Light/Dark Mode & Multi-Language (Bengali & English)
- Improved accessibility for a wider user base

---

## 🛠️ Technical Overview for Augment AI Agent

- **Frontend**: React (Vite), Flutter (mobile)
- **Backend**: Hono (Bun.js)
- **Database**: MySQL (per-tenant schema or shared-schema with tenant_id)
- **Authentication**: Firebase Auth (Google login optional)
- **Notification**: Firebase Push + SMS Gateway
- **Deployment**: Docker-ready, supports SaaS scaling
- **Tenancy**: Subdomain-based dynamic routing and DB context

---

## 🧾 Monetization Strategy

- Free plan for trial (1 branch, limited features)
- Basic plan: ৳499/mo (most core features)
- Premium plan: ৳999/mo (all features unlocked)
- Optional Add-ons: SMS credits, Biometric device integration, ID card print service
