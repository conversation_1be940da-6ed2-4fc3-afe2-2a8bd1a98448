{"name": "teaching-center-saas", "version": "1.0.0", "description": "Multi-tenant SaaS platform for teaching centers in Bangladesh", "main": "src/index.tsx", "scripts": {"start": "bun run src/server.ts", "dev": "bun --watch src/server.ts", "dev:css": "bun run scripts/watch-css.js", "build:css": "echo 'CSS is already built'", "dev:full": "concurrently \"bun run dev\" \"bun run dev:css\"", "test": "echo \"Tests will be run using Playwright MCP\"", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "bun run src/database/seed.ts", "db:init": "bun run scripts/init-database.js", "db:seed:dev": "bun run scripts/seed-dev-data.js", "db:setup": "bun run db:init && bun run db:seed", "db:setup:dev": "bun run db:init && bun run db:seed:dev", "db:reset:test": "bun run scripts/reset-test-db.js", "db:seed:test": "bun run scripts/seed-test-data.js", "files:clear:test": "bun run scripts/clear-test-files.js"}, "keywords": ["saas", "education", "teaching", "multi-tenant", "bangladesh"], "author": "Teaching Center SaaS Team", "license": "MIT", "engines": {"node": ">=18.0.0"}, "dependencies": {"@prisma/client": "^6.1.0", "@radix-ui/react-switch": "^1.2.5", "@tinymce/tinymce-react": "^6.3.0", "@types/dompurify": "^3.0.5", "autoprefixer": "^10.4.21", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dompurify": "^3.2.6", "dotenv": "^17.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "hono": "^4.8.4", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "postcss": "^8.5.6", "prisma": "^6.1.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "sharp": "^0.34.3", "tinymce": "^8.0.1", "zod": "^3.24.1"}, "devDependencies": {"@playwright/test": "^1.53.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-legacy": "^7.0.0", "concurrently": "^9.2.0", "esbuild": "^0.25.6", "tailwindcss": "^4.1.11", "vite": "^7.0.3"}}