// Test health endpoint
const testHealth = async () => {
  try {
    console.log('🔍 Testing health endpoint...');
    
    const response = await fetch('http://localhost:3000/health');
    const data = await response.json();
    
    console.log('📊 Health Response Status:', response.status);
    console.log('📊 Health Response Data:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Server is responding!');
    } else {
      console.log('❌ Server health check failed!');
    }
    
  } catch (error) {
    console.error('❌ Network Error:', error.message);
  }
};

testHealth();
