#!/usr/bin/env bun

/**
 * UI Screens Testing with <PERSON><PERSON> MCP
 * Tests all screens to ensure Tailwind CSS is working properly
 */

const BASE_URL = 'http://localhost:3000';

// Test credentials
const testUsers = {
  superAdmin: {
    email: '<EMAIL>',
    password: 'SuperAdmin123!'
  },
  centerAdmin: {
    email: '<EMAIL>',
    password: 'Owner123!'
  },
  teacher: {
    email: '<EMAIL>',
    password: 'Teacher123!'
  },
  student: {
    email: '<EMAIL>',
    password: 'Student123!'
  }
};

// Helper function to login via form submission
async function loginUser(credentials, tenantSubdomain = null) {
  const loginUrl = tenantSubdomain 
    ? `http://${tenantSubdomain}.localhost:3000/login`
    : `${BASE_URL}/login`;
    
  const response = await fetch(loginUrl, {
    method: 'POST',
    headers: { 
      'Content-Type': 'application/json',
      ...(tenantSubdomain && { 'X-Tenant-Subdomain': tenantSubdomain })
    },
    body: JSON.stringify(credentials),
    redirect: 'manual'
  });
  
  const cookies = response.headers.get('set-cookie');
  return cookies ? cookies.split(';')[0] : null;
}

// Helper function to check CSS classes in HTML
function checkTailwindClasses(html, expectedClasses) {
  const issues = [];
  
  expectedClasses.forEach(className => {
    if (!html.includes(className)) {
      issues.push(`Missing CSS class: ${className}`);
    }
  });
  
  return issues;
}

// Helper function to check responsive design elements
function checkResponsiveDesign(html) {
  const responsiveClasses = [
    'lg:flex', 'lg:hidden', 'lg:w-1/2', 'lg:px-20',
    'sm:px-6', 'sm:text-sm', 'md:', 'xl:'
  ];
  
  const foundResponsive = responsiveClasses.filter(cls => html.includes(cls));
  return {
    found: foundResponsive,
    missing: responsiveClasses.filter(cls => !html.includes(cls))
  };
}

console.log('🎨 Starting UI Screens Testing...\n');

// Test 1: Login Page (Public)
console.log('🧪 Test 1: Login Page Design');
try {
  const response = await fetch(`${BASE_URL}/login`);
  const html = await response.text();
  
  const expectedClasses = [
    'min-h-screen', 'flex', 'bg-gradient-to-br', 'from-blue-600',
    'via-purple-600', 'to-blue-800', 'rounded-lg', 'shadow-sm',
    'focus:ring-2', 'focus:ring-blue-600', 'btn', 'transition-all'
  ];
  
  const issues = checkTailwindClasses(html, expectedClasses);
  const responsive = checkResponsiveDesign(html);
  
  if (issues.length === 0) {
    console.log('✅ Login page CSS: All classes found');
  } else {
    console.log('❌ Login page CSS issues:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  }
  
  console.log(`📱 Responsive classes found: ${responsive.found.length}`);
  console.log(`📱 Responsive classes missing: ${responsive.missing.length}`);
  
} catch (error) {
  console.log('❌ Login page test failed:', error.message);
}

// Test 2: Super Admin Dashboard
console.log('\n🧪 Test 2: Super Admin Dashboard');
try {
  const sessionCookie = await loginUser(testUsers.superAdmin);
  
  if (!sessionCookie) {
    console.log('❌ Could not login as super admin');
  } else {
    const response = await fetch(`${BASE_URL}/admin/dashboard`, {
      headers: { 'Cookie': sessionCookie }
    });
    
    const html = await response.text();
    
    if (response.status === 200) {
      const expectedClasses = [
        'grid', 'grid-cols-1', 'grid-cols-2', 'grid-cols-4', 'gap-6',
        'bg-white', 'shadow-sm', 'rounded-lg', 'p-6', 'text-center',
        'text-3xl', 'font-bold', 'text-gray-900', 'text-sm', 'text-gray-600'
      ];
      
      const issues = checkTailwindClasses(html, expectedClasses);
      const responsive = checkResponsiveDesign(html);
      
      if (issues.length === 0) {
        console.log('✅ Admin dashboard CSS: All classes found');
      } else {
        console.log('❌ Admin dashboard CSS issues:');
        issues.forEach(issue => console.log(`   - ${issue}`));
      }
      
      console.log(`📱 Responsive classes found: ${responsive.found.length}`);
      
      // Check for specific dashboard elements
      const hasStats = html.includes('Total Centers') && html.includes('Active Subscriptions');
      console.log(`📊 Dashboard stats: ${hasStats ? '✅ Found' : '❌ Missing'}`);
      
    } else {
      console.log(`❌ Admin dashboard returned status: ${response.status}`);
    }
  }
} catch (error) {
  console.log('❌ Admin dashboard test failed:', error.message);
}

// Test 3: Tenant Dashboard (Center Admin)
console.log('\n🧪 Test 3: Tenant Dashboard (Center Admin)');
try {
  const sessionCookie = await loginUser(testUsers.centerAdmin, 'sunriseacademy');

  if (!sessionCookie) {
    console.log('❌ Could not login as center admin');
  } else {
    const response = await fetch(`${BASE_URL}/dashboard`, {
      headers: {
        'Cookie': sessionCookie,
        'X-Tenant-Subdomain': 'sunriseacademy'
      }
    });
    
    const html = await response.text();
    
    if (response.status === 200) {
      const expectedClasses = [
        'min-h-screen', 'bg-gray-50', 'flex', 'w-64', 'bg-white',
        'shadow-lg', 'nav-link', 'space-y-4', 'p-4', 'rounded-lg'
      ];
      
      const issues = checkTailwindClasses(html, expectedClasses);
      
      if (issues.length === 0) {
        console.log('✅ Tenant dashboard CSS: All classes found');
      } else {
        console.log('❌ Tenant dashboard CSS issues:');
        issues.forEach(issue => console.log(`   - ${issue}`));
      }
      
      // Check for navigation elements
      const hasNavigation = html.includes('Dashboard') && html.includes('Students');
      console.log(`🧭 Navigation elements: ${hasNavigation ? '✅ Found' : '❌ Missing'}`);
      
    } else {
      console.log(`❌ Tenant dashboard returned status: ${response.status}`);
    }
  }
} catch (error) {
  console.log('❌ Tenant dashboard test failed:', error.message);
}

// Test 4: Mobile Responsiveness Check
console.log('\n🧪 Test 4: Mobile Responsiveness Check');
try {
  const response = await fetch(`${BASE_URL}/login`);
  const html = await response.text();
  
  // Check for mobile-specific classes
  const mobileClasses = [
    'sm:', 'md:', 'lg:', 'xl:', 'hidden', 'block',
    'flex-col', 'w-full', 'px-4', 'py-2'
  ];
  
  const foundMobile = mobileClasses.filter(cls => html.includes(cls));
  
  console.log(`📱 Mobile responsive classes found: ${foundMobile.length}/${mobileClasses.length}`);
  console.log(`📱 Classes: ${foundMobile.join(', ')}`);
  
  if (foundMobile.length >= mobileClasses.length * 0.7) {
    console.log('✅ Good mobile responsiveness coverage');
  } else {
    console.log('⚠️  Limited mobile responsiveness');
  }
  
} catch (error) {
  console.log('❌ Mobile responsiveness test failed:', error.message);
}

// Test 5: Dark Mode Support
console.log('\n🧪 Test 5: Dark Mode Support');
try {
  const response = await fetch(`${BASE_URL}/login`);
  const html = await response.text();
  
  const darkModeElements = [
    'theme-dark', 'theme-light', 'dark:', 'bg-gray-800', 'text-white'
  ];
  
  const foundDarkMode = darkModeElements.filter(cls => html.includes(cls));
  
  console.log(`🌙 Dark mode classes found: ${foundDarkMode.length}/${darkModeElements.length}`);
  
  if (foundDarkMode.length > 0) {
    console.log('✅ Dark mode support detected');
  } else {
    console.log('⚠️  No dark mode support found');
  }
  
} catch (error) {
  console.log('❌ Dark mode test failed:', error.message);
}

console.log('\n🎉 UI Screens Testing Completed!');
console.log('\n📋 Summary:');
console.log('- Login page design and responsiveness');
console.log('- Admin dashboard layout and components');
console.log('- Tenant dashboard navigation and styling');
console.log('- Mobile responsiveness coverage');
console.log('- Dark mode support detection');
console.log('\n💡 Recommendations:');
console.log('- Ensure all Tailwind classes are properly compiled');
console.log('- Test on different screen sizes');
console.log('- Verify dark mode toggle functionality');
console.log('- Check form validation styling');
