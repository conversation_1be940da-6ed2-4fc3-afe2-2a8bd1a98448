# Subscription & Plan Management Tests

This file documents the TDD tests for subscription and plan management functionality using Playwright MCP.

## Test 7: Add New Subscription Plan (Free, Basic, Premium)

**Test Case**: Super admin should be able to create new subscription plans
**Endpoint**: POST /api/admin/plans
**Expected**: 201 status with plan details

### Test Data:
```json
{
  "name": "Enterprise",
  "description": "Enterprise features for large institutions",
  "features": ["all_features", "priority_support", "custom_integrations"],
  "maxStudents": 5000,
  "maxTeachers": 500,
  "maxBranches": 50,
  "priceMonthly": 1999.00,
  "priceWeekly": 599.00
}
```

### Expected Response:
```json
{
  "message": "Plan created successfully",
  "data": {
    "id": 4,
    "name": "Enterprise",
    "description": "Enterprise features for large institutions",
    "features": ["all_features", "priority_support", "custom_integrations"],
    "maxStudents": 5000,
    "maxTeachers": 500,
    "maxBranches": 50,
    "priceMonthly": 1999.00,
    "priceWeekly": 599.00,
    "isActive": true
  }
}
```

## Test 8: Edit Plan Pricing/Duration/Features

**Test Case**: Super admin should be able to update existing plans
**Endpoint**: PUT /api/admin/plans/:id
**Expected**: 200 status with updated plan details

### Test Data:
```json
{
  "priceMonthly": 1299.00,
  "priceWeekly": 399.00,
  "features": ["all_features", "priority_support", "custom_integrations", "white_label"]
}
```

### Expected Response:
```json
{
  "message": "Plan updated successfully",
  "data": {
    "id": 1,
    "name": "Premium",
    "priceMonthly": 1299.00,
    "priceWeekly": 399.00,
    "features": ["all_features", "priority_support", "custom_integrations", "white_label"]
  }
}
```

## Test 9: Delete a Plan

**Test Case**: Super admin should be able to delete plans (soft delete)
**Endpoint**: DELETE /api/admin/plans/:id
**Expected**: 200 status with success message

### Expected Response:
```json
{
  "message": "Plan deleted successfully",
  "data": {
    "id": 4,
    "isActive": false
  }
}
```

## Test 10: Activate/Deactivate a Plan

**Test Case**: Super admin should be able to activate/deactivate plans
**Endpoint**: PUT /api/admin/plans/:id/status
**Expected**: 200 status with updated status

### Test Data:
```json
{
  "isActive": false
}
```

### Expected Response:
```json
{
  "message": "Plan status updated successfully",
  "data": {
    "id": 1,
    "name": "Premium",
    "isActive": false
  }
}
```

## Test 11: Plan Feature Toggle Visibility for Center

**Test Case**: Center should only see features available in their current plan
**Endpoint**: GET /api/center/features
**Expected**: 200 status with available features

### Expected Response (for Premium plan):
```json
{
  "message": "Features retrieved successfully",
  "data": {
    "features": [
      "student_management",
      "teacher_management", 
      "attendance",
      "advanced_reports",
      "sms_notifications",
      "whatsapp_integration",
      "payroll",
      "inventory",
      "requisition",
      "result_management",
      "id_card_generator",
      "certificate_generator",
      "document_management",
      "backup_recovery"
    ],
    "isActive": true,
    "hasFeature": "function"
  }
}
```

## Additional Test Cases

### Test 11a: List All Plans (Admin)

**Test Case**: Super admin should be able to list all plans
**Endpoint**: GET /api/admin/plans
**Expected**: 200 status with plans list

### Test 11b: Get Plan Details

**Test Case**: Get specific plan details
**Endpoint**: GET /api/admin/plans/:id
**Expected**: 200 status with plan details

### Test 11c: Assign Plan to Center

**Test Case**: Super admin should be able to assign plans to centers
**Endpoint**: POST /api/admin/centers/:id/subscription
**Expected**: 201 status with subscription details

### Test 11d: Center Plan Upgrade/Downgrade

**Test Case**: Center should be able to request plan changes
**Endpoint**: POST /api/center/subscription/upgrade
**Expected**: 200 status with upgrade request details
