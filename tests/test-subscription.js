// Subscription & Plan Management Tests using fetch API
// This file implements TDD tests for subscription and plan management functionality

const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });
  
  const data = await response.json();
  return { status: response.status, data };
}

// Helper function to get super admin token
async function getSuperAdminToken() {
  const { status, data } = await apiRequest('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'SuperAdmin123!'
    })
  });
  
  if (status === 200 && data.token) {
    return data.token;
  }
  throw new Error('Failed to get super admin token');
}

// Helper function to get center owner token
async function getCenterOwnerToken() {
  const { status, data } = await apiRequest('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Owner123!'
    })
  });
  
  if (status === 200 && data.token) {
    return data.token;
  }
  throw new Error('Failed to get center owner token');
}

// Test 7: Add New Subscription Plan
async function testAddNewPlan(token) {
  console.log('🧪 Test 7: Add New Subscription Plan');
  
  const timestamp = Date.now();
  const planData = {
    name: `Enterprise_${timestamp}`,
    description: 'Enterprise features for large institutions',
    features: ['all_features', 'priority_support', 'custom_integrations'],
    maxStudents: 5000,
    maxTeachers: 500,
    maxBranches: 50,
    priceMonthly: 1999.00,
    priceWeekly: 599.00
  };
  
  const { status, data } = await apiRequest('/api/admin/plans', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(planData)
  });
  
  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  if (status === 201) {
    if (data.message === 'Plan created successfully' && data.data && data.data.name.startsWith('Enterprise_')) {
      console.log('✅ Test 7 PASSED: Plan created successfully');
      return { passed: true, planId: data.data.id };
    } else {
      console.log('❌ Test 7 FAILED: Invalid response structure');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 7 FAILED: Expected status 201, got', status);
    return { passed: false };
  }
}

// Test 8: Edit Plan Pricing/Duration/Features
async function testEditPlan(token, planId) {
  console.log('\n🧪 Test 8: Edit Plan Pricing/Duration/Features');
  
  const updateData = {
    priceMonthly: 1299.00,
    priceWeekly: 399.00,
    features: ['all_features', 'priority_support', 'custom_integrations', 'white_label']
  };
  
  const { status, data } = await apiRequest(`/api/admin/plans/${planId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(updateData)
  });
  
  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  if (status === 200) {
    if (data.message === 'Plan updated successfully' && data.data) {
      console.log('✅ Test 8 PASSED: Plan updated successfully');
      return { passed: true };
    } else {
      console.log('❌ Test 8 FAILED: Invalid response structure');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 8 FAILED: Expected status 200, got', status);
    return { passed: false };
  }
}

// Test 9: Delete a Plan
async function testDeletePlan(token, planId) {
  console.log('\n🧪 Test 9: Delete a Plan');
  
  const { status, data } = await apiRequest(`/api/admin/plans/${planId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  if (status === 200) {
    if (data.message === 'Plan deleted successfully') {
      console.log('✅ Test 9 PASSED: Plan deleted successfully');
      return { passed: true };
    } else {
      console.log('❌ Test 9 FAILED: Wrong success message');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 9 FAILED: Expected status 200, got', status);
    return { passed: false };
  }
}

// Test 10: Activate/Deactivate a Plan
async function testTogglePlanStatus(token) {
  console.log('\n🧪 Test 10: Activate/Deactivate a Plan');
  
  // Test with Premium plan (ID should be 3 based on our seed data)
  const planId = 3;
  
  const { status, data } = await apiRequest(`/api/admin/plans/${planId}/status`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      isActive: false
    })
  });
  
  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  if (status === 200) {
    if (data.message === 'Plan status updated successfully') {
      console.log('✅ Test 10 PASSED: Plan status updated successfully');
      return { passed: true };
    } else {
      console.log('❌ Test 10 FAILED: Wrong success message');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 10 FAILED: Expected status 200, got', status);
    return { passed: false };
  }
}

// Test 11: Plan Feature Toggle Visibility for Center
async function testCenterFeatures(token) {
  console.log('\n🧪 Test 11: Plan Feature Toggle Visibility for Center');

  // Use the main URL with Host header to simulate subdomain
  const response = await fetch(`${BASE_URL}/api/center/features`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'Host': 'sunriseacademy.localhost:3000'
    }
  });

  const data = await response.json();
  const status = response.status;

  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));

  if (status === 200) {
    if (data.message === 'Features retrieved successfully' && data.data && Array.isArray(data.data.features)) {
      console.log('✅ Test 11 PASSED: Center features retrieved successfully');
      return { passed: true };
    } else {
      console.log('❌ Test 11 FAILED: Invalid response structure');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 11 FAILED: Expected status 200, got', status);
    return { passed: false };
  }
}

// Run all subscription tests
async function runSubscriptionTests() {
  console.log('🚀 Starting Subscription & Plan Management Tests\n');
  
  const results = [];
  let superAdminToken, centerOwnerToken;
  
  try {
    // Get authentication tokens
    console.log('🔑 Getting authentication tokens...');
    superAdminToken = await getSuperAdminToken();
    centerOwnerToken = await getCenterOwnerToken();
    console.log('✅ Tokens obtained successfully\n');
  } catch (error) {
    console.error('❌ Failed to get authentication tokens:', error.message);
    return;
  }
  
  // Test 7: Add New Plan
  const test7 = await testAddNewPlan(superAdminToken);
  results.push(test7);
  
  // Test 8: Edit Plan (only if creation was successful)
  if (test7.passed && test7.planId) {
    const test8 = await testEditPlan(superAdminToken, test7.planId);
    results.push(test8);
    
    // Test 9: Delete Plan
    const test9 = await testDeletePlan(superAdminToken, test7.planId);
    results.push(test9);
  } else {
    console.log('\n⏭️  Skipping Tests 8-9: Plan creation failed');
    results.push({ passed: false, skipped: true });
    results.push({ passed: false, skipped: true });
  }
  
  // Test 10: Toggle Plan Status
  const test10 = await testTogglePlanStatus(superAdminToken);
  results.push(test10);
  
  // Test 11: Center Features (using center owner token)
  const test11 = await testCenterFeatures(centerOwnerToken);
  results.push(test11);
  
  // Summary
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  const skipped = results.filter(r => r.skipped).length;
  
  console.log('\n📊 Subscription Tests Summary:');
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed - skipped}/${total}`);
  console.log(`⏭️  Skipped: ${skipped}/${total}`);
  
  if (passed === total - skipped) {
    console.log('\n🎉 All subscription tests passed!');
  } else {
    console.log('\n🔧 Some subscription tests failed. Check implementation.');
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runSubscriptionTests().catch(console.error);
}

export default runSubscriptionTests;
