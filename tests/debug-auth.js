// Debug authentication and permissions
const BASE_URL = 'http://localhost:3000';

async function debugAuth() {
  console.log('🔍 Debugging authentication...');
  
  try {
    // Login as center owner
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Owner123!'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login status:', loginResponse.status);
    console.log('Login data:', JSON.stringify(loginData, null, 2));
    
    if (loginResponse.status !== 200 || !loginData.token) {
      console.log('❌ Login failed');
      return;
    }
    
    // Test center students endpoint
    const studentsResponse = await fetch(`${BASE_URL}/api/center/students`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json',
        'X-Tenant-Subdomain': 'sunriseacademy'
      }
    });
    
    const studentsData = await studentsResponse.json();
    console.log('\nStudents endpoint status:', studentsResponse.status);
    console.log('Students data:', JSON.stringify(studentsData, null, 2));
    
    // Test without tenant header
    const studentsResponse2 = await fetch(`${BASE_URL}/api/center/students`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const studentsData2 = await studentsResponse2.json();
    console.log('\nStudents endpoint (no tenant) status:', studentsResponse2.status);
    console.log('Students data (no tenant):', JSON.stringify(studentsData2, null, 2));
    
  } catch (error) {
    console.log('❌ Debug failed:', error.message);
  }
}

debugAuth();
