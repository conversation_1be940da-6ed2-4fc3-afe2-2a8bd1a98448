#!/usr/bin/env bun

/**
 * Playwright UI Testing Script
 * Visual testing of all screens to ensure proper CSS rendering
 */

import { spawn } from 'child_process';

const BASE_URL = 'http://localhost:3000';

// Test credentials
const testUsers = {
  superAdmin: {
    email: '<EMAIL>',
    password: 'SuperAdmin123!'
  },
  centerAdmin: {
    email: '<EMAIL>',
    password: 'Owner123!'
  }
};

console.log('🎭 Starting Playwright UI Testing...\n');

// Helper function to run Playwright commands
function runPlaywrightCommand(command) {
  return new Promise((resolve, reject) => {
    console.log(`🎬 Running: ${command}`);
    
    const process = spawn('bun', ['x', 'playwright', ...command.split(' ')], {
      stdio: 'inherit'
    });

    process.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Playwright command failed with code ${code}`));
      }
    });
  });
}

// Test 1: Login Page Visual Test
console.log('🧪 Test 1: Login Page Visual Test');
try {
  await runPlaywrightCommand(`test --headed --browser=chromium --timeout=30000 -c playwright.config.js`);
  console.log('✅ Login page visual test completed');
} catch (error) {
  console.log('❌ Login page visual test failed:', error.message);
}

console.log('\n🎉 Playwright UI Testing Completed!');

// Manual testing instructions
console.log('\n📋 Manual Testing Checklist:');
console.log('');
console.log('🔍 Login Page (http://localhost:3000/login):');
console.log('  ✓ Gradient background displays correctly');
console.log('  ✓ Form inputs have proper styling and focus states');
console.log('  ✓ Button has hover and loading states');
console.log('  ✓ Responsive design works on mobile');
console.log('  ✓ Language and theme switchers work');
console.log('');
console.log('🔍 Admin Dashboard (http://localhost:3000/admin/dashboard):');
console.log('  ✓ Header with user info displays correctly');
console.log('  ✓ Statistics cards are properly styled');
console.log('  ✓ Grid layout is responsive');
console.log('  ✓ Quick actions section is functional');
console.log('  ✓ Recent activity list displays properly');
console.log('');
console.log('🔍 Tenant Dashboard (with subdomain or header):');
console.log('  ✓ Navigation sidebar is styled correctly');
console.log('  ✓ Main content area has proper spacing');
console.log('  ✓ Cards and components use consistent styling');
console.log('  ✓ Mobile navigation works properly');
console.log('');
console.log('🔍 Responsive Design:');
console.log('  ✓ Desktop (1920x1080) - Full layout');
console.log('  ✓ Tablet (768x1024) - Adapted layout');
console.log('  ✓ Mobile (375x667) - Mobile-first design');
console.log('');
console.log('🔍 Dark Mode:');
console.log('  ✓ Theme toggle works correctly');
console.log('  ✓ All components adapt to dark theme');
console.log('  ✓ Text contrast is sufficient');
console.log('  ✓ Colors maintain brand consistency');
console.log('');
console.log('💡 CSS Issues to Check:');
console.log('  • Missing Tailwind classes in compiled CSS');
console.log('  • Incorrect responsive breakpoints');
console.log('  • Focus states not working properly');
console.log('  • Hover effects not applying');
console.log('  • Dark mode colors not switching');
console.log('  • Form validation styling missing');
console.log('');
console.log('🛠️  Quick Fixes:');
console.log('  1. Run: bun run dev:css (to watch CSS changes)');
console.log('  2. Check: public/styles/main.css (for compiled styles)');
console.log('  3. Verify: tailwind.config.js (for proper content paths)');
console.log('  4. Test: Different screen sizes in browser dev tools');
console.log('  5. Validate: HTML structure and class names');
console.log('');
console.log('🎯 Testing URLs:');
console.log(`  • Login: ${BASE_URL}/login`);
console.log(`  • Admin Dashboard: ${BASE_URL}/admin/dashboard`);
console.log(`  • Tenant Dashboard: ${BASE_URL}/dashboard (with X-Tenant-Subdomain header)`);
console.log(`  • Super Admin Login: ${testUsers.superAdmin.email} / ${testUsers.superAdmin.password}`);
console.log(`  • Center Admin Login: ${testUsers.centerAdmin.email} / ${testUsers.centerAdmin.password}`);
