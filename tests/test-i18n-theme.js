#!/usr/bin/env bun

/**
 * Test script for internationalization and theme functionality
 */

const BASE_URL = 'http://localhost:3000';

console.log('🌍 Starting I18n & Theme Tests...\n');

// Test 1: Language switching API
console.log('🧪 Test 1: Language Switching API');
try {
  const response = await fetch(`${BASE_URL}/api/i18n/language`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ language: 'bn' })
  });
  
  const result = await response.json();
  console.log(`Status: ${response.status}`);
  console.log(`Success: ${result.success}`);
  console.log(`Language: ${result.data?.language}`);
  
  if (response.ok && result.success && result.data?.language === 'bn') {
    console.log('✅ Test 1 PASSED: Language switching API works\n');
  } else {
    console.log('❌ Test 1 FAILED: Language switching API failed\n');
  }
} catch (error) {
  console.log(`❌ Test 1 FAILED: ${error.message}\n`);
}

// Test 2: Theme switching API
console.log('🧪 Test 2: Theme Switching API');
try {
  const response = await fetch(`${BASE_URL}/api/theme/theme`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ theme: 'dark' })
  });
  
  const result = await response.json();
  console.log(`Status: ${response.status}`);
  console.log(`Success: ${result.success}`);
  console.log(`Theme: ${result.data?.theme}`);
  
  if (response.ok && result.success && result.data?.theme === 'dark') {
    console.log('✅ Test 2 PASSED: Theme switching API works\n');
  } else {
    console.log('❌ Test 2 FAILED: Theme switching API failed\n');
  }
} catch (error) {
  console.log(`❌ Test 2 FAILED: ${error.message}\n`);
}

// Test 3: Bengali language in login page
console.log('🧪 Test 3: Bengali Language in Login Page');
try {
  const response = await fetch(`${BASE_URL}/login?lang=bn`);
  const html = await response.text();
  
  console.log(`Status: ${response.status}`);
  
  const hasBengaliText = html.includes('ইমেইল') || html.includes('বাংলা');
  const hasLanguageClass = html.includes('lang="bn"');
  
  console.log(`✓ Has Bengali text: ${hasBengaliText}`);
  console.log(`✓ Has language attribute: ${hasLanguageClass}`);
  
  if (response.ok && hasBengaliText) {
    console.log('✅ Test 3 PASSED: Bengali language works in login page\n');
  } else {
    console.log('❌ Test 3 FAILED: Bengali language not working\n');
  }
} catch (error) {
  console.log(`❌ Test 3 FAILED: ${error.message}\n`);
}

// Test 4: Dark theme in login page
console.log('🧪 Test 4: Dark Theme in Login Page');
try {
  const response = await fetch(`${BASE_URL}/login?theme=dark`);
  const html = await response.text();
  
  console.log(`Status: ${response.status}`);
  
  const hasDarkTheme = html.includes('theme-dark');
  const hasDarkColors = html.includes('#0f172a') || html.includes('#1e293b');
  
  console.log(`✓ Has dark theme class: ${hasDarkTheme}`);
  console.log(`✓ Has dark colors: ${hasDarkColors}`);
  
  if (response.ok && hasDarkTheme) {
    console.log('✅ Test 4 PASSED: Dark theme works in login page\n');
  } else {
    console.log('❌ Test 4 FAILED: Dark theme not working\n');
  }
} catch (error) {
  console.log(`❌ Test 4 FAILED: ${error.message}\n`);
}

// Test 5: Combined Bengali + Dark theme
console.log('🧪 Test 5: Combined Bengali + Dark Theme');
try {
  const response = await fetch(`${BASE_URL}/login?lang=bn&theme=dark`);
  const html = await response.text();
  
  console.log(`Status: ${response.status}`);
  
  const hasBengaliText = html.includes('ইমেইল') || html.includes('বাংলা');
  const hasDarkTheme = html.includes('theme-dark');
  const hasLanguageAttr = html.includes('lang="bn"');
  
  console.log(`✓ Has Bengali text: ${hasBengaliText}`);
  console.log(`✓ Has dark theme: ${hasDarkTheme}`);
  console.log(`✓ Has Bengali language attr: ${hasLanguageAttr}`);
  
  if (response.ok && hasBengaliText && hasDarkTheme && hasLanguageAttr) {
    console.log('✅ Test 5 PASSED: Combined Bengali + Dark theme works\n');
  } else {
    console.log('❌ Test 5 FAILED: Combined functionality not working\n');
  }
} catch (error) {
  console.log(`❌ Test 5 FAILED: ${error.message}\n`);
}

// Test 6: Get supported languages
console.log('🧪 Test 6: Get Supported Languages');
try {
  const response = await fetch(`${BASE_URL}/api/i18n/languages`);
  const result = await response.json();
  
  console.log(`Status: ${response.status}`);
  console.log(`Languages count: ${result.data?.languages?.length || 0}`);
  
  const hasEnglish = result.data?.languages?.some(lang => lang.code === 'en');
  const hasBengali = result.data?.languages?.some(lang => lang.code === 'bn');
  
  console.log(`✓ Has English: ${hasEnglish}`);
  console.log(`✓ Has Bengali: ${hasBengali}`);
  
  if (response.ok && hasEnglish && hasBengali) {
    console.log('✅ Test 6 PASSED: Supported languages API works\n');
  } else {
    console.log('❌ Test 6 FAILED: Supported languages API failed\n');
  }
} catch (error) {
  console.log(`❌ Test 6 FAILED: ${error.message}\n`);
}

// Test 7: Get supported themes
console.log('🧪 Test 7: Get Supported Themes');
try {
  const response = await fetch(`${BASE_URL}/api/theme/themes`);
  const result = await response.json();
  
  console.log(`Status: ${response.status}`);
  console.log(`Themes count: ${result.data?.themes?.length || 0}`);
  
  const hasLight = result.data?.themes?.some(theme => theme.value === 'light');
  const hasDark = result.data?.themes?.some(theme => theme.value === 'dark');
  
  console.log(`✓ Has Light theme: ${hasLight}`);
  console.log(`✓ Has Dark theme: ${hasDark}`);
  
  if (response.ok && hasLight && hasDark) {
    console.log('✅ Test 7 PASSED: Supported themes API works\n');
  } else {
    console.log('❌ Test 7 FAILED: Supported themes API failed\n');
  }
} catch (error) {
  console.log(`❌ Test 7 FAILED: ${error.message}\n`);
}

// Test 8: Theme switching buttons in UI
console.log('🧪 Test 8: Theme Switching Buttons in UI');
try {
  const response = await fetch(`${BASE_URL}/login`);
  const html = await response.text();
  
  console.log(`Status: ${response.status}`);
  
  const hasThemeSwitcher = html.includes('switchTheme(');
  const hasLightButton = html.includes('☀️') || html.includes('Light');
  const hasDarkButton = html.includes('🌙') || html.includes('Dark');
  
  console.log(`✓ Has theme switcher function: ${hasThemeSwitcher}`);
  console.log(`✓ Has light button: ${hasLightButton}`);
  console.log(`✓ Has dark button: ${hasDarkButton}`);
  
  if (response.ok && hasThemeSwitcher && hasLightButton && hasDarkButton) {
    console.log('✅ Test 8 PASSED: Theme switching UI elements present\n');
  } else {
    console.log('❌ Test 8 FAILED: Theme switching UI elements missing\n');
  }
} catch (error) {
  console.log(`❌ Test 8 FAILED: ${error.message}\n`);
}

console.log('🎉 I18n & Theme Tests Completed!');
