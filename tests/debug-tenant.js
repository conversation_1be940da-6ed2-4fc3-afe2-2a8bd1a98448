// Debug tenant middleware
const BASE_URL = 'http://localhost:3000';

async function testTenantDebug() {
  console.log('🔍 Testing tenant middleware with Host header');
  
  const response = await fetch(`${BASE_URL}/api/center/profile`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Host': 'sunriseacademy.localhost:3000'
    }
  });
  
  const data = await response.json();
  
  console.log('Status:', response.status);
  console.log('Response:', JSON.stringify(data, null, 2));
}

testTenantDebug();
