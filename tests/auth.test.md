# Authentication Tests

This file documents the TDD tests for authentication functionality using Playwright MCP.

## Test 1: Admin <PERSON> (Valid Credentials)

**Test Case**: Super admin should be able to login with valid credentials
**Endpoint**: POST /api/auth/login
**Expected**: 200 status with JWT token and user info

### Test Data:
- Email: <EMAIL>
- Password: SuperAdmin123!

### Expected Response:
```json
{
  "message": "Login successful",
  "token": "jwt_token_here",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "Super Administrator",
    "role": "super_admin",
    "tenantId": null,
    "branchId": null
  }
}
```

## Test 2: Admin <PERSON>gin (Invalid Credentials)

**Test Case**: Login should fail with invalid credentials
**Endpoint**: POST /api/auth/login
**Expected**: 401 status with error message

### Test Data:
- Email: <EMAIL>
- Password: WrongPassword123!

### Expected Response:
```json
{
  "error": "Authentication Failed",
  "message": "Invalid email or password"
}
```

## Test 3: Admin Logout

**Test Case**: Authenticated user should be able to logout
**Endpoint**: POST /api/auth/logout
**Expected**: 200 status with success message

### Expected Response:
```json
{
  "message": "Logout successful"
}
```

## Test 4: Center Owner Login (Valid Credentials)

**Test Case**: Center owner should be able to login with valid credentials
**Endpoint**: POST /api/auth/login
**Expected**: 200 status with JWT token and user info

### Test Data:
- Email: <EMAIL>
- Password: Owner123!

### Expected Response:
```json
{
  "message": "Login successful",
  "token": "jwt_token_here",
  "user": {
    "id": 2,
    "email": "<EMAIL>",
    "name": "Sunrise Owner",
    "role": "center_admin",
    "tenantId": 1,
    "branchId": null
  }
}
```

## Test 5: Blocked User Cannot Login

**Test Case**: Blocked user should not be able to login
**Endpoint**: POST /api/auth/login
**Expected**: 403 status with error message

### Test Data:
- Email: <EMAIL>
- Password: Blocked123!

### Expected Response:
```json
{
  "error": "Account Disabled",
  "message": "Your account has been disabled. Please contact support."
}
```

## Test 6: Forgot Password Flow

**Test Case**: User should be able to request password reset
**Endpoint**: POST /api/auth/forgot-password
**Expected**: 200 status with success message

### Test Data:
- Email: <EMAIL>

### Expected Response:
```json
{
  "message": "If an account with that email exists, a password reset link has been sent."
}
```

## Test 7: Session Timeout & Token Renewal

**Test Case**: Expired token should be rejected
**Endpoint**: Any authenticated endpoint
**Expected**: 401 status with token expired error

### Expected Response:
```json
{
  "error": "Unauthorized",
  "message": "Token expired"
}
```
