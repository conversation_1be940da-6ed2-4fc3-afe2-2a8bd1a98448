// Frontend Integration Tests for Teaching Center SaaS Platform
// Tests the complete frontend flow with SSR and direct data fetching

const BASE_URL = 'http://localhost:3000';

// Test 1: Complete Login Flow
async function testCompleteLoginFlow() {
  console.log('\n🧪 Test 1: Complete Login Flow');
  
  try {
    // Step 1: Get login page
    const loginResponse = await fetch(`${BASE_URL}/login`, {
      method: 'GET'
    });
    
    console.log('Login page status:', loginResponse.status);
    const loginHtml = await loginResponse.text();
    
    // Verify login page content
    const hasLoginForm = loginHtml.includes('<form id="loginForm"');
    const hasEmailInput = loginHtml.includes('type="email"');
    const hasPasswordInput = loginHtml.includes('type="password"');
    
    console.log('✓ Has login form:', hasLoginForm);
    console.log('✓ Has email input:', hasEmailInput);
    console.log('✓ Has password input:', hasPasswordInput);
    
    if (!hasLoginForm || !hasEmailInput || !hasPasswordInput) {
      console.log('❌ Test 1 FAILED: Login page missing required elements');
      return { passed: false };
    }
    
    // Step 2: Attempt login with valid credentials
    const loginData = {
      email: '<EMAIL>',
      password: 'SuperAdmin123!'
    };
    
    const loginSubmitResponse = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      redirect: 'manual',
      body: JSON.stringify(loginData)
    });
    
    console.log('Login submit status:', loginSubmitResponse.status);
    console.log('Login redirect location:', loginSubmitResponse.headers.get('location'));
    
    const isRedirect = loginSubmitResponse.status === 302 || loginSubmitResponse.status === 301;
    const redirectsToAdmin = loginSubmitResponse.headers.get('location')?.includes('/admin/dashboard');
    
    console.log('✓ Is redirect:', isRedirect);
    console.log('✓ Redirects to admin dashboard:', redirectsToAdmin);
    
    if (isRedirect && redirectsToAdmin) {
      console.log('✅ Test 1 PASSED: Complete login flow works correctly');
      return { passed: true, cookies: loginSubmitResponse.headers.get('set-cookie') };
    } else {
      console.log('❌ Test 1 FAILED: Login flow did not redirect correctly');
      return { passed: false };
    }
    
  } catch (error) {
    console.log('❌ Test 1 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 2: Dashboard Access with Authentication
async function testDashboardWithAuth() {
  console.log('\n🧪 Test 2: Dashboard Access with Authentication');
  
  try {
    // First login to get auth cookie
    const loginData = {
      email: '<EMAIL>',
      password: 'SuperAdmin123!'
    };
    
    const loginResponse = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      redirect: 'manual',
      body: JSON.stringify(loginData)
    });
    
    const authCookie = loginResponse.headers.get('set-cookie');
    console.log('Auth cookie received:', !!authCookie);
    
    if (!authCookie) {
      console.log('❌ Test 2 FAILED: No auth cookie received');
      return { passed: false };
    }
    
    // Now access dashboard with auth cookie
    const dashboardResponse = await fetch(`${BASE_URL}/admin/dashboard`, {
      method: 'GET',
      headers: {
        'Cookie': authCookie
      }
    });
    
    console.log('Dashboard status:', dashboardResponse.status);
    const dashboardHtml = await dashboardResponse.text();
    console.log('Dashboard HTML length:', dashboardHtml.length);
    
    // Verify dashboard content
    const hasWelcomeMessage = dashboardHtml.includes('Good ') && dashboardHtml.includes('Super Administrator');
    const hasStatsCards = dashboardHtml.includes('Total Centers');
    const hasQuickActions = dashboardHtml.includes('Quick Actions');
    const hasLogoutButton = dashboardHtml.includes('Sign out');
    
    console.log('✓ Has welcome message:', hasWelcomeMessage);
    console.log('✓ Has stats cards:', hasStatsCards);
    console.log('✓ Has quick actions:', hasQuickActions);
    console.log('✓ Has logout button:', hasLogoutButton);
    
    if (dashboardResponse.status === 200 && hasWelcomeMessage && hasStatsCards) {
      console.log('✅ Test 2 PASSED: Dashboard renders correctly with authentication');
      return { passed: true };
    } else {
      console.log('❌ Test 2 FAILED: Dashboard missing required elements');
      return { passed: false };
    }
    
  } catch (error) {
    console.log('❌ Test 2 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 3: Tenant-Specific Login Page
async function testTenantLoginPage() {
  console.log('\n🧪 Test 3: Tenant-Specific Login Page');
  
  try {
    const response = await fetch(`${BASE_URL}/login`, {
      method: 'GET',
      headers: {
        'X-Tenant-Subdomain': 'sunriseacademy'
      }
    });
    
    console.log('Status:', response.status);
    const html = await response.text();
    console.log('HTML Length:', html.length);
    
    // Check for tenant-specific branding
    const hasTenantName = html.includes('Sunrise Academy');
    const hasWelcomeBack = html.includes('Welcome back');
    const hasCreateAccount = html.includes('Create an account');
    
    console.log('✓ Has tenant name:', hasTenantName);
    console.log('✓ Has welcome back message:', hasWelcomeBack);
    console.log('✓ Has create account link:', hasCreateAccount);
    
    if (response.status === 200 && hasTenantName) {
      console.log('✅ Test 3 PASSED: Tenant-specific login page renders correctly');
      return { passed: true };
    } else {
      console.log('❌ Test 3 FAILED: Tenant branding not found');
      return { passed: false };
    }
    
  } catch (error) {
    console.log('❌ Test 3 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 4: Logout Functionality
async function testLogoutFunctionality() {
  console.log('\n🧪 Test 4: Logout Functionality');
  
  try {
    // First login
    const loginData = {
      email: '<EMAIL>',
      password: 'SuperAdmin123!'
    };
    
    const loginResponse = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      redirect: 'manual',
      body: JSON.stringify(loginData)
    });
    
    const authCookie = loginResponse.headers.get('set-cookie');
    console.log('Login successful:', !!authCookie);
    
    // Now logout
    const logoutResponse = await fetch(`${BASE_URL}/logout`, {
      method: 'POST',
      headers: {
        'Cookie': authCookie
      },
      redirect: 'manual'
    });
    
    console.log('Logout status:', logoutResponse.status);
    console.log('Logout redirect:', logoutResponse.headers.get('location'));
    
    const isRedirect = logoutResponse.status === 302 || logoutResponse.status === 301;
    const redirectsToLogin = logoutResponse.headers.get('location')?.includes('/login');
    
    console.log('✓ Is redirect:', isRedirect);
    console.log('✓ Redirects to login:', redirectsToLogin);
    
    if (isRedirect && redirectsToLogin) {
      console.log('✅ Test 4 PASSED: Logout functionality works correctly');
      return { passed: true };
    } else {
      console.log('❌ Test 4 FAILED: Logout did not redirect correctly');
      return { passed: false };
    }
    
  } catch (error) {
    console.log('❌ Test 4 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 5: Invalid Login Attempt
async function testInvalidLogin() {
  console.log('\n🧪 Test 5: Invalid Login Attempt');
  
  try {
    const loginData = {
      email: '<EMAIL>',
      password: 'wrongpassword'
    };
    
    const response = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      redirect: 'manual',
      body: JSON.stringify(loginData)
    });
    
    console.log('Status:', response.status);
    console.log('Location:', response.headers.get('location'));
    
    const isRedirect = response.status === 302 || response.status === 301;
    const redirectsToLoginWithError = response.headers.get('location')?.includes('/login?error=');
    
    console.log('✓ Is redirect:', isRedirect);
    console.log('✓ Redirects to login with error:', redirectsToLoginWithError);
    
    if (isRedirect && redirectsToLoginWithError) {
      console.log('✅ Test 5 PASSED: Invalid login handled correctly');
      return { passed: true };
    } else {
      console.log('❌ Test 5 FAILED: Invalid login not handled correctly');
      return { passed: false };
    }
    
  } catch (error) {
    console.log('❌ Test 5 FAILED:', error.message);
    return { passed: false };
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Frontend Integration Tests...\n');
  
  const tests = [
    testCompleteLoginFlow,
    testDashboardWithAuth,
    testTenantLoginPage,
    testLogoutFunctionality,
    testInvalidLogin
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await test();
    results.push(result);
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // Summary
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  console.log('\n📊 Frontend Integration Test Summary:');
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 All frontend integration tests passed! The SSR frontend is working perfectly.');
  } else {
    console.log('\n⚠️  Some frontend integration tests failed. Please check the implementation.');
  }
  
  return { passed, total, success: passed === total };
}

// Run tests if this file is executed directly
if (import.meta.main) {
  runAllTests();
}

export { runAllTests };
