// Single test to verify super admin login
const BASE_URL = 'http://localhost:3000';

async function testSuperAdminLogin() {
  console.log('🧪 Testing Super Admin Login');
  
  const response = await fetch(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'SuperAdmin123!'
    })
  });
  
  const data = await response.json();
  
  console.log('Status:', response.status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  if (response.status === 200 && data.token) {
    console.log('✅ Super Admin login successful!');
    return true;
  } else if (response.status === 429) {
    console.log('⏳ Rate limited - this is expected after multiple tests');
    return 'rate_limited';
  } else {
    console.log('❌ Super Admin login failed');
    return false;
  }
}

testSuperAdminLogin();
