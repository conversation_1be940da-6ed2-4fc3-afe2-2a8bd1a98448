// Frontend SSR Tests for Teaching Center SaaS Platform

const BASE_URL = 'http://localhost:3000';

// Test 1: Login Page Rendering
async function testLoginPageRendering() {
  console.log('\n🧪 Test 1: Login Page Rendering');
  
  try {
    const response = await fetch(`${BASE_URL}/login`, {
      method: 'GET',
      headers: {
        'X-Tenant-Subdomain': 'sunriseacademy'
      }
    });
    
    const html = await response.text();
    
    console.log('Status:', response.status);
    console.log('Content-Type:', response.headers.get('content-type'));
    console.log('HTML Length:', html.length);
    
    // Check for key elements
    const hasTitle = html.includes('<title>');
    const hasForm = html.includes('<form');
    const hasEmailInput = html.includes('type="email"');
    const hasPasswordInput = html.includes('type="password"');
    const hasTenantBranding = html.includes('Sunrise Academy');
    
    console.log('✓ Has title tag:', hasTitle);
    console.log('✓ Has form:', hasForm);
    console.log('✓ Has email input:', hasEmailInput);
    console.log('✓ Has password input:', hasPasswordInput);
    console.log('✓ Has tenant branding:', hasTenantBranding);
    
    if (response.status === 200 && hasTitle && hasForm && hasEmailInput && hasPasswordInput) {
      console.log('✅ Test 1 PASSED: Login page renders correctly');
      return { passed: true };
    } else {
      console.log('❌ Test 1 FAILED: Login page missing required elements');
      return { passed: false };
    }
  } catch (error) {
    console.log('❌ Test 1 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 2: Admin Login Page (No Tenant)
async function testAdminLoginPage() {
  console.log('\n🧪 Test 2: Admin Login Page (No Tenant)');
  
  try {
    const response = await fetch(`${BASE_URL}/login`, {
      method: 'GET'
    });
    
    const html = await response.text();
    
    console.log('Status:', response.status);
    console.log('HTML Length:', html.length);
    
    // Check for admin-specific elements
    const hasTitle = html.includes('<title>');
    const hasAdminBranding = html.includes('Admin Sign In') || html.includes('Platform Administration');
    const hasForm = html.includes('<form');
    
    console.log('✓ Has title tag:', hasTitle);
    console.log('✓ Has admin branding:', hasAdminBranding);
    console.log('✓ Has form:', hasForm);
    
    if (response.status === 200 && hasTitle && hasForm) {
      console.log('✅ Test 2 PASSED: Admin login page renders correctly');
      return { passed: true };
    } else {
      console.log('❌ Test 2 FAILED: Admin login page missing required elements');
      return { passed: false };
    }
  } catch (error) {
    console.log('❌ Test 2 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 3: Static CSS File Serving
async function testStaticCSSServing() {
  console.log('\n🧪 Test 3: Static CSS File Serving');
  
  try {
    const response = await fetch(`${BASE_URL}/styles/main.css`, {
      method: 'GET'
    });
    
    const css = await response.text();
    
    console.log('Status:', response.status);
    console.log('Content-Type:', response.headers.get('content-type'));
    console.log('CSS Length:', css.length);
    
    // Check for key CSS classes
    const hasBaseStyles = css.includes('body {');
    const hasUtilities = css.includes('.btn');
    const hasFormStyles = css.includes('.form-input');
    const hasCardStyles = css.includes('.card');
    
    console.log('✓ Has base styles:', hasBaseStyles);
    console.log('✓ Has utility classes:', hasUtilities);
    console.log('✓ Has form styles:', hasFormStyles);
    console.log('✓ Has card styles:', hasCardStyles);
    
    if (response.status === 200 && hasBaseStyles && hasUtilities) {
      console.log('✅ Test 3 PASSED: CSS file serves correctly');
      return { passed: true };
    } else {
      console.log('❌ Test 3 FAILED: CSS file missing or incomplete');
      return { passed: false };
    }
  } catch (error) {
    console.log('❌ Test 3 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 4: Static JavaScript File Serving
async function testStaticJSServing() {
  console.log('\n🧪 Test 4: Static JavaScript File Serving');
  
  try {
    const response = await fetch(`${BASE_URL}/js/main.js`, {
      method: 'GET'
    });
    
    const js = await response.text();
    
    console.log('Status:', response.status);
    console.log('Content-Type:', response.headers.get('content-type'));
    console.log('JS Length:', js.length);
    
    // Check for key JavaScript functions
    const hasInitialization = js.includes('DOMContentLoaded');
    const hasFormValidation = js.includes('validateForm');
    const hasNotifications = js.includes('showNotification');
    const hasTeachingCenterObject = js.includes('window.TeachingCenter');
    
    console.log('✓ Has initialization:', hasInitialization);
    console.log('✓ Has form validation:', hasFormValidation);
    console.log('✓ Has notifications:', hasNotifications);
    console.log('✓ Has global object:', hasTeachingCenterObject);
    
    if (response.status === 200 && hasInitialization && hasFormValidation) {
      console.log('✅ Test 4 PASSED: JavaScript file serves correctly');
      return { passed: true };
    } else {
      console.log('❌ Test 4 FAILED: JavaScript file missing or incomplete');
      return { passed: false };
    }
  } catch (error) {
    console.log('❌ Test 4 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 5: Root Redirect to Login
async function testRootRedirect() {
  console.log('\n🧪 Test 5: Root Redirect to Login');
  
  try {
    const response = await fetch(`${BASE_URL}/`, {
      method: 'GET',
      redirect: 'manual'
    });
    
    console.log('Status:', response.status);
    console.log('Location:', response.headers.get('location'));
    
    const isRedirect = response.status === 302 || response.status === 301;
    const redirectsToLogin = response.headers.get('location')?.includes('/login');
    
    console.log('✓ Is redirect:', isRedirect);
    console.log('✓ Redirects to login:', redirectsToLogin);
    
    if (isRedirect && redirectsToLogin) {
      console.log('✅ Test 5 PASSED: Root redirects to login correctly');
      return { passed: true };
    } else {
      console.log('❌ Test 5 FAILED: Root does not redirect to login');
      return { passed: false };
    }
  } catch (error) {
    console.log('❌ Test 5 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 6: Dashboard Access Without Auth
async function testDashboardWithoutAuth() {
  console.log('\n🧪 Test 6: Dashboard Access Without Auth');
  
  try {
    const response = await fetch(`${BASE_URL}/dashboard`, {
      method: 'GET',
      redirect: 'manual',
      headers: {
        'X-Tenant-Subdomain': 'sunriseacademy'
      }
    });
    
    console.log('Status:', response.status);
    console.log('Location:', response.headers.get('location'));
    
    const isRedirect = response.status === 302 || response.status === 301;
    const redirectsToLogin = response.headers.get('location')?.includes('/login');
    
    console.log('✓ Is redirect:', isRedirect);
    console.log('✓ Redirects to login:', redirectsToLogin);
    
    if (isRedirect && redirectsToLogin) {
      console.log('✅ Test 6 PASSED: Dashboard redirects to login when not authenticated');
      return { passed: true };
    } else {
      console.log('❌ Test 6 FAILED: Dashboard should redirect to login');
      return { passed: false };
    }
  } catch (error) {
    console.log('❌ Test 6 FAILED:', error.message);
    return { passed: false };
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Frontend SSR Tests...\n');
  
  const tests = [
    testLoginPageRendering,
    testAdminLoginPage,
    testStaticCSSServing,
    testStaticJSServing,
    testRootRedirect,
    testDashboardWithoutAuth
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await test();
    results.push(result);
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // Summary
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  console.log('\n📊 Frontend SSR Test Summary:');
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 All frontend tests passed! Frontend SSR is working correctly.');
  } else {
    console.log('\n⚠️  Some frontend tests failed. Please check the implementation.');
  }
  
  return { passed, total, success: passed === total };
}

// Run tests if this file is executed directly
if (import.meta.main) {
  runAllTests();
}

export { runAllTests };
