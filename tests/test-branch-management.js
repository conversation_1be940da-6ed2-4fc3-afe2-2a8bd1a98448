#!/usr/bin/env bun

/**
 * Test Suite: Branch Management (Tests 26-29)
 * 
 * Tests for branch management functionality including:
 * - Branch creation and management
 * - Staff assignment to branches
 * - Branch-specific configurations
 * - Multi-branch operations
 */

const BASE_URL = 'http://localhost:3000';

console.log('🏢 Starting Branch Management Tests...\n');

// Test data
const testData = {
  superAdmin: {
    email: '<EMAIL>',
    password: 'SuperAdmin123!'
  },
  centerAdmin: {
    email: '<EMAIL>',
    password: 'Owner123!'
  },
  branch: {
    name: 'Main Campus',
    address: '123 Education Street, Dhaka',
    phone: '+8801234567890',
    email: '<EMAIL>',
    capacity: 500,
    facilities: ['Library', 'Computer Lab', 'Science Lab'],
    status: 'active'
  },
  secondBranch: {
    name: 'Secondary Campus',
    address: '456 Learning Avenue, Dhaka',
    phone: '+8801234567891',
    email: '<EMAIL>',
    capacity: 300,
    facilities: ['Library', 'Computer Lab'],
    status: 'active'
  },
  staff: {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+8801234567892',
    role: 'teacher',
    subjects: ['Mathematics', 'Physics']
  }
};

let sessionCookies = {};
let createdBranches = [];
let createdStaff = [];

// Helper function to login and get session cookie
async function login(credentials) {
  const response = await fetch(`${BASE_URL}/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(credentials),
    redirect: 'manual' // Don't follow redirects
  });

  // Extract session cookie from response
  const cookies = response.headers.get('set-cookie');
  return cookies ? cookies.split(';')[0] : null;
}

// Test 26: Branch Creation and Management
console.log('🧪 Test 26: Branch Creation and Management');
try {
  // Login as center admin
  sessionCookies.centerAdmin = await login(testData.centerAdmin);

  if (!sessionCookies.centerAdmin) {
    console.log('❌ Test 26 FAILED: Could not authenticate center admin');
  } else {
    // Create a branch
    const createResponse = await fetch(`${BASE_URL}/api/center/branches`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': sessionCookies.centerAdmin,
        'X-Tenant-ID': 'sunriseacademy'
      },
      body: JSON.stringify(testData.branch)
    });
    
    const createResult = await createResponse.json();
    console.log(`Create Status: ${createResponse.status}`);
    console.log(`Create Success: ${createResult.success}`);
    
    if (createResponse.ok && createResult.success) {
      createdBranches.push(createResult.data.id);
      
      // Get branch details
      const getResponse = await fetch(`${BASE_URL}/api/center/branches/${createResult.data.id}`, {
        headers: {
          'Cookie': sessionCookies.centerAdmin,
          'X-Tenant-ID': 'sunriseacademy'
        }
      });
      
      const getResult = await getResponse.json();
      console.log(`Get Status: ${getResponse.status}`);
      console.log(`Branch Name: ${getResult.data?.name}`);
      console.log(`Branch Address: ${getResult.data?.address}`);
      
      if (getResponse.ok && getResult.data?.name === testData.branch.name) {
        console.log('✅ Test 26 PASSED: Branch creation and retrieval works\n');
      } else {
        console.log('❌ Test 26 FAILED: Branch retrieval failed\n');
      }
    } else {
      console.log('❌ Test 26 FAILED: Branch creation failed\n');
    }
  }
} catch (error) {
  console.log(`❌ Test 26 FAILED: ${error.message}\n`);
}

// Test 27: Branch List and Filtering
console.log('🧪 Test 27: Branch List and Filtering');
try {
  // Create second branch
  const createResponse = await fetch(`${BASE_URL}/api/center/branches`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cookie': sessionCookies.centerAdmin,
      'X-Tenant-ID': 'sunriseacademy'
    },
    body: JSON.stringify(testData.secondBranch)
  });

  const createResult = await createResponse.json();
  if (createResponse.ok && createResult.success) {
    createdBranches.push(createResult.data.id);
  }

  // Get all branches
  const listResponse = await fetch(`${BASE_URL}/api/center/branches`, {
    headers: {
      'Cookie': sessionCookies.centerAdmin,
      'X-Tenant-ID': 'sunriseacademy'
    }
  });
  
  const listResult = await listResponse.json();
  console.log(`List Status: ${listResponse.status}`);
  console.log(`Total Branches: ${listResult.data?.branches?.length || 0}`);
  
  const hasMainCampus = listResult.data?.branches?.some(b => b.name === testData.branch.name);
  const hasSecondaryCampus = listResult.data?.branches?.some(b => b.name === testData.secondBranch.name);
  
  console.log(`✓ Has Main Campus: ${hasMainCampus}`);
  console.log(`✓ Has Secondary Campus: ${hasSecondaryCampus}`);
  
  if (listResponse.ok && hasMainCampus && hasSecondaryCampus) {
    console.log('✅ Test 27 PASSED: Branch listing works\n');
  } else {
    console.log('❌ Test 27 FAILED: Branch listing failed\n');
  }
} catch (error) {
  console.log(`❌ Test 27 FAILED: ${error.message}\n`);
}

// Test 28: Staff Assignment to Branches
console.log('🧪 Test 28: Staff Assignment to Branches');
try {
  if (createdBranches.length > 0) {
    // Create staff member
    const staffData = {
      ...testData.staff,
      branch_id: createdBranches[0]
    };
    
    const createStaffResponse = await fetch(`${BASE_URL}/api/center/staff`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': sessionCookies.centerAdmin,
        'X-Tenant-ID': 'sunriseacademy'
      },
      body: JSON.stringify(staffData)
    });
    
    const createStaffResult = await createStaffResponse.json();
    console.log(`Create Staff Status: ${createStaffResponse.status}`);
    console.log(`Create Staff Success: ${createStaffResult.success}`);
    
    if (createStaffResponse.ok && createStaffResult.success) {
      createdStaff.push(createStaffResult.data.id);
      
      // Get branch staff
      const branchStaffResponse = await fetch(`${BASE_URL}/api/center/branches/${createdBranches[0]}/staff`, {
        headers: {
          'Cookie': sessionCookies.centerAdmin,
          'X-Tenant-ID': 'sunriseacademy'
        }
      });
      
      const branchStaffResult = await branchStaffResponse.json();
      console.log(`Branch Staff Status: ${branchStaffResponse.status}`);
      console.log(`Staff Count: ${branchStaffResult.data?.staff?.length || 0}`);
      
      const hasAssignedStaff = branchStaffResult.data?.staff?.some(s => s.name === testData.staff.name);
      console.log(`✓ Has Assigned Staff: ${hasAssignedStaff}`);
      
      if (branchStaffResponse.ok && hasAssignedStaff) {
        console.log('✅ Test 28 PASSED: Staff assignment to branches works\n');
      } else {
        console.log('❌ Test 28 FAILED: Staff assignment failed\n');
      }
    } else {
      console.log('❌ Test 28 FAILED: Staff creation failed\n');
    }
  } else {
    console.log('❌ Test 28 FAILED: No branches available for staff assignment\n');
  }
} catch (error) {
  console.log(`❌ Test 28 FAILED: ${error.message}\n`);
}

// Test 29: Branch Update and Deactivation
console.log('🧪 Test 29: Branch Update and Deactivation');
try {
  if (createdBranches.length > 0) {
    const branchId = createdBranches[0];
    
    // Update branch
    const updateData = {
      name: 'Updated Main Campus',
      capacity: 600,
      facilities: ['Library', 'Computer Lab', 'Science Lab', 'Sports Complex']
    };
    
    const updateResponse = await fetch(`${BASE_URL}/api/center/branches/${branchId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': sessionCookies.centerAdmin,
        'X-Tenant-ID': 'sunriseacademy'
      },
      body: JSON.stringify(updateData)
    });
    
    const updateResult = await updateResponse.json();
    console.log(`Update Status: ${updateResponse.status}`);
    console.log(`Update Success: ${updateResult.success}`);
    
    if (updateResponse.ok && updateResult.success) {
      // Verify update
      const getResponse = await fetch(`${BASE_URL}/api/center/branches/${branchId}`, {
        headers: {
          'Cookie': sessionCookies.centerAdmin,
          'X-Tenant-ID': 'sunriseacademy'
        }
      });
      
      const getResult = await getResponse.json();
      const isUpdated = getResult.data?.name === updateData.name && 
                       getResult.data?.capacity === updateData.capacity;
      
      console.log(`✓ Name Updated: ${getResult.data?.name === updateData.name}`);
      console.log(`✓ Capacity Updated: ${getResult.data?.capacity === updateData.capacity}`);
      
      if (isUpdated) {
        console.log('✅ Test 29 PASSED: Branch update and management works\n');
      } else {
        console.log('❌ Test 29 FAILED: Branch update verification failed\n');
      }
    } else {
      console.log('❌ Test 29 FAILED: Branch update failed\n');
    }
  } else {
    console.log('❌ Test 29 FAILED: No branches available for update\n');
  }
} catch (error) {
  console.log(`❌ Test 29 FAILED: ${error.message}\n`);
}

console.log('🎉 Branch Management Tests Completed!');
