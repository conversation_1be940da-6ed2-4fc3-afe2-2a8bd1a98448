// Authentication Tests using fetch API
// This file implements TDD tests for authentication functionality

const BASE_URL = 'http://localhost:3000';

// Test credentials from test-credentials.md
const TEST_CREDENTIALS = {
  superAdmin: {
    email: '<EMAIL>',
    password: 'SuperAdmin123!'
  },
  centerOwner: {
    email: '<EMAIL>',
    password: 'Owner123!'
  },
  blockedUser: {
    email: '<EMAIL>',
    password: 'Blocked123!'
  }
};

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });
  
  const data = await response.json();
  return { status: response.status, data };
}

// Test 1: Admin Login (Valid Credentials)
async function testAdminLoginValid() {
  console.log('🧪 Test 1: <PERSON><PERSON> (Valid Credentials)');
  
  const { status, data } = await apiRequest('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify(TEST_CREDENTIALS.superAdmin)
  });
  
  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  // Assertions
  if (status === 200) {
    if (data.message === 'Login successful' && data.token && data.user) {
      console.log('✅ Test 1 PASSED: Admin login successful');
      return { passed: true, token: data.token };
    } else {
      console.log('❌ Test 1 FAILED: Invalid response structure');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 1 FAILED: Expected status 200, got', status);
    return { passed: false };
  }
}

// Test 2: Admin Login (Invalid Credentials)
async function testAdminLoginInvalid() {
  console.log('\n🧪 Test 2: Admin Login (Invalid Credentials)');
  
  const { status, data } = await apiRequest('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: TEST_CREDENTIALS.superAdmin.email,
      password: 'WrongPassword123!'
    })
  });
  
  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  // Assertions
  if (status === 401) {
    if (data.error === 'Authentication Failed') {
      console.log('✅ Test 2 PASSED: Invalid credentials rejected');
      return { passed: true };
    } else {
      console.log('❌ Test 2 FAILED: Wrong error message');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 2 FAILED: Expected status 401, got', status);
    return { passed: false };
  }
}

// Test 3: Admin Logout
async function testAdminLogout(token) {
  console.log('\n🧪 Test 3: Admin Logout');
  
  const { status, data } = await apiRequest('/api/auth/logout', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  // Assertions
  if (status === 200) {
    if (data.message === 'Logout successful') {
      console.log('✅ Test 3 PASSED: Logout successful');
      return { passed: true };
    } else {
      console.log('❌ Test 3 FAILED: Wrong success message');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 3 FAILED: Expected status 200, got', status);
    return { passed: false };
  }
}

// Test 4: Center Owner Login (Valid Credentials)
async function testCenterOwnerLogin() {
  console.log('\n🧪 Test 4: Center Owner Login (Valid Credentials)');
  
  const { status, data } = await apiRequest('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify(TEST_CREDENTIALS.centerOwner)
  });
  
  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  // Assertions
  if (status === 200) {
    if (data.message === 'Login successful' && data.token && data.user) {
      console.log('✅ Test 4 PASSED: Center owner login successful');
      return { passed: true };
    } else {
      console.log('❌ Test 4 FAILED: Invalid response structure');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 4 FAILED: Expected status 200, got', status);
    return { passed: false };
  }
}

// Test 5: Blocked User Cannot Login
async function testBlockedUserLogin() {
  console.log('\n🧪 Test 5: Blocked User Cannot Login');
  
  const { status, data } = await apiRequest('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify(TEST_CREDENTIALS.blockedUser)
  });
  
  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  // Assertions
  if (status === 403) {
    if (data.error === 'Account Disabled') {
      console.log('✅ Test 5 PASSED: Blocked user rejected');
      return { passed: true };
    } else {
      console.log('❌ Test 5 FAILED: Wrong error message');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 5 FAILED: Expected status 403, got', status);
    return { passed: false };
  }
}

// Test 6: Forgot Password Flow
async function testForgotPassword() {
  console.log('\n🧪 Test 6: Forgot Password Flow');
  
  const { status, data } = await apiRequest('/api/auth/forgot-password', {
    method: 'POST',
    body: JSON.stringify({
      email: TEST_CREDENTIALS.superAdmin.email
    })
  });
  
  console.log('Status:', status);
  console.log('Response:', JSON.stringify(data, null, 2));
  
  // Assertions
  if (status === 200) {
    if (data.message.includes('password reset link has been sent')) {
      console.log('✅ Test 6 PASSED: Forgot password request accepted');
      return { passed: true };
    } else {
      console.log('❌ Test 6 FAILED: Wrong success message');
      return { passed: false };
    }
  } else {
    console.log('❌ Test 6 FAILED: Expected status 200, got', status);
    return { passed: false };
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Authentication Tests\n');
  
  const results = [];
  
  // Test 1: Admin Login (Valid)
  const test1 = await testAdminLoginValid();
  results.push(test1);
  
  // Test 2: Admin Login (Invalid)
  const test2 = await testAdminLoginInvalid();
  results.push(test2);
  
  // Test 3: Admin Logout (only if login was successful)
  if (test1.passed && test1.token) {
    const test3 = await testAdminLogout(test1.token);
    results.push(test3);
  } else {
    console.log('\n⏭️  Skipping Test 3: Admin Logout (login failed)');
    results.push({ passed: false, skipped: true });
  }
  
  // Test 4: Center Owner Login
  const test4 = await testCenterOwnerLogin();
  results.push(test4);
  
  // Test 5: Blocked User Login
  const test5 = await testBlockedUserLogin();
  results.push(test5);
  
  // Test 6: Forgot Password
  const test6 = await testForgotPassword();
  results.push(test6);
  
  // Summary
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  const skipped = results.filter(r => r.skipped).length;
  
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed - skipped}/${total}`);
  console.log(`⏭️  Skipped: ${skipped}/${total}`);
  
  if (passed === total - skipped) {
    console.log('\n🎉 All tests passed!');
  } else {
    console.log('\n🔧 Some tests failed. Check implementation.');
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error);
}

export default runAllTests;
