const { test, expect } = require('@playwright/test');

test.describe('UI Screens CSS Testing', () => {
  
  test('Login page should have proper Tailwind CSS styling', async ({ page }) => {
    await page.goto('/login');
    
    // Check if the page loads
    await expect(page).toHaveTitle(/Sign In/);
    
    // Check for gradient background
    const gradientDiv = page.locator('.bg-gradient-to-br');
    await expect(gradientDiv).toBeVisible();
    
    // Check for form elements
    const emailInput = page.locator('input[type="email"]');
    const passwordInput = page.locator('input[type="password"]');
    const submitButton = page.locator('button[type="submit"]');
    
    await expect(emailInput).toBeVisible();
    await expect(passwordInput).toBeVisible();
    await expect(submitButton).toBeVisible();
    
    // Check responsive classes
    const responsiveElement = page.locator('.lg\\:flex');
    await expect(responsiveElement).toBeVisible();
    
    // Check if CSS is properly loaded
    const bodyElement = page.locator('body');
    await expect(bodyElement).toHaveClass(/min-h-screen/);
  });

  test('Login page should be responsive', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto('/login');
    
    const desktopLayout = page.locator('.lg\\:w-1\\/2');
    await expect(desktopLayout).toBeVisible();
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    
    // Mobile layout should still be functional
    const form = page.locator('#loginForm');
    await expect(form).toBeVisible();
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    
    await expect(form).toBeVisible();
  });

  test('Admin dashboard should load with proper styling', async ({ page }) => {
    // First login as super admin
    await page.goto('/login');
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'SuperAdmin123!');
    await page.click('button[type="submit"]');
    
    // Should redirect to admin dashboard
    await page.waitForURL('/admin/dashboard');
    
    // Check for dashboard elements
    const statsGrid = page.locator('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4');
    await expect(statsGrid).toBeVisible();
    
    // Check for statistics cards
    const statsCards = page.locator('.card');
    await expect(statsCards.first()).toBeVisible();
    
    // Check for header
    const header = page.locator('header');
    await expect(header).toBeVisible();
    
    // Check for welcome section
    const welcomeSection = page.locator('.bg-gradient-to-r');
    await expect(welcomeSection).toBeVisible();
  });

  test('Form inputs should have proper focus states', async ({ page }) => {
    await page.goto('/login');
    
    const emailInput = page.locator('input[type="email"]');
    
    // Focus the input
    await emailInput.focus();
    
    // Check if focus styles are applied (this might need adjustment based on actual CSS)
    const focusedInput = page.locator('input[type="email"]:focus');
    await expect(focusedInput).toBeFocused();
  });

  test('Dark mode toggle should work', async ({ page }) => {
    await page.goto('/login');
    
    // Look for theme toggle button
    const themeToggle = page.locator('button').filter({ hasText: /dark|light/i }).first();
    
    if (await themeToggle.isVisible()) {
      await themeToggle.click();
      
      // Check if theme class changes
      const body = page.locator('body');
      // This test might need adjustment based on how dark mode is implemented
      await expect(body).toHaveClass(/theme-/);
    }
  });

  test('Navigation elements should be properly styled', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'SuperAdmin123!');
    await page.click('button[type="submit"]');
    
    await page.waitForURL('/admin/dashboard');
    
    // Check for navigation elements
    const signOutButton = page.locator('button').filter({ hasText: /sign out/i });
    await expect(signOutButton).toBeVisible();
    
    // Check for user info display
    const userInfo = page.locator('.text-sm.font-medium.text-gray-900');
    await expect(userInfo).toBeVisible();
  });

  test('CSS animations and transitions should work', async ({ page }) => {
    await page.goto('/login');
    
    // Check for transition classes
    const transitionElements = page.locator('.transition-all, .transition-colors');
    await expect(transitionElements.first()).toBeVisible();
    
    // Test button hover state
    const submitButton = page.locator('button[type="submit"]');
    await submitButton.hover();
    
    // The hover effect should be visible (this is hard to test automatically)
    await expect(submitButton).toBeVisible();
  });

  test('Error states should be properly styled', async ({ page }) => {
    await page.goto('/login?error=Invalid%20credentials');
    
    // Check if error message is displayed with proper styling
    // This depends on how errors are displayed in your implementation
    const errorMessage = page.locator('.text-red-600, .alert-error, .error');
    
    // If error styling exists, it should be visible
    if (await errorMessage.count() > 0) {
      await expect(errorMessage.first()).toBeVisible();
    }
  });

});
