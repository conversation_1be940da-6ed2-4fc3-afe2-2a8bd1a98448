// Debug dashboard content
const BASE_URL = 'http://localhost:3000';

async function debugDashboard() {
  console.log('🔍 Debugging dashboard content...');
  
  try {
    // First login
    const loginData = {
      email: '<EMAIL>',
      password: 'SuperAdmin123!'
    };
    
    const loginResponse = await fetch(`${BASE_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      redirect: 'manual',
      body: JSON.stringify(loginData)
    });
    
    const authCookie = loginResponse.headers.get('set-cookie');
    console.log('Auth cookie received:', !!authCookie);
    
    if (!authCookie) {
      console.log('❌ No auth cookie received');
      return;
    }
    
    // Now access dashboard with auth cookie
    const dashboardResponse = await fetch(`${BASE_URL}/admin/dashboard`, {
      method: 'GET',
      headers: {
        'Cookie': authCookie
      }
    });
    
    console.log('Dashboard status:', dashboardResponse.status);
    const dashboardHtml = await dashboardResponse.text();
    console.log('Dashboard HTML length:', dashboardHtml.length);
    
    // Check for specific content
    console.log('\n🔍 Content Analysis:');
    console.log('Contains "Good ":', dashboardHtml.includes('Good '));
    console.log('Contains "superadmin":', dashboardHtml.includes('superadmin'));
    console.log('Contains "Super Admin":', dashboardHtml.includes('Super Admin'));
    console.log('Contains "Total Centers":', dashboardHtml.includes('Total Centers'));
    console.log('Contains "Quick Actions":', dashboardHtml.includes('Quick Actions'));
    console.log('Contains "Sign out":', dashboardHtml.includes('Sign out'));
    
    // Extract welcome section
    const welcomeMatch = dashboardHtml.match(/Good [^<]+/);
    if (welcomeMatch) {
      console.log('\n🎯 Welcome message found:', welcomeMatch[0]);
    } else {
      console.log('\n❌ No welcome message found');
    }
    
    // Extract user name references
    const userMatches = dashboardHtml.match(/superadmin|Super Admin|admin/gi);
    if (userMatches) {
      console.log('\n👤 User references found:', userMatches);
    } else {
      console.log('\n❌ No user references found');
    }
    
    // Show a snippet of the HTML around the welcome area
    const bodyMatch = dashboardHtml.match(/<main[^>]*>([\s\S]*?)<\/main>/);
    if (bodyMatch) {
      const mainContent = bodyMatch[1];
      const welcomeSection = mainContent.substring(0, 500);
      console.log('\n📄 Main content preview (first 500 chars):');
      console.log(welcomeSection);
    }
    
  } catch (error) {
    console.log('❌ Debug failed:', error.message);
  }
}

debugDashboard();
