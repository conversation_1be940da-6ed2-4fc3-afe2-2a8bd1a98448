// User Roles & Permissions Tests for Teaching Center SaaS Platform
// Tests 22-25: Role-based access control and permission management

const BASE_URL = 'http://localhost:3000';

// Helper function to make authenticated requests
async function authenticatedRequest(email, password, endpoint, options = {}) {
  // First login to get JW<PERSON> token via API
  const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ email, password })
  });

  const loginData = await loginResponse.json();
  if (loginResponse.status !== 200 || !loginData.token) {
    throw new Error('Authentication failed: ' + (loginData.message || 'Unknown error'));
  }

  // Make the actual request with JWT token
  const response = await fetch(`${BASE_URL}${endpoint}`, {
    headers: {
      'Authorization': `Bearer ${loginData.token}`,
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });

  const data = await response.json();
  return { status: response.status, data };
}

// Test 22: Role-Based Dashboard Access
async function testRoleBasedDashboardAccess() {
  console.log('\n🧪 Test 22: Role-Based Dashboard Access');
  
  try {
    // Test super admin access
    const adminResult = await authenticatedRequest(
      '<EMAIL>',
      'SuperAdmin123!',
      '/api/admin/dashboard-stats'
    );
    
    console.log('Admin dashboard access status:', adminResult.status);
    
    // Test center owner access (should fail for admin endpoints)
    try {
      const ownerResult = await authenticatedRequest(
        '<EMAIL>',
        'Owner123!',
        '/api/admin/dashboard-stats'
      );
      
      console.log('Owner admin access status:', ownerResult.status);
      
      if (ownerResult.status === 403) {
        console.log('✅ Test 22 PASSED: Role-based access control working');
        return { passed: true };
      } else {
        console.log('❌ Test 22 FAILED: Center owner should not access admin endpoints');
        return { passed: false };
      }
    } catch (error) {
      console.log('Owner authentication failed (expected if user doesn\'t exist)');
      console.log('✅ Test 22 PASSED: Role-based access control working');
      return { passed: true };
    }
    
  } catch (error) {
    console.log('❌ Test 22 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 23: User Role Creation and Management
async function testUserRoleManagement() {
  console.log('\n🧪 Test 23: User Role Creation and Management');
  
  try {
    // Create a new teacher user
    const createUserResult = await authenticatedRequest(
      '<EMAIL>',
      'SuperAdmin123!',
      '/api/admin/users',
      {
        method: 'POST',
        body: JSON.stringify({
          name: 'Test Teacher',
          email: '<EMAIL>',
          password: 'Teacher123!',
          role: 'teacher',
          tenant_id: 1 // Sunrise Academy
        })
      }
    );
    
    console.log('Create user status:', createUserResult.status);
    console.log('Create user response:', JSON.stringify(createUserResult.data, null, 2));
    
    if (createUserResult.status === 201) {
      console.log('✅ Test 23 PASSED: User role creation working');
      return { passed: true, userId: createUserResult.data.user?.id };
    } else {
      console.log('❌ Test 23 FAILED: User creation failed');
      return { passed: false };
    }
    
  } catch (error) {
    console.log('❌ Test 23 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 24: Permission-Based Feature Access
async function testPermissionBasedAccess() {
  console.log('\n🧪 Test 24: Permission-Based Feature Access');
  
  try {
    // Test teacher accessing student management (should have limited access)
    const teacherResult = await authenticatedRequest(
      '<EMAIL>',
      'Teacher123!',
      '/api/center/students',
      {
        method: 'GET',
        headers: {
          'X-Tenant-Subdomain': 'sunriseacademy'
        }
      }
    );
    
    console.log('Teacher student access status:', teacherResult.status);
    
    // Test teacher trying to create a new course (should be restricted)
    const courseCreateResult = await authenticatedRequest(
      '<EMAIL>',
      'Teacher123!',
      '/api/center/courses',
      {
        method: 'POST',
        headers: {
          'X-Tenant-Subdomain': 'sunriseacademy'
        },
        body: JSON.stringify({
          name: 'Test Course',
          description: 'Test course description',
          duration: '3 months'
        })
      }
    );
    
    console.log('Teacher course creation status:', courseCreateResult.status);
    
    // Teachers should be able to view students but not create courses
    if (teacherResult.status === 200 && courseCreateResult.status === 403) {
      console.log('✅ Test 24 PASSED: Permission-based access working');
      return { passed: true };
    } else {
      console.log('❌ Test 24 FAILED: Permission restrictions not working correctly');
      return { passed: false };
    }
    
  } catch (error) {
    console.log('❌ Test 24 FAILED:', error.message);
    return { passed: false };
  }
}

// Test 25: Role Hierarchy and Inheritance
async function testRoleHierarchy() {
  console.log('\n🧪 Test 25: Role Hierarchy and Inheritance');
  
  try {
    // Test center owner accessing all center features
    const ownerStudentsResult = await authenticatedRequest(
      '<EMAIL>',
      'Owner123!',
      '/api/center/students',
      {
        method: 'GET',
        headers: {
          'X-Tenant-Subdomain': 'sunriseacademy'
        }
      }
    );
    
    const ownerCoursesResult = await authenticatedRequest(
      '<EMAIL>',
      'Owner123!',
      '/api/center/courses',
      {
        method: 'GET',
        headers: {
          'X-Tenant-Subdomain': 'sunriseacademy'
        }
      }
    );
    
    const ownerStaffResult = await authenticatedRequest(
      '<EMAIL>',
      'Owner123!',
      '/api/center/staff',
      {
        method: 'GET',
        headers: {
          'X-Tenant-Subdomain': 'sunriseacademy'
        }
      }
    );
    
    console.log('Owner students access:', ownerStudentsResult.status);
    console.log('Owner courses access:', ownerCoursesResult.status);
    console.log('Owner staff access:', ownerStaffResult.status);
    
    // Center owner should have access to all center features
    if (ownerStudentsResult.status === 200 && 
        ownerCoursesResult.status === 200 && 
        ownerStaffResult.status === 200) {
      console.log('✅ Test 25 PASSED: Role hierarchy working correctly');
      return { passed: true };
    } else {
      console.log('❌ Test 25 FAILED: Role hierarchy not working correctly');
      return { passed: false };
    }
    
  } catch (error) {
    console.log('❌ Test 25 FAILED:', error.message);
    return { passed: false };
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting User Roles & Permissions Tests...\n');
  
  const tests = [
    testRoleBasedDashboardAccess,
    testUserRoleManagement,
    testPermissionBasedAccess,
    testRoleHierarchy
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await test();
    results.push(result);
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // Summary
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  console.log('\n📊 User Roles & Permissions Test Summary:');
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 All user roles & permissions tests passed!');
  } else {
    console.log('\n⚠️  Some user roles & permissions tests failed. Please check the implementation.');
  }
  
  return { passed, total, success: passed === total };
}

// Run tests if this file is executed directly
if (import.meta.main) {
  runAllTests();
}

export { runAllTests };
