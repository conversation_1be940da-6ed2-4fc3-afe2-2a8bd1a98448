# Center Management & Multi-Tenancy Tests

This file documents the TDD tests for center management and multi-tenancy functionality using Playwright MCP.

## Test 12: Create New Center with Subdomain

**Test Case**: Super admin should be able to create new centers with unique subdomains
**Endpoint**: POST /api/admin/centers
**Expected**: 201 status with center details

### Test Data:
```json
{
  "name": "New Test Center",
  "subdomain": "newtestcenter",
  "email": "<EMAIL>",
  "phone": "02-1234567",
  "address": "123 Test Street, Dhaka",
  "planId": 2
}
```

### Expected Response:
```json
{
  "message": "Center created successfully",
  "data": {
    "id": 4,
    "name": "New Test Center",
    "subdomain": "newtestcenter",
    "email": "<EMAIL>",
    "status": "active"
  }
}
```

## Test 13: Subdomain Routing (Multi-tenant Access)

**Test Case**: Different subdomains should route to different tenant contexts
**Endpoint**: GET /api/center/profile
**Expected**: 200 status with tenant-specific data

### Test Scenarios:
1. **sunriseacademy.localhost:3000** → Sunrise Academy data
2. **abccoaching.localhost:3000** → ABC Coaching data
3. **invalid.localhost:3000** → 400 error (tenant not found)

### Expected Response (Sunrise Academy):
```json
{
  "message": "Center profile retrieved successfully",
  "data": {
    "id": 1,
    "name": "Sunrise Academy",
    "subdomain": "sunriseacademy",
    "logoUrl": null,
    "themeColor": "#3B82F6",
    "status": "active"
  }
}
```

## Test 14: Tenant Data Isolation

**Test Case**: Users from one tenant should not access another tenant's data
**Endpoint**: GET /api/center/dashboard
**Expected**: 403 status when accessing wrong tenant

### Test Scenario:
- Login as Sunrise Academy user
- Try to access ABC Coaching data using ABC subdomain
- Should be rejected with 403 Forbidden

## Test 15: Center Profile Update

**Test Case**: Center admin should be able to update center profile
**Endpoint**: PUT /api/center/profile
**Expected**: 200 status with success message

### Test Data:
```json
{
  "name": "Sunrise Academy Updated",
  "logoUrl": "https://example.com/logo.png",
  "themeColor": "#FF5722",
  "phone": "02-9876543",
  "address": "123 Updated Street, Dhaka",
  "website": "https://sunriseacademy.com"
}
```

## Test 16: Center Subscription Status Check

**Test Case**: System should check subscription status before allowing access
**Endpoint**: GET /api/center/subscription
**Expected**: 200 status with subscription details

### Expected Response:
```json
{
  "message": "Subscription details retrieved successfully",
  "data": {
    "id": 1,
    "planId": 3,
    "status": "active",
    "expiresAt": "2024-12-31T23:59:59.000Z",
    "autoRenew": true,
    "plan": {
      "name": "Premium",
      "features": ["student_management", "teacher_management", "..."],
      "maxStudents": 1000,
      "maxTeachers": 100,
      "maxBranches": 10
    }
  }
}
```

## Test 17: Expired Subscription Access Restriction

**Test Case**: Centers with expired subscriptions should have limited access
**Endpoint**: Any center endpoint
**Expected**: 402 status (Payment Required) for expired subscriptions

### Test Scenario:
- Use testcenter.localhost:3000 (expired subscription)
- Try to access any center feature
- Should return 402 Payment Required

## Test 18: Center Feature Access Based on Plan

**Test Case**: Centers should only access features included in their plan
**Endpoint**: GET /api/center/features
**Expected**: 200 status with plan-specific features

### Test Scenarios:
1. **Free Plan**: Basic features only
2. **Basic Plan**: Standard features
3. **Premium Plan**: All features

## Test 19: Center Admin User Creation

**Test Case**: When creating a center, a default admin user should be created
**Endpoint**: POST /api/admin/centers
**Expected**: Center and admin user created

### Test Flow:
1. Create center with admin email
2. Verify admin user exists in database
3. Test admin login with temporary password

## Test 20: Center Suspension/Activation

**Test Case**: Super admin should be able to suspend/activate centers
**Endpoint**: PUT /api/admin/centers/:id/status
**Expected**: 200 status with updated status

### Test Data:
```json
{
  "status": "suspended"
}
```

### Expected Response:
```json
{
  "message": "Center status updated successfully",
  "data": {
    "id": 1,
    "status": "suspended"
  }
}
```

## Test 21: Suspended Center Access Restriction

**Test Case**: Suspended centers should not be able to access the system
**Endpoint**: Any center endpoint
**Expected**: 403 status (Forbidden) for suspended centers

### Test Scenario:
- Suspend a center using admin endpoint
- Try to login as center user
- Try to access center endpoints
- Should return 403 Forbidden

## Additional Test Cases

### Test 21a: Subdomain Validation

**Test Case**: Subdomain should follow naming conventions
**Endpoint**: POST /api/admin/centers
**Expected**: 400 status for invalid subdomains

### Invalid Subdomains:
- Contains spaces: "test center"
- Contains special chars: "test@center"
- Too short: "a"
- Reserved words: "www", "api", "admin"

### Test 21b: Center Deletion (Soft Delete)

**Test Case**: Centers should be soft deleted, not permanently removed
**Endpoint**: DELETE /api/admin/centers/:id
**Expected**: 200 status with soft delete confirmation

### Test 21c: Center Statistics

**Test Case**: Admin should see center usage statistics
**Endpoint**: GET /api/admin/centers/:id/stats
**Expected**: 200 status with usage statistics

### Expected Response:
```json
{
  "message": "Center statistics retrieved successfully",
  "data": {
    "totalStudents": 150,
    "totalTeachers": 12,
    "totalBranches": 2,
    "subscriptionStatus": "active",
    "lastActivity": "2024-01-15T10:30:00.000Z"
  }
}
```
