// Test login for coaching center admin
const testLogin = async () => {
  try {
    console.log('🔐 Testing login for coaching center admin...');
    
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        tenant_slug: 'abc'
      })
    });

    const data = await response.json();
    
    console.log('📊 Response Status:', response.status);
    console.log('📊 Response Data:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success) {
      console.log('✅ LOGIN SUCCESSFUL!');
      console.log('👤 User:', data.user.name);
      console.log('📧 Email:', data.user.email);
      console.log('🏢 Center:', data.user.center_name);
      console.log('🔑 Role:', data.user.role);
      console.log('🌐 Subdomain:', data.user.center_slug);
      console.log('🎫 Token:', data.token ? 'Generated' : 'Missing');
      
      console.log('\n🎉 COACHING CENTER LOGIN WORKING!');
      console.log('🔗 Use these credentials:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: password123');
      console.log('   Center URL: http://abc.localhost:5173');
      console.log('   Or direct: http://localhost:5173/login');
      
    } else {
      console.log('❌ LOGIN FAILED!');
      console.log('Error:', data.error || data.message);
    }
    
  } catch (error) {
    console.error('❌ Network Error:', error.message);
  }
};

testLogin();
