# Test Credentials and Dummy Data

This file contains all the test credentials and dummy data needed for development and testing of the Teaching Center Management SaaS platform.

## 🔐 Admin Credentials

### Super Admin
- **Email**: <EMAIL>
- **Password**: SuperAdmin123!
- **Role**: Super Administrator
- **Permissions**: Full system access, can manage all centers

### System Admin
- **Email**: <EMAIL>
- **Password**: Admin123!
- **Role**: System Administrator
- **Permissions**: Manage centers, plans, and system settings

## 🏫 Center Owner Credentials

### Center 1 - Sunrise Academy
- **Subdomain**: sunriseacademy.localhost:3000
- **Email**: <EMAIL>
- **Password**: Owner123!
- **Center Name**: Sunrise Academy
- **Plan**: Premium
- **Status**: Active

### Center 2 - ABC Coaching
- **Subdomain**: abccoaching.localhost:3000
- **Email**: <EMAIL>
- **Password**: Owner123!
- **Center Name**: ABC Coaching Center
- **Plan**: Basic
- **Status**: Active

### Center 3 - Test Center (Expired)
- **Subdomain**: testcenter.localhost:3000
- **Email**: <EMAIL>
- **Password**: Owner123!
- **Center Name**: Test Center
- **Plan**: Free
- **Status**: Expired

## 👥 Staff Credentials (for Sunrise Academy)

### Branch Manager
- **Email**: <EMAIL>
- **Password**: Manager123!
- **Role**: Branch Manager
- **Branch**: Main Branch

### Teacher 1
- **Email**: <EMAIL> <EMAIL> / admin123
- **Password**: Teacher123!
- **Role**: Teacher
- **Subjects**: Mathematics, Physics
- **Branch**: Main Branch

### Teacher 2
- **Email**: <EMAIL>
- **Password**: Teacher123!
- **Role**: Teacher
- **Subjects**: English, Chemistry
- **Branch**: Main Branch

### HR Manager
- **Email**: <EMAIL>
- **Password**: HR123!
- **Role**: HR Manager
- **Permissions**: Employee management, payroll

### Accountant
- **Email**: <EMAIL>
- **Password**: Account123!
- **Role**: Accountant
- **Permissions**: Financial management, reports

## 👨‍🎓 Student Credentials

### Student 1
- **Email**: <EMAIL>
- **Password**: Student123!
- **Student ID**: STU001
- **Name**: Rahul Ahmed
- **Course**: HSC Science
- **Batch**: Morning
- **Guardian**: Karim Ahmed (***********)

### Student 2
- **Email**: <EMAIL>
- **Password**: Student123!
- **Student ID**: STU002
- **Name**: Fatima Khan
- **Course**: SSC General
- **Batch**: Evening
- **Guardian**: Nasir Khan (***********)

## 💳 Payment Test Data

### bKash Test
- **Number**: ***********
- **PIN**: 1234
- **OTP**: 123456

### Nagad Test
- **Number**: ***********
- **PIN**: 1234
- **OTP**: 123456

### Test Card
- **Card Number**: ****************
- **Expiry**: 12/25
- **CVV**: 123
- **Name**: Test User

## 🏢 Dummy Center Data

### Branches
1. **Main Branch**
   - Address: 123 Main Street, Dhaka
   - Phone: 02-9876543
   - Manager: <EMAIL>

2. **Secondary Branch**
   - Address: 456 Secondary Road, Chittagong
   - Phone: 031-654321
   - Manager: <EMAIL>

### Courses
1. **HSC Science**
   - Duration: 2 years
   - Fee: 5000 BDT/month
   - Subjects: Physics, Chemistry, Mathematics, Biology

2. **SSC General**
   - Duration: 2 years
   - Fee: 3000 BDT/month
   - Subjects: Bangla, English, Mathematics, Science

3. **IELTS Preparation**
   - Duration: 3 months
   - Fee: 8000 BDT/month
   - Subjects: Speaking, Listening, Reading, Writing

## 📱 SMS Gateway Test

### Test Phone Numbers
- **Valid**: ***********, 01812345679, 01912345680
- **Invalid**: 01234567890 (for testing error handling)

### SMS Templates
- **Absence Alert**: "Your child {student_name} was absent today from {course_name} class."
- **Fee Due**: "Fee payment of {amount} BDT is due for {student_name}. Please pay by {due_date}."
- **Exam Reminder**: "Exam for {subject} is scheduled on {date} at {time}."

## 🔑 API Keys (Test Environment)

### Firebase
- **API Key**: test_firebase_api_key_123
- **Project ID**: teaching-center-test
- **Messaging Sender ID**: 123456789

### SMS Gateway
- **API Key**: test_sms_api_key_456
- **Sender ID**: TEACHING

### Payment Gateways
- **SSLCommerz Store ID**: test_store_123
- **SSLCommerz Store Password**: test_password_456

## 📊 Test Data Sets

### Attendance Data
- **Present Students**: 85% average
- **Absent Students**: 15% average
- **Teacher Attendance**: 95% average

### Financial Data
- **Monthly Income**: 150,000 BDT
- **Monthly Expenses**: 80,000 BDT
- **Outstanding Fees**: 25,000 BDT

### Inventory Items
1. **Whiteboard Markers** - Quantity: 50, Expiry: 2024-12-31
2. **Projector** - Quantity: 3, Status: Working
3. **Chairs** - Quantity: 100, Condition: Good
4. **Books** - Quantity: 200, Subject: Various

## 🚫 Blocked/Test Users

### Blocked User
- **Email**: <EMAIL>
- **Password**: Blocked123!
- **Status**: Blocked
- **Reason**: Policy violation

### Inactive User
- **Email**: <EMAIL>
- **Password**: Inactive123!
- **Status**: Inactive
- **Last Login**: 2023-01-01

## 🌐 Environment Variables

```env
# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=teaching_center_test
DB_USER=test_user
DB_PASS=test_password

# JWT
JWT_SECRET=test_jwt_secret_key_123456789
JWT_EXPIRES_IN=24h

# Firebase
FIREBASE_API_KEY=test_firebase_api_key_123
FIREBASE_PROJECT_ID=teaching-center-test

# SMS
SMS_API_KEY=test_sms_api_key_456
SMS_SENDER_ID=TEACHING

# Payment
SSLCOMMERZ_STORE_ID=test_store_123
SSLCOMMERZ_STORE_PASSWORD=test_password_456
```

## 📝 Notes for Testing

1. **Local Development**: Use localhost subdomains for testing multi-tenancy
2. **Database**: Use separate test database to avoid data conflicts
3. **File Uploads**: Test with various file types and sizes
4. **Mobile Testing**: Test responsive design on different screen sizes
5. **Performance**: Test with large datasets for scalability
6. **Security**: Test with invalid tokens, SQL injection attempts, XSS
7. **Notifications**: Use test phone numbers to avoid sending real SMS
8. **Payments**: Always use test/sandbox payment gateways

## 🔄 Data Reset Commands

```bash
# Reset test database
npm run db:reset:test

# Seed test data
npm run db:seed:test

# Clear uploaded files
npm run files:clear:test
```
