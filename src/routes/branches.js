/**
 * Branch Management Routes
 * Handles CRUD operations for center branches
 */

import { Hono } from 'hono';
import { executeQuery } from '../config/database.js';
import { authMiddleware } from '../middleware/auth.js';

const branches = new Hono();

// Apply authentication middleware to all routes
branches.use('*', authMiddleware);

/**
 * GET /api/center/branches
 * Get all branches for the current center
 */
branches.get('/', async (c) => {
  try {
    const user = c.get('user');
    const tenant = c.get('tenant');

    if (!tenant) {
      return c.json({
        success: false,
        message: 'Tenant not found'
      }, 400);
    }

    // Check permissions
    if (!['center_admin', 'admin', 'super_admin'].includes(user.role)) {
      return c.json({
        success: false,
        message: 'Insufficient permissions'
      }, 403);
    }

    const query = `
      SELECT 
        b.*,
        u.name as manager_name,
        u.email as manager_email,
        (SELECT COUNT(*) FROM users WHERE branch_id = b.id AND status = 'active') as staff_count
      FROM branches b
      LEFT JOIN users u ON b.manager_id = u.id
      WHERE b.tenant_id = ?
      ORDER BY b.created_at DESC
    `;

    const branches = await executeQuery(query, [tenant.id]);

    // Parse facilities JSON
    const processedBranches = branches.map(branch => ({
      ...branch,
      facilities: branch.facilities ? JSON.parse(branch.facilities) : []
    }));

    return c.json({
      success: true,
      data: {
        branches: processedBranches,
        total: processedBranches.length
      }
    });

  } catch (error) {
    console.error('Get branches error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch branches'
    }, 500);
  }
});

/**
 * GET /api/center/branches/:id
 * Get specific branch details
 */
branches.get('/:id', async (c) => {
  try {
    const user = c.get('user');
    const tenant = c.get('tenant');
    const branchId = c.req.param('id');

    if (!tenant) {
      return c.json({
        success: false,
        message: 'Tenant not found'
      }, 400);
    }

    // Check permissions
    if (!['center_admin', 'admin', 'super_admin'].includes(user.role)) {
      return c.json({
        success: false,
        message: 'Insufficient permissions'
      }, 403);
    }

    const query = `
      SELECT 
        b.*,
        u.name as manager_name,
        u.email as manager_email,
        (SELECT COUNT(*) FROM users WHERE branch_id = b.id AND status = 'active') as staff_count
      FROM branches b
      LEFT JOIN users u ON b.manager_id = u.id
      WHERE b.id = ? AND b.tenant_id = ?
    `;

    const result = await executeQuery(query, [branchId, tenant.id]);

    if (result.length === 0) {
      return c.json({
        success: false,
        message: 'Branch not found'
      }, 404);
    }

    const branch = result[0];
    branch.facilities = branch.facilities ? JSON.parse(branch.facilities) : [];

    return c.json({
      success: true,
      data: branch
    });

  } catch (error) {
    console.error('Get branch error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch branch'
    }, 500);
  }
});

/**
 * POST /api/center/branches
 * Create a new branch
 */
branches.post('/', async (c) => {
  try {
    const user = c.get('user');
    const tenant = c.get('tenant');
    const data = await c.req.json();

    if (!tenant) {
      return c.json({
        success: false,
        message: 'Tenant not found'
      }, 400);
    }

    // Check permissions
    if (!['center_admin', 'admin', 'super_admin'].includes(user.role)) {
      return c.json({
        success: false,
        message: 'Insufficient permissions'
      }, 403);
    }

    // Validate required fields
    if (!data.name) {
      return c.json({
        success: false,
        message: 'Branch name is required'
      }, 400);
    }

    // Check if branch name already exists for this tenant
    const existingBranch = await executeQuery(
      'SELECT id FROM branches WHERE name = ? AND tenant_id = ?',
      [data.name, tenant.id]
    );

    if (existingBranch.length > 0) {
      return c.json({
        success: false,
        message: 'Branch name already exists'
      }, 400);
    }

    const query = `
      INSERT INTO branches (
        tenant_id, name, address, phone, email, capacity, 
        facilities, status, manager_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;

    const facilitiesJson = data.facilities ? JSON.stringify(data.facilities) : null;

    const result = await executeQuery(query, [
      tenant.id,
      data.name,
      data.address || null,
      data.phone || null,
      data.email || null,
      data.capacity || 0,
      facilitiesJson,
      data.status || 'active',
      data.manager_id || null
    ]);

    return c.json({
      success: true,
      message: 'Branch created successfully',
      data: {
        id: result.insertId,
        name: data.name
      }
    }, 201);

  } catch (error) {
    console.error('Create branch error:', error);
    return c.json({
      success: false,
      message: 'Failed to create branch'
    }, 500);
  }
});

/**
 * PUT /api/center/branches/:id
 * Update branch details
 */
branches.put('/:id', async (c) => {
  try {
    const user = c.get('user');
    const tenant = c.get('tenant');
    const branchId = c.req.param('id');
    const data = await c.req.json();

    if (!tenant) {
      return c.json({
        success: false,
        message: 'Tenant not found'
      }, 400);
    }

    // Check permissions
    if (!['center_admin', 'admin', 'super_admin'].includes(user.role)) {
      return c.json({
        success: false,
        message: 'Insufficient permissions'
      }, 403);
    }

    // Check if branch exists and belongs to tenant
    const existingBranch = await executeQuery(
      'SELECT id FROM branches WHERE id = ? AND tenant_id = ?',
      [branchId, tenant.id]
    );

    if (existingBranch.length === 0) {
      return c.json({
        success: false,
        message: 'Branch not found'
      }, 404);
    }

    // Build update query dynamically
    const updateFields = [];
    const updateValues = [];

    if (data.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(data.name);
    }
    if (data.address !== undefined) {
      updateFields.push('address = ?');
      updateValues.push(data.address);
    }
    if (data.phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(data.phone);
    }
    if (data.email !== undefined) {
      updateFields.push('email = ?');
      updateValues.push(data.email);
    }
    if (data.capacity !== undefined) {
      updateFields.push('capacity = ?');
      updateValues.push(data.capacity);
    }
    if (data.facilities !== undefined) {
      updateFields.push('facilities = ?');
      updateValues.push(JSON.stringify(data.facilities));
    }
    if (data.status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(data.status);
    }
    if (data.manager_id !== undefined) {
      updateFields.push('manager_id = ?');
      updateValues.push(data.manager_id);
    }

    if (updateFields.length === 0) {
      return c.json({
        success: false,
        message: 'No fields to update'
      }, 400);
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(branchId);

    const query = `UPDATE branches SET ${updateFields.join(', ')} WHERE id = ?`;
    await executeQuery(query, updateValues);

    return c.json({
      success: true,
      message: 'Branch updated successfully'
    });

  } catch (error) {
    console.error('Update branch error:', error);
    return c.json({
      success: false,
      message: 'Failed to update branch'
    }, 500);
  }
});

/**
 * GET /api/center/branches/:id/staff
 * Get staff assigned to a specific branch
 */
branches.get('/:id/staff', async (c) => {
  try {
    const user = c.get('user');
    const tenant = c.get('tenant');
    const branchId = c.req.param('id');

    if (!tenant) {
      return c.json({
        success: false,
        message: 'Tenant not found'
      }, 400);
    }

    // Check permissions
    if (!['center_admin', 'admin', 'super_admin'].includes(user.role)) {
      return c.json({
        success: false,
        message: 'Insufficient permissions'
      }, 403);
    }

    // Check if branch exists and belongs to tenant
    const branchExists = await executeQuery(
      'SELECT id FROM branches WHERE id = ? AND tenant_id = ?',
      [branchId, tenant.id]
    );

    if (branchExists.length === 0) {
      return c.json({
        success: false,
        message: 'Branch not found'
      }, 404);
    }

    const query = `
      SELECT 
        u.id, u.name, u.email, u.phone, u.role, u.status,
        sb.role as branch_role, sb.assigned_at
      FROM users u
      LEFT JOIN staff_branches sb ON u.id = sb.user_id AND sb.branch_id = ?
      WHERE (u.branch_id = ? OR sb.branch_id = ?) 
        AND u.tenant_id = ? 
        AND u.status = 'active'
        AND u.role IN ('teacher', 'hr', 'accountant', 'admin')
      ORDER BY u.name
    `;

    const staff = await executeQuery(query, [branchId, branchId, branchId, tenant.id]);

    return c.json({
      success: true,
      data: {
        staff,
        total: staff.length
      }
    });

  } catch (error) {
    console.error('Get branch staff error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch branch staff'
    }, 500);
  }
});

export default branches;
