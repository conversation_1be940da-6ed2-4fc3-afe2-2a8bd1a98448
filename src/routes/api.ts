import { Hono } from 'hono';
import { setCookie, getCookie, deleteCookie } from 'hono/cookie';

import { executeQuery } from '../config/database.js';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { coaching } from './coaching.js';

const api = new Hono();

// CORS is handled globally in index.tsx, no need for route-specific CORS

// Middleware to get user from JWT token
async function getUserFromToken(c: any) {
  try {
    // Check Authorization header first (most reliable for API calls)
    let token = null;
    const authHeader = c.req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      console.log('🔍 Auth Debug - Token from Authorization header:', token ? 'Present' : 'Missing');
    }

    // Fallback to cookies
    if (!token) {
      token = getCookie(c, 'auth_token') || getCookie(c, 'auth_backup');
      console.log('🔍 Auth Debug - Token from cookies:', token ? 'Present' : 'Missing');
    }

    if (!token) {
      console.log('🔍 Auth Debug - No token found in headers or cookies');
      return null;
    }

    let decoded: any;

    // Handle development tokens
    if (token.startsWith('dev.')) {
      try {
        const devTokenData = token.substring(4);
        decoded = JSON.parse(atob(devTokenData));
        console.log('🔍 Auth Debug - Development token decoded:', decoded);
      } catch (error) {
        console.error('🔍 Auth Debug - Invalid development token:', error);
        return null;
      }
    } else {
      // Handle regular JWT tokens
      try {
        decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
        console.log('🔍 Auth Debug - JWT token decoded:', decoded);
      } catch (error) {
        console.error('🔍 Auth Debug - JWT verification failed:', error);
        return null;
      }
    }

    const user = await executeQuery(
      'SELECT * FROM users WHERE id = ? AND status = "active"',
      [decoded.userId]
    );
    console.log('🔍 Auth Debug - User found:', user.length > 0 ? 'Yes' : 'No');

    return user[0] || null;
  } catch (error) {
    console.error('🔍 Auth Debug - Token verification error:', error);
    return null;
  }
}

// Auth Routes
api.post('/auth/login', async (c) => {
  try {
    const { email, password, tenant_slug } = await c.req.json();
    console.log('🔐 Login attempt for:', email, 'with tenant:', tenant_slug);

    if (!email || !password) {
      return c.json({ error: 'Email and password are required' }, 400);
    }

    // Build query to include center information
    let query = `
      SELECT u.*, c.name as center_name, c.subdomain as center_slug
      FROM users u
      LEFT JOIN centers c ON u.center_id = c.id
      WHERE u.email = ? AND u.status = "active"
    `;
    let params = [email];

    // If tenant_slug is provided, validate user belongs to that center
    if (tenant_slug) {
      query += ' AND c.subdomain = ?';
      params.push(tenant_slug);
    }

    const users = await executeQuery(query, params);

    if (users.length === 0) {
      return c.json({ error: 'Invalid credentials or access denied' }, 401);
    }

    const user = users[0];

    // Verify password
    console.log('🔐 Verifying password for user:', user.email);
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    console.log('🔐 Password valid:', isValidPassword);
    if (!isValidPassword) {
      return c.json({ error: 'Invalid credentials' }, 401);
    }

    // For center admin, ensure they're accessing from correct subdomain
    if (user.role === 'center_admin' && tenant_slug && user.center_slug !== tenant_slug) {
      return c.json({ error: 'Access denied for this center' }, 403);
    }

    // Generate JWT token with center information
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        role: user.role,
        center_id: user.center_id,
        center_slug: user.center_slug
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // Set auth cookie with proper cross-port configuration
    const cookieOptions = {
      httpOnly: false, // Allow JavaScript access for development
      secure: false, // HTTP for localhost
      sameSite: 'none' as const, // Allow cross-origin for different ports
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/',
      domain: 'localhost' // Explicit domain for cross-port sharing
    };

    console.log('🍪 Setting cookie with options:', cookieOptions);
    setCookie(c, 'auth_token', token, cookieOptions);

    // Also set a backup cookie without domain restriction
    setCookie(c, 'auth_backup', token, {
      httpOnly: false,
      secure: false,
      sameSite: 'lax' as const,
      maxAge: 24 * 60 * 60,
      path: '/'
    });

    return c.json({
      success: true,
      token: token, // Include token in response for localStorage backup
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        center_id: user.center_id,
        center_slug: user.center_slug,
        center_name: user.center_name
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to process login request',
      details: error.message
    }, 500);
  }
});

api.post('/auth/logout', async (c) => {
  deleteCookie(c, 'auth_token');
  return c.json({ success: true });
});

api.get('/auth/me', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    // Get center information if user has center_id
    let userWithCenter = { ...user };
    if (user.center_id) {
      const centers = await executeQuery(
        'SELECT name, subdomain FROM centers WHERE id = ?',
        [user.center_id]
      );
      if (centers.length > 0) {
        userWithCenter.center_name = centers[0].name;
        userWithCenter.center_slug = centers[0].subdomain;
      }
    }

    return c.json({
      user: {
        id: userWithCenter.id,
        name: userWithCenter.name,
        email: userWithCenter.email,
        role: userWithCenter.role,
        center_id: userWithCenter.center_id,
        center_slug: userWithCenter.center_slug,
        center_name: userWithCenter.center_name
      }
    });
  } catch (error) {
    console.error('Auth check error:', error);
    return c.json({ error: 'Authentication failed' }, 500);
  }
});

// Dashboard Stats Routes
api.get('/dashboard/stats', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    let stats = {};

    if (user.role === 'super_admin') {
      // Admin stats with mock data for demo
      stats = {
        totalCenters: 24,
        activeSubscriptions: 18,
        totalUsers: 1247,
        monthlyRevenue: 12450,
        totalBranches: 45,
        totalCourses: 156,
        totalTeachers: 89,
        totalStudents: 892
      };
    } else {
      // Tenant stats with mock data for demo
      stats = {
        totalStudents: 156,
        activeCourses: 12,
        totalTeachers: 8,
        monthlyRevenue: 4250,
        totalBranches: 3,
        activeEnrollments: 234,
        completedCourses: 45,
        pendingPayments: 12
      };
    }

    return c.json({ stats });
  } catch (error) {
    console.error('Stats error:', error);
    return c.json({ error: 'Failed to fetch stats' }, 500);
  }
});

// Dashboard Charts Data
api.get('/dashboard/charts', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    // Mock chart data for demo
    const chartData = {
      enrollmentTrend: [
        { month: 'Jan', students: 120, revenue: 3600 },
        { month: 'Feb', students: 135, revenue: 4050 },
        { month: 'Mar', students: 142, revenue: 4260 },
        { month: 'Apr', students: 156, revenue: 4680 },
        { month: 'May', students: 168, revenue: 5040 },
        { month: 'Jun', students: 175, revenue: 5250 }
      ],
      coursePopularity: [
        { course: 'Mathematics', students: 45, percentage: 28.8 },
        { course: 'Physics', students: 38, percentage: 24.4 },
        { course: 'Chemistry', students: 32, percentage: 20.5 },
        { course: 'Biology', students: 25, percentage: 16.0 },
        { course: 'English', students: 16, percentage: 10.3 }
      ],
      revenueBreakdown: [
        { category: 'Course Fees', amount: 3200, percentage: 75.3 },
        { category: 'Registration', amount: 650, percentage: 15.3 },
        { category: 'Materials', amount: 400, percentage: 9.4 }
      ],
      attendanceRate: [
        { day: 'Mon', rate: 92 },
        { day: 'Tue', rate: 88 },
        { day: 'Wed', rate: 95 },
        { day: 'Thu', rate: 89 },
        { day: 'Fri', rate: 91 },
        { day: 'Sat', rate: 87 }
      ]
    };

    return c.json({ chartData });
  } catch (error) {
    console.error('Charts error:', error);
    return c.json({ error: 'Failed to fetch chart data' }, 500);
  }
});

api.get('/dashboard/activity', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    let query = `
      SELECT 
        'New student enrolled' as title,
        CONCAT(u.name, ' enrolled in Mathematics course') as description,
        DATE_FORMAT(u.created_at, '%M %d, %Y at %h:%i %p') as time
      FROM users u 
      WHERE u.role = 'student' 
    `;
    
    let params = [];
    
    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND u.tenant_id = ?';
      params.push(user.tenant_id);
    }
    
    query += ' ORDER BY u.created_at DESC LIMIT 5';
    
    const activities = await executeQuery(query, params);
    
    return c.json({ 
      activities: activities || [
        {
          title: 'New student enrolled',
          description: 'John Doe enrolled in Mathematics course',
          time: '2 hours ago'
        },
        {
          title: 'Payment received',
          description: 'Monthly subscription payment processed',
          time: '4 hours ago'
        },
        {
          title: 'Class schedule updated',
          description: 'Physics class rescheduled for tomorrow',
          time: '1 day ago'
        }
      ]
    });
  } catch (error) {
    console.error('Activity error:', error);
    return c.json({ error: 'Failed to fetch activity' }, 500);
  }
});

// Student Management Routes
api.get('/students', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    let query = `
      SELECT
        id, name, email, phone, date_of_birth, enrollment_date,
        status, created_at, updated_at
      FROM users
      WHERE role = 'student' AND status != 'deleted'
    `;
    let params = [];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND tenant_id = ?';
      params.push(user.tenant_id);
    }

    query += ' ORDER BY created_at DESC';

    const students = await executeQuery(query, params);
    return c.json({ students });
  } catch (error) {
    console.error('Students fetch error:', error);
    return c.json({ error: 'Failed to fetch students' }, 500);
  }
});

api.post('/students', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { name, email, phone, date_of_birth } = await c.req.json();

    if (!name || !email) {
      return c.json({ error: 'Name and email are required' }, 400);
    }

    // Check if email already exists
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUser.length > 0) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    // Create student
    const result = await executeQuery(
      `INSERT INTO users (name, email, phone, date_of_birth, role, tenant_id, status, enrollment_date, created_at, updated_at)
       VALUES (?, ?, ?, ?, 'student', ?, 'active', NOW(), NOW(), NOW())`,
      [name, email, phone, date_of_birth, user.tenant_id]
    );

    const newStudent = await executeQuery(
      'SELECT id, name, email, phone, date_of_birth, enrollment_date, status FROM users WHERE id = ?',
      [result.insertId]
    );

    return c.json({ student: newStudent[0] });
  } catch (error) {
    console.error('Student creation error:', error);
    return c.json({ error: 'Failed to create student' }, 500);
  }
});

api.put('/students/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const studentId = c.req.param('id');
    const { name, email, phone, date_of_birth, status } = await c.req.json();

    if (!name || !email) {
      return c.json({ error: 'Name and email are required' }, 400);
    }

    // Check if student exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM users WHERE id = ? AND role = "student"';
    let checkParams = [studentId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingStudent = await executeQuery(checkQuery, checkParams);
    if (existingStudent.length === 0) {
      return c.json({ error: 'Student not found' }, 404);
    }

    // Update student
    await executeQuery(
      `UPDATE users SET name = ?, email = ?, phone = ?, date_of_birth = ?, status = ?, updated_at = NOW()
       WHERE id = ?`,
      [name, email, phone, date_of_birth, status, studentId]
    );

    const updatedStudent = await executeQuery(
      'SELECT id, name, email, phone, date_of_birth, enrollment_date, status FROM users WHERE id = ?',
      [studentId]
    );

    return c.json({ student: updatedStudent[0] });
  } catch (error) {
    console.error('Student update error:', error);
    return c.json({ error: 'Failed to update student' }, 500);
  }
});

api.delete('/students/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const studentId = c.req.param('id');

    // Check if student exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM users WHERE id = ? AND role = "student"';
    let checkParams = [studentId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingStudent = await executeQuery(checkQuery, checkParams);
    if (existingStudent.length === 0) {
      return c.json({ error: 'Student not found' }, 404);
    }

    // Soft delete student
    await executeQuery(
      'UPDATE users SET status = "deleted", updated_at = NOW() WHERE id = ?',
      [studentId]
    );

    return c.json({ success: true });
  } catch (error) {
    console.error('Student deletion error:', error);
    return c.json({ error: 'Failed to delete student' }, 500);
  }
});

// Course Management Routes
api.get('/courses', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    let query = `
      SELECT
        id, name, description, duration, price, status,
        start_date, end_date, created_at, updated_at
      FROM courses
      WHERE status != 'deleted'
    `;
    let params = [];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND tenant_id = ?';
      params.push(user.tenant_id);
    }

    query += ' ORDER BY created_at DESC';

    const courses = await executeQuery(query, params);
    return c.json({ courses });
  } catch (error) {
    console.error('Courses fetch error:', error);
    return c.json({ error: 'Failed to fetch courses' }, 500);
  }
});

api.post('/courses', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { name, description, duration, price, start_date, end_date } = await c.req.json();

    if (!name || !duration || !price) {
      return c.json({ error: 'Name, duration, and price are required' }, 400);
    }

    // Create course
    const result = await executeQuery(
      `INSERT INTO courses (name, description, duration, price, start_date, end_date, tenant_id, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [name, description, duration, price, start_date, end_date, user.tenant_id]
    );

    const newCourse = await executeQuery(
      'SELECT id, name, description, duration, price, start_date, end_date, status FROM courses WHERE id = ?',
      [result.insertId]
    );

    return c.json({ course: newCourse[0] });
  } catch (error) {
    console.error('Course creation error:', error);
    return c.json({ error: 'Failed to create course' }, 500);
  }
});

api.put('/courses/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const courseId = c.req.param('id');
    const { name, description, duration, price, start_date, end_date, status } = await c.req.json();

    if (!name || !duration || !price) {
      return c.json({ error: 'Name, duration, and price are required' }, 400);
    }

    // Check if course exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM courses WHERE id = ?';
    let checkParams = [courseId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingCourse = await executeQuery(checkQuery, checkParams);
    if (existingCourse.length === 0) {
      return c.json({ error: 'Course not found' }, 404);
    }

    // Update course
    await executeQuery(
      `UPDATE courses SET name = ?, description = ?, duration = ?, price = ?, start_date = ?, end_date = ?, status = ?, updated_at = NOW()
       WHERE id = ?`,
      [name, description, duration, price, start_date, end_date, status, courseId]
    );

    const updatedCourse = await executeQuery(
      'SELECT id, name, description, duration, price, start_date, end_date, status FROM courses WHERE id = ?',
      [courseId]
    );

    return c.json({ course: updatedCourse[0] });
  } catch (error) {
    console.error('Course update error:', error);
    return c.json({ error: 'Failed to update course' }, 500);
  }
});

api.delete('/courses/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const courseId = c.req.param('id');

    // Check if course exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM courses WHERE id = ?';
    let checkParams = [courseId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingCourse = await executeQuery(checkQuery, checkParams);
    if (existingCourse.length === 0) {
      return c.json({ error: 'Course not found' }, 404);
    }

    // Soft delete course
    await executeQuery(
      'UPDATE courses SET status = "deleted", updated_at = NOW() WHERE id = ?',
      [courseId]
    );

    return c.json({ success: true });
  } catch (error) {
    console.error('Course deletion error:', error);
    return c.json({ error: 'Failed to delete course' }, 500);
  }
});

// Branch Management Routes
api.get('/branches', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    let query = `
      SELECT
        id, name, address, phone, email, manager_name, status,
        created_at, updated_at
      FROM branches
      WHERE status != 'deleted'
    `;
    let params = [];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND tenant_id = ?';
      params.push(user.tenant_id);
    }

    query += ' ORDER BY created_at DESC';

    const branches = await executeQuery(query, params);
    return c.json({ branches });
  } catch (error) {
    console.error('Branches fetch error:', error);
    return c.json({ error: 'Failed to fetch branches' }, 500);
  }
});

api.post('/branches', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { name, address, phone, email, manager_name } = await c.req.json();

    if (!name || !address) {
      return c.json({ error: 'Name and address are required' }, 400);
    }

    // Create branch
    const result = await executeQuery(
      `INSERT INTO branches (name, address, phone, email, manager_name, tenant_id, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [name, address, phone, email, manager_name, user.tenant_id]
    );

    const newBranch = await executeQuery(
      'SELECT id, name, address, phone, email, manager_name, status FROM branches WHERE id = ?',
      [result.insertId]
    );

    return c.json({ branch: newBranch[0] });
  } catch (error) {
    console.error('Branch creation error:', error);
    return c.json({ error: 'Failed to create branch' }, 500);
  }
});

api.put('/branches/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const branchId = c.req.param('id');
    const { name, address, phone, email, manager_name, status } = await c.req.json();

    if (!name || !address) {
      return c.json({ error: 'Name and address are required' }, 400);
    }

    // Check if branch exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM branches WHERE id = ?';
    let checkParams = [branchId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingBranch = await executeQuery(checkQuery, checkParams);
    if (existingBranch.length === 0) {
      return c.json({ error: 'Branch not found' }, 404);
    }

    // Update branch
    await executeQuery(
      `UPDATE branches SET name = ?, address = ?, phone = ?, email = ?, manager_name = ?, status = ?, updated_at = NOW()
       WHERE id = ?`,
      [name, address, phone, email, manager_name, status, branchId]
    );

    const updatedBranch = await executeQuery(
      'SELECT id, name, address, phone, email, manager_name, status FROM branches WHERE id = ?',
      [branchId]
    );

    return c.json({ branch: updatedBranch[0] });
  } catch (error) {
    console.error('Branch update error:', error);
    return c.json({ error: 'Failed to update branch' }, 500);
  }
});

api.delete('/branches/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const branchId = c.req.param('id');

    // Check if branch exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM branches WHERE id = ?';
    let checkParams = [branchId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingBranch = await executeQuery(checkQuery, checkParams);
    if (existingBranch.length === 0) {
      return c.json({ error: 'Branch not found' }, 404);
    }

    // Soft delete branch
    await executeQuery(
      'UPDATE branches SET status = "deleted", updated_at = NOW() WHERE id = ?',
      [branchId]
    );

    return c.json({ success: true });
  } catch (error) {
    console.error('Branch deletion error:', error);
    return c.json({ error: 'Failed to delete branch' }, 500);
  }
});

// Teacher Management Routes
api.get('/teachers', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    let query = `
      SELECT
        id, name, email, phone, subject, qualification, experience,
        salary, status, created_at, updated_at
      FROM users
      WHERE role = 'teacher' AND status != 'deleted'
    `;
    let params = [];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND tenant_id = ?';
      params.push(user.tenant_id);
    }

    query += ' ORDER BY created_at DESC';

    const teachers = await executeQuery(query, params);
    return c.json({ teachers });
  } catch (error) {
    console.error('Teachers fetch error:', error);
    return c.json({ error: 'Failed to fetch teachers' }, 500);
  }
});

api.post('/teachers', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { name, email, phone, subject, qualification, experience, salary } = await c.req.json();

    if (!name || !email || !subject) {
      return c.json({ error: 'Name, email, and subject are required' }, 400);
    }

    // Check if email already exists
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUser.length > 0) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    // Create teacher
    const result = await executeQuery(
      `INSERT INTO users (name, email, phone, subject, qualification, experience, salary, role, tenant_id, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, 'teacher', ?, 'active', NOW(), NOW())`,
      [name, email, phone, subject, qualification, experience, salary, user.tenant_id]
    );

    const newTeacher = await executeQuery(
      'SELECT id, name, email, phone, subject, qualification, experience, salary, status FROM users WHERE id = ?',
      [result.insertId]
    );

    return c.json({ teacher: newTeacher[0] });
  } catch (error) {
    console.error('Teacher creation error:', error);
    return c.json({ error: 'Failed to create teacher' }, 500);
  }
});

api.put('/teachers/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const teacherId = c.req.param('id');
    const { name, email, phone, subject, qualification, experience, salary, status } = await c.req.json();

    if (!name || !email || !subject) {
      return c.json({ error: 'Name, email, and subject are required' }, 400);
    }

    // Check if teacher exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM users WHERE id = ? AND role = "teacher"';
    let checkParams = [teacherId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingTeacher = await executeQuery(checkQuery, checkParams);
    if (existingTeacher.length === 0) {
      return c.json({ error: 'Teacher not found' }, 404);
    }

    // Update teacher
    await executeQuery(
      `UPDATE users SET name = ?, email = ?, phone = ?, subject = ?, qualification = ?, experience = ?, salary = ?, status = ?, updated_at = NOW()
       WHERE id = ?`,
      [name, email, phone, subject, qualification, experience, salary, status, teacherId]
    );

    const updatedTeacher = await executeQuery(
      'SELECT id, name, email, phone, subject, qualification, experience, salary, status FROM users WHERE id = ?',
      [teacherId]
    );

    return c.json({ teacher: updatedTeacher[0] });
  } catch (error) {
    console.error('Teacher update error:', error);
    return c.json({ error: 'Failed to update teacher' }, 500);
  }
});

api.delete('/teachers/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const teacherId = c.req.param('id');

    // Check if teacher exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM users WHERE id = ? AND role = "teacher"';
    let checkParams = [teacherId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingTeacher = await executeQuery(checkQuery, checkParams);
    if (existingTeacher.length === 0) {
      return c.json({ error: 'Teacher not found' }, 404);
    }

    // Soft delete teacher
    await executeQuery(
      'UPDATE users SET status = "deleted", updated_at = NOW() WHERE id = ?',
      [teacherId]
    );

    return c.json({ success: true });
  } catch (error) {
    console.error('Teacher deletion error:', error);
    return c.json({ error: 'Failed to delete teacher' }, 500);
  }
});

// Schedule Management Routes
api.get('/schedules', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    let query = `
      SELECT
        s.id, s.day_of_week, s.start_time, s.end_time, s.room, s.batch, s.status, s.created_at,
        c.name as course_name, u.name as teacher_name
      FROM schedules s
      LEFT JOIN courses c ON s.course_id = c.id
      LEFT JOIN users u ON s.teacher_id = u.id
      WHERE s.status != 'deleted'
    `;
    let params = [];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND s.tenant_id = ?';
      params.push(user.tenant_id);
    }

    query += ' ORDER BY s.day_of_week, s.start_time';

    const schedules = await executeQuery(query, params);
    return c.json({ schedules });
  } catch (error) {
    console.error('Schedules fetch error:', error);
    return c.json({ error: 'Failed to fetch schedules' }, 500);
  }
});

api.post('/schedules', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { course_id, teacher_id, room, day_of_week, start_time, end_time, batch } = await c.req.json();

    if (!course_id || !teacher_id || !room || !day_of_week || !start_time || !end_time) {
      return c.json({ error: 'Course, teacher, room, day, start time, and end time are required' }, 400);
    }

    // Check for conflicts
    const conflictQuery = `
      SELECT id FROM schedules
      WHERE day_of_week = ? AND teacher_id = ? AND status = 'active'
      AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))
      AND tenant_id = ?
    `;

    const conflicts = await executeQuery(conflictQuery, [
      day_of_week, teacher_id, start_time, start_time, end_time, end_time, user.tenant_id
    ]);

    if (conflicts.length > 0) {
      return c.json({ error: 'Teacher has a conflicting schedule at this time' }, 400);
    }

    // Create schedule
    const result = await executeQuery(
      `INSERT INTO schedules (course_id, teacher_id, room, day_of_week, start_time, end_time, batch, tenant_id, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [course_id, teacher_id, room, day_of_week, start_time, end_time, batch, user.tenant_id]
    );

    const newSchedule = await executeQuery(
      `SELECT s.*, c.name as course_name, u.name as teacher_name
       FROM schedules s
       LEFT JOIN courses c ON s.course_id = c.id
       LEFT JOIN users u ON s.teacher_id = u.id
       WHERE s.id = ?`,
      [result.insertId]
    );

    return c.json({ schedule: newSchedule[0] });
  } catch (error) {
    console.error('Schedule creation error:', error);
    return c.json({ error: 'Failed to create schedule' }, 500);
  }
});

api.put('/schedules/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const scheduleId = c.req.param('id');
    const { course_id, teacher_id, room, day_of_week, start_time, end_time, batch, status } = await c.req.json();

    // Check if schedule exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM schedules WHERE id = ?';
    let checkParams = [scheduleId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingSchedule = await executeQuery(checkQuery, checkParams);
    if (existingSchedule.length === 0) {
      return c.json({ error: 'Schedule not found' }, 404);
    }

    // Check for conflicts (excluding current schedule)
    const conflictQuery = `
      SELECT id FROM schedules
      WHERE day_of_week = ? AND teacher_id = ? AND status = 'active' AND id != ?
      AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))
      AND tenant_id = ?
    `;

    const conflicts = await executeQuery(conflictQuery, [
      day_of_week, teacher_id, scheduleId, start_time, start_time, end_time, end_time, user.tenant_id
    ]);

    if (conflicts.length > 0) {
      return c.json({ error: 'Teacher has a conflicting schedule at this time' }, 400);
    }

    // Update schedule
    await executeQuery(
      `UPDATE schedules SET course_id = ?, teacher_id = ?, room = ?, day_of_week = ?, start_time = ?, end_time = ?, batch = ?, status = ?, updated_at = NOW()
       WHERE id = ?`,
      [course_id, teacher_id, room, day_of_week, start_time, end_time, batch, status || 'active', scheduleId]
    );

    const updatedSchedule = await executeQuery(
      `SELECT s.*, c.name as course_name, u.name as teacher_name
       FROM schedules s
       LEFT JOIN courses c ON s.course_id = c.id
       LEFT JOIN users u ON s.teacher_id = u.id
       WHERE s.id = ?`,
      [scheduleId]
    );

    return c.json({ schedule: updatedSchedule[0] });
  } catch (error) {
    console.error('Schedule update error:', error);
    return c.json({ error: 'Failed to update schedule' }, 500);
  }
});

api.delete('/schedules/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const scheduleId = c.req.param('id');

    // Check if schedule exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM schedules WHERE id = ?';
    let checkParams = [scheduleId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingSchedule = await executeQuery(checkQuery, checkParams);
    if (existingSchedule.length === 0) {
      return c.json({ error: 'Schedule not found' }, 404);
    }

    // Soft delete schedule
    await executeQuery(
      'UPDATE schedules SET status = "deleted", updated_at = NOW() WHERE id = ?',
      [scheduleId]
    );

    return c.json({ success: true });
  } catch (error) {
    console.error('Schedule deletion error:', error);
    return c.json({ error: 'Failed to delete schedule' }, 500);
  }
});

// Attendance Management Routes
api.get('/attendance', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { date, course } = c.req.query();

    let query = `
      SELECT
        a.id, a.student_id, a.date, a.status, a.notes, a.created_at,
        u.name as student_name, c.name as course_name,
        marker.name as marked_by
      FROM attendance a
      LEFT JOIN users u ON a.student_id = u.id
      LEFT JOIN courses c ON u.course_id = c.id
      LEFT JOIN users marker ON a.marked_by = marker.id
      WHERE a.status != 'deleted'
    `;
    let params = [];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND a.tenant_id = ?';
      params.push(user.tenant_id);
    }

    if (date) {
      query += ' AND a.date = ?';
      params.push(date);
    }

    if (course) {
      query += ' AND c.name = ?';
      params.push(course);
    }

    query += ' ORDER BY a.date DESC, u.name';

    const attendance = await executeQuery(query, params);
    return c.json({ attendance });
  } catch (error) {
    console.error('Attendance fetch error:', error);
    return c.json({ error: 'Failed to fetch attendance' }, 500);
  }
});

api.post('/attendance', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { student_id, date, status, notes } = await c.req.json();

    if (!student_id || !date || !status) {
      return c.json({ error: 'Student ID, date, and status are required' }, 400);
    }

    if (!['present', 'absent', 'late'].includes(status)) {
      return c.json({ error: 'Invalid status. Must be present, absent, or late' }, 400);
    }

    // Check if attendance already exists for this student and date
    const existingQuery = `
      SELECT id FROM attendance
      WHERE student_id = ? AND date = ? AND tenant_id = ?
    `;

    const existing = await executeQuery(existingQuery, [student_id, date, user.tenant_id]);

    if (existing.length > 0) {
      // Update existing attendance
      await executeQuery(
        `UPDATE attendance SET status = ?, notes = ?, marked_by = ?, updated_at = NOW()
         WHERE id = ?`,
        [status, notes, user.id, existing[0].id]
      );

      const updated = await executeQuery(
        `SELECT a.*, u.name as student_name, c.name as course_name
         FROM attendance a
         LEFT JOIN users u ON a.student_id = u.id
         LEFT JOIN courses c ON u.course_id = c.id
         WHERE a.id = ?`,
        [existing[0].id]
      );

      return c.json({ attendance: updated[0] });
    } else {
      // Create new attendance record
      const result = await executeQuery(
        `INSERT INTO attendance (student_id, date, status, notes, marked_by, tenant_id, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [student_id, date, status, notes, user.id, user.tenant_id]
      );

      const newAttendance = await executeQuery(
        `SELECT a.*, u.name as student_name, c.name as course_name
         FROM attendance a
         LEFT JOIN users u ON a.student_id = u.id
         LEFT JOIN courses c ON u.course_id = c.id
         WHERE a.id = ?`,
        [result.insertId]
      );

      return c.json({ attendance: newAttendance[0] });
    }
  } catch (error) {
    console.error('Attendance creation error:', error);
    return c.json({ error: 'Failed to mark attendance' }, 500);
  }
});

api.get('/attendance/reports', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { start_date, end_date, student_id, course_id } = c.req.query();

    let query = `
      SELECT
        u.name as student_name,
        c.name as course_name,
        COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
        COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
        COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
        COUNT(a.id) as total_days,
        ROUND((COUNT(CASE WHEN a.status = 'present' THEN 1 END) / COUNT(a.id)) * 100, 2) as attendance_percentage
      FROM attendance a
      LEFT JOIN users u ON a.student_id = u.id
      LEFT JOIN courses c ON u.course_id = c.id
      WHERE a.status != 'deleted'
    `;
    let params = [];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND a.tenant_id = ?';
      params.push(user.tenant_id);
    }

    if (start_date) {
      query += ' AND a.date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      query += ' AND a.date <= ?';
      params.push(end_date);
    }

    if (student_id) {
      query += ' AND a.student_id = ?';
      params.push(student_id);
    }

    if (course_id) {
      query += ' AND u.course_id = ?';
      params.push(course_id);
    }

    query += ' GROUP BY a.student_id, u.course_id ORDER BY u.name';

    const reports = await executeQuery(query, params);
    return c.json({ reports });
  } catch (error) {
    console.error('Attendance reports error:', error);
    return c.json({ error: 'Failed to fetch attendance reports' }, 500);
  }
});

// Fee Management Routes
api.get('/fees', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    let query = `
      SELECT
        f.id, f.student_id, f.amount, f.due_date, f.paid_date, f.status,
        f.payment_method, f.notes, f.created_at,
        u.name as student_name, c.name as course_name
      FROM fees f
      LEFT JOIN users u ON f.student_id = u.id
      LEFT JOIN courses c ON u.course_id = c.id
      WHERE f.status != 'deleted'
    `;
    let params = [];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND f.tenant_id = ?';
      params.push(user.tenant_id);
    }

    query += ' ORDER BY f.due_date DESC, f.created_at DESC';

    const fees = await executeQuery(query, params);
    return c.json({ fees });
  } catch (error) {
    console.error('Fees fetch error:', error);
    return c.json({ error: 'Failed to fetch fees' }, 500);
  }
});

api.post('/fees', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { student_id, amount, due_date, notes } = await c.req.json();

    if (!student_id || !amount || !due_date) {
      return c.json({ error: 'Student ID, amount, and due date are required' }, 400);
    }

    // Create fee record
    const result = await executeQuery(
      `INSERT INTO fees (student_id, amount, due_date, notes, status, tenant_id, created_at, updated_at)
       VALUES (?, ?, ?, ?, 'pending', ?, NOW(), NOW())`,
      [student_id, amount, due_date, notes, user.tenant_id]
    );

    const newFee = await executeQuery(
      `SELECT f.*, u.name as student_name, c.name as course_name
       FROM fees f
       LEFT JOIN users u ON f.student_id = u.id
       LEFT JOIN courses c ON u.course_id = c.id
       WHERE f.id = ?`,
      [result.insertId]
    );

    return c.json({ fee: newFee[0] });
  } catch (error) {
    console.error('Fee creation error:', error);
    return c.json({ error: 'Failed to create fee record' }, 500);
  }
});

api.post('/fees/:id/pay', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const feeId = c.req.param('id');
    const { payment_method, paid_date } = await c.req.json();

    // Check if fee exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM fees WHERE id = ?';
    let checkParams = [feeId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingFee = await executeQuery(checkQuery, checkParams);
    if (existingFee.length === 0) {
      return c.json({ error: 'Fee record not found' }, 404);
    }

    // Mark fee as paid
    await executeQuery(
      `UPDATE fees SET status = 'paid', payment_method = ?, paid_date = ?, updated_at = NOW()
       WHERE id = ?`,
      [payment_method, paid_date, feeId]
    );

    const updatedFee = await executeQuery(
      `SELECT f.*, u.name as student_name, c.name as course_name
       FROM fees f
       LEFT JOIN users u ON f.student_id = u.id
       LEFT JOIN courses c ON u.course_id = c.id
       WHERE f.id = ?`,
      [feeId]
    );

    return c.json({ fee: updatedFee[0] });
  } catch (error) {
    console.error('Fee payment error:', error);
    return c.json({ error: 'Failed to mark fee as paid' }, 500);
  }
});

api.get('/fees/:id/invoice', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const feeId = c.req.param('id');

    // Get fee details
    let query = `
      SELECT f.*, u.name as student_name, u.email as student_email, c.name as course_name
      FROM fees f
      LEFT JOIN users u ON f.student_id = u.id
      LEFT JOIN courses c ON u.course_id = c.id
      WHERE f.id = ?
    `;
    let params = [feeId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND f.tenant_id = ?';
      params.push(user.tenant_id);
    }

    const feeDetails = await executeQuery(query, params);
    if (feeDetails.length === 0) {
      return c.json({ error: 'Fee record not found' }, 404);
    }

    // Generate simple invoice (in a real app, you'd use a PDF library)
    const invoice = {
      invoice_number: `INV-${feeId}`,
      date: new Date().toISOString().split('T')[0],
      student: feeDetails[0].student_name,
      course: feeDetails[0].course_name,
      amount: feeDetails[0].amount,
      due_date: feeDetails[0].due_date,
      status: feeDetails[0].status
    };

    return c.json({ invoice });
  } catch (error) {
    console.error('Invoice generation error:', error);
    return c.json({ error: 'Failed to generate invoice' }, 500);
  }
});

// Results Management Routes
api.get('/results', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    let query = `
      SELECT
        r.id, r.student_id, r.exam_name, r.exam_date, r.total_marks,
        r.obtained_marks, r.percentage, r.grade, r.status, r.remarks, r.created_at,
        u.name as student_name, c.name as course_name
      FROM results r
      LEFT JOIN users u ON r.student_id = u.id
      LEFT JOIN courses c ON u.course_id = c.id
      WHERE r.status != 'deleted'
    `;
    let params = [];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND r.tenant_id = ?';
      params.push(user.tenant_id);
    }

    query += ' ORDER BY r.exam_date DESC, r.created_at DESC';

    const results = await executeQuery(query, params);
    return c.json({ results });
  } catch (error) {
    console.error('Results fetch error:', error);
    return c.json({ error: 'Failed to fetch results' }, 500);
  }
});

api.post('/results', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { student_id, exam_name, exam_date, total_marks, obtained_marks, percentage, grade, remarks } = await c.req.json();

    if (!student_id || !exam_name || !exam_date || !total_marks || obtained_marks === undefined) {
      return c.json({ error: 'Student ID, exam name, exam date, total marks, and obtained marks are required' }, 400);
    }

    // Create result record
    const result = await executeQuery(
      `INSERT INTO results (student_id, exam_name, exam_date, total_marks, obtained_marks, percentage, grade, remarks, status, tenant_id, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())`,
      [student_id, exam_name, exam_date, total_marks, obtained_marks, percentage, grade, remarks, user.tenant_id]
    );

    const newResult = await executeQuery(
      `SELECT r.*, u.name as student_name, c.name as course_name
       FROM results r
       LEFT JOIN users u ON r.student_id = u.id
       LEFT JOIN courses c ON u.course_id = c.id
       WHERE r.id = ?`,
      [result.insertId]
    );

    return c.json({ result: newResult[0] });
  } catch (error) {
    console.error('Result creation error:', error);
    return c.json({ error: 'Failed to create result' }, 500);
  }
});

api.put('/results/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const resultId = c.req.param('id');
    const { student_id, exam_name, exam_date, total_marks, obtained_marks, percentage, grade, remarks } = await c.req.json();

    // Check if result exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM results WHERE id = ?';
    let checkParams = [resultId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingResult = await executeQuery(checkQuery, checkParams);
    if (existingResult.length === 0) {
      return c.json({ error: 'Result not found' }, 404);
    }

    // Update result
    await executeQuery(
      `UPDATE results SET student_id = ?, exam_name = ?, exam_date = ?, total_marks = ?, obtained_marks = ?, percentage = ?, grade = ?, remarks = ?, updated_at = NOW()
       WHERE id = ?`,
      [student_id, exam_name, exam_date, total_marks, obtained_marks, percentage, grade, remarks, resultId]
    );

    const updatedResult = await executeQuery(
      `SELECT r.*, u.name as student_name, c.name as course_name
       FROM results r
       LEFT JOIN users u ON r.student_id = u.id
       LEFT JOIN courses c ON u.course_id = c.id
       WHERE r.id = ?`,
      [resultId]
    );

    return c.json({ result: updatedResult[0] });
  } catch (error) {
    console.error('Result update error:', error);
    return c.json({ error: 'Failed to update result' }, 500);
  }
});

api.delete('/results/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const resultId = c.req.param('id');

    // Check if result exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM results WHERE id = ?';
    let checkParams = [resultId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingResult = await executeQuery(checkQuery, checkParams);
    if (existingResult.length === 0) {
      return c.json({ error: 'Result not found' }, 404);
    }

    // Soft delete result
    await executeQuery(
      'UPDATE results SET status = "deleted", updated_at = NOW() WHERE id = ?',
      [resultId]
    );

    return c.json({ success: true });
  } catch (error) {
    console.error('Result deletion error:', error);
    return c.json({ error: 'Failed to delete result' }, 500);
  }
});

// HRM & Payroll Routes
api.get('/employees', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    let query = `
      SELECT id, name, email, phone, position, department, salary, join_date, status, created_at
      FROM employees
      WHERE status != 'deleted'
    `;
    let params = [];

    if (user.role !== 'super_admin' && user.tenant_id) {
      query += ' AND tenant_id = ?';
      params.push(user.tenant_id);
    }

    query += ' ORDER BY created_at DESC';

    const employees = await executeQuery(query, params);
    return c.json({ employees });
  } catch (error) {
    console.error('Employees fetch error:', error);
    return c.json({ error: 'Failed to fetch employees' }, 500);
  }
});

api.post('/employees', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const { name, email, phone, position, department, salary, join_date } = await c.req.json();

    if (!name || !email || !position || !salary || !join_date) {
      return c.json({ error: 'Name, email, position, salary, and join date are required' }, 400);
    }

    // Create employee record
    const result = await executeQuery(
      `INSERT INTO employees (name, email, phone, position, department, salary, join_date, status, tenant_id, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())`,
      [name, email, phone, position, department, salary, join_date, user.tenant_id]
    );

    const newEmployee = await executeQuery(
      'SELECT * FROM employees WHERE id = ?',
      [result.insertId]
    );

    return c.json({ employee: newEmployee[0] });
  } catch (error) {
    console.error('Employee creation error:', error);
    return c.json({ error: 'Failed to create employee' }, 500);
  }
});

api.put('/employees/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const employeeId = c.req.param('id');
    const { name, email, phone, position, department, salary, join_date } = await c.req.json();

    // Check if employee exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM employees WHERE id = ?';
    let checkParams = [employeeId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingEmployee = await executeQuery(checkQuery, checkParams);
    if (existingEmployee.length === 0) {
      return c.json({ error: 'Employee not found' }, 404);
    }

    // Update employee
    await executeQuery(
      `UPDATE employees SET name = ?, email = ?, phone = ?, position = ?, department = ?, salary = ?, join_date = ?, updated_at = NOW()
       WHERE id = ?`,
      [name, email, phone, position, department, salary, join_date, employeeId]
    );

    const updatedEmployee = await executeQuery(
      'SELECT * FROM employees WHERE id = ?',
      [employeeId]
    );

    return c.json({ employee: updatedEmployee[0] });
  } catch (error) {
    console.error('Employee update error:', error);
    return c.json({ error: 'Failed to update employee' }, 500);
  }
});

api.delete('/employees/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const employeeId = c.req.param('id');

    // Check if employee exists and belongs to user's tenant
    let checkQuery = 'SELECT id FROM employees WHERE id = ?';
    let checkParams = [employeeId];

    if (user.role !== 'super_admin' && user.tenant_id) {
      checkQuery += ' AND tenant_id = ?';
      checkParams.push(user.tenant_id);
    }

    const existingEmployee = await executeQuery(checkQuery, checkParams);
    if (existingEmployee.length === 0) {
      return c.json({ error: 'Employee not found' }, 404);
    }

    // Soft delete employee
    await executeQuery(
      'UPDATE employees SET status = "deleted", updated_at = NOW() WHERE id = ?',
      [employeeId]
    );

    return c.json({ success: true });
  } catch (error) {
    console.error('Employee deletion error:', error);
    return c.json({ error: 'Failed to delete employee' }, 500);
  }
});

// Super Admin Routes
api.get('/admin/centers', async (c) => {
  try {
    console.log('🔍 Admin Centers Debug - Headers:', c.req.header());
    const user = await getUserFromToken(c);
    console.log('🔍 Admin Centers Debug - User:', user ? 'Found' : 'Not found');
    if (!user || user.role !== 'super_admin') {
      console.log('🔍 Admin Centers Debug - Access denied, user role:', user?.role);
      return c.json({ error: 'Access denied' }, 403);
    }

    const centers = await executeQuery(`
      SELECT
        c.id, c.name, c.subdomain as slug, c.email, c.phone, c.address,
        'premium' as subscription_plan, c.status, c.created_at,
        u.name as admin_name, u.email as admin_email,
        (SELECT COUNT(*) FROM users WHERE tenant_id = c.id AND role = 'student') as total_students,
        (SELECT COUNT(*) FROM users WHERE tenant_id = c.id AND role = 'teacher') as total_teachers
      FROM centers c
      LEFT JOIN users u ON c.id = u.tenant_id AND u.role = 'center_admin'
      ORDER BY c.created_at DESC
    `);

    return c.json({ centers });
  } catch (error) {
    console.error('Error fetching centers:', error);
    return c.json({ error: 'Failed to fetch centers' }, 500);
  }
});

api.post('/admin/centers', async (c) => {
  try {
    console.log('🔍 Admin Create Center Debug - Headers:', c.req.header());
    const user = await getUserFromToken(c);
    console.log('🔍 Admin Create Center Debug - User:', user ? 'Found' : 'Not found');
    if (!user || user.role !== 'super_admin') {
      console.log('🔍 Admin Create Center Debug - Access denied, user role:', user?.role);
      return c.json({ error: 'Access denied' }, 403);
    }

    const {
      name, slug, email, phone, address, subscription_plan,
      adminName, adminEmail, adminPassword
    } = await c.req.json();

    if (!name || !adminName || !adminEmail || !adminPassword) {
      return c.json({ error: 'Required fields missing' }, 400);
    }

    // Generate slug if not provided
    const centerSlug = slug || name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

    // Check if subdomain already exists
    const existingCenters = await executeQuery(
      'SELECT id FROM centers WHERE subdomain = ?',
      [centerSlug]
    );
    if (existingCenters.length > 0) {
      return c.json({ error: 'Subdomain already exists' }, 400);
    }

    // Check if admin email already exists
    const existingUsers = await executeQuery(
      'SELECT id FROM users WHERE email = ?',
      [adminEmail]
    );
    if (existingUsers.length > 0) {
      return c.json({ error: 'Admin email already exists' }, 400);
    }

    // Create center
    const centerResult = await executeQuery(
      `INSERT INTO centers (name, subdomain, email, phone, address, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [name, centerSlug, email, phone, address]
    );

    const centerId = centerResult.insertId;

    // Hash password
    const hashedPassword = await bcrypt.hash(adminPassword, 10);

    // Create admin user
    await executeQuery(
      `INSERT INTO users (name, email, password_hash, role, tenant_id, status, created_at, updated_at)
       VALUES (?, ?, ?, 'center_admin', ?, 'active', NOW(), NOW())`,
      [adminName, adminEmail, hashedPassword, centerId]
    );

    return c.json({
      success: true,
      message: 'Coaching center created successfully',
      tenant_id: tenantId
    });

  } catch (error) {
    console.error('Error creating center:', error);
    return c.json({ error: 'Failed to create center' }, 500);
  }
});

api.put('/admin/centers/:id/status', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || user.role !== 'super_admin') {
      return c.json({ error: 'Access denied' }, 403);
    }

    const centerId = c.req.param('id');
    const { status } = await c.req.json();

    if (!['active', 'inactive', 'suspended'].includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    await executeQuery(
      'UPDATE centers SET status = ?, updated_at = NOW() WHERE id = ?',
      [status, centerId]
    );

    return c.json({ success: true });

  } catch (error) {
    console.error('Error updating center status:', error);
    return c.json({ error: 'Failed to update center status' }, 500);
  }
});

api.get('/admin/stats', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || user.role !== 'super_admin') {
      return c.json({ error: 'Access denied' }, 403);
    }

    // Get system-wide statistics
    const [totalCenters] = await executeQuery('SELECT COUNT(*) as count FROM centers');
    const [activeCenters] = await executeQuery('SELECT COUNT(*) as count FROM centers WHERE status = "active"');
    const [totalUsers] = await executeQuery('SELECT COUNT(*) as count FROM users WHERE status = "active"');

    // Mock revenue data (in real app, calculate from payments)
    const totalRevenue = 50000;
    const monthlyGrowth = 12.5;

    const stats = {
      totalCenters: totalCenters.count,
      activeCenters: activeCenters.count,
      totalUsers: totalUsers.count,
      totalRevenue,
      monthlyGrowth
    };

    return c.json({ stats });

  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return c.json({ error: 'Failed to fetch statistics' }, 500);
  }
});

// Dashboard Stats for Center Admin
api.get('/dashboard/stats', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Not authenticated' }, 401);
    }

    const tenantSlug = c.req.query('tenant_slug');

    // For center admin, ensure they can only access their tenant's data
    if (user.role === 'center_admin' && user.tenant_id) {
      // Get tenant-specific stats
      const [totalStudents] = await executeQuery(
        'SELECT COUNT(*) as count FROM users WHERE tenant_id = ? AND role = "student" AND status = "active"',
        [user.tenant_id]
      );

      const [activeCourses] = await executeQuery(
        'SELECT COUNT(*) as count FROM courses WHERE tenant_id = ? AND status = "active"',
        [user.tenant_id]
      );

      // Mock stats for demo
      const stats = {
        students: {
          total: totalStudents.count,
          active: totalStudents.count,
          newThisMonth: Math.floor(totalStudents.count * 0.1)
        },
        courses: {
          total: activeCourses.count,
          active: activeCourses.count,
          enrollments: totalStudents.count
        },
        attendance: {
          todayRate: 85.5,
          weeklyAverage: 82.3,
          totalSessions: 150
        },
        financial: {
          monthlyRevenue: 15000,
          pendingFees: 2500,
          collectionRate: 92.5
        },
        recentActivities: [
          {
            id: 1,
            type: 'enrollment',
            message: 'New student enrolled in Mathematics course',
            timestamp: new Date().toISOString()
          },
          {
            id: 2,
            type: 'payment',
            message: 'Fee payment received from John Doe',
            timestamp: new Date(Date.now() - 3600000).toISOString()
          }
        ]
      };

      return c.json({ stats });
    }

    return c.json({ error: 'Access denied' }, 403);

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return c.json({ error: 'Failed to fetch dashboard stats' }, 500);
  }
});

// Admin Subscriptions Routes
api.get('/admin/subscriptions', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || user.role !== 'super_admin') {
      return c.json({ error: 'Access denied' }, 403);
    }

    const subscriptions = await executeQuery(`
      SELECT
        s.id, s.status, s.start_date, s.end_date, s.amount, s.payment_status, s.auto_renewal,
        c.id as center_id, c.name as center_name,
        p.name as plan_name
      FROM subscriptions s
      LEFT JOIN centers c ON s.tenant_id = c.id
      LEFT JOIN plans p ON s.plan_id = p.id
      ORDER BY s.created_at DESC
    `);

    return c.json({ subscriptions });
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
    return c.json({ error: 'Failed to fetch subscriptions' }, 500);
  }
});

// Admin Plans Routes
api.get('/admin/plans', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || user.role !== 'super_admin') {
      return c.json({ error: 'Access denied' }, 403);
    }

    const plans = await executeQuery(`
      SELECT
        id, name, description, price, duration_months, max_students, max_teachers,
        features, is_active, created_at
      FROM plans
      ORDER BY created_at DESC
    `);

    // Parse features JSON for each plan
    const plansWithFeatures = plans.map(plan => ({
      ...plan,
      features: plan.features ? JSON.parse(plan.features) : []
    }));

    return c.json({ plans: plansWithFeatures });
  } catch (error) {
    console.error('Error fetching plans:', error);
    return c.json({ error: 'Failed to fetch plans' }, 500);
  }
});

// Center Info Route (for subdomain homepages)
api.get('/center/info', async (c) => {
  try {
    const tenantSlug = c.req.query('tenant_slug');

    if (!tenantSlug) {
      return c.json({ error: 'Tenant slug required' }, 400);
    }

    const center = await executeQuery(
      'SELECT id, name, subdomain, email, phone, address FROM centers WHERE subdomain = ? AND status = "active"',
      [tenantSlug]
    );

    if (center.length === 0) {
      return c.json({ error: 'Center not found' }, 404);
    }

    return c.json({ center: center[0] });
  } catch (error) {
    console.error('Error fetching center info:', error);
    return c.json({ error: 'Failed to fetch center info' }, 500);
  }
});

// Mount coaching routes
api.route('/coaching', coaching);

// Health check
api.get('/health', (c) => {
  return c.json({ status: 'ok', timestamp: new Date().toISOString() });
});

export { api };
