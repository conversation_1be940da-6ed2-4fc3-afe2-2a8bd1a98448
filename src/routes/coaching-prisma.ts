import { Hono } from 'hono';
import { z } from 'zod';
import { getTenantDb, getTenantInfo } from '../middleware/tenant-prisma.js';
import { authMiddleware } from '../middleware/auth-prisma.js';

const coaching = new Hono();

// All coaching routes require authentication
coaching.use('*', authMiddleware);

/**
 * GET /api/coaching/dashboard
 * Get coaching center dashboard data
 */
coaching.get('/dashboard', async (c) => {
  try {
    const tenantDb = getTenantDb(c);
    const tenantInfo = getTenantInfo(c);

    // Get dashboard statistics
    const [
      totalStudents,
      totalTeachers,
      totalCourses,
      totalBranches,
      recentStudents
    ] = await Promise.all([
      tenantDb.user.count({ where: { role: 'STUDENT', status: 'ACTIVE' } }),
      tenantDb.user.count({ where: { role: 'TEACHER', status: 'ACTIVE' } }),
      tenantDb.course.count({ where: { status: 'ACTIVE' } }),
      tenantDb.branch.count({ where: { status: 'ACTIVE' } }),
      tenantDb.user.findMany({
        where: { role: 'STUDENT', status: 'ACTIVE' },
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          email: true,
          createdAt: true,
        }
      })
    ]);

    return c.json({
      success: true,
      data: {
        stats: {
          totalStudents,
          totalTeachers,
          totalCourses,
          totalBranches,
        },
        recentStudents,
        center: {
          id: tenantInfo.tenant.id,
          name: tenantInfo.tenant.name,
          subdomain: tenantInfo.tenant.subdomain,
        }
      }
    });

  } catch (error) {
    console.error('Get coaching dashboard error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch dashboard data' 
    }, 500);
  }
});

/**
 * GET /api/coaching/students
 * Get all students with pagination
 */
coaching.get('/students', async (c) => {
  try {
    const tenantDb = getTenantDb(c);
    
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const search = c.req.query('search');
    
    const skip = (page - 1) * limit;
    
    const where: any = {
      role: 'STUDENT'
    };
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { email: { contains: search } },
        { phone: { contains: search } }
      ];
    }
    
    const [students, total] = await Promise.all([
      tenantDb.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          branch: true,
          studentEnrollments: {
            include: {
              course: true,
              batch: true
            }
          }
        }
      }),
      tenantDb.user.count({ where })
    ]);
    
    return c.json({
      success: true,
      data: {
        students,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get students error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch students' 
    }, 500);
  }
});

/**
 * GET /api/coaching/courses
 * Get all courses
 */
coaching.get('/courses', async (c) => {
  try {
    const tenantDb = getTenantDb(c);
    
    const courses = await tenantDb.course.findMany({
      where: { status: 'ACTIVE' },
      include: {
        subject: true,
        branch: true,
        batches: {
          where: { status: 'ACTIVE' },
          include: {
            teacher: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            _count: {
              select: {
                enrollments: true
              }
            }
          }
        },
        _count: {
          select: {
            enrollments: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
    
    return c.json({
      success: true,
      data: { courses }
    });

  } catch (error) {
    console.error('Get courses error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch courses' 
    }, 500);
  }
});

/**
 * GET /api/coaching/branches
 * Get all branches
 */
coaching.get('/branches', async (c) => {
  try {
    const tenantDb = getTenantDb(c);
    
    const branches = await tenantDb.branch.findMany({
      where: { status: 'ACTIVE' },
      include: {
        _count: {
          select: {
            users: true,
            courses: true,
            rooms: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
    
    return c.json({
      success: true,
      data: { branches }
    });

  } catch (error) {
    console.error('Get branches error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch branches' 
    }, 500);
  }
});

/**
 * POST /api/coaching/branches
 * Create a new branch
 */
coaching.post('/branches', async (c) => {
  try {
    const tenantDb = getTenantDb(c);
    const body = await c.req.json();

    const branchSchema = z.object({
      branchCode: z.string().min(1, 'Branch code is required'),
      branchName: z.string().min(1, 'Branch name is required'),
      address: z.string().optional(),
      phone: z.string().optional(),
      email: z.string().email().optional(),
      managerId: z.string().optional(),
      establishmentDate: z.string().optional(),
    });

    const validatedData = branchSchema.parse(body);

    // Check if branch code already exists
    const existingBranch = await tenantDb.branch.findUnique({
      where: { branchCode: validatedData.branchCode }
    });

    if (existingBranch) {
      return c.json({
        success: false,
        message: 'Branch code already exists'
      }, 400);
    }

    const branch = await tenantDb.branch.create({
      data: {
        ...validatedData,
        establishmentDate: validatedData.establishmentDate ? new Date(validatedData.establishmentDate) : null,
        status: 'ACTIVE'
      }
    });

    return c.json({
      success: true,
      message: 'Branch created successfully',
      data: { branch }
    }, 201);

  } catch (error) {
    console.error('Create branch error:', error);
    return c.json({
      success: false,
      message: 'Failed to create branch'
    }, 500);
  }
});

/**
 * GET /api/coaching/info
 * Get coaching center information
 */
coaching.get('/info', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    
    return c.json({
      success: true,
      data: {
        center: tenantInfo.tenant,
        tenantSlug: tenantInfo.tenantSlug,
        databaseConnected: !!tenantInfo.db
      }
    });

  } catch (error) {
    console.error('Get coaching info error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch coaching center info' 
    }, 500);
  }
});

export default coaching;
