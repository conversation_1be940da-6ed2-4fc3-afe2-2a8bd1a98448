import { Hono } from 'hono';
import { serveStatic } from 'hono/bun';

const staticRoutes = new Hono();

/**
 * Static File Routes
 * Serves CSS, JS, images, and other static assets
 */

// Serve CSS files
staticRoutes.get('/styles/*', serveStatic({ 
  root: './public',
  rewriteRequestPath: (path) => path.replace(/^\/styles/, '/styles')
}));

// Serve JavaScript files
staticRoutes.get('/js/*', serveStatic({ 
  root: './public',
  rewriteRequestPath: (path) => path.replace(/^\/js/, '/js')
}));

// Serve images
staticRoutes.get('/images/*', serveStatic({ 
  root: './public',
  rewriteRequestPath: (path) => path.replace(/^\/images/, '/images')
}));

// Serve favicon
staticRoutes.get('/favicon.ico', serveStatic({ 
  root: './public',
  path: './favicon.ico'
}));

// Serve uploads
staticRoutes.get('/uploads/*', serveStatic({ 
  root: './uploads',
  rewriteRequestPath: (path) => path.replace(/^\/uploads/, '')
}));

export { staticRoutes };
