import { Hono } from 'hono';
import { executeQuery } from '../config/database.js';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const coaching = new Hono();

// Helper function to get user from token
async function getUserFromToken(c: any) {
  try {
    let token = null;
    const authHeader = c.req.header('Authorization');
    console.log('🔍 Coaching Auth Debug - Authorization header:', authHeader ? 'Present' : 'Missing');

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      console.log('🔍 Coaching Auth Debug - Token extracted:', token ? 'Yes' : 'No');
    }

    if (!token) {
      console.log('🔍 Coaching Auth Debug - No token found');
      return null;
    }

    let decoded: any;

    if (token.startsWith('dev.')) {
      try {
        const devTokenData = token.substring(4);
        decoded = JSON.parse(atob(devTokenData));
        console.log('🔍 Coaching Auth Debug - Development token decoded:', decoded);
      } catch (error) {
        console.error('🔍 Coaching Auth Debug - Dev token decode error:', error);
        return null;
      }
    } else {
      try {
        decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
        console.log('🔍 Coaching Auth Debug - JWT token decoded:', decoded);
      } catch (error) {
        console.error('🔍 Coaching Auth Debug - JWT verification failed:', error);
        return null;
      }
    }

    const user = await executeQuery(
      'SELECT * FROM users WHERE id = ? AND status = "active"',
      [decoded.userId]
    );

    console.log('🔍 Coaching Auth Debug - User found:', user.length > 0 ? 'Yes' : 'No');

    return user[0] || null;
  } catch (error) {
    console.error('🔍 Coaching Auth Debug - General error:', error);
    return null;
  }
}

// Helper function to check center access
function checkCenterAccess(user: any, centerId: number) {
  if (user.role === 'super_admin') return true;
  return user.center_id === centerId;
}

// ==================== DASHBOARD ROUTES ====================

// Dashboard stats
coaching.get('/dashboard/stats', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    // Get dashboard statistics
    const [totalStudents] = await executeQuery(
      'SELECT COUNT(*) as count FROM users WHERE center_id = ? AND role = "student" AND status = "active"',
      [centerId]
    );

    const [totalTeachers] = await executeQuery(
      'SELECT COUNT(*) as count FROM users WHERE center_id = ? AND role = "teacher" AND status = "active"',
      [centerId]
    );

    const [totalBatches] = await executeQuery(
      'SELECT COUNT(*) as count FROM batches WHERE center_id = ? AND status = "active"',
      [centerId]
    );

    const [totalCourses] = await executeQuery(
      'SELECT COUNT(*) as count FROM courses WHERE center_id = ? AND status = "active"',
      [centerId]
    );

    const [monthlyRevenue] = await executeQuery(
      'SELECT COALESCE(SUM(amount), 0) as revenue FROM payments WHERE center_id = ? AND MONTH(payment_date) = MONTH(CURRENT_DATE()) AND YEAR(payment_date) = YEAR(CURRENT_DATE())',
      [centerId]
    );

    const [pendingFees] = await executeQuery(
      'SELECT COALESCE(SUM(amount), 0) as pending FROM fees WHERE center_id = ? AND status = "pending"',
      [centerId]
    );

    // Recent activities
    const recentPayments = await executeQuery(
      `SELECT p.amount, p.payment_date, u.name as student_name 
       FROM payments p 
       JOIN users u ON p.student_id = u.id 
       WHERE p.center_id = ? 
       ORDER BY p.payment_date DESC 
       LIMIT 5`,
      [centerId]
    );

    const stats = {
      totalStudents: totalStudents.count,
      totalTeachers: totalTeachers.count,
      totalBatches: totalBatches.count,
      totalCourses: totalCourses.count,
      monthlyRevenue: monthlyRevenue.revenue,
      pendingFees: pendingFees.pending,
      recentPayments
    };

    return c.json({ stats });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return c.json({ error: 'Failed to fetch dashboard stats' }, 500);
  }
});

// ==================== STUDENT ROUTES ====================

// Get all students
coaching.get('/students', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const students = await executeQuery(
      `SELECT u.*, 
              COUNT(DISTINCT e.batch_id) as enrolled_batches,
              COALESCE(SUM(f.amount), 0) as total_fees,
              COALESCE(SUM(CASE WHEN f.status = 'pending' THEN f.amount ELSE 0 END), 0) as pending_fees
       FROM users u
       LEFT JOIN enrollments e ON u.id = e.student_id AND e.status = 'active'
       LEFT JOIN fees f ON u.id = f.student_id
       WHERE u.center_id = ? AND u.role = 'student'
       GROUP BY u.id
       ORDER BY u.created_at DESC`,
      [centerId]
    );

    return c.json({ students });
  } catch (error) {
    console.error('Error fetching students:', error);
    return c.json({ error: 'Failed to fetch students' }, 500);
  }
});

// Get single student by ID
coaching.get('/students/:studentId', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const studentId = c.req.param('studentId');
    if (!studentId) {
      return c.json({ error: 'Student ID is required' }, 400);
    }

    const students = await executeQuery(
      `SELECT u.*,
              COUNT(DISTINCT e.batch_id) as enrolled_batches,
              COALESCE(SUM(f.amount), 0) as total_fees,
              COALESCE(SUM(CASE WHEN f.status = 'pending' THEN f.amount ELSE 0 END), 0) as pending_fees
       FROM users u
       LEFT JOIN enrollments e ON u.id = e.student_id AND e.status = 'active'
       LEFT JOIN fees f ON u.id = f.student_id
       WHERE u.center_id = ? AND u.role = 'student' AND u.id = ?
       GROUP BY u.id`,
      [centerId, studentId]
    );

    if (students.length === 0) {
      return c.json({ error: 'Student not found' }, 404);
    }

    return c.json({ student: students[0] });
  } catch (error) {
    console.error('Error fetching student:', error);
    return c.json({ error: 'Failed to fetch student' }, 500);
  }
});

// Add new student
coaching.post('/students', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const {
      name, email, phone, address, date_of_birth, gender, blood_group,
      parent_name, parent_phone, emergency_contact
    } = await c.req.json();

    if (!name || !email) {
      return c.json({ error: 'Name and email are required' }, 400);
    }

    // Check if email already exists in this center
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE email = ? AND center_id = ?',
      [email, centerId]
    );

    if (existingUser.length > 0) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    // Generate default password
    const defaultPassword = 'student123';
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    const result = await executeQuery(
      `INSERT INTO users (
        center_id, name, email, password_hash, phone, address, date_of_birth,
        gender, blood_group, role, parent_name, parent_phone, emergency_contact, admission_date, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'student', ?, ?, ?, CURRENT_DATE(), 'active')`,
      [centerId, name, email, hashedPassword, phone, address, date_of_birth, gender, blood_group, parent_name, parent_phone, emergency_contact]
    );

    return c.json({ 
      success: true, 
      studentId: result.insertId,
      message: 'Student added successfully',
      defaultPassword 
    });
  } catch (error) {
    console.error('Error adding student:', error);
    return c.json({ error: 'Failed to add student' }, 500);
  }
});

// Update student
coaching.put('/students/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const studentId = c.req.param('id');
    const centerId = user.center_id;
    const updateData = await c.req.json();

    // Build dynamic update query
    const updateFields = [];
    const updateValues = [];

    const allowedFields = ['name', 'email', 'phone', 'address', 'date_of_birth', 'gender', 'blood_group', 'parent_name', 'parent_phone', 'emergency_contact', 'status'];
    
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        updateFields.push(`${field} = ?`);
        updateValues.push(updateData[field]);
      }
    }

    if (updateFields.length === 0) {
      return c.json({ error: 'No fields to update' }, 400);
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(studentId, centerId);

    // Set current user ID for audit trail
    await executeQuery('SET @current_user_id = ?', [user.userId]);

    await executeQuery(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ? AND center_id = ? AND role = 'student'`,
      updateValues
    );

    return c.json({ success: true, message: 'Student updated successfully' });
  } catch (error) {
    console.error('Error updating student:', error);
    return c.json({ error: 'Failed to update student' }, 500);
  }
});

// Get student audit trail
coaching.get('/students/:id/audit-trail', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const studentId = c.req.param('id');
    const centerId = user.center_id;

    // Verify student belongs to the center
    const student = await executeQuery(
      'SELECT id FROM users WHERE id = ? AND center_id = ? AND role = "student"',
      [studentId, centerId]
    );

    if (student.length === 0) {
      return c.json({ error: 'Student not found' }, 404);
    }

    // Get audit trail
    const auditLogs = await executeQuery(
      `SELECT
        sal.id,
        sal.action_type,
        sal.old_value,
        sal.new_value,
        sal.field_name,
        sal.description,
        sal.performed_by,
        performer.name as performed_by_name,
        sal.performed_at,
        sal.ip_address
       FROM student_audit_logs sal
       LEFT JOIN users performer ON sal.performed_by = performer.id
       WHERE sal.student_id = ? AND sal.center_id = ?
       ORDER BY sal.performed_at DESC
       LIMIT 50`,
      [studentId, centerId]
    );

    return c.json({ auditLogs });
  } catch (error) {
    console.error('Error fetching audit trail:', error);
    return c.json({ error: 'Failed to fetch audit trail' }, 500);
  }
});

// ==================== TEACHER ROUTES ====================

// Get all teachers
coaching.get('/teachers', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const teachers = await executeQuery(
      `SELECT u.*, 
              COUNT(DISTINCT b.id) as assigned_batches
       FROM users u
       LEFT JOIN batches b ON u.id = b.teacher_id AND b.status = 'active'
       WHERE u.center_id = ? AND u.role = 'teacher'
       GROUP BY u.id
       ORDER BY u.created_at DESC`,
      [centerId]
    );

    return c.json({ teachers });
  } catch (error) {
    console.error('Error fetching teachers:', error);
    return c.json({ error: 'Failed to fetch teachers' }, 500);
  }
});

// Add new teacher
coaching.post('/teachers', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const {
      name, email, phone, address, qualification, experience_years, salary, employee_id
    } = await c.req.json();

    if (!name || !email) {
      return c.json({ error: 'Name and email are required' }, 400);
    }

    // Check if email already exists in this center
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE email = ? AND center_id = ?',
      [email, centerId]
    );

    if (existingUser.length > 0) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    // Generate default password
    const defaultPassword = 'teacher123';
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    const result = await executeQuery(
      `INSERT INTO users (
        center_id, name, email, password_hash, phone, address, 
        qualification, experience_years, salary, employee_id, role, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'teacher', 'active')`,
      [centerId, name, email, hashedPassword, phone, address, qualification, experience_years, salary, employee_id]
    );

    return c.json({ 
      success: true, 
      teacherId: result.insertId,
      message: 'Teacher added successfully',
      defaultPassword 
    });
  } catch (error) {
    console.error('Error adding teacher:', error);
    return c.json({ error: 'Failed to add teacher' }, 500);
  }
});

// Update teacher
coaching.put('/teachers/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const teacherId = c.req.param('id');
    const { name, email, phone, subject, qualification, salary, status } = await c.req.json();

    // Validate required fields
    if (!name || !email) {
      return c.json({ error: 'Name and email are required' }, 400);
    }

    // Check if teacher exists and belongs to the center
    const existingTeacher = await executeQuery(
      'SELECT id FROM users WHERE id = ? AND center_id = ? AND role = "teacher"',
      [teacherId, centerId]
    );

    if (existingTeacher.length === 0) {
      return c.json({ error: 'Teacher not found' }, 404);
    }

    // Check if email is already taken by another user
    const emailCheck = await executeQuery(
      'SELECT id FROM users WHERE email = ? AND id != ?',
      [email, teacherId]
    );

    if (emailCheck.length > 0) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    // Update teacher
    await executeQuery(
      `UPDATE users SET
        name = ?,
        email = ?,
        phone = ?,
        subject = ?,
        qualification = ?,
        salary = ?,
        status = ?,
        updated_at = NOW()
      WHERE id = ? AND center_id = ?`,
      [name, email, phone, subject, qualification, salary, status || 'active', teacherId, centerId]
    );

    return c.json({
      success: true,
      message: 'Teacher updated successfully'
    });

  } catch (error) {
    console.error('Error updating teacher:', error);
    return c.json({ error: 'Failed to update teacher' }, 500);
  }
});

// ==================== COURSE ROUTES ====================

// Get all courses
coaching.get('/courses', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const courses = await executeQuery(
      `SELECT c.*,
              COUNT(DISTINCT b.id) as total_batches,
              COUNT(DISTINCT e.student_id) as enrolled_students
       FROM courses c
       LEFT JOIN batches b ON c.id = b.course_id AND b.status = 'active'
       LEFT JOIN enrollments e ON b.id = e.batch_id AND e.status = 'active'
       WHERE c.center_id = ?
       GROUP BY c.id
       ORDER BY c.created_at DESC`,
      [centerId]
    );

    return c.json({ courses });
  } catch (error) {
    console.error('Error fetching courses:', error);
    return c.json({ error: 'Failed to fetch courses' }, 500);
  }
});

// Add new course
coaching.post('/courses', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const { name, code, description, duration_months, fee } = await c.req.json();

    if (!name) {
      return c.json({ error: 'Course name is required' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO courses (center_id, name, code, description, duration_months, fee, status)
       VALUES (?, ?, ?, ?, ?, ?, 'active')`,
      [centerId, name, code, description, duration_months, fee]
    );

    return c.json({
      success: true,
      courseId: result.insertId,
      message: 'Course added successfully'
    });
  } catch (error) {
    console.error('Error adding course:', error);
    return c.json({ error: 'Failed to add course' }, 500);
  }
});

// ==================== BATCH ROUTES ====================

// Get all batches
coaching.get('/batches', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const batches = await executeQuery(
      `SELECT b.*,
              c.name as course_name,
              u.name as teacher_name,
              COUNT(DISTINCT e.student_id) as enrolled_students
       FROM batches b
       LEFT JOIN courses c ON b.course_id = c.id
       LEFT JOIN users u ON b.teacher_id = u.id
       LEFT JOIN enrollments e ON b.id = e.batch_id AND e.status = 'active'
       WHERE b.center_id = ?
       GROUP BY b.id
       ORDER BY b.created_at DESC`,
      [centerId]
    );

    return c.json({ batches });
  } catch (error) {
    console.error('Error fetching batches:', error);
    return c.json({ error: 'Failed to fetch batches' }, 500);
  }
});

// Add new batch
coaching.post('/batches', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const {
      course_id, name, teacher_id, start_date, end_date,
      schedule_days, start_time, end_time, max_students
    } = await c.req.json();

    if (!course_id || !name) {
      return c.json({ error: 'Course and batch name are required' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO batches (
        center_id, course_id, name, teacher_id, start_date, end_date,
        schedule_days, start_time, end_time, max_students, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')`,
      [centerId, course_id, name, teacher_id, start_date, end_date,
       JSON.stringify(schedule_days), start_time, end_time, max_students]
    );

    return c.json({
      success: true,
      batchId: result.insertId,
      message: 'Batch created successfully'
    });
  } catch (error) {
    console.error('Error creating batch:', error);
    return c.json({ error: 'Failed to create batch' }, 500);
  }
});

// ==================== ENROLLMENT ROUTES ====================

// Get enrollments for a batch
coaching.get('/batches/:id/enrollments', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const batchId = c.req.param('id');
    const centerId = user.center_id;

    const enrollments = await executeQuery(
      `SELECT e.*, u.name as student_name, u.email, u.phone
       FROM enrollments e
       JOIN users u ON e.student_id = u.id
       WHERE e.batch_id = ? AND e.center_id = ?
       ORDER BY e.enrollment_date DESC`,
      [batchId, centerId]
    );

    return c.json({ enrollments });
  } catch (error) {
    console.error('Error fetching enrollments:', error);
    return c.json({ error: 'Failed to fetch enrollments' }, 500);
  }
});

// Enroll student in batch
coaching.post('/enrollments', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const { student_id, batch_id } = await c.req.json();

    if (!student_id || !batch_id) {
      return c.json({ error: 'Student and batch are required' }, 400);
    }

    // Check if already enrolled
    const existing = await executeQuery(
      'SELECT id FROM enrollments WHERE student_id = ? AND batch_id = ?',
      [student_id, batch_id]
    );

    if (existing.length > 0) {
      return c.json({ error: 'Student already enrolled in this batch' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO enrollments (center_id, student_id, batch_id, enrollment_date, status)
       VALUES (?, ?, ?, CURRENT_DATE(), 'active')`,
      [centerId, student_id, batch_id]
    );

    return c.json({
      success: true,
      enrollmentId: result.insertId,
      message: 'Student enrolled successfully'
    });
  } catch (error) {
    console.error('Error enrolling student:', error);
    return c.json({ error: 'Failed to enroll student' }, 500);
  }
});

// ==================== FEE ROUTES ====================

// Get fees for a student or all pending fees
coaching.get('/fees', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const studentId = c.req.query('student_id');
    const status = c.req.query('status') || 'all';

    let query = `
      SELECT f.*, u.name as student_name, b.name as batch_name, c.name as course_name
      FROM fees f
      JOIN users u ON f.student_id = u.id
      JOIN batches b ON f.batch_id = b.id
      JOIN courses c ON b.course_id = c.id
      WHERE f.center_id = ?
    `;
    const params = [centerId];

    if (studentId) {
      query += ' AND f.student_id = ?';
      params.push(studentId);
    }

    if (status !== 'all') {
      query += ' AND f.status = ?';
      params.push(status);
    }

    query += ' ORDER BY f.due_date DESC';

    const fees = await executeQuery(query, params);

    return c.json({ fees });
  } catch (error) {
    console.error('Error fetching fees:', error);
    return c.json({ error: 'Failed to fetch fees' }, 500);
  }
});

// Create fee record
coaching.post('/fees', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const { student_id, batch_id, amount, due_date } = await c.req.json();

    if (!student_id || !batch_id || !amount || !due_date) {
      return c.json({ error: 'All fields are required' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO fees (center_id, student_id, batch_id, amount, due_date, status)
       VALUES (?, ?, ?, ?, ?, 'pending')`,
      [centerId, student_id, batch_id, amount, due_date]
    );

    return c.json({
      success: true,
      feeId: result.insertId,
      message: 'Fee record created successfully'
    });
  } catch (error) {
    console.error('Error creating fee record:', error);
    return c.json({ error: 'Failed to create fee record' }, 500);
  }
});

// ==================== PAYMENT ROUTES ====================

// Get payments
coaching.get('/payments', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const studentId = c.req.query('student_id');

    let query = `
      SELECT p.*, u.name as student_name, f.amount as fee_amount
      FROM payments p
      JOIN users u ON p.student_id = u.id
      JOIN fees f ON p.fee_id = f.id
      WHERE p.center_id = ?
    `;
    const params = [centerId];

    if (studentId) {
      query += ' AND p.student_id = ?';
      params.push(studentId);
    }

    query += ' ORDER BY p.payment_date DESC';

    const payments = await executeQuery(query, params);

    return c.json({ payments });
  } catch (error) {
    console.error('Error fetching payments:', error);
    return c.json({ error: 'Failed to fetch payments' }, 500);
  }
});

// Record payment
coaching.post('/payments', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const {
      fee_id, student_id, amount, payment_method, payment_date,
      transaction_id, receipt_number, notes
    } = await c.req.json();

    if (!fee_id || !student_id || !amount || !payment_method || !payment_date) {
      return c.json({ error: 'Required fields missing' }, 400);
    }

    // Generate receipt number if not provided
    const receiptNum = receipt_number || `RCP${Date.now()}`;

    const result = await executeQuery(
      `INSERT INTO payments (
        center_id, fee_id, student_id, amount, payment_method, payment_date,
        transaction_id, receipt_number, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [centerId, fee_id, student_id, amount, payment_method, payment_date,
       transaction_id, receiptNum, notes]
    );

    // Update fee status to paid
    await executeQuery(
      'UPDATE fees SET status = "paid" WHERE id = ?',
      [fee_id]
    );

    return c.json({
      success: true,
      paymentId: result.insertId,
      receiptNumber: receiptNum,
      message: 'Payment recorded successfully'
    });
  } catch (error) {
    console.error('Error recording payment:', error);
    return c.json({ error: 'Failed to record payment' }, 500);
  }
});

// ==================== ATTENDANCE ROUTES ====================

// Get attendance for a batch and date
coaching.get('/attendance', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const batchId = c.req.query('batch_id');
    const date = c.req.query('date');

    if (!batchId || !date) {
      return c.json({ error: 'Batch ID and date are required' }, 400);
    }

    const attendance = await executeQuery(
      `SELECT a.*, u.name as student_name
       FROM attendance a
       JOIN users u ON a.student_id = u.id
       WHERE a.center_id = ? AND a.batch_id = ? AND a.date = ?
       ORDER BY u.name`,
      [centerId, batchId, date]
    );

    return c.json({ attendance });
  } catch (error) {
    console.error('Error fetching attendance:', error);
    return c.json({ error: 'Failed to fetch attendance' }, 500);
  }
});

// Mark attendance
coaching.post('/attendance', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'teacher', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const { batch_id, date, attendance_records } = await c.req.json();

    if (!batch_id || !date || !attendance_records || !Array.isArray(attendance_records)) {
      return c.json({ error: 'Invalid attendance data' }, 400);
    }

    // Delete existing attendance for this batch and date
    await executeQuery(
      'DELETE FROM attendance WHERE center_id = ? AND batch_id = ? AND date = ?',
      [centerId, batch_id, date]
    );

    // Insert new attendance records
    for (const record of attendance_records) {
      await executeQuery(
        `INSERT INTO attendance (center_id, student_id, batch_id, date, status, notes, marked_by)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [centerId, record.student_id, batch_id, date, record.status, record.notes || null, user.id]
      );
    }

    return c.json({
      success: true,
      message: 'Attendance marked successfully'
    });
  } catch (error) {
    console.error('Error marking attendance:', error);
    return c.json({ error: 'Failed to mark attendance' }, 500);
  }
});

// ==================== ASSIGNMENT ROUTES ====================

// Get assignments for a batch
coaching.get('/assignments', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const batchId = c.req.query('batch_id');

    let query = `
      SELECT a.*, u.name as teacher_name, b.name as batch_name
      FROM assignments a
      JOIN users u ON a.teacher_id = u.id
      JOIN batches b ON a.batch_id = b.id
      WHERE a.center_id = ?
    `;
    const params = [centerId];

    if (batchId) {
      query += ' AND a.batch_id = ?';
      params.push(batchId);
    }

    query += ' ORDER BY a.due_date DESC';

    const assignments = await executeQuery(query, params);

    return c.json({ assignments });
  } catch (error) {
    console.error('Error fetching assignments:', error);
    return c.json({ error: 'Failed to fetch assignments' }, 500);
  }
});

// Create assignment
coaching.post('/assignments', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'teacher', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const { batch_id, title, description, due_date, max_marks } = await c.req.json();

    if (!batch_id || !title) {
      return c.json({ error: 'Batch and title are required' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO assignments (center_id, batch_id, teacher_id, title, description, due_date, max_marks, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, 'active')`,
      [centerId, batch_id, user.id, title, description, due_date, max_marks || 100]
    );

    return c.json({
      success: true,
      assignmentId: result.insertId,
      message: 'Assignment created successfully'
    });
  } catch (error) {
    console.error('Error creating assignment:', error);
    return c.json({ error: 'Failed to create assignment' }, 500);
  }
});

// ==================== NOTIFICATION ROUTES ====================

// Get notifications for user
coaching.get('/notifications', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const userId = user.id;

    const notifications = await executeQuery(
      `SELECT * FROM notifications
       WHERE center_id = ? AND (user_id = ? OR user_id IS NULL)
       ORDER BY created_at DESC
       LIMIT 50`,
      [centerId, userId]
    );

    return c.json({ notifications });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return c.json({ error: 'Failed to fetch notifications' }, 500);
  }
});

// Mark notification as read
coaching.put('/notifications/:id/read', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const notificationId = c.req.param('id');

    await executeQuery(
      'UPDATE notifications SET is_read = TRUE WHERE id = ? AND user_id = ?',
      [notificationId, user.id]
    );

    return c.json({ success: true, message: 'Notification marked as read' });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return c.json({ error: 'Failed to mark notification as read' }, 500);
  }
});

// ==================== EXPENSE MANAGEMENT ROUTES ====================

// Get expense types
coaching.get('/expense-types', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const expenseTypes = await executeQuery(
      'SELECT * FROM expense_types WHERE center_id = ? ORDER BY category, name',
      [centerId]
    );

    return c.json({ expenseTypes });
  } catch (error) {
    console.error('Error fetching expense types:', error);
    return c.json({ error: 'Failed to fetch expense types' }, 500);
  }
});

// Add expense type
coaching.post('/expense-types', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const { name, description, category } = await c.req.json();

    if (!name) {
      return c.json({ error: 'Expense type name is required' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO expense_types (center_id, name, description, category, is_active)
       VALUES (?, ?, ?, ?, TRUE)`,
      [centerId, name, description, category || 'other']
    );

    return c.json({
      success: true,
      expenseTypeId: result.insertId,
      message: 'Expense type added successfully'
    });
  } catch (error) {
    console.error('Error adding expense type:', error);
    return c.json({ error: 'Failed to add expense type' }, 500);
  }
});

// Get expenses
coaching.get('/expenses', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const status = c.req.query('status') || 'all';
    const startDate = c.req.query('start_date');
    const endDate = c.req.query('end_date');

    let query = `
      SELECT e.*, et.name as expense_type_name, et.category,
             pg.name as payment_gateway_name,
             u.name as created_by_name
      FROM expenses e
      JOIN expense_types et ON e.expense_type_id = et.id
      LEFT JOIN payment_gateways pg ON e.payment_gateway_id = pg.id
      LEFT JOIN users u ON e.created_by = u.id
      WHERE e.center_id = ?
    `;
    const params = [centerId];

    if (status !== 'all') {
      query += ' AND e.status = ?';
      params.push(status);
    }

    if (startDate) {
      query += ' AND e.expense_date >= ?';
      params.push(startDate);
    }

    if (endDate) {
      query += ' AND e.expense_date <= ?';
      params.push(endDate);
    }

    query += ' ORDER BY e.expense_date DESC';

    const expenses = await executeQuery(query, params);

    return c.json({ expenses });
  } catch (error) {
    console.error('Error fetching expenses:', error);
    return c.json({ error: 'Failed to fetch expenses' }, 500);
  }
});

// Add expense
coaching.post('/expenses', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const {
      expense_type_id, title, description, amount, expense_date,
      payment_method, payment_gateway_id, vendor_name, vendor_contact,
      receipt_number, notes
    } = await c.req.json();

    if (!expense_type_id || !title || !amount || !expense_date || !payment_method) {
      return c.json({ error: 'Required fields missing' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO expenses (
        center_id, expense_type_id, title, description, amount, expense_date,
        payment_method, payment_gateway_id, vendor_name, vendor_contact,
        receipt_number, notes, status, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?)`,
      [centerId, expense_type_id, title, description, amount, expense_date,
       payment_method, payment_gateway_id, vendor_name, vendor_contact,
       receipt_number, notes, user.id]
    );

    // Create transaction record
    const transactionId = `EXP${Date.now()}`;
    await executeQuery(
      `INSERT INTO transactions (
        center_id, transaction_id, type, expense_id, amount, payment_method,
        payment_gateway_id, status, transaction_date, description, receipt_number, processed_by
      ) VALUES (?, ?, 'expense', ?, ?, ?, ?, 'pending', ?, ?, ?, ?)`,
      [centerId, transactionId, result.insertId, amount, payment_method,
       payment_gateway_id, expense_date, title, receipt_number, user.id]
    );

    return c.json({
      success: true,
      expenseId: result.insertId,
      transactionId,
      message: 'Expense added successfully'
    });
  } catch (error) {
    console.error('Error adding expense:', error);
    return c.json({ error: 'Failed to add expense' }, 500);
  }
});

// Update expense payment status
coaching.put('/expenses/:id/status', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const expenseId = c.req.param('id');
    const { status, payment_date, notes } = await c.req.json();

    // Validate status
    if (!['pending', 'paid', 'cancelled'].includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    // Check if expense exists and belongs to the center
    const expense = await executeQuery(
      'SELECT id, amount FROM expenses WHERE id = ? AND center_id = ?',
      [expenseId, centerId]
    );

    if (expense.length === 0) {
      return c.json({ error: 'Expense not found' }, 404);
    }

    // Update expense status
    await executeQuery(
      `UPDATE expenses SET
        status = ?,
        payment_date = ?,
        notes = ?,
        updated_at = NOW()
      WHERE id = ? AND center_id = ?`,
      [status, payment_date || null, notes || null, expenseId, centerId]
    );

    // Update related transaction status if exists
    await executeQuery(
      `UPDATE transactions SET
        status = ?,
        updated_at = NOW()
      WHERE expense_id = ? AND center_id = ?`,
      [status, expenseId, centerId]
    );

    return c.json({
      success: true,
      message: 'Expense status updated successfully'
    });

  } catch (error) {
    console.error('Error updating expense status:', error);
    return c.json({ error: 'Failed to update expense status' }, 500);
  }
});

// ==================== PAYMENT GATEWAY ROUTES ====================

// Get payment gateways
coaching.get('/payment-gateways', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const gateways = await executeQuery(
      'SELECT id, name, type, is_active, is_default FROM payment_gateways WHERE center_id = ? ORDER BY is_default DESC, name',
      [centerId]
    );

    return c.json({ gateways });
  } catch (error) {
    console.error('Error fetching payment gateways:', error);
    return c.json({ error: 'Failed to fetch payment gateways' }, 500);
  }
});

// Add/Update payment gateway
coaching.post('/payment-gateways', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const {
      name, type, api_key, api_secret, webhook_url, is_active, is_default, configuration
    } = await c.req.json();

    if (!name || !type) {
      return c.json({ error: 'Name and type are required' }, 400);
    }

    // If setting as default, unset other defaults
    if (is_default) {
      await executeQuery(
        'UPDATE payment_gateways SET is_default = FALSE WHERE center_id = ?',
        [centerId]
      );
    }

    const result = await executeQuery(
      `INSERT INTO payment_gateways (
        center_id, name, type, api_key, api_secret, webhook_url,
        is_active, is_default, configuration
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [centerId, name, type, api_key, api_secret, webhook_url,
       is_active, is_default, JSON.stringify(configuration || {})]
    );

    return c.json({
      success: true,
      gatewayId: result.insertId,
      message: 'Payment gateway added successfully'
    });
  } catch (error) {
    console.error('Error adding payment gateway:', error);
    return c.json({ error: 'Failed to add payment gateway' }, 500);
  }
});

// Update payment gateway status
coaching.put('/payment-gateways/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const gatewayId = c.req.param('id');
    const centerId = user.center_id;
    const { is_active, is_default } = await c.req.json();

    // If setting as default, unset other defaults
    if (is_default) {
      await executeQuery(
        'UPDATE payment_gateways SET is_default = FALSE WHERE center_id = ?',
        [centerId]
      );
    }

    await executeQuery(
      'UPDATE payment_gateways SET is_active = ?, is_default = ? WHERE id = ? AND center_id = ?',
      [is_active, is_default, gatewayId, centerId]
    );

    return c.json({ success: true, message: 'Payment gateway updated successfully' });
  } catch (error) {
    console.error('Error updating payment gateway:', error);
    return c.json({ error: 'Failed to update payment gateway' }, 500);
  }
});

// ==================== TRANSACTION ROUTES ====================

// Get all transactions
coaching.get('/transactions', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const type = c.req.query('type') || 'all';
    const status = c.req.query('status') || 'all';
    const startDate = c.req.query('start_date');
    const endDate = c.req.query('end_date');

    let query = `
      SELECT t.*,
             pg.name as payment_gateway_name,
             u1.name as student_name,
             u2.name as processed_by_name,
             e.title as expense_title,
             f.amount as fee_amount
      FROM transactions t
      LEFT JOIN payment_gateways pg ON t.payment_gateway_id = pg.id
      LEFT JOIN users u1 ON t.student_id = u1.id
      LEFT JOIN users u2 ON t.processed_by = u2.id
      LEFT JOIN expenses e ON t.expense_id = e.id
      LEFT JOIN fees f ON t.fee_id = f.id
      WHERE t.center_id = ?
    `;
    const params = [centerId];

    if (type !== 'all') {
      query += ' AND t.type = ?';
      params.push(type);
    }

    if (status !== 'all') {
      query += ' AND t.status = ?';
      params.push(status);
    }

    if (startDate) {
      query += ' AND DATE(t.transaction_date) >= ?';
      params.push(startDate);
    }

    if (endDate) {
      query += ' AND DATE(t.transaction_date) <= ?';
      params.push(endDate);
    }

    query += ' ORDER BY t.transaction_date DESC LIMIT 100';

    const transactions = await executeQuery(query, params);

    return c.json({ transactions });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    return c.json({ error: 'Failed to fetch transactions' }, 500);
  }
});

// Create fee collection transaction
coaching.post('/transactions/fee-collection', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const {
      student_id, fee_id, amount, payment_method, payment_gateway_id,
      gateway_transaction_id, gateway_payment_id, description, receipt_number
    } = await c.req.json();

    if (!student_id || !amount || !payment_method) {
      return c.json({ error: 'Required fields missing' }, 400);
    }

    const transactionId = `FEE${Date.now()}`;
    const receiptNum = receipt_number || `RCP${Date.now()}`;

    const result = await executeQuery(
      `INSERT INTO transactions (
        center_id, transaction_id, type, student_id, fee_id, amount,
        payment_method, payment_gateway_id, gateway_transaction_id,
        gateway_payment_id, status, transaction_date, description,
        receipt_number, processed_by
      ) VALUES (?, ?, 'fee_collection', ?, ?, ?, ?, ?, ?, ?, 'completed', NOW(), ?, ?, ?)`,
      [centerId, transactionId, student_id, fee_id, amount, payment_method,
       payment_gateway_id, gateway_transaction_id, gateway_payment_id,
       description, receiptNum, user.id]
    );

    // Update fee status if fee_id is provided
    if (fee_id) {
      await executeQuery(
        'UPDATE fees SET status = "paid" WHERE id = ? AND center_id = ?',
        [fee_id, centerId]
      );
    }

    return c.json({
      success: true,
      transactionId: result.insertId,
      receiptNumber: receiptNum,
      message: 'Fee collection recorded successfully'
    });
  } catch (error) {
    console.error('Error recording fee collection:', error);
    return c.json({ error: 'Failed to record fee collection' }, 500);
  }
});

// Get transaction statistics
coaching.get('/transactions/stats', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    // Get monthly stats
    const [monthlyIncome] = await executeQuery(
      `SELECT COALESCE(SUM(amount), 0) as total
       FROM transactions
       WHERE center_id = ? AND type = 'fee_collection' AND status = 'completed'
       AND MONTH(transaction_date) = MONTH(CURRENT_DATE())
       AND YEAR(transaction_date) = YEAR(CURRENT_DATE())`,
      [centerId]
    );

    const [monthlyExpenses] = await executeQuery(
      `SELECT COALESCE(SUM(amount), 0) as total
       FROM expenses
       WHERE center_id = ? AND status = 'paid'
       AND MONTH(expense_date) = MONTH(CURRENT_DATE())
       AND YEAR(expense_date) = YEAR(CURRENT_DATE())`,
      [centerId]
    );

    const [totalPending] = await executeQuery(
      `SELECT COALESCE(SUM(amount), 0) as total
       FROM transactions
       WHERE center_id = ? AND status = 'pending'`,
      [centerId]
    );

    // Get payment method breakdown
    const paymentMethodStats = await executeQuery(
      `SELECT payment_method, COUNT(*) as count, SUM(amount) as total_amount
       FROM transactions
       WHERE center_id = ? AND status = 'completed'
       AND MONTH(transaction_date) = MONTH(CURRENT_DATE())
       AND YEAR(transaction_date) = YEAR(CURRENT_DATE())
       GROUP BY payment_method`,
      [centerId]
    );

    const stats = {
      monthlyIncome: monthlyIncome.total,
      monthlyExpenses: monthlyExpenses.total,
      netIncome: monthlyIncome.total - monthlyExpenses.total,
      totalPending: totalPending.total,
      paymentMethodStats
    };

    return c.json({ stats });
  } catch (error) {
    console.error('Error fetching transaction stats:', error);
    return c.json({ error: 'Failed to fetch transaction stats' }, 500);
  }
});

// ==================== HRM ROUTES ====================

// Get all employees
coaching.get('/employees', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const employees = await executeQuery(
      `SELECT e.*,
              COUNT(DISTINCT lr.id) as total_leave_requests,
              COUNT(DISTINCT CASE WHEN lr.status = 'pending' THEN lr.id END) as pending_leaves
       FROM employees e
       LEFT JOIN leave_requests lr ON e.id = lr.employee_id
       WHERE e.center_id = ?
       GROUP BY e.id
       ORDER BY e.created_at DESC`,
      [centerId]
    );

    return c.json({ employees });
  } catch (error) {
    console.error('Error fetching employees:', error);
    return c.json({ error: 'Failed to fetch employees' }, 500);
  }
});

// Add new employee
coaching.post('/employees', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const {
      employee_id, name, email, phone, position, department,
      hire_date, salary, address, emergency_contact
    } = await c.req.json();

    if (!employee_id || !name || !email) {
      return c.json({ error: 'Employee ID, name and email are required' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO employees (
        center_id, employee_id, name, email, phone, position,
        department, hire_date, salary, address, emergency_contact
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [centerId, employee_id, name, email, phone, position,
       department, hire_date, salary, address, emergency_contact]
    );

    return c.json({
      success: true,
      employeeId: result.insertId,
      message: 'Employee added successfully'
    });

  } catch (error) {
    console.error('Error adding employee:', error);
    return c.json({ error: 'Failed to add employee' }, 500);
  }
});

// Get leave requests
coaching.get('/leave-requests', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const status = c.req.query('status') || 'all';
    const employeeId = c.req.query('employee_id');

    let query = `
      SELECT lr.*, e.name as employee_name, e.employee_id, e.position,
             u.name as approved_by_name
      FROM leave_requests lr
      JOIN employees e ON lr.employee_id = e.id
      LEFT JOIN users u ON lr.approved_by = u.id
      WHERE lr.center_id = ?
    `;
    const params = [centerId];

    if (status !== 'all') {
      query += ' AND lr.status = ?';
      params.push(status);
    }

    if (employeeId) {
      query += ' AND lr.employee_id = ?';
      params.push(employeeId);
    }

    query += ' ORDER BY lr.created_at DESC';

    const leaveRequests = await executeQuery(query, params);

    return c.json({ leaveRequests });
  } catch (error) {
    console.error('Error fetching leave requests:', error);
    return c.json({ error: 'Failed to fetch leave requests' }, 500);
  }
});

// Create leave request
coaching.post('/leave-requests', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const {
      employee_id, leave_type, start_date, end_date,
      days_requested, reason
    } = await c.req.json();

    if (!employee_id || !leave_type || !start_date || !end_date || !days_requested) {
      return c.json({ error: 'All required fields must be provided' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO leave_requests (
        center_id, employee_id, leave_type, start_date, end_date,
        days_requested, reason
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [centerId, employee_id, leave_type, start_date, end_date, days_requested, reason]
    );

    return c.json({
      success: true,
      leaveRequestId: result.insertId,
      message: 'Leave request submitted successfully'
    });

  } catch (error) {
    console.error('Error creating leave request:', error);
    return c.json({ error: 'Failed to create leave request' }, 500);
  }
});

// Approve/Reject leave request
coaching.put('/leave-requests/:id/status', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const leaveRequestId = c.req.param('id');
    const { status, notes } = await c.req.json();

    if (!['approved', 'rejected'].includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    await executeQuery(
      `UPDATE leave_requests SET
        status = ?,
        approved_by = ?,
        approved_at = NOW(),
        notes = ?
      WHERE id = ? AND center_id = ?`,
      [status, user.id, notes, leaveRequestId, centerId]
    );

    return c.json({
      success: true,
      message: `Leave request ${status} successfully`
    });

  } catch (error) {
    console.error('Error updating leave request:', error);
    return c.json({ error: 'Failed to update leave request' }, 500);
  }
});

// Get salary records
coaching.get('/salary-records', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const month = c.req.query('month');
    const year = c.req.query('year');
    const employeeId = c.req.query('employee_id');

    let query = `
      SELECT sr.*, e.name as employee_name, e.employee_id, e.position
      FROM salary_records sr
      JOIN employees e ON sr.employee_id = e.id
      WHERE sr.center_id = ?
    `;
    const params = [centerId];

    if (month) {
      query += ' AND sr.month = ?';
      params.push(month);
    }

    if (year) {
      query += ' AND sr.year = ?';
      params.push(year);
    }

    if (employeeId) {
      query += ' AND sr.employee_id = ?';
      params.push(employeeId);
    }

    query += ' ORDER BY sr.year DESC, sr.month DESC, e.name';

    const salaryRecords = await executeQuery(query, params);

    return c.json({ salaryRecords });
  } catch (error) {
    console.error('Error fetching salary records:', error);
    return c.json({ error: 'Failed to fetch salary records' }, 500);
  }
});

// Create/Update salary record
coaching.post('/salary-records', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const {
      employee_id, month, year, basic_salary, allowances, deductions,
      overtime_hours, overtime_rate, notes
    } = await c.req.json();

    if (!employee_id || !month || !year || !basic_salary) {
      return c.json({ error: 'Employee, month, year and basic salary are required' }, 400);
    }

    const overtimePay = (overtime_hours || 0) * (overtime_rate || 0);
    const grossSalary = parseFloat(basic_salary) + parseFloat(allowances || 0) + overtimePay;
    const netSalary = grossSalary - parseFloat(deductions || 0);

    // Check if record already exists
    const existing = await executeQuery(
      'SELECT id FROM salary_records WHERE employee_id = ? AND month = ? AND year = ?',
      [employee_id, month, year]
    );

    let result;
    if (existing.length > 0) {
      // Update existing record
      result = await executeQuery(
        `UPDATE salary_records SET
          basic_salary = ?, allowances = ?, deductions = ?,
          overtime_hours = ?, overtime_rate = ?, gross_salary = ?,
          net_salary = ?, notes = ?, updated_at = NOW()
        WHERE id = ?`,
        [basic_salary, allowances || 0, deductions || 0, overtime_hours || 0,
         overtime_rate || 0, grossSalary, netSalary, notes, existing[0].id]
      );
    } else {
      // Create new record
      result = await executeQuery(
        `INSERT INTO salary_records (
          center_id, employee_id, month, year, basic_salary, allowances,
          deductions, overtime_hours, overtime_rate, gross_salary, net_salary, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [centerId, employee_id, month, year, basic_salary, allowances || 0,
         deductions || 0, overtime_hours || 0, overtime_rate || 0, grossSalary, netSalary, notes]
      );
    }

    return c.json({
      success: true,
      salaryRecordId: existing.length > 0 ? existing[0].id : result.insertId,
      message: existing.length > 0 ? 'Salary record updated successfully' : 'Salary record created successfully'
    });

  } catch (error) {
    console.error('Error managing salary record:', error);
    return c.json({ error: 'Failed to manage salary record' }, 500);
  }
});

// Update salary payment status
coaching.put('/salary-records/:id/payment', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const salaryRecordId = c.req.param('id');
    const { status, payment_date } = await c.req.json();

    if (!['draft', 'processed', 'paid'].includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    await executeQuery(
      `UPDATE salary_records SET
        status = ?,
        payment_date = ?,
        updated_at = NOW()
      WHERE id = ? AND center_id = ?`,
      [status, payment_date || null, salaryRecordId, centerId]
    );

    return c.json({
      success: true,
      message: 'Salary payment status updated successfully'
    });

  } catch (error) {
    console.error('Error updating salary payment:', error);
    return c.json({ error: 'Failed to update salary payment' }, 500);
  }
});

// ==================== INVENTORY ROUTES ====================

// Get inventory items
coaching.get('/inventory/items', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const categoryId = c.req.query('category_id');
    const lowStock = c.req.query('low_stock') === 'true';

    let query = `
      SELECT i.*, ic.name as category_name
      FROM inventory_items i
      JOIN inventory_categories ic ON i.category_id = ic.id
      WHERE i.center_id = ?
    `;
    const params = [centerId];

    if (categoryId) {
      query += ' AND i.category_id = ?';
      params.push(categoryId);
    }

    if (lowStock) {
      query += ' AND i.current_stock <= i.minimum_stock';
    }

    query += ' ORDER BY i.name';

    const items = await executeQuery(query, params);

    return c.json({ items });
  } catch (error) {
    console.error('Error fetching inventory items:', error);
    return c.json({ error: 'Failed to fetch inventory items' }, 500);
  }
});

// Get inventory categories
coaching.get('/inventory/categories', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;

    const categories = await executeQuery(
      `SELECT ic.*, COUNT(ii.id) as item_count
       FROM inventory_categories ic
       LEFT JOIN inventory_items ii ON ic.id = ii.category_id AND ii.status = 'active'
       WHERE ic.center_id = ?
       GROUP BY ic.id
       ORDER BY ic.name`,
      [centerId]
    );

    return c.json({ categories });
  } catch (error) {
    console.error('Error fetching inventory categories:', error);
    return c.json({ error: 'Failed to fetch inventory categories' }, 500);
  }
});

// Add inventory item
coaching.post('/inventory/items', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const {
      category_id, item_code, name, description, unit,
      current_stock, minimum_stock, unit_price
    } = await c.req.json();

    if (!category_id || !item_code || !name) {
      return c.json({ error: 'Category, item code and name are required' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO inventory_items (
        center_id, category_id, item_code, name, description, unit,
        current_stock, minimum_stock, unit_price
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [centerId, category_id, item_code, name, description, unit,
       current_stock || 0, minimum_stock || 0, unit_price || 0]
    );

    return c.json({
      success: true,
      itemId: result.insertId,
      message: 'Inventory item added successfully'
    });

  } catch (error) {
    console.error('Error adding inventory item:', error);
    return c.json({ error: 'Failed to add inventory item' }, 500);
  }
});

// Update inventory stock
coaching.put('/inventory/items/:id/stock', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const itemId = c.req.param('id');
    const {
      transaction_type, quantity, unit_price, reference_type,
      reference_id, notes, transaction_date
    } = await c.req.json();

    if (!transaction_type || !quantity || !reference_type) {
      return c.json({ error: 'Transaction type, quantity and reference type are required' }, 400);
    }

    // Get current stock
    const item = await executeQuery(
      'SELECT current_stock FROM inventory_items WHERE id = ? AND center_id = ?',
      [itemId, centerId]
    );

    if (item.length === 0) {
      return c.json({ error: 'Item not found' }, 404);
    }

    let newStock = item[0].current_stock;
    if (transaction_type === 'in') {
      newStock += parseInt(quantity);
    } else if (transaction_type === 'out') {
      newStock -= parseInt(quantity);
    } else if (transaction_type === 'adjustment') {
      newStock = parseInt(quantity);
    }

    if (newStock < 0) {
      return c.json({ error: 'Insufficient stock' }, 400);
    }

    // Update stock
    await executeQuery(
      'UPDATE inventory_items SET current_stock = ? WHERE id = ?',
      [newStock, itemId]
    );

    // Record transaction
    await executeQuery(
      `INSERT INTO inventory_transactions (
        center_id, item_id, transaction_type, quantity, unit_price,
        total_amount, reference_type, reference_id, notes,
        transaction_date, processed_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [centerId, itemId, transaction_type, quantity, unit_price || 0,
       (quantity * (unit_price || 0)), reference_type, reference_id,
       notes, transaction_date || new Date().toISOString().split('T')[0], user.id]
    );

    return c.json({
      success: true,
      newStock,
      message: 'Stock updated successfully'
    });

  } catch (error) {
    console.error('Error updating stock:', error);
    return c.json({ error: 'Failed to update stock' }, 500);
  }
});

// ==================== VENDOR MANAGEMENT ROUTES ====================

// Get all vendors
coaching.get('/vendors', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const vendors = await executeQuery(
      `SELECT v.*,
              COUNT(DISTINCT po.id) as total_orders,
              COALESCE(SUM(po.total_amount), 0) as total_purchases,
              MAX(po.order_date) as last_purchase_date
       FROM vendors v
       LEFT JOIN purchase_orders po ON v.id = po.vendor_id
       WHERE v.center_id = ?
       GROUP BY v.id
       ORDER BY v.created_at DESC`,
      [centerId]
    );

    return c.json({ vendors });
  } catch (error) {
    console.error('Error fetching vendors:', error);
    return c.json({ error: 'Failed to fetch vendors' }, 500);
  }
});

// Add new vendor
coaching.post('/vendors', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const {
      vendor_code, name, contact_person, email, phone,
      address, payment_terms, credit_limit, notes
    } = await c.req.json();

    if (!vendor_code || !name) {
      return c.json({ error: 'Vendor code and name are required' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO vendors (
        center_id, vendor_code, name, contact_person, email, phone,
        address, payment_terms, credit_limit, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [centerId, vendor_code, name, contact_person, email, phone,
       address, payment_terms, credit_limit || 0, notes]
    );

    return c.json({
      success: true,
      vendorId: result.insertId,
      message: 'Vendor added successfully'
    });

  } catch (error) {
    console.error('Error adding vendor:', error);
    return c.json({ error: 'Failed to add vendor' }, 500);
  }
});

// Update vendor
coaching.put('/vendors/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const vendorId = c.req.param('id');
    const {
      vendor_code, name, contact_person, email, phone,
      address, payment_terms, credit_limit, status, notes
    } = await c.req.json();

    if (!vendor_code || !name) {
      return c.json({ error: 'Vendor code and name are required' }, 400);
    }

    await executeQuery(
      `UPDATE vendors SET
        vendor_code = ?, name = ?, contact_person = ?, email = ?, phone = ?,
        address = ?, payment_terms = ?, credit_limit = ?, status = ?, notes = ?,
        updated_at = NOW()
      WHERE id = ? AND center_id = ?`,
      [vendor_code, name, contact_person, email, phone, address,
       payment_terms, credit_limit || 0, status || 'active', notes, vendorId, centerId]
    );

    return c.json({
      success: true,
      message: 'Vendor updated successfully'
    });

  } catch (error) {
    console.error('Error updating vendor:', error);
    return c.json({ error: 'Failed to update vendor' }, 500);
  }
});

// ==================== PURCHASE ORDER ROUTES ====================

// Get all purchase orders
coaching.get('/purchase-orders', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const status = c.req.query('status') || 'all';

    let query = `
      SELECT po.*, v.name as vendor_name, v.vendor_code,
             u.name as created_by_name, a.name as approved_by_name
      FROM purchase_orders po
      JOIN vendors v ON po.vendor_id = v.id
      JOIN users u ON po.created_by = u.id
      LEFT JOIN users a ON po.approved_by = a.id
      WHERE po.center_id = ?
    `;
    const params = [centerId];

    if (status !== 'all') {
      query += ' AND po.status = ?';
      params.push(status);
    }

    query += ' ORDER BY po.created_at DESC';

    const purchaseOrders = await executeQuery(query, params);

    return c.json({ purchaseOrders });
  } catch (error) {
    console.error('Error fetching purchase orders:', error);
    return c.json({ error: 'Failed to fetch purchase orders' }, 500);
  }
});

// Create new purchase order
coaching.post('/purchase-orders', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const {
      po_number, vendor_id, order_date, expected_delivery_date,
      items, notes, tax_amount, discount_amount
    } = await c.req.json();

    if (!po_number || !vendor_id || !items || items.length === 0) {
      return c.json({ error: 'PO number, vendor, and items are required' }, 400);
    }

    // Calculate totals
    const subtotal = items.reduce((sum: number, item: any) =>
      sum + (item.quantity * item.unit_price), 0);
    const total_amount = subtotal + (tax_amount || 0) - (discount_amount || 0);

    // Create purchase order
    const poResult = await executeQuery(
      `INSERT INTO purchase_orders (
        center_id, po_number, vendor_id, order_date, expected_delivery_date,
        subtotal, tax_amount, discount_amount, total_amount, notes, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [centerId, po_number, vendor_id, order_date, expected_delivery_date,
       subtotal, tax_amount || 0, discount_amount || 0, total_amount, notes, user.id]
    );

    // Add purchase order items
    for (const item of items) {
      await executeQuery(
        `INSERT INTO purchase_order_items (
          purchase_order_id, item_id, quantity_ordered, unit_price, total_price
        ) VALUES (?, ?, ?, ?, ?)`,
        [poResult.insertId, item.item_id, item.quantity, item.unit_price,
         item.quantity * item.unit_price]
      );
    }

    return c.json({
      success: true,
      purchaseOrderId: poResult.insertId,
      message: 'Purchase order created successfully'
    });

  } catch (error) {
    console.error('Error creating purchase order:', error);
    return c.json({ error: 'Failed to create purchase order' }, 500);
  }
});

// Update purchase order status
coaching.put('/purchase-orders/:id/status', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const poId = c.req.param('id');
    const { status, notes } = await c.req.json();

    const validStatuses = ['draft', 'pending', 'approved', 'ordered', 'partially_received', 'received', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    // If approving, set approval details
    let updateQuery = `UPDATE purchase_orders SET status = ?, notes = ?, updated_at = NOW()`;
    let params = [status, notes];

    if (status === 'approved') {
      updateQuery += `, approved_by = ?, approved_at = NOW()`;
      params.push(user.id);
    }

    updateQuery += ` WHERE id = ? AND center_id = ?`;
    params.push(poId, centerId);

    await executeQuery(updateQuery, params);

    // If status is 'received', update inventory stock
    if (status === 'received') {
      const poItems = await executeQuery(
        `SELECT poi.*, ii.current_stock
         FROM purchase_order_items poi
         JOIN inventory_items ii ON poi.item_id = ii.id
         WHERE poi.purchase_order_id = ?`,
        [poId]
      );

      for (const item of poItems) {
        // Update stock
        await executeQuery(
          `UPDATE inventory_items SET
            current_stock = current_stock + ?,
            cost_price = ?,
            updated_at = NOW()
          WHERE id = ?`,
          [item.quantity_ordered, item.unit_price, item.item_id]
        );

        // Record inventory transaction
        await executeQuery(
          `INSERT INTO inventory_transactions (
            center_id, item_id, transaction_type, quantity, unit_price,
            total_amount, reference_type, reference_id, notes,
            transaction_date, processed_by
          ) VALUES (?, ?, 'in', ?, ?, ?, 'purchase', ?, 'Purchase order received', CURDATE(), ?)`,
          [centerId, item.item_id, item.quantity_ordered, item.unit_price,
           item.total_price, poId, user.id]
        );
      }
    }

    return c.json({
      success: true,
      message: `Purchase order ${status} successfully`
    });

  } catch (error) {
    console.error('Error updating purchase order:', error);
    return c.json({ error: 'Failed to update purchase order' }, 500);
  }
});

// ==================== WASTAGE MANAGEMENT ROUTES ====================

// Get wastage records
coaching.get('/wastage', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const status = c.req.query('status') || 'all';

    let query = `
      SELECT wr.*, ii.name as item_name, ii.item_code,
             u1.name as reported_by_name, u2.name as approved_by_name
      FROM wastage_records wr
      JOIN inventory_items ii ON wr.item_id = ii.id
      JOIN users u1 ON wr.reported_by = u1.id
      LEFT JOIN users u2 ON wr.approved_by = u2.id
      WHERE wr.center_id = ?
    `;
    const params = [centerId];

    if (status !== 'all') {
      query += ' AND wr.status = ?';
      params.push(status);
    }

    query += ' ORDER BY wr.created_at DESC';

    const wastageRecords = await executeQuery(query, params);

    return c.json({ wastageRecords });
  } catch (error) {
    console.error('Error fetching wastage records:', error);
    return c.json({ error: 'Failed to fetch wastage records' }, 500);
  }
});

// Record new wastage
coaching.post('/wastage', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const {
      item_id, quantity, unit_cost, reason, description, wastage_date
    } = await c.req.json();

    if (!item_id || !quantity || !unit_cost || !reason) {
      return c.json({ error: 'Item, quantity, unit cost, and reason are required' }, 400);
    }

    const total_cost = quantity * unit_cost;

    const result = await executeQuery(
      `INSERT INTO wastage_records (
        center_id, item_id, quantity, unit_cost, total_cost,
        reason, description, wastage_date, reported_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [centerId, item_id, quantity, unit_cost, total_cost,
       reason, description, wastage_date, user.id]
    );

    return c.json({
      success: true,
      wastageId: result.insertId,
      message: 'Wastage recorded successfully'
    });

  } catch (error) {
    console.error('Error recording wastage:', error);
    return c.json({ error: 'Failed to record wastage' }, 500);
  }
});

// Approve/Reject wastage
coaching.put('/wastage/:id/status', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const wastageId = c.req.param('id');
    const { status, notes } = await c.req.json();

    if (!['approved', 'rejected'].includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    // Get wastage details
    const wastage = await executeQuery(
      'SELECT * FROM wastage_records WHERE id = ? AND center_id = ?',
      [wastageId, centerId]
    );

    if (wastage.length === 0) {
      return c.json({ error: 'Wastage record not found' }, 404);
    }

    // Update wastage status
    await executeQuery(
      `UPDATE wastage_records SET
        status = ?, approved_by = ?, approved_at = NOW(), notes = ?
      WHERE id = ? AND center_id = ?`,
      [status, user.id, notes, wastageId, centerId]
    );

    // If approved, reduce inventory stock
    if (status === 'approved') {
      await executeQuery(
        `UPDATE inventory_items SET
          current_stock = current_stock - ?
        WHERE id = ? AND current_stock >= ?`,
        [wastage[0].quantity, wastage[0].item_id, wastage[0].quantity]
      );

      // Record inventory transaction
      await executeQuery(
        `INSERT INTO inventory_transactions (
          center_id, item_id, transaction_type, quantity, unit_price,
          total_amount, reference_type, reference_id, notes,
          transaction_date, processed_by
        ) VALUES (?, ?, 'out', ?, ?, ?, 'wastage', ?, ?, CURDATE(), ?)`,
        [centerId, wastage[0].item_id, wastage[0].quantity, wastage[0].unit_cost,
         wastage[0].total_cost, wastageId, `Wastage: ${wastage[0].reason}`, user.id]
      );
    }

    return c.json({
      success: true,
      message: `Wastage ${status} successfully`
    });

  } catch (error) {
    console.error('Error updating wastage status:', error);
    return c.json({ error: 'Failed to update wastage status' }, 500);
  }
});

// ==================== SALES & POS ROUTES ====================

// Get saleable items for POS
coaching.get('/pos/items', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const search = c.req.query('search') || '';

    let query = `
      SELECT ii.*, ic.name as category_name
      FROM inventory_items ii
      JOIN inventory_categories ic ON ii.category_id = ic.id
      WHERE ii.center_id = ? AND ii.is_saleable = TRUE AND ii.current_stock > 0
    `;
    const params = [centerId];

    if (search) {
      query += ` AND (ii.name LIKE ? OR ii.item_code LIKE ? OR ii.barcode LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    query += ' ORDER BY ii.name';

    const items = await executeQuery(query, params);

    return c.json({ items });
  } catch (error) {
    console.error('Error fetching POS items:', error);
    return c.json({ error: 'Failed to fetch POS items' }, 500);
  }
});

// Get students for POS customer selection
coaching.get('/pos/students', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const search = c.req.query('search') || '';

    let query = `
      SELECT id, name, email, phone
      FROM users
      WHERE center_id = ? AND role = 'student' AND status = 'active'
    `;
    const params = [centerId];

    if (search) {
      query += ` AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    query += ' ORDER BY name LIMIT 20';

    const students = await executeQuery(query, params);

    return c.json({ students });
  } catch (error) {
    console.error('Error fetching students:', error);
    return c.json({ error: 'Failed to fetch students' }, 500);
  }
});

// Create sale
coaching.post('/sales', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const {
      sale_number, student_id, customer_name, customer_phone,
      items, payment_method, payment_status, amount_paid,
      tax_amount, discount_amount, notes
    } = await c.req.json();

    if (!sale_number || !items || items.length === 0) {
      return c.json({ error: 'Sale number and items are required' }, 400);
    }

    // Calculate totals
    const subtotal = items.reduce((sum: number, item: any) =>
      sum + (item.quantity * item.unit_price), 0);
    const total_amount = subtotal + (tax_amount || 0) - (discount_amount || 0);
    const amount_due = total_amount - (amount_paid || 0);

    // Create sale
    const saleResult = await executeQuery(
      `INSERT INTO sales (
        center_id, sale_number, student_id, customer_name, customer_phone,
        sale_date, subtotal, tax_amount, discount_amount, total_amount,
        payment_method, payment_status, amount_paid, amount_due,
        notes, cashier_id
      ) VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [centerId, sale_number, student_id, customer_name, customer_phone,
       subtotal, tax_amount || 0, discount_amount || 0, total_amount,
       payment_method, payment_status || 'pending', amount_paid || 0, amount_due,
       notes, user.id]
    );

    // Add sale items and update inventory
    for (const item of items) {
      // Check stock availability
      const stockCheck = await executeQuery(
        'SELECT current_stock FROM inventory_items WHERE id = ?',
        [item.item_id]
      );

      if (stockCheck.length === 0 || stockCheck[0].current_stock < item.quantity) {
        return c.json({ error: `Insufficient stock for item ${item.item_id}` }, 400);
      }

      // Add sale item
      await executeQuery(
        `INSERT INTO sale_items (
          sale_id, item_id, quantity, unit_price, total_price
        ) VALUES (?, ?, ?, ?, ?)`,
        [saleResult.insertId, item.item_id, item.quantity, item.unit_price,
         item.quantity * item.unit_price]
      );

      // Update inventory stock
      await executeQuery(
        `UPDATE inventory_items SET
          current_stock = current_stock - ?
        WHERE id = ?`,
        [item.quantity, item.item_id]
      );

      // Record inventory transaction
      await executeQuery(
        `INSERT INTO inventory_transactions (
          center_id, item_id, transaction_type, quantity, unit_price,
          total_amount, reference_type, reference_id, notes,
          transaction_date, processed_by
        ) VALUES (?, ?, 'out', ?, ?, ?, 'sale', ?, 'Sale transaction', CURDATE(), ?)`,
        [centerId, item.item_id, item.quantity, item.unit_price,
         item.quantity * item.unit_price, saleResult.insertId, user.id]
      );
    }

    // Update student balance if credit sale
    if (student_id && payment_status === 'pending') {
      await executeQuery(
        `INSERT INTO student_account_balances (center_id, student_id, balance)
         VALUES (?, ?, ?)
         ON DUPLICATE KEY UPDATE balance = balance + ?`,
        [centerId, student_id, amount_due, amount_due]
      );
    }

    return c.json({
      success: true,
      saleId: saleResult.insertId,
      message: 'Sale created successfully'
    });

  } catch (error) {
    console.error('Error creating sale:', error);
    return c.json({ error: 'Failed to create sale' }, 500);
  }
});

// Get all sales
coaching.get('/sales', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const status = c.req.query('status') || 'all';
    const startDate = c.req.query('start_date');
    const endDate = c.req.query('end_date');

    let query = `
      SELECT s.*, u1.name as student_name, u2.name as cashier_name
      FROM sales s
      LEFT JOIN users u1 ON s.student_id = u1.id
      JOIN users u2 ON s.cashier_id = u2.id
      WHERE s.center_id = ?
    `;
    const params = [centerId];

    if (status !== 'all') {
      query += ' AND s.payment_status = ?';
      params.push(status);
    }

    if (startDate) {
      query += ' AND DATE(s.sale_date) >= ?';
      params.push(startDate);
    }

    if (endDate) {
      query += ' AND DATE(s.sale_date) <= ?';
      params.push(endDate);
    }

    query += ' ORDER BY s.sale_date DESC';

    const sales = await executeQuery(query, params);

    return c.json({ sales });
  } catch (error) {
    console.error('Error fetching sales:', error);
    return c.json({ error: 'Failed to fetch sales' }, 500);
  }
});

// Record payment for sale
coaching.post('/sales/:id/payment', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const saleId = c.req.param('id');
    const { amount, payment_method, notes } = await c.req.json();

    if (!amount || amount <= 0) {
      return c.json({ error: 'Valid payment amount is required' }, 400);
    }

    // Get sale details
    const sale = await executeQuery(
      'SELECT * FROM sales WHERE id = ? AND center_id = ?',
      [saleId, centerId]
    );

    if (sale.length === 0) {
      return c.json({ error: 'Sale not found' }, 404);
    }

    const newAmountPaid = parseFloat(sale[0].amount_paid) + parseFloat(amount);
    const newAmountDue = parseFloat(sale[0].total_amount) - newAmountPaid;
    const newPaymentStatus = newAmountDue <= 0 ? 'paid' : 'partial';

    // Update sale payment
    await executeQuery(
      `UPDATE sales SET
        amount_paid = ?, amount_due = ?, payment_status = ?
      WHERE id = ?`,
      [newAmountPaid, newAmountDue, newPaymentStatus, saleId]
    );

    // Update student balance if applicable
    if (sale[0].student_id) {
      await executeQuery(
        `UPDATE student_account_balances SET
          balance = balance - ?, last_payment_date = CURDATE()
        WHERE center_id = ? AND student_id = ?`,
        [amount, centerId, sale[0].student_id]
      );
    }

    // Record transaction
    await executeQuery(
      `INSERT INTO transactions (
        center_id, type, amount, description, transaction_date, reference_id
      ) VALUES (?, 'income', ?, ?, NOW(), ?)`,
      [centerId, amount, `Payment for sale ${sale[0].sale_number}`, saleId]
    );

    return c.json({
      success: true,
      message: 'Payment recorded successfully'
    });

  } catch (error) {
    console.error('Error recording payment:', error);
    return c.json({ error: 'Failed to record payment' }, 500);
  }
});

// Get inventory reports
coaching.get('/reports/inventory', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const reportType = c.req.query('type') || 'summary';

    if (reportType === 'summary') {
      const summary = await executeQuery(
        `SELECT
          COUNT(*) as total_items,
          SUM(current_stock * cost_price) as total_value,
          COUNT(CASE WHEN current_stock <= minimum_stock THEN 1 END) as low_stock_items,
          COUNT(CASE WHEN is_saleable = TRUE THEN 1 END) as saleable_items
        FROM inventory_items
        WHERE center_id = ? AND status = 'active'`,
        [centerId]
      );

      return c.json({ summary: summary[0] });
    }

    return c.json({ error: 'Invalid report type' }, 400);
  } catch (error) {
    console.error('Error generating inventory report:', error);
    return c.json({ error: 'Failed to generate inventory report' }, 500);
  }
});

// Get sales reports
coaching.get('/reports/sales', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const reportType = c.req.query('type') || 'daily';
    const date = c.req.query('date') || new Date().toISOString().split('T')[0];

    if (reportType === 'daily') {
      const dailySales = await executeQuery(
        `SELECT
          COUNT(*) as total_sales,
          SUM(total_amount) as total_revenue,
          SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END) as paid_amount,
          SUM(CASE WHEN payment_status != 'paid' THEN amount_due ELSE 0 END) as pending_amount
        FROM sales
        WHERE center_id = ? AND DATE(sale_date) = ?`,
        [centerId, date]
      );

      return c.json({ dailySales: dailySales[0] });
    }

    return c.json({ error: 'Invalid report type' }, 400);
  } catch (error) {
    console.error('Error generating sales report:', error);
    return c.json({ error: 'Failed to generate sales report' }, 500);
  }
});

// ==================== MULTI-BRANCH MANAGEMENT ROUTES ====================

// Get user's available branches
coaching.get('/user-branches', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    // Get branches user has access to
    const branches = await executeQuery(
      `SELECT b.*, uba.role as user_role, uba.is_primary
       FROM branches b
       JOIN user_branch_assignments uba ON b.id = uba.branch_id
       WHERE uba.user_id = ? AND uba.status = 'active' AND b.status = 'active'
       ORDER BY uba.is_primary DESC, b.branch_name`,
      [user.id]
    );

    return c.json({ branches });
  } catch (error) {
    console.error('Error fetching user branches:', error);
    return c.json({ error: 'Failed to fetch branches' }, 500);
  }
});

// Get all branches (super admin and center admin)
coaching.get('/branches', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['super_admin', 'center_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branches = await executeQuery(
      `SELECT b.*, u.name as manager_name,
              COUNT(DISTINCT ub.user_id) as total_users,
              COUNT(DISTINCT r.id) as total_rooms
       FROM branches b
       LEFT JOIN users u ON b.manager_id = u.id
       LEFT JOIN user_branch_assignments ub ON b.id = ub.branch_id AND ub.status = 'active'
       LEFT JOIN rooms r ON b.id = r.branch_id AND r.status = 'active'
       GROUP BY b.id
       ORDER BY b.created_at DESC`
    );

    return c.json({ branches });
  } catch (error) {
    console.error('Error fetching branches:', error);
    return c.json({ error: 'Failed to fetch branches' }, 500);
  }
});

// Create new branch
coaching.post('/branches', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['super_admin', 'center_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const {
      branch_code, branch_name, address, phone, email,
      manager_id, establishment_date, settings
    } = await c.req.json();

    if (!branch_code || !branch_name) {
      return c.json({ error: 'Branch code and name are required' }, 400);
    }

    // Check if branch code already exists
    const existingBranch = await executeQuery(
      'SELECT id FROM branches WHERE branch_code = ?',
      [branch_code]
    );

    if (existingBranch.length > 0) {
      return c.json({ error: 'Branch code already exists' }, 400);
    }

    // Get center ID from user
    const centerId = user.center_id || 1;

    const result = await executeQuery(
      `INSERT INTO branches (
        center_id, branch_code, branch_name, address, phone, email,
        manager_id, establishment_date, settings
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [centerId, branch_code, branch_name, address, phone, email,
       manager_id, establishment_date, JSON.stringify(settings || {})]
    );

    return c.json({
      success: true,
      branchId: result.insertId,
      message: 'Branch created successfully'
    });

  } catch (error) {
    console.error('Error creating branch:', error);
    return c.json({ error: 'Failed to create branch' }, 500);
  }
});

// Update branch
coaching.put('/branches/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['super_admin', 'center_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branchId = c.req.param('id');
    const {
      branch_code, branch_name, address, phone, email,
      manager_id, establishment_date, status, settings
    } = await c.req.json();

    await executeQuery(
      `UPDATE branches SET
        branch_code = ?, branch_name = ?, address = ?, phone = ?, email = ?,
        manager_id = ?, establishment_date = ?, status = ?, settings = ?,
        updated_at = NOW()
      WHERE id = ?`,
      [branch_code, branch_name, address, phone, email,
       manager_id, establishment_date, status, JSON.stringify(settings || {}), branchId]
    );

    return c.json({
      success: true,
      message: 'Branch updated successfully'
    });

  } catch (error) {
    console.error('Error updating branch:', error);
    return c.json({ error: 'Failed to update branch' }, 500);
  }
});

// Get branch statistics
coaching.get('/branches/:id/stats', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branchId = c.req.param('id');

    // Check if user has access to this branch
    const hasAccess = await executeQuery(
      'SELECT 1 FROM user_branch_assignments WHERE user_id = ? AND branch_id = ? AND status = "active"',
      [user.id, branchId]
    );

    if (hasAccess.length === 0 && user.role !== 'super_admin') {
      return c.json({ error: 'Access denied to this branch' }, 403);
    }

    const stats = await executeQuery(
      `SELECT
        (SELECT COUNT(*) FROM users WHERE branch_id = ? AND role = 'student' AND status = 'active') as total_students,
        (SELECT COUNT(*) FROM users WHERE branch_id = ? AND role = 'teacher' AND status = 'active') as total_teachers,
        (SELECT COUNT(*) FROM rooms WHERE branch_id = ? AND status = 'active') as total_rooms,
        (SELECT COUNT(*) FROM courses WHERE branch_id = ? AND status = 'active') as total_courses,
        (SELECT COUNT(*) FROM batches WHERE branch_id = ? AND status = 'active') as total_batches,
        (SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE branch_id = ? AND type = 'income' AND DATE(transaction_date) = CURDATE()) as today_income,
        (SELECT COALESCE(SUM(amount), 0) FROM expenses WHERE branch_id = ? AND DATE(expense_date) = CURDATE()) as today_expenses`,
      [branchId, branchId, branchId, branchId, branchId, branchId, branchId]
    );

    return c.json({ stats: stats[0] });
  } catch (error) {
    console.error('Error fetching branch stats:', error);
    return c.json({ error: 'Failed to fetch branch statistics' }, 500);
  }
});

// ==================== ROOM MANAGEMENT ROUTES ====================

// Get rooms for current branch
coaching.get('/rooms', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branchId = c.req.query('branch_id') || user.branch_id;

    const rooms = await executeQuery(
      `SELECT r.*,
              COUNT(DISTINCT s.id) as scheduled_classes,
              COUNT(DISTINCT CASE WHEN s.status = 'active' AND s.day_of_week = DAYNAME(CURDATE()) THEN s.id END) as today_classes
       FROM rooms r
       LEFT JOIN schedules s ON r.id = s.room_id
       WHERE r.branch_id = ? AND r.status != 'deleted'
       GROUP BY r.id
       ORDER BY r.room_code`,
      [branchId]
    );

    return c.json({ rooms });
  } catch (error) {
    console.error('Error fetching rooms:', error);
    return c.json({ error: 'Failed to fetch rooms' }, 500);
  }
});

// Create new room
coaching.post('/rooms', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branchId = c.req.query('branch_id') || user.branch_id;
    const { room_code, room_name, room_type, capacity, equipment, notes } = await c.req.json();

    if (!room_code || !room_name) {
      return c.json({ error: 'Room code and name are required' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO rooms (
        branch_id, room_code, room_name, room_type, capacity, equipment, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [branchId, room_code, room_name, room_type, capacity || 0, equipment, notes]
    );

    return c.json({
      success: true,
      roomId: result.insertId,
      message: 'Room created successfully'
    });

  } catch (error) {
    console.error('Error creating room:', error);
    return c.json({ error: 'Failed to create room' }, 500);
  }
});

// Update room
coaching.put('/rooms/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const roomId = c.req.param('id');
    const { room_code, room_name, room_type, capacity, equipment, status, notes } = await c.req.json();

    await executeQuery(
      `UPDATE rooms SET
        room_code = ?, room_name = ?, room_type = ?, capacity = ?,
        equipment = ?, status = ?, notes = ?, updated_at = NOW()
      WHERE id = ?`,
      [room_code, room_name, room_type, capacity, equipment, status, notes, roomId]
    );

    return c.json({
      success: true,
      message: 'Room updated successfully'
    });

  } catch (error) {
    console.error('Error updating room:', error);
    return c.json({ error: 'Failed to update room' }, 500);
  }
});

// ==================== SCHEDULE MANAGEMENT ROUTES ====================

// Get schedules for current branch
coaching.get('/schedules', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branchId = c.req.query('branch_id') || user.branch_id;
    const dayOfWeek = c.req.query('day_of_week');

    let query = `
      SELECT s.*, c.name as course_name, b.name as batch_name,
             r.room_name, r.room_code, u.name as teacher_name
      FROM schedules s
      JOIN courses c ON s.course_id = c.id
      LEFT JOIN batches b ON s.batch_id = b.id
      JOIN rooms r ON s.room_id = r.id
      JOIN users u ON s.teacher_id = u.id
      WHERE s.branch_id = ? AND s.status = 'active'
    `;
    const params = [branchId];

    if (dayOfWeek) {
      query += ' AND s.day_of_week = ?';
      params.push(dayOfWeek);
    }

    query += ' ORDER BY s.day_of_week, s.start_time';

    const schedules = await executeQuery(query, params);

    return c.json({ schedules });
  } catch (error) {
    console.error('Error fetching schedules:', error);
    return c.json({ error: 'Failed to fetch schedules' }, 500);
  }
});

// Create new schedule
coaching.post('/schedules', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branchId = c.req.query('branch_id') || user.branch_id;
    const {
      course_id, batch_id, room_id, teacher_id, day_of_week,
      start_time, end_time, effective_from, effective_to, notes
    } = await c.req.json();

    // Check for conflicts
    const conflicts = await executeQuery(
      `SELECT 'room' as conflict_type, r.room_name as resource_name
       FROM schedules s
       JOIN rooms r ON s.room_id = r.id
       WHERE s.branch_id = ? AND s.room_id = ? AND s.day_of_week = ?
         AND s.status = 'active'
         AND ((s.start_time <= ? AND s.end_time > ?) OR (s.start_time < ? AND s.end_time >= ?))
       UNION
       SELECT 'teacher' as conflict_type, u.name as resource_name
       FROM schedules s
       JOIN users u ON s.teacher_id = u.id
       WHERE s.branch_id = ? AND s.teacher_id = ? AND s.day_of_week = ?
         AND s.status = 'active'
         AND ((s.start_time <= ? AND s.end_time > ?) OR (s.start_time < ? AND s.end_time >= ?))`,
      [branchId, room_id, day_of_week, start_time, start_time, end_time, end_time,
       branchId, teacher_id, day_of_week, start_time, start_time, end_time, end_time]
    );

    if (conflicts.length > 0) {
      return c.json({
        error: 'Schedule conflict detected',
        conflicts: conflicts
      }, 409);
    }

    const result = await executeQuery(
      `INSERT INTO schedules (
        branch_id, course_id, batch_id, room_id, teacher_id, day_of_week,
        start_time, end_time, effective_from, effective_to, notes, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [branchId, course_id, batch_id, room_id, teacher_id, day_of_week,
       start_time, end_time, effective_from, effective_to, notes, user.id]
    );

    return c.json({
      success: true,
      scheduleId: result.insertId,
      message: 'Schedule created successfully'
    });

  } catch (error) {
    console.error('Error creating schedule:', error);
    return c.json({ error: 'Failed to create schedule' }, 500);
  }
});

// ==================== STUDENT TRANSFER ROUTES ====================

// Get student transfers
coaching.get('/student-transfers', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branchId = c.req.query('branch_id') || user.branch_id;
    const status = c.req.query('status') || 'all';

    let query = `
      SELECT st.*, s.name as student_name, s.email as student_email,
             fb.branch_name as from_branch_name, tb.branch_name as to_branch_name,
             u1.name as requested_by_name, u2.name as approved_by_name
      FROM student_transfers st
      JOIN users s ON st.student_id = s.id
      JOIN branches fb ON st.from_branch_id = fb.id
      JOIN branches tb ON st.to_branch_id = tb.id
      JOIN users u1 ON st.requested_by = u1.id
      LEFT JOIN users u2 ON st.approved_by = u2.id
      WHERE (st.from_branch_id = ? OR st.to_branch_id = ?)
    `;
    const params = [branchId, branchId];

    if (status !== 'all') {
      query += ' AND st.status = ?';
      params.push(status);
    }

    query += ' ORDER BY st.created_at DESC';

    const transfers = await executeQuery(query, params);

    return c.json({ transfers });
  } catch (error) {
    console.error('Error fetching student transfers:', error);
    return c.json({ error: 'Failed to fetch student transfers' }, 500);
  }
});

// Request student transfer
coaching.post('/student-transfers', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const { student_id, from_branch_id, to_branch_id, transfer_date, reason, notes } = await c.req.json();

    if (!student_id || !from_branch_id || !to_branch_id || !reason) {
      return c.json({ error: 'Student, branches, and reason are required' }, 400);
    }

    if (from_branch_id === to_branch_id) {
      return c.json({ error: 'Source and destination branches cannot be the same' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO student_transfers (
        student_id, from_branch_id, to_branch_id, transfer_date, reason, notes, requested_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [student_id, from_branch_id, to_branch_id, transfer_date, reason, notes, user.id]
    );

    return c.json({
      success: true,
      transferId: result.insertId,
      message: 'Student transfer request created successfully'
    });

  } catch (error) {
    console.error('Error creating student transfer:', error);
    return c.json({ error: 'Failed to create student transfer request' }, 500);
  }
});

// Approve/Reject student transfer
coaching.put('/student-transfers/:id/status', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const transferId = c.req.param('id');
    const { status, notes } = await c.req.json();

    if (!['approved', 'rejected'].includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    // Get transfer details
    const transfer = await executeQuery(
      'SELECT * FROM student_transfers WHERE id = ?',
      [transferId]
    );

    if (transfer.length === 0) {
      return c.json({ error: 'Transfer not found' }, 404);
    }

    // Update transfer status
    await executeQuery(
      `UPDATE student_transfers SET
        status = ?, approved_by = ?, approved_at = NOW(), notes = ?
      WHERE id = ?`,
      [status, user.id, notes, transferId]
    );

    // If approved, update student's branch
    if (status === 'approved') {
      await executeQuery(
        'UPDATE users SET branch_id = ? WHERE id = ?',
        [transfer[0].to_branch_id, transfer[0].student_id]
      );

      // Update transfer status to completed
      await executeQuery(
        'UPDATE student_transfers SET status = "completed" WHERE id = ?',
        [transferId]
      );
    }

    return c.json({
      success: true,
      message: `Student transfer ${status} successfully`
    });

  } catch (error) {
    console.error('Error updating student transfer:', error);
    return c.json({ error: 'Failed to update student transfer' }, 500);
  }
});

// ==================== INVENTORY TRANSFER ROUTES ====================

// Get inventory transfers
coaching.get('/inventory-transfers', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branchId = c.req.query('branch_id') || user.branch_id;
    const status = c.req.query('status') || 'all';

    let query = `
      SELECT it.*, fb.branch_name as from_branch_name, tb.branch_name as to_branch_name,
             u1.name as requested_by_name, u2.name as approved_by_name, u3.name as received_by_name
      FROM inventory_transfers it
      JOIN branches fb ON it.from_branch_id = fb.id
      JOIN branches tb ON it.to_branch_id = tb.id
      JOIN users u1 ON it.requested_by = u1.id
      LEFT JOIN users u2 ON it.approved_by = u2.id
      LEFT JOIN users u3 ON it.received_by = u3.id
      WHERE (it.from_branch_id = ? OR it.to_branch_id = ?)
    `;
    const params = [branchId, branchId];

    if (status !== 'all') {
      query += ' AND it.status = ?';
      params.push(status);
    }

    query += ' ORDER BY it.created_at DESC';

    const transfers = await executeQuery(query, params);

    return c.json({ transfers });
  } catch (error) {
    console.error('Error fetching inventory transfers:', error);
    return c.json({ error: 'Failed to fetch inventory transfers' }, 500);
  }
});

// Create inventory transfer request
coaching.post('/inventory-transfers', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const {
      transfer_number, from_branch_id, to_branch_id, transfer_date,
      items, notes
    } = await c.req.json();

    if (!transfer_number || !from_branch_id || !to_branch_id || !items || items.length === 0) {
      return c.json({ error: 'Transfer number, branches, and items are required' }, 400);
    }

    // Calculate total value
    const total_value = items.reduce((sum: number, item: any) =>
      sum + (item.quantity_requested * item.unit_price), 0);

    // Create transfer
    const transferResult = await executeQuery(
      `INSERT INTO inventory_transfers (
        transfer_number, from_branch_id, to_branch_id, transfer_date,
        total_value, notes, requested_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [transfer_number, from_branch_id, to_branch_id, transfer_date,
       total_value, notes, user.id]
    );

    // Add transfer items
    for (const item of items) {
      await executeQuery(
        `INSERT INTO inventory_transfer_items (
          transfer_id, item_id, quantity_requested, unit_price, total_value
        ) VALUES (?, ?, ?, ?, ?)`,
        [transferResult.insertId, item.item_id, item.quantity_requested,
         item.unit_price, item.quantity_requested * item.unit_price]
      );
    }

    return c.json({
      success: true,
      transferId: transferResult.insertId,
      message: 'Inventory transfer request created successfully'
    });

  } catch (error) {
    console.error('Error creating inventory transfer:', error);
    return c.json({ error: 'Failed to create inventory transfer request' }, 500);
  }
});

// ==================== SCHEDULE MANAGEMENT ROUTES ====================

// Get schedules for a branch
coaching.get('/schedules', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branchId = c.req.query('branch_id') || user.branch_id;
    if (!branchId) {
      return c.json({ error: 'Branch ID is required' }, 400);
    }

    const schedules = await executeQuery(
      `SELECT s.*,
              c.name as course_name,
              b.name as batch_name,
              r.room_name, r.room_code,
              u.name as teacher_name
       FROM schedules s
       LEFT JOIN courses c ON s.course_id = c.id
       LEFT JOIN batches b ON s.batch_id = b.id
       LEFT JOIN rooms r ON s.room_id = r.id
       LEFT JOIN users u ON s.teacher_id = u.id
       WHERE s.branch_id = ? AND s.status = 'active'
       ORDER BY s.day_of_week, s.start_time`,
      [branchId]
    );

    return c.json({ schedules });
  } catch (error) {
    console.error('Error fetching schedules:', error);
    return c.json({ error: 'Failed to fetch schedules' }, 500);
  }
});

// Create weekly schedule (multiple days at once)
coaching.post('/schedules/weekly', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const branchId = c.req.query('branch_id') || user.branch_id;
    const { schedules } = await c.req.json();

    if (!schedules || !Array.isArray(schedules) || schedules.length === 0) {
      return c.json({ error: 'Schedules array is required' }, 400);
    }

    // Check for conflicts across all schedules
    const conflicts = [];
    for (const schedule of schedules) {
      const dayConflicts = await executeQuery(
        `SELECT 'room' as conflict_type, r.room_name as resource_name, s.day_of_week, s.start_time, s.end_time
         FROM schedules s
         JOIN rooms r ON s.room_id = r.id
         WHERE s.branch_id = ? AND s.room_id = ? AND s.day_of_week = ?
           AND s.status = 'active'
           AND ((s.start_time <= ? AND s.end_time > ?) OR (s.start_time < ? AND s.end_time >= ?))
         UNION
         SELECT 'teacher' as conflict_type, u.name as resource_name, s.day_of_week, s.start_time, s.end_time
         FROM schedules s
         JOIN users u ON s.teacher_id = u.id
         WHERE s.branch_id = ? AND s.teacher_id = ? AND s.day_of_week = ?
           AND s.status = 'active'
           AND ((s.start_time <= ? AND s.end_time > ?) OR (s.start_time < ? AND s.end_time >= ?))`,
        [branchId, schedule.room_id, schedule.day_of_week, schedule.start_time, schedule.start_time, schedule.end_time, schedule.end_time,
         branchId, schedule.teacher_id, schedule.day_of_week, schedule.start_time, schedule.start_time, schedule.end_time, schedule.end_time]
      );

      if (dayConflicts.length > 0) {
        conflicts.push(...dayConflicts.map(conflict => ({
          ...conflict,
          day: schedule.day_of_week
        })));
      }
    }

    if (conflicts.length > 0) {
      return c.json({
        error: 'Schedule conflicts detected',
        conflicts: conflicts
      }, 409);
    }

    // Insert all schedules
    const insertedIds = [];
    for (const schedule of schedules) {
      const result = await executeQuery(
        `INSERT INTO schedules (
          branch_id, course_id, batch_id, room_id, teacher_id, day_of_week,
          start_time, end_time, effective_from, effective_to, notes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [branchId, schedule.course_id, schedule.batch_id, schedule.room_id,
         schedule.teacher_id, schedule.day_of_week, schedule.start_time, schedule.end_time,
         schedule.effective_from, schedule.effective_to, schedule.notes, user.id]
      );
      insertedIds.push(result.insertId);
    }

    return c.json({
      success: true,
      scheduleIds: insertedIds,
      message: 'Weekly schedule created successfully'
    });

  } catch (error) {
    console.error('Error creating weekly schedule:', error);
    return c.json({ error: 'Failed to create weekly schedule' }, 500);
  }
});

// ==================== SETTINGS MANAGEMENT ROUTES ====================

// Upload image for settings
coaching.post('/settings/upload-image', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id || 1;

    // Parse multipart form data
    const body = await c.req.parseBody();
    const file = body['image'] as File;
    const imageType = body['type'] as string; // 'logo' or 'watermark'

    if (!file) {
      return c.json({ error: 'No image file provided' }, 400);
    }

    if (!imageType || !['logo', 'watermark'].includes(imageType)) {
      return c.json({ error: 'Invalid image type. Must be "logo" or "watermark"' }, 400);
    }

    // Convert File to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Validate image
    const { validateImage } = await import('../utils/imageUpload');
    const isValid = await validateImage(buffer, file.name);
    if (!isValid) {
      return c.json({ error: 'Invalid image file' }, 400);
    }

    // Process and save image
    const { processAndSaveImage, deleteImage } = await import('../utils/imageUpload');

    // Get current image to delete if exists
    const currentSettings = await executeQuery(
      'SELECT setting_value FROM center_settings WHERE center_id = ? AND setting_key = ?',
      [centerId, imageType === 'logo' ? 'invoice_logo' : 'invoice_watermark']
    );

    // Process new image
    const result = await processAndSaveImage(buffer, file.name, {
      maxWidth: imageType === 'logo' ? 800 : 600,
      maxHeight: imageType === 'logo' ? 400 : 300,
      quality: 85,
      format: 'webp',
      prefix: imageType
    });

    // Save filename to database
    const settingKey = imageType === 'logo' ? 'invoice_logo' : 'invoice_watermark';
    await executeQuery(
      `INSERT INTO center_settings (center_id, setting_key, setting_value, setting_type)
       VALUES (?, ?, ?, 'string')
       ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), setting_type = VALUES(setting_type)`,
      [centerId, settingKey, result.filename]
    );

    // Delete old image if exists
    if (currentSettings.length > 0 && currentSettings[0].setting_value) {
      await deleteImage(currentSettings[0].setting_value);
    }

    return c.json({
      success: true,
      message: 'Image uploaded successfully',
      image: {
        filename: result.filename,
        url: result.url,
        size: result.size,
        width: result.width,
        height: result.height
      }
    });

  } catch (error) {
    console.error('Error uploading image:', error);
    return c.json({ error: 'Failed to upload image' }, 500);
  }
});

// Get center settings
coaching.get('/settings', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id || 1;
    const settings = await executeQuery(
      'SELECT setting_key, setting_value, setting_type FROM center_settings WHERE center_id = ?',
      [centerId]
    );

    // Convert to key-value object
    const settingsObj: any = {};
    settings.forEach((setting: any) => {
      let value = setting.setting_value;
      if (setting.setting_type === 'json' && value) {
        try {
          value = JSON.parse(value);
        } catch (e) {
          // Keep as string if JSON parsing fails
        }
      } else if (setting.setting_type === 'boolean') {
        value = value === 'true' || value === '1';
      } else if (setting.setting_type === 'number') {
        value = parseFloat(value) || 0;
      } else if (['invoice_logo', 'invoice_watermark'].includes(setting.setting_key) && value) {
        // Convert image filename to full URL
        value = `/uploads/images/${value}`;
      }
      settingsObj[setting.setting_key] = value;
    });

    return c.json({ success: true, settings: settingsObj });
  } catch (error) {
    console.error('Error fetching settings:', error);
    return c.json({ error: 'Failed to fetch settings' }, 500);
  }
});

// Update center settings
coaching.put('/settings', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id || 1;
    const settings = await c.req.json();

    // Update each setting
    for (const [key, value] of Object.entries(settings)) {
      let settingValue = value;
      let settingType = 'text';

      // Determine setting type
      if (typeof value === 'boolean') {
        settingType = 'boolean';
        settingValue = value ? '1' : '0';
      } else if (typeof value === 'number') {
        settingType = 'number';
        settingValue = value.toString();
      } else if (typeof value === 'object') {
        settingType = 'json';
        settingValue = JSON.stringify(value);
      } else if (key.includes('logo') || key.includes('watermark') || key.includes('image')) {
        settingType = 'image';
      }

      await executeQuery(
        `INSERT INTO center_settings (center_id, setting_key, setting_value, setting_type)
         VALUES (?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), setting_type = VALUES(setting_type)`,
        [centerId, key, settingValue, settingType]
      );
    }

    return c.json({
      success: true,
      message: 'Settings updated successfully'
    });

  } catch (error) {
    console.error('Error updating settings:', error);
    return c.json({ error: 'Failed to update settings' }, 500);
  }
});

// ==================== STUDENT COURSE ASSIGNMENT ROUTES ====================

// Assign course to student
coaching.post('/students/:studentId/courses', async (c) => {
  try {
    console.log('🔍 Course Assignment Debug - Starting assignment process');

    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      console.log('🔍 Course Assignment Debug - Unauthorized user:', user?.role);
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const studentId = c.req.param('studentId');
    const requestBody = await c.req.json();
    console.log('🔍 Course Assignment Debug - Request data:', { studentId, requestBody });

    const {
      course_id, batch_id, enrollment_type, monthly_fee, one_time_fee,
      discount_amount, discount_percentage, notes
    } = requestBody;

    if (!course_id || !enrollment_type) {
      return c.json({ error: 'Course ID and enrollment type are required' }, 400);
    }

    // Check if student already enrolled in this course
    const existingEnrollment = await executeQuery(
      'SELECT id FROM student_courses WHERE student_id = ? AND course_id = ? AND status = "active"',
      [studentId, course_id]
    );

    if (existingEnrollment.length > 0) {
      return c.json({ error: 'Student is already enrolled in this course' }, 400);
    }

    // Insert course assignment
    // Handle empty batch_id - convert empty string to null
    const batchIdValue = batch_id && batch_id.trim() !== '' ? batch_id : null;
    console.log('🔍 Course Assignment Debug - Processed batch_id:', batchIdValue);

    const result = await executeQuery(
      `INSERT INTO student_courses (
        student_id, course_id, batch_id, enrollment_type, monthly_fee, one_time_fee,
        discount_amount, discount_percentage, notes, enrollment_date
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE())`,
      [studentId, course_id, batchIdValue, enrollment_type, monthly_fee, one_time_fee,
       discount_amount, discount_percentage, notes]
    );

    // Generate invoice
    const invoiceNumber = `INV-${Date.now()}-${result.insertId}`;
    const amount = enrollment_type === 'monthly' ? monthly_fee : one_time_fee;
    const totalAmount = amount - (discount_amount || 0);

    await executeQuery(
      `INSERT INTO invoices (
        invoice_number, student_id, student_course_id, invoice_type,
        amount, discount_amount, total_amount, due_date
      ) VALUES (?, ?, ?, ?, ?, ?, ?, DATE_ADD(CURDATE(), INTERVAL 7 DAY))`,
      [invoiceNumber, studentId, result.insertId,
       enrollment_type === 'monthly' ? 'monthly_fee' : 'one_time_fee',
       amount, discount_amount || 0, totalAmount]
    );

    return c.json({
      success: true,
      enrollmentId: result.insertId,
      invoiceNumber,
      message: 'Course assigned successfully'
    });

  } catch (error) {
    console.error('Error assigning course:', error);
    return c.json({ error: 'Failed to assign course' }, 500);
  }
});

// Get student course assignments
coaching.get('/students/:studentId/courses', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const studentId = c.req.param('studentId');

    const courses = await executeQuery(
      `SELECT sc.*, c.name as course_name, c.code as course_code,
              b.name as batch_name, i.invoice_number, i.payment_status, i.total_amount
       FROM student_courses sc
       LEFT JOIN courses c ON sc.course_id = c.id
       LEFT JOIN batches b ON sc.batch_id = b.id
       LEFT JOIN invoices i ON sc.id = i.student_course_id
       WHERE sc.student_id = ?
       ORDER BY sc.created_at DESC`,
      [studentId]
    );

    return c.json({ courses });
  } catch (error) {
    console.error('Error fetching student courses:', error);
    return c.json({ error: 'Failed to fetch student courses' }, 500);
  }
});

// ==================== INVOICE ROUTES ====================

// Get invoice details
coaching.get('/invoices/:invoiceId', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const invoiceId = c.req.param('invoiceId');

    const invoice = await executeQuery(
      `SELECT i.*, u.name as student_name, u.email as student_email, u.phone as student_phone,
              u.address as student_address, u.class_name, u.class_section,
              c.name as course_name, c.code as course_code,
              b.name as batch_name, sc.enrollment_type, sc.notes
       FROM invoices i
       JOIN users u ON i.student_id = u.id
       JOIN student_courses sc ON i.student_course_id = sc.id
       JOIN courses c ON sc.course_id = c.id
       LEFT JOIN batches b ON sc.batch_id = b.id
       WHERE i.id = ?`,
      [invoiceId]
    );

    if (invoice.length === 0) {
      return c.json({ error: 'Invoice not found' }, 404);
    }

    return c.json({ invoice: invoice[0] });
  } catch (error) {
    console.error('Error fetching invoice:', error);
    return c.json({ error: 'Failed to fetch invoice' }, 500);
  }
});

// Get all invoices
coaching.get('/invoices', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const invoices = await executeQuery(
      `SELECT i.*, u.name as student_name, c.name as course_name
       FROM invoices i
       JOIN users u ON i.student_id = u.id
       JOIN student_courses sc ON i.student_course_id = sc.id
       JOIN courses c ON sc.course_id = c.id
       ORDER BY i.created_at DESC`
    );

    return c.json({ invoices });
  } catch (error) {
    console.error('Error fetching invoices:', error);
    return c.json({ error: 'Failed to fetch invoices' }, 500);
  }
});

// ==================== EXAM MANAGEMENT ROUTES ====================

// Get all exams
coaching.get('/exams', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id || 1;
    const exams = await executeQuery(
      `SELECT e.*, c.name as subject_name, r.room_name, r.room_code,
              COUNT(DISTINCT eb.batch_id) as batch_count,
              COUNT(DISTINCT es.supervisor_id) as supervisor_count,
              COUNT(DISTINCT er.student_id) as student_count
       FROM exams e
       LEFT JOIN courses c ON e.subject_id = c.id
       LEFT JOIN rooms r ON e.room_id = r.id
       LEFT JOIN exam_batches eb ON e.id = eb.exam_id
       LEFT JOIN exam_supervisors es ON e.id = es.exam_id
       LEFT JOIN exam_results er ON e.id = er.exam_id
       WHERE e.center_id = ?
       GROUP BY e.id
       ORDER BY e.exam_date DESC, e.start_time DESC`,
      [centerId]
    );

    return c.json({ exams });
  } catch (error) {
    console.error('Error fetching exams:', error);
    return c.json({ error: 'Failed to fetch exams' }, 500);
  }
});

// Get exam by ID with details
coaching.get('/exams/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const examId = c.req.param('id');

    // Get exam details
    const exam = await executeQuery(
      `SELECT e.*, c.name as subject_name, c.code as subject_code,
              r.room_name, r.room_code, r.capacity,
              u.name as created_by_name
       FROM exams e
       LEFT JOIN courses c ON e.subject_id = c.id
       LEFT JOIN rooms r ON e.room_id = r.id
       LEFT JOIN users u ON e.created_by = u.id
       WHERE e.id = ?`,
      [examId]
    );

    if (exam.length === 0) {
      return c.json({ error: 'Exam not found' }, 404);
    }

    // Get assigned batches
    const batches = await executeQuery(
      `SELECT b.*, eb.created_at as assigned_at
       FROM exam_batches eb
       JOIN batches b ON eb.batch_id = b.id
       WHERE eb.exam_id = ?`,
      [examId]
    );

    // Get assigned supervisors
    const supervisors = await executeQuery(
      `SELECT u.name, u.email, es.supervisor_role, es.assigned_at
       FROM exam_supervisors es
       JOIN users u ON es.supervisor_id = u.id
       WHERE es.exam_id = ?`,
      [examId]
    );

    // Get exam statistics
    const statistics = await executeQuery(
      `SELECT * FROM exam_statistics WHERE exam_id = ?`,
      [examId]
    );

    return c.json({
      exam: exam[0],
      batches,
      supervisors,
      statistics
    });
  } catch (error) {
    console.error('Error fetching exam details:', error);
    return c.json({ error: 'Failed to fetch exam details' }, 500);
  }
});

// Create new exam
coaching.post('/exams', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin', 'teacher'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const {
      exam_name, exam_description, exam_type, subject_id, exam_date,
      start_time, end_time, room_id, total_marks, passing_marks,
      grading_scale, exam_instructions, batch_ids, supervisor_ids
    } = await c.req.json();

    if (!exam_name || !exam_date || !start_time || !end_time || !total_marks) {
      return c.json({ error: 'Required fields missing' }, 400);
    }

    // Calculate duration
    const startDateTime = new Date(`${exam_date} ${start_time}`);
    const endDateTime = new Date(`${exam_date} ${end_time}`);
    const duration_minutes = Math.round((endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60));

    const centerId = user.center_id || 1;

    // Insert exam
    const examResult = await executeQuery(
      `INSERT INTO exams (
        center_id, exam_name, exam_description, exam_type, subject_id,
        exam_date, start_time, end_time, duration_minutes, room_id,
        total_marks, passing_marks, grading_scale, exam_instructions,
        created_by, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft')`,
      [centerId, exam_name, exam_description, exam_type, subject_id,
       exam_date, start_time, end_time, duration_minutes, room_id,
       total_marks, passing_marks, JSON.stringify(grading_scale || {}),
       exam_instructions, user.id]
    );

    const examId = examResult.insertId;

    // Assign batches
    if (batch_ids && batch_ids.length > 0) {
      for (const batchId of batch_ids) {
        await executeQuery(
          'INSERT INTO exam_batches (exam_id, batch_id) VALUES (?, ?)',
          [examId, batchId]
        );
      }
    }

    // Assign supervisors
    if (supervisor_ids && supervisor_ids.length > 0) {
      for (const supervisor of supervisor_ids) {
        await executeQuery(
          'INSERT INTO exam_supervisors (exam_id, supervisor_id, supervisor_role) VALUES (?, ?, ?)',
          [examId, supervisor.id, supervisor.role || 'assistant_supervisor']
        );
      }
    }

    return c.json({
      success: true,
      examId,
      message: 'Exam created successfully'
    });

  } catch (error) {
    console.error('Error creating exam:', error);
    return c.json({ error: 'Failed to create exam' }, 500);
  }
});

// Update exam status
coaching.put('/exams/:id/status', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin', 'teacher'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const examId = c.req.param('id');
    const { status } = await c.req.json();

    const validStatuses = ['draft', 'scheduled', 'in_progress', 'completed', 'results_published', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    await executeQuery(
      'UPDATE exams SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [status, examId]
    );

    return c.json({
      success: true,
      message: `Exam status updated to ${status}`
    });

  } catch (error) {
    console.error('Error updating exam status:', error);
    return c.json({ error: 'Failed to update exam status' }, 500);
  }
});

// ==================== RESULT MANAGEMENT ROUTES ====================

// Get exam results for a specific exam and batch
coaching.get('/exams/:examId/results/:batchId', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const examId = c.req.param('examId');
    const batchId = c.req.param('batchId');

    // Get exam details
    const exam = await executeQuery(
      'SELECT exam_name, total_marks, passing_marks FROM exams WHERE id = ?',
      [examId]
    );

    if (exam.length === 0) {
      return c.json({ error: 'Exam not found' }, 404);
    }

    // Get students in the batch with their results
    const students = await executeQuery(
      `SELECT u.id, u.name, u.email, u.profile_image_webp, u.class_name, u.class_section,
              ub.roll_number,
              er.obtained_marks, er.percentage, er.grade, er.position_in_batch,
              er.position_overall, er.is_absent, er.notes, er.entered_at
       FROM user_batches ub
       JOIN users u ON ub.user_id = u.id
       LEFT JOIN exam_results er ON (er.student_id = u.id AND er.exam_id = ? AND er.batch_id = ?)
       WHERE ub.batch_id = ? AND u.role = 'student'
       ORDER BY ub.roll_number, u.name`,
      [examId, batchId, batchId]
    );

    return c.json({
      exam: exam[0],
      students
    });

  } catch (error) {
    console.error('Error fetching exam results:', error);
    return c.json({ error: 'Failed to fetch exam results' }, 500);
  }
});

// Submit/Update exam results for students
coaching.post('/exams/:examId/results', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin', 'teacher'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const examId = c.req.param('examId');
    const { results } = await c.req.json(); // Array of student results

    if (!results || !Array.isArray(results)) {
      return c.json({ error: 'Results array is required' }, 400);
    }

    // Get exam details for validation
    const exam = await executeQuery(
      'SELECT total_marks, passing_marks, grading_scale FROM exams WHERE id = ?',
      [examId]
    );

    if (exam.length === 0) {
      return c.json({ error: 'Exam not found' }, 404);
    }

    const { total_marks, passing_marks, grading_scale } = exam[0];
    const gradingScale = grading_scale ? JSON.parse(grading_scale) : {};

    // Process each result
    for (const result of results) {
      const { student_id, batch_id, obtained_marks, is_absent, notes } = result;

      // Validate marks
      if (!is_absent && (obtained_marks < 0 || obtained_marks > total_marks)) {
        return c.json({
          error: `Invalid marks for student ${student_id}. Marks should be between 0 and ${total_marks}`
        }, 400);
      }

      // Calculate percentage and grade
      const percentage = is_absent ? 0 : Math.round((obtained_marks / total_marks) * 100 * 100) / 100;
      const grade = is_absent ? 'ABS' : calculateGrade(percentage, gradingScale);

      // Insert or update result
      await executeQuery(
        `INSERT INTO exam_results (
          exam_id, student_id, batch_id, obtained_marks, percentage, grade,
          is_absent, notes, entered_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          obtained_marks = VALUES(obtained_marks),
          percentage = VALUES(percentage),
          grade = VALUES(grade),
          is_absent = VALUES(is_absent),
          notes = VALUES(notes),
          entered_by = VALUES(entered_by),
          updated_at = CURRENT_TIMESTAMP`,
        [examId, student_id, batch_id, is_absent ? null : obtained_marks,
         percentage, grade, is_absent, notes, user.id]
      );
    }

    // Calculate positions and statistics
    await calculatePositions(parseInt(examId));
    await calculateStatistics(parseInt(examId));

    return c.json({
      success: true,
      message: 'Results saved successfully'
    });

  } catch (error) {
    console.error('Error saving exam results:', error);
    return c.json({ error: 'Failed to save exam results' }, 500);
  }
});

// Helper function to calculate grade based on percentage
async function calculateGrade(percentage: number, gradingScale: any): Promise<string> {
  // Default grading scale if none provided
  const defaultGradingScale = [
    { min: 80, grade: 'A+' },
    { min: 70, grade: 'A' },
    { min: 60, grade: 'B+' },
    { min: 50, grade: 'B' },
    { min: 40, grade: 'C' },
    { min: 33, grade: 'D' },
    { min: 0, grade: 'F' }
  ];

  const scale = Object.keys(gradingScale).length > 0
    ? Object.entries(gradingScale).map(([grade, min]) => ({ grade, min: Number(min) }))
    : defaultGradingScale;

  // Sort by minimum percentage in descending order
  const sortedScale = scale.sort((a, b) => b.min - a.min);

  // Find the appropriate grade
  for (const { min, grade } of sortedScale) {
    if (percentage >= min) {
      return grade;
    }
  }

  return 'F'; // Default if no match found
}

// Helper function to calculate positions (ranks) for an exam
async function calculatePositions(examId: number): Promise<void> {
  try {
    // Calculate positions within each batch
    const batches = await executeQuery(
      'SELECT DISTINCT batch_id FROM exam_results WHERE exam_id = ?',
      [examId]
    );

    for (const { batch_id } of batches) {
      const batchResults = await executeQuery(
        `SELECT id, obtained_marks FROM exam_results
         WHERE exam_id = ? AND batch_id = ? AND is_absent = FALSE
         ORDER BY obtained_marks DESC, id ASC`,
        [examId, batch_id]
      );

      let currentRank = 1;
      let currentMarks = null;
      let sameRankCount = 0;

      for (let i = 0; i < batchResults.length; i++) {
        const result = batchResults[i];

        // If marks are different from previous student, assign new rank
        if (result.obtained_marks !== currentMarks) {
          currentRank = i + 1;
          currentMarks = result.obtained_marks;
          sameRankCount = 0;
        } else {
          sameRankCount++;
        }

        await executeQuery(
          'UPDATE exam_results SET position_in_batch = ? WHERE id = ?',
          [currentRank, result.id]
        );
      }
    }

    // Calculate overall positions across all batches
    const allResults = await executeQuery(
      `SELECT id, obtained_marks FROM exam_results
       WHERE exam_id = ? AND is_absent = FALSE
       ORDER BY obtained_marks DESC, id ASC`,
      [examId]
    );

    let currentRank = 1;
    let currentMarks = null;
    let sameRankCount = 0;

    for (let i = 0; i < allResults.length; i++) {
      const result = allResults[i];

      // If marks are different from previous student, assign new rank
      if (result.obtained_marks !== currentMarks) {
        currentRank = i + 1;
        currentMarks = result.obtained_marks;
        sameRankCount = 0;
      } else {
        sameRankCount++;
      }

      await executeQuery(
        'UPDATE exam_results SET position_overall = ? WHERE id = ?',
        [currentRank, result.id]
      );
    }
  } catch (error) {
    console.error('Error calculating positions:', error);
    throw error;
  }
}

// Helper function to calculate statistics for an exam
async function calculateStatistics(examId: number): Promise<void> {
  try {
    // Get exam details
    const exam = await executeQuery(
      'SELECT total_marks, passing_marks FROM exams WHERE id = ?',
      [examId]
    );

    if (exam.length === 0) {
      throw new Error('Exam not found');
    }

    const { total_marks, passing_marks } = exam[0];

    // Calculate overall statistics
    await calculateBatchStatistics(examId, null, total_marks, passing_marks);

    // Calculate batch-wise statistics
    const batches = await executeQuery(
      'SELECT DISTINCT batch_id FROM exam_results WHERE exam_id = ?',
      [examId]
    );

    for (const { batch_id } of batches) {
      await calculateBatchStatistics(examId, batch_id, total_marks, passing_marks);
    }
  } catch (error) {
    console.error('Error calculating statistics:', error);
    throw error;
  }
}

// Helper function to calculate statistics for a specific batch or overall
async function calculateBatchStatistics(
  examId: number,
  batchId: number | null,
  totalMarks: number,
  passingMarks: number
): Promise<void> {
  try {
    // Build the query based on whether we're calculating for a specific batch or overall
    const whereClause = batchId
      ? 'exam_id = ? AND batch_id = ?'
      : 'exam_id = ?';

    const queryParams = batchId
      ? [examId, batchId]
      : [examId];

    // Get all results
    const results = await executeQuery(
      `SELECT obtained_marks, is_absent, grade FROM exam_results WHERE ${whereClause}`,
      queryParams
    );

    // Calculate statistics
    const totalStudents = results.length;
    const absentStudents = results.filter(r => r.is_absent).length;
    const presentStudents = totalStudents - absentStudents;

    let highestMarks = 0;
    let lowestMarks = totalMarks;
    let totalObtainedMarks = 0;
    let passedStudents = 0;

    // Grade distribution
    const gradeDistribution = {};

    for (const result of results) {
      if (!result.is_absent) {
        const marks = result.obtained_marks;

        // Update highest and lowest marks
        if (marks > highestMarks) highestMarks = marks;
        if (marks < lowestMarks) lowestMarks = marks;

        // Add to total for average calculation
        totalObtainedMarks += marks;

        // Count passed students
        if (marks >= passingMarks) passedStudents++;

        // Update grade distribution
        const grade = result.grade;
        gradeDistribution[grade] = (gradeDistribution[grade] || 0) + 1;
      }
    }

    // Calculate averages
    const averageMarks = presentStudents > 0
      ? Math.round((totalObtainedMarks / presentStudents) * 100) / 100
      : 0;

    const averagePercentage = presentStudents > 0
      ? Math.round((totalObtainedMarks / (presentStudents * totalMarks)) * 100 * 100) / 100
      : 0;

    // Insert or update statistics
    await executeQuery(
      `INSERT INTO exam_statistics (
        exam_id, batch_id, total_students, present_students, absent_students,
        passed_students, failed_students, highest_marks, lowest_marks,
        average_marks, average_percentage, grade_distribution
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        total_students = VALUES(total_students),
        present_students = VALUES(present_students),
        absent_students = VALUES(absent_students),
        passed_students = VALUES(passed_students),
        failed_students = VALUES(failed_students),
        highest_marks = VALUES(highest_marks),
        lowest_marks = VALUES(lowest_marks),
        average_marks = VALUES(average_marks),
        average_percentage = VALUES(average_percentage),
        grade_distribution = VALUES(grade_distribution),
        calculated_at = CURRENT_TIMESTAMP`,
      [
        examId, batchId, totalStudents, presentStudents, absentStudents,
        passedStudents, presentStudents - passedStudents,
        highestMarks, presentStudents > 0 ? lowestMarks : 0,
        averageMarks, averagePercentage, JSON.stringify(gradeDistribution)
      ]
    );
  } catch (error) {
    console.error('Error calculating batch statistics:', error);
    throw error;
  }
}

// ==================== ROOM BOOKING MANAGEMENT ROUTES ====================

// Get all room bookings with conflict detection
coaching.get('/room-bookings', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const { room_id, start_date, end_date, booking_type, status } = c.req.query();
    const centerId = user.center_id || 1;

    let whereClause = 'rb.center_id = ?';
    let queryParams = [centerId];

    // Add filters
    if (room_id) {
      whereClause += ' AND rb.room_id = ?';
      queryParams.push(room_id);
    }
    if (start_date) {
      whereClause += ' AND DATE(rb.start_datetime) >= ?';
      queryParams.push(start_date);
    }
    if (end_date) {
      whereClause += ' AND DATE(rb.end_datetime) <= ?';
      queryParams.push(end_date);
    }
    if (booking_type) {
      whereClause += ' AND rb.booking_type = ?';
      queryParams.push(booking_type);
    }
    if (status) {
      whereClause += ' AND rb.status = ?';
      queryParams.push(status);
    }

    const bookings = await executeQuery(
      `SELECT rb.*, r.room_name, r.room_code, r.capacity,
              u1.name as organizer_name, u2.name as created_by_name
       FROM room_bookings rb
       LEFT JOIN rooms r ON rb.room_id = r.id
       LEFT JOIN users u1 ON rb.organizer_id = u1.id
       LEFT JOIN users u2 ON rb.created_by = u2.id
       WHERE ${whereClause}
       ORDER BY rb.start_datetime ASC`,
      queryParams
    );

    return c.json({ bookings });
  } catch (error) {
    console.error('Error fetching room bookings:', error);
    return c.json({ error: 'Failed to fetch room bookings' }, 500);
  }
});

// Check for room conflicts
coaching.post('/room-bookings/check-conflicts', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const { room_id, start_datetime, end_datetime, exclude_booking_id } = await c.req.json();

    if (!room_id || !start_datetime || !end_datetime) {
      return c.json({ error: 'Room ID, start time, and end time are required' }, 400);
    }

    let whereClause = `room_id = ? AND status NOT IN ('cancelled')
                       AND ((? BETWEEN start_datetime AND end_datetime)
                         OR (? BETWEEN start_datetime AND end_datetime)
                         OR (start_datetime BETWEEN ? AND ?)
                         OR (end_datetime BETWEEN ? AND ?))`;

    let queryParams = [room_id, start_datetime, end_datetime, start_datetime, end_datetime, start_datetime, end_datetime];

    // Exclude current booking if updating
    if (exclude_booking_id) {
      whereClause += ' AND id != ?';
      queryParams.push(exclude_booking_id);
    }

    const conflicts = await executeQuery(
      `SELECT rb.*, r.room_name, r.room_code,
              u.name as organizer_name
       FROM room_bookings rb
       LEFT JOIN rooms r ON rb.room_id = r.id
       LEFT JOIN users u ON rb.organizer_id = u.id
       WHERE ${whereClause}
       ORDER BY rb.start_datetime ASC`,
      queryParams
    );

    // Also check for schedule conflicts
    const scheduleConflicts = await executeQuery(
      `SELECT s.*, c.name as course_name, b.name as batch_name,
              r.room_name, r.room_code, u.name as teacher_name
       FROM schedules s
       LEFT JOIN courses c ON s.course_id = c.id
       LEFT JOIN batches b ON s.batch_id = b.id
       LEFT JOIN rooms r ON s.room_id = r.id
       LEFT JOIN users u ON s.teacher_id = u.id
       WHERE s.room_id = ? AND s.status = 'active'
         AND DAYNAME(?) = CASE s.day_of_week
           WHEN 'monday' THEN 'Monday'
           WHEN 'tuesday' THEN 'Tuesday'
           WHEN 'wednesday' THEN 'Wednesday'
           WHEN 'thursday' THEN 'Thursday'
           WHEN 'friday' THEN 'Friday'
           WHEN 'saturday' THEN 'Saturday'
           WHEN 'sunday' THEN 'Sunday'
         END
         AND ((TIME(?) BETWEEN s.start_time AND s.end_time)
           OR (TIME(?) BETWEEN s.start_time AND s.end_time)
           OR (s.start_time BETWEEN TIME(?) AND TIME(?))
           OR (s.end_time BETWEEN TIME(?) AND TIME(?)))`,
      [room_id, start_datetime, start_datetime, end_datetime, start_datetime, end_datetime, start_datetime, end_datetime]
    );

    return c.json({
      hasConflicts: conflicts.length > 0 || scheduleConflicts.length > 0,
      bookingConflicts: conflicts,
      scheduleConflicts: scheduleConflicts,
      totalConflicts: conflicts.length + scheduleConflicts.length
    });

  } catch (error) {
    console.error('Error checking room conflicts:', error);
    return c.json({ error: 'Failed to check room conflicts' }, 500);
  }
});

// Create new room booking
coaching.post('/room-bookings', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin', 'teacher'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const {
      room_id, booking_type, title, description, organizer_id,
      attendees_count, start_datetime, end_datetime, recurring_pattern,
      equipment_requirements, special_notes, attendee_ids
    } = await c.req.json();

    if (!room_id || !booking_type || !title || !start_datetime || !end_datetime) {
      return c.json({ error: 'Required fields missing' }, 400);
    }

    const centerId = user.center_id || 1;

    try {
      // Insert booking (triggers will check for conflicts)
      const bookingResult = await executeQuery(
        `INSERT INTO room_bookings (
          center_id, room_id, booking_type, title, description, organizer_id,
          attendees_count, start_datetime, end_datetime, recurring_pattern,
          equipment_requirements, special_notes, created_by, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')`,
        [centerId, room_id, booking_type, title, description, organizer_id,
         attendees_count, start_datetime, end_datetime,
         recurring_pattern ? JSON.stringify(recurring_pattern) : null,
         equipment_requirements, special_notes, user.id]
      );

      const bookingId = bookingResult.insertId;

      // Add attendees if provided
      if (attendee_ids && attendee_ids.length > 0) {
        for (const attendeeId of attendee_ids) {
          await executeQuery(
            'INSERT INTO room_booking_attendees (booking_id, user_id) VALUES (?, ?)',
            [bookingId, attendeeId]
          );
        }
      }

      return c.json({
        success: true,
        bookingId,
        message: 'Room booking created successfully'
      });

    } catch (dbError: any) {
      if (dbError.message && dbError.message.includes('Room booking conflict detected')) {
        return c.json({ error: 'Room booking conflict detected. Please choose a different time or room.' }, 409);
      }
      throw dbError;
    }

  } catch (error) {
    console.error('Error creating room booking:', error);
    return c.json({ error: 'Failed to create room booking' }, 500);
  }
});

// Update room booking
coaching.put('/room-bookings/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin', 'teacher'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const bookingId = c.req.param('id');
    const {
      room_id, booking_type, title, description, organizer_id,
      attendees_count, start_datetime, end_datetime, recurring_pattern,
      equipment_requirements, special_notes, status, attendee_ids
    } = await c.req.json();

    if (!room_id || !booking_type || !title || !start_datetime || !end_datetime) {
      return c.json({ error: 'Required fields missing' }, 400);
    }

    try {
      // Update booking (triggers will check for conflicts)
      await executeQuery(
        `UPDATE room_bookings SET
          room_id = ?, booking_type = ?, title = ?, description = ?,
          organizer_id = ?, attendees_count = ?, start_datetime = ?,
          end_datetime = ?, recurring_pattern = ?, equipment_requirements = ?,
          special_notes = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?`,
        [room_id, booking_type, title, description, organizer_id,
         attendees_count, start_datetime, end_datetime,
         recurring_pattern ? JSON.stringify(recurring_pattern) : null,
         equipment_requirements, special_notes, status || 'pending', bookingId]
      );

      // Update attendees if provided
      if (attendee_ids) {
        // Remove existing attendees
        await executeQuery(
          'DELETE FROM room_booking_attendees WHERE booking_id = ?',
          [bookingId]
        );

        // Add new attendees
        for (const attendeeId of attendee_ids) {
          await executeQuery(
            'INSERT INTO room_booking_attendees (booking_id, user_id) VALUES (?, ?)',
            [bookingId, attendeeId]
          );
        }
      }

      return c.json({
        success: true,
        message: 'Room booking updated successfully'
      });

    } catch (dbError: any) {
      if (dbError.message && dbError.message.includes('Room booking conflict detected')) {
        return c.json({ error: 'Room booking conflict detected. Please choose a different time or room.' }, 409);
      }
      throw dbError;
    }

  } catch (error) {
    console.error('Error updating room booking:', error);
    return c.json({ error: 'Failed to update room booking' }, 500);
  }
});

// Get room booking by ID
coaching.get('/room-bookings/:id', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const bookingId = c.req.param('id');

    // Get booking details
    const booking = await executeQuery(
      `SELECT rb.*, r.room_name, r.room_code, r.capacity,
              u1.name as organizer_name, u2.name as created_by_name
       FROM room_bookings rb
       LEFT JOIN rooms r ON rb.room_id = r.id
       LEFT JOIN users u1 ON rb.organizer_id = u1.id
       LEFT JOIN users u2 ON rb.created_by = u2.id
       WHERE rb.id = ?`,
      [bookingId]
    );

    if (booking.length === 0) {
      return c.json({ error: 'Booking not found' }, 404);
    }

    // Get attendees
    const attendees = await executeQuery(
      `SELECT rba.*, u.name, u.email, u.role
       FROM room_booking_attendees rba
       JOIN users u ON rba.user_id = u.id
       WHERE rba.booking_id = ?`,
      [bookingId]
    );

    // Get audit log
    const auditLog = await executeQuery(
      `SELECT rbal.*, u.name as action_by_name
       FROM room_booking_audit_log rbal
       LEFT JOIN users u ON rbal.action_by = u.id
       WHERE rbal.booking_id = ?
       ORDER BY rbal.action_time DESC`,
      [bookingId]
    );

    return c.json({
      booking: booking[0],
      attendees,
      auditLog
    });
  } catch (error) {
    console.error('Error fetching room booking details:', error);
    return c.json({ error: 'Failed to fetch room booking details' }, 500);
  }
});

// Update room booking status
coaching.put('/room-bookings/:id/status', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const bookingId = c.req.param('id');
    const { status } = await c.req.json();

    const validStatuses = ['pending', 'approved', 'confirmed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return c.json({ error: 'Invalid status' }, 400);
    }

    await executeQuery(
      'UPDATE room_bookings SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [status, bookingId]
    );

    return c.json({
      success: true,
      message: `Booking status updated to ${status}`
    });

  } catch (error) {
    console.error('Error updating booking status:', error);
    return c.json({ error: 'Failed to update booking status' }, 500);
  }
});

// Get room calendar (all bookings for a specific room or date range)
coaching.get('/room-calendar', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const { start_date, end_date, room_id } = c.req.query();
    const centerId = user.center_id || 1;

    if (!start_date || !end_date) {
      return c.json({ error: 'Start date and end date are required' }, 400);
    }

    let whereClause = 'rb.center_id = ? AND DATE(rb.start_datetime) >= ? AND DATE(rb.end_datetime) <= ?';
    let queryParams = [centerId, start_date, end_date];

    if (room_id) {
      whereClause += ' AND rb.room_id = ?';
      queryParams.push(room_id);
    }

    // Get room bookings
    const bookings = await executeQuery(
      `SELECT rb.id, rb.room_id, rb.booking_type, rb.title, rb.description,
              rb.start_datetime, rb.end_datetime, rb.status,
              r.room_name, r.room_code, u.name as organizer_name
       FROM room_bookings rb
       LEFT JOIN rooms r ON rb.room_id = r.id
       LEFT JOIN users u ON rb.organizer_id = u.id
       WHERE ${whereClause} AND rb.status != 'cancelled'
       ORDER BY rb.start_datetime ASC`,
      queryParams
    );

    // Get class schedules that fall within the date range
    const schedules = await executeQuery(
      `SELECT s.id, s.room_id, s.day_of_week, s.start_time, s.end_time,
              c.name as course_name, b.name as batch_name,
              r.room_name, r.room_code, u.name as teacher_name
       FROM schedules s
       LEFT JOIN courses c ON s.course_id = c.id
       LEFT JOIN batches b ON s.batch_id = b.id
       LEFT JOIN rooms r ON s.room_id = r.id
       LEFT JOIN users u ON s.teacher_id = u.id
       WHERE s.status = 'active'
         ${room_id ? 'AND s.room_id = ?' : ''}`,
      room_id ? [room_id] : []
    );

    // Convert schedules to calendar events
    const calendarEvents = [];

    // Add bookings
    for (const booking of bookings) {
      calendarEvents.push({
        id: `booking-${booking.id}`,
        title: booking.title,
        start: booking.start_datetime,
        end: booking.end_datetime,
        resourceId: booking.room_id.toString(),
        extendedProps: {
          type: booking.booking_type,
          description: booking.description,
          status: booking.status,
          room: booking.room_name,
          organizer: booking.organizer_name
        },
        backgroundColor: getEventColor(booking.booking_type),
        borderColor: getEventColor(booking.booking_type)
      });
    }

    // Add schedules (recurring events)
    const startMoment = new Date(start_date);
    const endMoment = new Date(end_date);

    for (const schedule of schedules) {
      // Map day of week to number (0 = Sunday, 1 = Monday, etc.)
      const dayMap = {
        'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
        'thursday': 4, 'friday': 5, 'saturday': 6
      };

      const scheduleDay = dayMap[schedule.day_of_week];

      // Iterate through each day in the date range
      for (let d = new Date(startMoment); d <= endMoment; d.setDate(d.getDate() + 1)) {
        // If day of week matches schedule
        if (d.getDay() === scheduleDay) {
          const dateStr = d.toISOString().split('T')[0];
          const startDateTime = `${dateStr}T${schedule.start_time}`;
          const endDateTime = `${dateStr}T${schedule.end_time}`;

          calendarEvents.push({
            id: `schedule-${schedule.id}-${dateStr}`,
            title: `${schedule.course_name} - ${schedule.batch_name}`,
            start: startDateTime,
            end: endDateTime,
            resourceId: schedule.room_id.toString(),
            extendedProps: {
              type: 'class',
              description: `Regular class schedule`,
              room: schedule.room_name,
              teacher: schedule.teacher_name
            },
            backgroundColor: '#3788d8',
            borderColor: '#3788d8',
            rendering: 'background'
          });
        }
      }
    }

    return c.json({ events: calendarEvents });
  } catch (error) {
    console.error('Error fetching room calendar:', error);
    return c.json({ error: 'Failed to fetch room calendar' }, 500);
  }
});

// Helper function to get event color based on booking type
function getEventColor(bookingType: string): string {
  switch (bookingType) {
    case 'class': return '#3788d8'; // Blue
    case 'exam': return '#e74c3c';  // Red
    case 'meeting': return '#2ecc71'; // Green
    case 'conference': return '#9b59b6'; // Purple
    case 'training': return '#f39c12'; // Orange
    case 'workshop': return '#1abc9c'; // Teal
    default: return '#7f8c8d'; // Gray
  }
}

// ==================== EXAM RESULTS MANAGEMENT ROUTES ====================

// Get all exam results with comprehensive data
coaching.get('/exam-results', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const results = await executeQuery(
      `SELECT
        er.id,
        er.exam_id,
        e.exam_name,
        e.exam_date,
        e.exam_type,
        er.student_id,
        u.name as student_name,
        u.email as student_email,
        s.name as subject_name,
        b.name as batch_name,
        er.total_marks,
        er.obtained_marks,
        er.percentage,
        er.grade,
        er.position_in_batch,
        er.position_overall,
        er.is_absent,
        er.notes,
        er.status,
        er.created_at,
        er.updated_at
       FROM exam_results er
       JOIN exams e ON er.exam_id = e.id
       JOIN users u ON er.student_id = u.id
       LEFT JOIN subjects s ON e.subject_id = s.id
       LEFT JOIN batches b ON er.batch_id = b.id
       WHERE e.center_id = ?
       ORDER BY e.exam_date DESC, er.percentage DESC`,
      [centerId]
    );

    return c.json({ results });
  } catch (error) {
    console.error('Error fetching exam results:', error);
    return c.json({ error: 'Failed to fetch exam results' }, 500);
  }
});

// Get exam results statistics
coaching.get('/exam-results/stats', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    // Get comprehensive statistics
    const [totalResults] = await executeQuery(
      `SELECT COUNT(*) as count FROM exam_results er
       JOIN exams e ON er.exam_id = e.id
       WHERE e.center_id = ?`,
      [centerId]
    );

    const [publishedResults] = await executeQuery(
      `SELECT COUNT(*) as count FROM exam_results er
       JOIN exams e ON er.exam_id = e.id
       WHERE e.center_id = ? AND er.status = 'published'`,
      [centerId]
    );

    const [draftResults] = await executeQuery(
      `SELECT COUNT(*) as count FROM exam_results er
       JOIN exams e ON er.exam_id = e.id
       WHERE e.center_id = ? AND er.status = 'draft'`,
      [centerId]
    );

    const [averageData] = await executeQuery(
      `SELECT AVG(er.percentage) as avg_percentage FROM exam_results er
       JOIN exams e ON er.exam_id = e.id
       WHERE e.center_id = ? AND er.is_absent = 0`,
      [centerId]
    );

    const [passData] = await executeQuery(
      `SELECT
        COUNT(*) as total_attempts,
        SUM(CASE WHEN er.percentage >= e.passing_marks THEN 1 ELSE 0 END) as passed
       FROM exam_results er
       JOIN exams e ON er.exam_id = e.id
       WHERE e.center_id = ? AND er.is_absent = 0`,
      [centerId]
    );

    const [studentCount] = await executeQuery(
      `SELECT COUNT(DISTINCT er.student_id) as count FROM exam_results er
       JOIN exams e ON er.exam_id = e.id
       WHERE e.center_id = ?`,
      [centerId]
    );

    const [examCount] = await executeQuery(
      `SELECT COUNT(DISTINCT er.exam_id) as count FROM exam_results er
       JOIN exams e ON er.exam_id = e.id
       WHERE e.center_id = ?`,
      [centerId]
    );

    const stats = {
      totalResults: totalResults.count || 0,
      publishedResults: publishedResults.count || 0,
      draftResults: draftResults.count || 0,
      averagePercentage: averageData.avg_percentage || 0,
      passRate: passData.total_attempts > 0 ? (passData.passed / passData.total_attempts) * 100 : 0,
      totalStudents: studentCount.count || 0,
      examsWithResults: examCount.count || 0
    };

    return c.json({ stats });
  } catch (error) {
    console.error('Error fetching exam results stats:', error);
    return c.json({ error: 'Failed to fetch statistics' }, 500);
  }
});

// Delete exam result
coaching.delete('/exam-results/:resultId', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const resultId = c.req.param('resultId');

    // Verify the result belongs to the user's center
    const result = await executeQuery(
      `SELECT er.id FROM exam_results er
       JOIN exams e ON er.exam_id = e.id
       WHERE er.id = ? AND e.center_id = ?`,
      [resultId, user.center_id]
    );

    if (result.length === 0) {
      return c.json({ error: 'Result not found' }, 404);
    }

    await executeQuery('DELETE FROM exam_results WHERE id = ?', [resultId]);

    return c.json({ success: true, message: 'Result deleted successfully' });
  } catch (error) {
    console.error('Error deleting exam result:', error);
    return c.json({ error: 'Failed to delete result' }, 500);
  }
});

// Publish exam results
coaching.post('/exams/:examId/publish-results', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const examId = c.req.param('examId');

    // Verify the exam belongs to the user's center
    const exam = await executeQuery(
      'SELECT id FROM exams WHERE id = ? AND center_id = ?',
      [examId, user.center_id]
    );

    if (exam.length === 0) {
      return c.json({ error: 'Exam not found' }, 404);
    }

    // Update all results for this exam to published status
    await executeQuery(
      'UPDATE exam_results SET status = "published" WHERE exam_id = ?',
      [examId]
    );

    return c.json({ success: true, message: 'Results published successfully' });
  } catch (error) {
    console.error('Error publishing results:', error);
    return c.json({ error: 'Failed to publish results' }, 500);
  }
});

// ==================== STUDENT TRANSFER ROUTES ====================

// Get available branches for transfer
coaching.get('/branches', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const branches = await executeQuery(
      'SELECT id, branch_code, branch_name, address, status FROM branches WHERE status = "active" ORDER BY branch_name',
      []
    );

    return c.json({ branches });
  } catch (error) {
    console.error('Error fetching branches:', error);
    return c.json({ error: 'Failed to fetch branches' }, 500);
  }
});

// Transfer student to different branch
coaching.post('/students/:studentId/transfer', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const studentId = c.req.param('studentId');
    const centerId = user.center_id;
    const { to_branch_id, transfer_reason, effective_date } = await c.req.json();

    if (!to_branch_id || !transfer_reason || !effective_date) {
      return c.json({ error: 'Missing required fields' }, 400);
    }

    // Verify student belongs to the center
    const student = await executeQuery(
      'SELECT id, name, branch_id FROM users WHERE id = ? AND center_id = ? AND role = "student"',
      [studentId, centerId]
    );

    if (student.length === 0) {
      return c.json({ error: 'Student not found' }, 404);
    }

    const currentStudent = student[0];

    // Check if trying to transfer to same branch
    if (currentStudent.branch_id === to_branch_id) {
      return c.json({ error: 'Cannot transfer to the same branch' }, 400);
    }

    // Verify target branch exists and is active
    const targetBranch = await executeQuery(
      'SELECT id, branch_name FROM branches WHERE id = ? AND status = "active"',
      [to_branch_id]
    );

    if (targetBranch.length === 0) {
      return c.json({ error: 'Target branch not found or inactive' }, 404);
    }

    // Set current user ID for audit trail
    await executeQuery('SET @current_user_id = ?', [user.userId]);

    // Create transfer record
    const transferResult = await executeQuery(
      `INSERT INTO student_transfers (
        center_id, student_id, from_branch_id, to_branch_id,
        transfer_reason, transfer_date, effective_date, status, requested_by
      ) VALUES (?, ?, ?, ?, ?, CURDATE(), ?, 'completed', ?)`,
      [centerId, studentId, currentStudent.branch_id, to_branch_id, transfer_reason, effective_date, user.userId]
    );

    // Update student's branch
    await executeQuery(
      'UPDATE users SET branch_id = ? WHERE id = ?',
      [to_branch_id, studentId]
    );

    // Update related data (courses, batches, etc.) to new branch
    await executeQuery(
      'UPDATE student_courses SET branch_id = ? WHERE student_id = ?',
      [to_branch_id, studentId]
    );

    await executeQuery(
      'UPDATE fees SET branch_id = ? WHERE student_id = ?',
      [to_branch_id, studentId]
    );

    await executeQuery(
      'UPDATE attendance SET branch_id = ? WHERE student_id = ?',
      [to_branch_id, studentId]
    );

    return c.json({
      success: true,
      transferId: transferResult.insertId,
      message: 'Student transferred successfully'
    });
  } catch (error) {
    console.error('Error transferring student:', error);
    return c.json({ error: 'Failed to transfer student' }, 500);
  }
});

// Get student transfer history
coaching.get('/students/:studentId/transfers', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const studentId = c.req.param('studentId');
    const centerId = user.center_id;

    // Verify student belongs to the center
    const student = await executeQuery(
      'SELECT id FROM users WHERE id = ? AND center_id = ? AND role = "student"',
      [studentId, centerId]
    );

    if (student.length === 0) {
      return c.json({ error: 'Student not found' }, 404);
    }

    // Get transfer history
    const transfers = await executeQuery(
      `SELECT
        st.id,
        st.transfer_reason,
        st.transfer_date,
        st.effective_date,
        st.status,
        fb.branch_name as from_branch,
        tb.branch_name as to_branch,
        requester.name as requested_by_name,
        approver.name as approved_by_name,
        st.approved_at,
        st.notes,
        st.created_at
       FROM student_transfers st
       JOIN branches fb ON st.from_branch_id = fb.id
       JOIN branches tb ON st.to_branch_id = tb.id
       LEFT JOIN users requester ON st.requested_by = requester.id
       LEFT JOIN users approver ON st.approved_by = approver.id
       WHERE st.student_id = ? AND st.center_id = ?
       ORDER BY st.transfer_date DESC, st.created_at DESC`,
      [studentId, centerId]
    );

    return c.json({ transfers });
  } catch (error) {
    console.error('Error fetching transfer history:', error);
    return c.json({ error: 'Failed to fetch transfer history' }, 500);
  }
});

// ==================== CERTIFICATE GENERATOR ROUTES ====================

// Get certificate templates
coaching.get('/certificate-templates', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const templates = await executeQuery(
      `SELECT id, name, template_type, background_color, border_style,
              font_family, language, layout, is_active, created_at
       FROM certificate_templates
       WHERE center_id = ? AND is_active = 1
       ORDER BY name`,
      [centerId]
    );

    return c.json({ templates });
  } catch (error) {
    console.error('Error fetching certificate templates:', error);
    return c.json({ error: 'Failed to fetch certificate templates' }, 500);
  }
});

// Get center information for certificates
coaching.get('/center-info', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const center = await executeQuery(
      `SELECT name, address, phone, email, logo_url as logo
       FROM centers
       WHERE id = ?`,
      [centerId]
    );

    if (center.length === 0) {
      return c.json({ error: 'Center not found' }, 404);
    }

    return c.json({ center: center[0] });
  } catch (error) {
    console.error('Error fetching center info:', error);
    return c.json({ error: 'Failed to fetch center info' }, 500);
  }
});

// Generate certificate
coaching.post('/certificates/generate', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const {
      student_id,
      template_id,
      certificate_title,
      course_name,
      completion_date,
      grade,
      percentage,
      custom_text,
      language
    } = await c.req.json();

    if (!student_id || !template_id) {
      return c.json({ error: 'Student ID and Template ID are required' }, 400);
    }

    // Verify student belongs to the center
    const student = await executeQuery(
      'SELECT id, name, email FROM users WHERE id = ? AND center_id = ? AND role = "student"',
      [student_id, centerId]
    );

    if (student.length === 0) {
      return c.json({ error: 'Student not found' }, 404);
    }

    // Verify template belongs to the center
    const template = await executeQuery(
      'SELECT id, name FROM certificate_templates WHERE id = ? AND center_id = ? AND is_active = 1',
      [template_id, centerId]
    );

    if (template.length === 0) {
      return c.json({ error: 'Template not found' }, 404);
    }

    // Generate certificate record
    const certificateResult = await executeQuery(
      `INSERT INTO certificates (
        center_id, student_id, template_id, certificate_title, course_name,
        completion_date, grade, percentage, custom_text, language,
        issued_by, issued_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        centerId, student_id, template_id, certificate_title, course_name,
        completion_date, grade, percentage, custom_text, language, user.userId
      ]
    );

    return c.json({
      success: true,
      certificateId: certificateResult.insertId,
      message: 'Certificate generated successfully'
    });
  } catch (error) {
    console.error('Error generating certificate:', error);
    return c.json({ error: 'Failed to generate certificate' }, 500);
  }
});

// Get student certificates
coaching.get('/students/:studentId/certificates', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const studentId = c.req.param('studentId');
    const centerId = user.center_id;

    // Verify student belongs to the center
    const student = await executeQuery(
      'SELECT id FROM users WHERE id = ? AND center_id = ? AND role = "student"',
      [studentId, centerId]
    );

    if (student.length === 0) {
      return c.json({ error: 'Student not found' }, 404);
    }

    // Get certificates
    const certificates = await executeQuery(
      `SELECT
        c.id,
        c.certificate_title,
        c.course_name,
        c.completion_date,
        c.grade,
        c.percentage,
        c.language,
        c.issued_at,
        ct.name as template_name,
        issuer.name as issued_by_name
       FROM certificates c
       JOIN certificate_templates ct ON c.template_id = ct.id
       LEFT JOIN users issuer ON c.issued_by = issuer.id
       WHERE c.student_id = ? AND c.center_id = ?
       ORDER BY c.issued_at DESC`,
      [studentId, centerId]
    );

    return c.json({ certificates });
  } catch (error) {
    console.error('Error fetching certificates:', error);
    return c.json({ error: 'Failed to fetch certificates' }, 500);
  }
});

// ==================== ID CARD GENERATOR ROUTES ====================

// Get ID card templates
coaching.get('/id-card-templates', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const templates = await executeQuery(
      `SELECT id, name, card_type, orientation, background_color, text_color,
              layout, include_photo, include_qr, include_barcode, is_active, created_at
       FROM id_card_templates
       WHERE center_id = ? AND is_active = 1
       ORDER BY name`,
      [centerId]
    );

    return c.json({ templates });
  } catch (error) {
    console.error('Error fetching ID card templates:', error);
    return c.json({ error: 'Failed to fetch ID card templates' }, 500);
  }
});

// Generate ID card
coaching.post('/id-cards/generate', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const {
      student_id,
      template_id,
      card_title,
      validity_date,
      emergency_contact,
      custom_fields
    } = await c.req.json();

    if (!student_id || !template_id) {
      return c.json({ error: 'Student ID and Template ID are required' }, 400);
    }

    // Verify student belongs to the center
    const student = await executeQuery(
      'SELECT id, name, email FROM users WHERE id = ? AND center_id = ? AND role = "student"',
      [student_id, centerId]
    );

    if (student.length === 0) {
      return c.json({ error: 'Student not found' }, 404);
    }

    // Verify template belongs to the center
    const template = await executeQuery(
      'SELECT id, name FROM id_card_templates WHERE id = ? AND center_id = ? AND is_active = 1',
      [template_id, centerId]
    );

    if (template.length === 0) {
      return c.json({ error: 'Template not found' }, 404);
    }

    // Generate ID card record
    const idCardResult = await executeQuery(
      `INSERT INTO id_cards (
        center_id, student_id, template_id, card_title, validity_date,
        emergency_contact, custom_fields, issued_by, issued_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        centerId, student_id, template_id, card_title, validity_date,
        emergency_contact, JSON.stringify(custom_fields), user.userId
      ]
    );

    return c.json({
      success: true,
      idCardId: idCardResult.insertId,
      message: 'ID card generated successfully'
    });
  } catch (error) {
    console.error('Error generating ID card:', error);
    return c.json({ error: 'Failed to generate ID card' }, 500);
  }
});

// Get student ID cards
coaching.get('/students/:studentId/id-cards', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const studentId = c.req.param('studentId');
    const centerId = user.center_id;

    // Verify student belongs to the center
    const student = await executeQuery(
      'SELECT id FROM users WHERE id = ? AND center_id = ? AND role = "student"',
      [studentId, centerId]
    );

    if (student.length === 0) {
      return c.json({ error: 'Student not found' }, 404);
    }

    // Get ID cards
    const idCards = await executeQuery(
      `SELECT
        ic.id,
        ic.card_title,
        ic.validity_date,
        ic.emergency_contact,
        ic.issued_at,
        ic.status,
        ict.name as template_name,
        ict.card_type,
        issuer.name as issued_by_name
       FROM id_cards ic
       JOIN id_card_templates ict ON ic.template_id = ict.id
       LEFT JOIN users issuer ON ic.issued_by = issuer.id
       WHERE ic.student_id = ? AND ic.center_id = ?
       ORDER BY ic.issued_at DESC`,
      [studentId, centerId]
    );

    return c.json({ idCards });
  } catch (error) {
    console.error('Error fetching ID cards:', error);
    return c.json({ error: 'Failed to fetch ID cards' }, 500);
  }
});

// ==================== USER MANAGEMENT ROUTES ====================

// Get all users for the center
coaching.get('/users', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const users = await executeQuery(
      `SELECT
        u.id, u.name, u.email, u.phone, u.role, u.status, u.branch_id,
        u.last_login, u.created_at,
        b.branch_name
       FROM users u
       LEFT JOIN branches b ON u.branch_id = b.id
       WHERE u.center_id = ? AND u.role != 'student'
       ORDER BY u.created_at DESC`,
      [centerId]
    );

    return c.json({ users });
  } catch (error) {
    console.error('Error fetching users:', error);
    return c.json({ error: 'Failed to fetch users' }, 500);
  }
});

// Create new user
coaching.post('/users', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const { name, email, phone, role, branch_id, password, permissions } = await c.req.json();

    if (!name || !email || !role || !password) {
      return c.json({ error: 'Missing required fields' }, 400);
    }

    // Check if email already exists
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUser.length > 0) {
      return c.json({ error: 'Email already exists' }, 400);
    }

    // Hash password
    const hashedPassword = await Bun.password.hash(password);

    // Create user
    const result = await executeQuery(
      `INSERT INTO users (
        center_id, name, email, phone, role, branch_id, password,
        permissions, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())`,
      [
        centerId, name, email, phone, role, branch_id || null,
        hashedPassword, JSON.stringify(permissions || [])
      ]
    );

    return c.json({
      success: true,
      userId: result.insertId,
      message: 'User created successfully'
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return c.json({ error: 'Failed to create user' }, 500);
  }
});

// Update user
coaching.put('/users/:userId', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const userId = c.req.param('userId');
    const centerId = user.center_id;
    const { name, email, phone, role, branch_id, status, permissions } = await c.req.json();

    // Verify user belongs to the center
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE id = ? AND center_id = ?',
      [userId, centerId]
    );

    if (existingUser.length === 0) {
      return c.json({ error: 'User not found' }, 404);
    }

    // Build update query
    const updateFields = [];
    const updateValues = [];

    if (name) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    if (email) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }
    if (phone) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }
    if (role) {
      updateFields.push('role = ?');
      updateValues.push(role);
    }
    if (branch_id !== undefined) {
      updateFields.push('branch_id = ?');
      updateValues.push(branch_id || null);
    }
    if (status) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }
    if (permissions) {
      updateFields.push('permissions = ?');
      updateValues.push(JSON.stringify(permissions));
    }

    if (updateFields.length === 0) {
      return c.json({ error: 'No fields to update' }, 400);
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(userId, centerId);

    await executeQuery(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ? AND center_id = ?`,
      updateValues
    );

    return c.json({ success: true, message: 'User updated successfully' });
  } catch (error) {
    console.error('Error updating user:', error);
    return c.json({ error: 'Failed to update user' }, 500);
  }
});

// Reset user password
coaching.put('/users/:userId/password', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const userId = c.req.param('userId');
    const centerId = user.center_id;
    const { password } = await c.req.json();

    if (!password) {
      return c.json({ error: 'Password is required' }, 400);
    }

    // Verify user belongs to the center
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE id = ? AND center_id = ?',
      [userId, centerId]
    );

    if (existingUser.length === 0) {
      return c.json({ error: 'User not found' }, 404);
    }

    // Hash new password
    const hashedPassword = await Bun.password.hash(password);

    // Update password
    await executeQuery(
      'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ? AND center_id = ?',
      [hashedPassword, userId, centerId]
    );

    return c.json({ success: true, message: 'Password reset successfully' });
  } catch (error) {
    console.error('Error resetting password:', error);
    return c.json({ error: 'Failed to reset password' }, 500);
  }
});

// Delete user
coaching.delete('/users/:userId', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const userId = c.req.param('userId');
    const centerId = user.center_id;

    // Verify user belongs to the center and is not center_admin
    const existingUser = await executeQuery(
      'SELECT id, role FROM users WHERE id = ? AND center_id = ?',
      [userId, centerId]
    );

    if (existingUser.length === 0) {
      return c.json({ error: 'User not found' }, 404);
    }

    if (existingUser[0].role === 'center_admin') {
      return c.json({ error: 'Cannot delete center admin' }, 400);
    }

    // Delete user
    await executeQuery(
      'DELETE FROM users WHERE id = ? AND center_id = ?',
      [userId, centerId]
    );

    return c.json({ success: true, message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    return c.json({ error: 'Failed to delete user' }, 500);
  }
});

// Get user activity logs
coaching.get('/users/:userId/activity', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const userId = c.req.param('userId');
    const centerId = user.center_id;

    // Verify user belongs to the center
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE id = ? AND center_id = ?',
      [userId, centerId]
    );

    if (existingUser.length === 0) {
      return c.json({ error: 'User not found' }, 404);
    }

    // Get activity logs
    const activities = await executeQuery(
      `SELECT
        action_type, description, performed_at, ip_address
       FROM student_audit_logs
       WHERE performed_by = ? AND center_id = ?
       ORDER BY performed_at DESC
       LIMIT 50`,
      [userId, centerId]
    );

    return c.json({ activities });
  } catch (error) {
    console.error('Error fetching user activity:', error);
    return c.json({ error: 'Failed to fetch user activity' }, 500);
  }
});

// ==================== REPORTS ROUTES ====================

// Get dashboard reports
coaching.get('/reports/dashboard', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const startDate = c.req.query('startDate') || new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
    const endDate = c.req.query('endDate') || new Date().toISOString().split('T')[0];

    // Students data
    const [totalStudents] = await executeQuery(
      'SELECT COUNT(*) as count FROM users WHERE center_id = ? AND role = "student"',
      [centerId]
    );

    const [activeStudents] = await executeQuery(
      'SELECT COUNT(*) as count FROM users WHERE center_id = ? AND role = "student" AND status = "active"',
      [centerId]
    );

    const [inactiveStudents] = await executeQuery(
      'SELECT COUNT(*) as count FROM users WHERE center_id = ? AND role = "student" AND status = "inactive"',
      [centerId]
    );

    // Attendance data
    const [attendanceStats] = await executeQuery(
      `SELECT
        COUNT(*) as totalSessions,
        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as presentCount,
        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absentCount
       FROM attendance
       WHERE center_id = ? AND date BETWEEN ? AND ?`,
      [centerId, startDate, endDate]
    );

    const averageRate = attendanceStats.totalSessions > 0
      ? (attendanceStats.presentCount / attendanceStats.totalSessions) * 100
      : 0;

    // Financial data
    const [revenueData] = await executeQuery(
      `SELECT
        COALESCE(SUM(amount), 0) as totalRevenue
       FROM fees
       WHERE center_id = ? AND status = 'paid' AND payment_date BETWEEN ? AND ?`,
      [centerId, startDate, endDate]
    );

    const [expenseData] = await executeQuery(
      `SELECT
        COALESCE(SUM(amount), 0) as totalExpenses
       FROM expenses
       WHERE center_id = ? AND expense_date BETWEEN ? AND ?`,
      [centerId, startDate, endDate]
    );

    // Courses data
    const [coursesData] = await executeQuery(
      'SELECT COUNT(*) as totalCourses FROM courses WHERE center_id = ?',
      [centerId]
    );

    const [activeCourses] = await executeQuery(
      'SELECT COUNT(*) as count FROM courses WHERE center_id = ? AND status = "active"',
      [centerId]
    );

    const [enrollmentsData] = await executeQuery(
      'SELECT COUNT(*) as totalEnrollments FROM enrollments e JOIN courses c ON e.course_id = c.id WHERE c.center_id = ?',
      [centerId]
    );

    const reportData = {
      students: {
        total: totalStudents.count || 0,
        active: activeStudents.count || 0,
        inactive: inactiveStudents.count || 0,
        byMonth: [] // Can be implemented later
      },
      attendance: {
        averageRate: averageRate || 0,
        totalSessions: attendanceStats.totalSessions || 0,
        presentCount: attendanceStats.presentCount || 0,
        absentCount: attendanceStats.absentCount || 0,
        byMonth: [] // Can be implemented later
      },
      financial: {
        totalRevenue: revenueData.totalRevenue || 0,
        totalExpenses: expenseData.totalExpenses || 0,
        netProfit: (revenueData.totalRevenue || 0) - (expenseData.totalExpenses || 0),
        feeCollection: revenueData.totalRevenue || 0,
        byMonth: [] // Can be implemented later
      },
      courses: {
        totalCourses: coursesData.totalCourses || 0,
        activeCourses: activeCourses.count || 0,
        totalEnrollments: enrollmentsData.totalEnrollments || 0,
        popularCourses: [] // Can be implemented later
      }
    };

    return c.json({ reportData });
  } catch (error) {
    console.error('Error fetching report data:', error);
    return c.json({ error: 'Failed to fetch report data' }, 500);
  }
});

// Export reports (placeholder)
coaching.get('/reports/export', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    // This is a placeholder - actual implementation would generate PDF/Excel files
    return c.json({ message: 'Export functionality coming soon' });
  } catch (error) {
    console.error('Error exporting report:', error);
    return c.json({ error: 'Failed to export report' }, 500);
  }
});

// ==================== ASSIGNMENTS ROUTES ====================

// Get all assignments
coaching.get('/assignments', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    if (!centerId) {
      return c.json({ error: 'No center associated' }, 400);
    }

    const assignments = await executeQuery(
      `SELECT
        a.id, a.title, a.description, a.batch_id, a.due_date,
        a.max_marks as total_marks, a.status, a.teacher_id as created_by, a.created_at, a.updated_at,
        b.name as batch_name, b.course_id,
        c.name as course_name,
        u.name as created_by_name
       FROM assignments a
       LEFT JOIN batches b ON a.batch_id = b.id
       LEFT JOIN courses c ON b.course_id = c.id
       LEFT JOIN users u ON a.teacher_id = u.id
       WHERE a.center_id = ?
       ORDER BY a.created_at DESC`,
      [centerId]
    );

    return c.json({ assignments });
  } catch (error) {
    console.error('Error fetching assignments:', error);
    return c.json({ error: 'Failed to fetch assignments' }, 500);
  }
});

// Create new assignment
coaching.post('/assignments', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const centerId = user.center_id;
    const { title, description, batch_id, due_date, total_marks, status } = await c.req.json();

    if (!title || !batch_id || !due_date) {
      return c.json({ error: 'Missing required fields' }, 400);
    }

    const result = await executeQuery(
      `INSERT INTO assignments (
        center_id, title, description, batch_id, due_date,
        max_marks, status, teacher_id, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        centerId, title, description, batch_id, due_date,
        total_marks || 100, status || 'active', user.id
      ]
    );

    return c.json({
      success: true,
      assignmentId: result.insertId,
      message: 'Assignment created successfully'
    });
  } catch (error) {
    console.error('Error creating assignment:', error);
    return c.json({ error: 'Failed to create assignment' }, 500);
  }
});

// Update assignment
coaching.put('/assignments/:assignmentId', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const assignmentId = c.req.param('assignmentId');
    const centerId = user.center_id;
    const { title, description, batch_id, due_date, total_marks, status } = await c.req.json();

    // Verify assignment belongs to the center
    const existingAssignment = await executeQuery(
      'SELECT id FROM assignments WHERE id = ? AND center_id = ?',
      [assignmentId, centerId]
    );

    if (existingAssignment.length === 0) {
      return c.json({ error: 'Assignment not found' }, 404);
    }

    await executeQuery(
      `UPDATE assignments SET
        title = ?, description = ?, batch_id = ?, due_date = ?,
        max_marks = ?, status = ?, updated_at = NOW()
       WHERE id = ? AND center_id = ?`,
      [
        title, description, batch_id, due_date,
        total_marks || 100, status || 'active',
        assignmentId, centerId
      ]
    );

    return c.json({ success: true, message: 'Assignment updated successfully' });
  } catch (error) {
    console.error('Error updating assignment:', error);
    return c.json({ error: 'Failed to update assignment' }, 500);
  }
});

// Delete assignment
coaching.delete('/assignments/:assignmentId', async (c) => {
  try {
    const user = await getUserFromToken(c);
    if (!user) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const assignmentId = c.req.param('assignmentId');
    const centerId = user.center_id;

    // Verify assignment belongs to the center
    const existingAssignment = await executeQuery(
      'SELECT id FROM assignments WHERE id = ? AND center_id = ?',
      [assignmentId, centerId]
    );

    if (existingAssignment.length === 0) {
      return c.json({ error: 'Assignment not found' }, 404);
    }

    // Delete assignment
    await executeQuery(
      'DELETE FROM assignments WHERE id = ? AND center_id = ?',
      [assignmentId, centerId]
    );

    return c.json({ success: true, message: 'Assignment deleted successfully' });
  } catch (error) {
    console.error('Error deleting assignment:', error);
    return c.json({ error: 'Failed to delete assignment' }, 500);
  }
});

export { coaching };
