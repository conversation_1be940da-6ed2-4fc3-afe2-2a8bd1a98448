import { Hono } from 'hono';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import { getTenantInfo } from '../middleware/tenant-prisma.js';
import { authMiddleware } from '../middleware/auth-prisma.js';
import { executeTenantQuery } from '../config/tenant-prisma.js';

const coaching = new Hono();

// Public routes (no authentication required)
const publicRoutes = ['/auth/teacher-login'];

/**
 * GET /api/coaching/public/pages/:slug
 * Public endpoint to get a page by slug (no authentication required)
 */
coaching.get('/public/pages/:slug', async (c) => {
  try {
    const tenantSlug = c.req.header('x-tenant-slug');

    if (!tenantSlug) {
      return c.json({
        success: false,
        message: 'Tenant not specified'
      }, 400);
    }

    const slug = c.req.param('slug');

    const pages = await executeTenantQuery(
      tenantSlug,
      `SELECT p.id, p.title, p.slug, p.content, p.metaDescription, p.createdAt, p.updatedAt
       FROM custom_pages p
       WHERE p.slug = ? AND p.isPublished = ?`,
      [slug, true]
    );

    if (pages.length === 0) {
      return c.json({
        success: false,
        message: 'Page not found'
      }, 404);
    }

    return c.json({
      success: true,
      data: { page: pages[0] }
    });

  } catch (error) {
    console.error('Get public page error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch page'
    }, 500);
  }
});

// Apply authentication middleware to all routes except public ones
coaching.use('*', async (c, next) => {
  const path = c.req.path.replace('/api/coaching', '');
  if (publicRoutes.includes(path) || path.startsWith('/public/pages/')) {
    return next();
  }
  return authMiddleware(c, next);
});

/**
 * GET /api/coaching/dashboard
 * Get coaching center dashboard data
 */
coaching.get('/dashboard', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    // Get dashboard statistics using direct queries
    const [studentsResult, teachersResult, branchesResult] = await Promise.all([
      executeTenantQuery(tenantSlug, 'SELECT COUNT(*) as count FROM users WHERE role = ? AND status = ?', ['STUDENT', 'ACTIVE']),
      executeTenantQuery(tenantSlug, 'SELECT COUNT(*) as count FROM users WHERE role = ? AND status = ?', ['TEACHER', 'ACTIVE']),
      executeTenantQuery(tenantSlug, 'SELECT COUNT(*) as count FROM branches WHERE status = ?', ['ACTIVE'])
    ]);

    const recentStudents = await executeTenantQuery(
      tenantSlug,
      'SELECT id, name, email, createdAt FROM users WHERE role = ? AND status = ? ORDER BY createdAt DESC LIMIT 5',
      ['STUDENT', 'ACTIVE']
    );

    return c.json({
      success: true,
      data: {
        stats: {
          totalStudents: studentsResult[0]?.count || 0,
          totalTeachers: teachersResult[0]?.count || 0,
          totalBranches: branchesResult[0]?.count || 0,
        },
        recentStudents,
        center: {
          id: tenantInfo.tenant.id,
          name: tenantInfo.tenant.name,
          subdomain: tenantInfo.tenant.subdomain,
        }
      }
    });

  } catch (error) {
    console.error('Get coaching dashboard error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch dashboard data' 
    }, 500);
  }
});

/**
 * GET /api/coaching/branches
 * Get all branches
 */
coaching.get('/branches', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    
    const branches = await executeTenantQuery(
      tenantSlug,
      `SELECT b.*, 
        (SELECT COUNT(*) FROM users WHERE branchId = b.id) as userCount,
        (SELECT COUNT(*) FROM rooms WHERE branchId = b.id) as roomCount
       FROM branches b 
       WHERE b.status = ? 
       ORDER BY b.createdAt DESC`,
      ['ACTIVE']
    );
    
    return c.json({
      success: true,
      data: { branches }
    });

  } catch (error) {
    console.error('Get branches error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch branches' 
    }, 500);
  }
});

/**
 * POST /api/coaching/branches
 * Create a new branch
 */
coaching.post('/branches', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    const body = await c.req.json();
    
    const branchSchema = z.object({
      branchCode: z.string().min(1, 'Branch code is required'),
      branchName: z.string().min(1, 'Branch name is required'),
      address: z.string().optional(),
      phone: z.string().optional(),
      email: z.string().email().optional(),
      managerId: z.string().optional(),
    });
    
    const validatedData = branchSchema.parse(body);
    
    // Check if branch code already exists
    const existingBranch = await executeTenantQuery(
      tenantSlug,
      'SELECT id FROM branches WHERE branchCode = ?',
      [validatedData.branchCode]
    );
    
    if (existingBranch.length > 0) {
      return c.json({
        success: false,
        message: 'Branch code already exists'
      }, 400);
    }
    
    // Generate ID
    const branchId = `branch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create branch
    await executeTenantQuery(
      tenantSlug,
      `INSERT INTO branches (id, branchCode, branchName, address, phone, email, managerId, status, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [branchId, validatedData.branchCode, validatedData.branchName, validatedData.address || null, validatedData.phone || null, validatedData.email || null, validatedData.managerId || null, 'ACTIVE']
    );
    
    // Get the created branch
    const newBranch = await executeTenantQuery(
      tenantSlug,
      'SELECT * FROM branches WHERE id = ?',
      [branchId]
    );
    
    return c.json({
      success: true,
      message: 'Branch created successfully',
      data: { branch: newBranch[0] }
    }, 201);

  } catch (error) {
    console.error('Create branch error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to create branch' 
    }, 500);
  }
});

/**
 * GET /api/coaching/users
 * Get all users with filtering
 */
coaching.get('/users', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const role = c.req.query('role');
    const search = c.req.query('search');
    
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE u.status = ?';
    let params: any[] = ['ACTIVE'];
    
    if (role) {
      whereClause += ' AND u.role = ?';
      params.push(role);
    }
    
    if (search) {
      whereClause += ' AND (u.name LIKE ? OR u.email LIKE ? OR u.phone LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    const users = await executeTenantQuery(
      tenantSlug,
      `SELECT u.*, b.branchName, b.branchCode
       FROM users u
       LEFT JOIN branches b ON u.branchId = b.id
       ${whereClause}
       ORDER BY u.createdAt DESC
       LIMIT ${limit} OFFSET ${offset}`,
      params
    );
    
    const totalResult = await executeTenantQuery(
      tenantSlug,
      `SELECT COUNT(*) as count FROM users u ${whereClause}`,
      params
    );
    
    return c.json({
      success: true,
      data: {
        users,
        pagination: {
          page,
          limit,
          total: totalResult[0]?.count || 0,
          totalPages: Math.ceil((totalResult[0]?.count || 0) / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch users' 
    }, 500);
  }
});

/**
 * POST /api/coaching/users
 * Create a new user (teacher, staff, student)
 */
coaching.post('/users', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    const body = await c.req.json();
    
    const userSchema = z.object({
      name: z.string().min(1, 'Name is required'),
      email: z.string().email('Valid email is required'),
      password: z.string().min(6, 'Password must be at least 6 characters'),
      role: z.enum(['TEACHER', 'STAFF', 'STUDENT'], { required_error: 'Role is required' }),
      phone: z.string().optional(),
      branchId: z.string().min(1, 'Branch is required'),
    });
    
    const validatedData = userSchema.parse(body);
    
    // Check if email already exists
    const existingUser = await executeTenantQuery(
      tenantSlug,
      'SELECT id FROM users WHERE email = ?',
      [validatedData.email]
    );
    
    if (existingUser.length > 0) {
      return c.json({
        success: false,
        message: 'Email already exists'
      }, 400);
    }
    
    // Hash password
    const passwordHash = await bcrypt.hash(validatedData.password, 12);
    
    // Generate ID
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create user
    await executeTenantQuery(
      tenantSlug,
      `INSERT INTO users (id, name, email, passwordHash, role, phone, branchId, status, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [userId, validatedData.name, validatedData.email, passwordHash, validatedData.role, validatedData.phone || null, validatedData.branchId, 'ACTIVE']
    );
    
    // Get the created user (without password)
    const newUser = await executeTenantQuery(
      tenantSlug,
      'SELECT id, name, email, role, phone, branchId, status, createdAt FROM users WHERE id = ?',
      [userId]
    );
    
    return c.json({
      success: true,
      message: 'User created successfully',
      data: { user: newUser[0] }
    }, 201);

  } catch (error) {
    console.error('Create user error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to create user' 
    }, 500);
  }
});

/**
 * POST /api/coaching/auth/teacher-login
 * Teacher login endpoint
 */
coaching.post('/auth/teacher-login', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    const body = await c.req.json();

    const loginSchema = z.object({
      email: z.string().email('Valid email is required'),
      password: z.string().min(1, 'Password is required'),
    });

    const { email, password } = loginSchema.parse(body);

    // Find teacher
    const teachers = await executeTenantQuery(
      tenantSlug,
      'SELECT * FROM users WHERE email = ? AND role = ? AND status = ?',
      [email, 'TEACHER', 'ACTIVE']
    );

    if (teachers.length === 0) {
      return c.json({
        success: false,
        message: 'Invalid credentials'
      }, 401);
    }

    const teacher = teachers[0];

    // Verify password
    const isValidPassword = await bcrypt.compare(password, teacher.passwordHash);
    if (!isValidPassword) {
      return c.json({
        success: false,
        message: 'Invalid credentials'
      }, 401);
    }

    // Generate JWT token
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      {
        userId: teacher.id,
        email: teacher.email,
        role: teacher.role,
        branchId: teacher.branchId,
        tenantSlug: tenantSlug,
        loginType: 'teacher'
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    return c.json({
      success: true,
      message: 'Login successful',
      data: {
        token,
        teacher: {
          id: teacher.id,
          name: teacher.name,
          email: teacher.email,
          role: teacher.role,
          branchId: teacher.branchId,
          permissions: JSON.parse(teacher.permissions || '[]')
        }
      }
    });

  } catch (error) {
    console.error('Teacher login error:', error);
    return c.json({
      success: false,
      message: 'Login failed'
    }, 500);
  }
});

/**
 * GET /api/coaching/teacher/schedule
 * Get teacher's schedule
 */
coaching.get('/teacher/schedule', async (c) => {
  try {
    const user = c.get('user');
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    if (user.role !== 'TEACHER') {
      return c.json({
        success: false,
        message: 'Access denied. Teachers only.'
      }, 403);
    }

    const schedules = await executeTenantQuery(
      tenantSlug,
      `SELECT s.*, c.courseName, c.courseCode, b.branchName, r.roomName,
              sub.subjectName, sub.subjectCode
       FROM schedules s
       LEFT JOIN courses c ON s.courseId = c.id
       LEFT JOIN branches b ON s.branchId = b.id
       LEFT JOIN rooms r ON s.roomId = r.id
       LEFT JOIN subjects sub ON c.subjectId = sub.id
       WHERE s.teacherId = ? AND s.status = ?
       ORDER BY s.dayOfWeek, s.startTime`,
      [user.id, 'ACTIVE']
    );

    return c.json({
      success: true,
      data: { schedules }
    });

  } catch (error) {
    console.error('Get teacher schedule error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch schedule'
    }, 500);
  }
});

/**
 * GET /api/coaching/teacher/students
 * Get students assigned to teacher
 */
coaching.get('/teacher/students', async (c) => {
  try {
    const user = c.get('user');
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    if (user.role !== 'TEACHER') {
      return c.json({
        success: false,
        message: 'Access denied. Teachers only.'
      }, 403);
    }

    // Get students from batches where this teacher is assigned
    const students = await executeTenantQuery(
      tenantSlug,
      `SELECT DISTINCT u.id, u.name, u.email, u.phone,
              b.branchName, c.name as courseName, bt.name as batchName
       FROM users u
       JOIN enrollments e ON u.id = e.studentId
       JOIN batches bt ON e.batchId = bt.id
       JOIN courses c ON bt.courseId = c.id
       JOIN branches b ON u.branchId = b.id
       WHERE bt.teacherId = ? AND u.role = ? AND u.status = ?
       ORDER BY u.name`,
      [user.id, 'STUDENT', 'ACTIVE']
    );

    return c.json({
      success: true,
      data: { students }
    });

  } catch (error) {
    console.error('Get teacher students error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch students'
    }, 500);
  }
});

// ============================================================================
// COURSES MANAGEMENT
// ============================================================================

/**
 * GET /api/coaching/courses
 * Get all courses
 */
coaching.get('/courses', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    const courses = await executeTenantQuery(
      tenantSlug,
      `SELECT c.*, s.name as subjectName, s.code as subjectCode,
              b.branchName, COUNT(bt.id) as batchCount
       FROM courses c
       LEFT JOIN subjects s ON c.subjectId = s.id
       LEFT JOIN branches b ON c.branchId = b.id
       LEFT JOIN batches bt ON c.id = bt.courseId AND bt.status = 'ACTIVE'
       WHERE c.status = ?
       GROUP BY c.id
       ORDER BY c.createdAt DESC`,
      ['ACTIVE']
    );

    return c.json({
      success: true,
      data: { courses }
    });

  } catch (error) {
    console.error('Get courses error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch courses'
    }, 500);
  }
});

/**
 * POST /api/coaching/courses
 * Create a new course
 */
coaching.post('/courses', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    const body = await c.req.json();

    const courseSchema = z.object({
      name: z.string().min(1, 'Course name is required'),
      code: z.string().optional(),
      description: z.string().optional(),
      subjectId: z.string().min(1, 'Subject is required'),
      branchId: z.string().optional(),
      durationMonths: z.number().optional(),
      fee: z.number().optional(),
    });

    const validatedData = courseSchema.parse(body);

    // Generate ID
    const courseId = `course_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create course
    await executeTenantQuery(
      tenantSlug,
      `INSERT INTO courses (id, name, code, description, subjectId, branchId, durationMonths, fee, status, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [courseId, validatedData.name, validatedData.code || null, validatedData.description || null,
       validatedData.subjectId, validatedData.branchId || null, validatedData.durationMonths || null,
       validatedData.fee || null, 'ACTIVE']
    );

    // Get the created course
    const newCourse = await executeTenantQuery(
      tenantSlug,
      'SELECT * FROM courses WHERE id = ?',
      [courseId]
    );

    return c.json({
      success: true,
      message: 'Course created successfully',
      data: { course: newCourse[0] }
    }, 201);

  } catch (error) {
    console.error('Create course error:', error);
    return c.json({
      success: false,
      message: 'Failed to create course'
    }, 500);
  }
});

// ============================================================================
// SUBJECTS MANAGEMENT
// ============================================================================

/**
 * GET /api/coaching/subjects
 * Get all subjects
 */
coaching.get('/subjects', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    const subjects = await executeTenantQuery(
      tenantSlug,
      `SELECT s.*, COUNT(c.id) as courseCount
       FROM subjects s
       LEFT JOIN courses c ON s.id = c.subjectId AND c.status = 'ACTIVE'
       WHERE s.status = ?
       GROUP BY s.id
       ORDER BY s.name`,
      ['ACTIVE']
    );

    return c.json({
      success: true,
      data: { subjects }
    });

  } catch (error) {
    console.error('Get subjects error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch subjects'
    }, 500);
  }
});

// ============================================================================
// BATCHES MANAGEMENT
// ============================================================================

/**
 * GET /api/coaching/batches
 * Get all batches
 */
coaching.get('/batches', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    const batches = await executeTenantQuery(
      tenantSlug,
      `SELECT b.*, c.name as courseName, c.code as courseCode,
              u.name as teacherName, br.branchName,
              COUNT(e.id) as enrolledStudents
       FROM batches b
       LEFT JOIN courses c ON b.courseId = c.id
       LEFT JOIN users u ON b.teacherId = u.id
       LEFT JOIN branches br ON b.branchId = br.id
       LEFT JOIN enrollments e ON b.id = e.batchId AND e.status = 'ACTIVE'
       WHERE b.status = ?
       GROUP BY b.id
       ORDER BY b.createdAt DESC`,
      ['ACTIVE']
    );

    return c.json({
      success: true,
      data: { batches }
    });

  } catch (error) {
    console.error('Get batches error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch batches'
    }, 500);
  }
});

/**
 * POST /api/coaching/batches
 * Create a new batch
 */
coaching.post('/batches', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    const body = await c.req.json();

    const batchSchema = z.object({
      name: z.string().min(1, 'Batch name is required'),
      courseId: z.string().min(1, 'Course is required'),
      teacherId: z.string().optional(),
      branchId: z.string().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
      maxStudents: z.number().optional(),
    });

    const validatedData = batchSchema.parse(body);

    // Generate ID
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create batch
    await executeTenantQuery(
      tenantSlug,
      `INSERT INTO batches (id, name, courseId, teacherId, branchId, startDate, endDate, maxStudents, status, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [batchId, validatedData.name, validatedData.courseId, validatedData.teacherId || null,
       validatedData.branchId || null, validatedData.startDate || null, validatedData.endDate || null,
       validatedData.maxStudents || 30, 'ACTIVE']
    );

    // Get the created batch
    const newBatch = await executeTenantQuery(
      tenantSlug,
      'SELECT * FROM batches WHERE id = ?',
      [batchId]
    );

    return c.json({
      success: true,
      message: 'Batch created successfully',
      data: { batch: newBatch[0] }
    }, 201);

  } catch (error) {
    console.error('Create batch error:', error);
    return c.json({
      success: false,
      message: 'Failed to create batch'
    }, 500);
  }
});

// ============================================================================
// SCHEDULES MANAGEMENT
// ============================================================================

/**
 * GET /api/coaching/schedules
 * Get all schedules
 */
coaching.get('/schedules', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    const schedules = await executeTenantQuery(
      tenantSlug,
      `SELECT s.*, b.name as batchName, c.name as courseName,
              r.roomName, r.roomCode, u.name as teacherName
       FROM schedules s
       LEFT JOIN batches b ON s.batchId = b.id
       LEFT JOIN courses c ON b.courseId = c.id
       LEFT JOIN rooms r ON s.roomId = r.id
       LEFT JOIN users u ON b.teacherId = u.id
       WHERE s.isActive = ?
       ORDER BY s.dayOfWeek, s.startTime`,
      [true]
    );

    return c.json({
      success: true,
      data: { schedules }
    });

  } catch (error) {
    console.error('Get schedules error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch schedules'
    }, 500);
  }
});

/**
 * POST /api/coaching/schedules
 * Create a new schedule
 */
coaching.post('/schedules', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    const body = await c.req.json();

    const scheduleSchema = z.object({
      batchId: z.string().min(1, 'Batch is required'),
      roomId: z.string().optional(),
      dayOfWeek: z.enum(['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY']),
      startTime: z.string().min(1, 'Start time is required'),
      endTime: z.string().min(1, 'End time is required'),
    });

    const validatedData = scheduleSchema.parse(body);

    // Check for conflicts
    const conflicts = await executeTenantQuery(
      tenantSlug,
      `SELECT COUNT(*) as count FROM schedules s
       WHERE s.roomId = ? AND s.dayOfWeek = ? AND s.isActive = ?
       AND ((? BETWEEN s.startTime AND s.endTime) OR (? BETWEEN s.startTime AND s.endTime)
       OR (s.startTime BETWEEN ? AND ?) OR (s.endTime BETWEEN ? AND ?))`,
      [validatedData.roomId, validatedData.dayOfWeek, true,
       validatedData.startTime, validatedData.endTime,
       validatedData.startTime, validatedData.endTime,
       validatedData.startTime, validatedData.endTime]
    );

    if (conflicts[0]?.count > 0) {
      return c.json({
        success: false,
        message: 'Schedule conflict detected. Room is already booked for this time slot.'
      }, 400);
    }

    // Generate ID
    const scheduleId = `schedule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create schedule
    await executeTenantQuery(
      tenantSlug,
      `INSERT INTO schedules (id, batchId, roomId, dayOfWeek, startTime, endTime, isActive, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [scheduleId, validatedData.batchId, validatedData.roomId || null, validatedData.dayOfWeek,
       validatedData.startTime, validatedData.endTime, true]
    );

    return c.json({
      success: true,
      message: 'Schedule created successfully',
      data: { scheduleId }
    }, 201);

  } catch (error) {
    console.error('Create schedule error:', error);
    return c.json({
      success: false,
      message: 'Failed to create schedule'
    }, 500);
  }
});

// ============================================================================
// EXAMS MANAGEMENT
// ============================================================================

/**
 * GET /api/coaching/exams
 * Get all exams
 */
coaching.get('/exams', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    const exams = await executeTenantQuery(
      tenantSlug,
      `SELECT e.*, COUNT(er.id) as resultCount
       FROM exams e
       LEFT JOIN exam_results er ON e.id = er.examId
       WHERE e.status != ?
       GROUP BY e.id
       ORDER BY e.examDate DESC`,
      ['CANCELLED']
    );

    return c.json({
      success: true,
      data: { exams }
    });

  } catch (error) {
    console.error('Get exams error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch exams'
    }, 500);
  }
});

/**
 * POST /api/coaching/exams
 * Create a new exam
 */
coaching.post('/exams', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    const body = await c.req.json();

    const examSchema = z.object({
      name: z.string().min(1, 'Exam name is required'),
      description: z.string().optional(),
      examDate: z.string().min(1, 'Exam date is required'),
      duration: z.number().min(1, 'Duration is required'),
      totalMarks: z.number().min(1, 'Total marks is required'),
      passingMarks: z.number().min(1, 'Passing marks is required'),
      examType: z.enum(['WRITTEN', 'ORAL', 'PRACTICAL', 'ONLINE']).optional(),
    });

    const validatedData = examSchema.parse(body);

    // Generate ID
    const examId = `exam_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create exam
    await executeTenantQuery(
      tenantSlug,
      `INSERT INTO exams (id, name, description, examDate, duration, totalMarks, passingMarks, examType, status, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [examId, validatedData.name, validatedData.description || null, validatedData.examDate,
       validatedData.duration, validatedData.totalMarks, validatedData.passingMarks,
       validatedData.examType || 'WRITTEN', 'SCHEDULED']
    );

    return c.json({
      success: true,
      message: 'Exam created successfully',
      data: { examId }
    }, 201);

  } catch (error) {
    console.error('Create exam error:', error);
    return c.json({
      success: false,
      message: 'Failed to create exam'
    }, 500);
  }
});

/**
 * POST /api/coaching/exams/:examId/results
 * Add exam results
 */
coaching.post('/exams/:examId/results', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    const examId = c.req.param('examId');
    const body = await c.req.json();

    const resultSchema = z.object({
      studentId: z.string().min(1, 'Student ID is required'),
      obtainedMarks: z.number().min(0, 'Obtained marks must be positive'),
      isAbsent: z.boolean().optional(),
    });

    const validatedData = resultSchema.parse(body);

    // Get exam details for calculation
    const exam = await executeTenantQuery(
      tenantSlug,
      'SELECT totalMarks, passingMarks FROM exams WHERE id = ?',
      [examId]
    );

    if (exam.length === 0) {
      return c.json({
        success: false,
        message: 'Exam not found'
      }, 404);
    }

    const totalMarks = exam[0].totalMarks;
    const percentage = (validatedData.obtainedMarks / totalMarks) * 100;
    const grade = percentage >= 90 ? 'A+' : percentage >= 80 ? 'A' : percentage >= 70 ? 'B' : percentage >= 60 ? 'C' : percentage >= 50 ? 'D' : 'F';

    // Generate ID
    const resultId = `result_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create result
    await executeTenantQuery(
      tenantSlug,
      `INSERT INTO exam_results (id, examId, studentId, marksObtained, grade, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
      [resultId, examId, validatedData.studentId, validatedData.obtainedMarks, grade]
    );

    return c.json({
      success: true,
      message: 'Result added successfully',
      data: { resultId, percentage, grade }
    }, 201);

  } catch (error) {
    console.error('Add exam result error:', error);
    return c.json({
      success: false,
      message: 'Failed to add result'
    }, 500);
  }
});

// ============================================================================
// ASSET MANAGEMENT (INVENTORY)
// ============================================================================

/**
 * GET /api/coaching/inventory
 * Get all inventory items
 */
coaching.get('/inventory', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    const inventory = await executeTenantQuery(
      tenantSlug,
      `SELECT i.*, b.branchName
       FROM inventory_items i
       LEFT JOIN branches b ON i.branchId = b.id
       WHERE i.status = ?
       ORDER BY i.createdAt DESC`,
      ['ACTIVE']
    );

    return c.json({
      success: true,
      data: { inventory }
    });

  } catch (error) {
    console.error('Get inventory error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch inventory'
    }, 500);
  }
});

/**
 * POST /api/coaching/inventory
 * Add new inventory item
 */
coaching.post('/inventory', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    const body = await c.req.json();

    const inventorySchema = z.object({
      name: z.string().min(1, 'Item name is required'),
      category: z.string().min(1, 'Category is required'),
      quantity: z.number().min(0, 'Quantity must be positive'),
      unitPrice: z.number().min(0, 'Unit price must be positive'),
      branchId: z.string().optional(),
      vendorId: z.string().optional(),
      description: z.string().optional(),
    });

    const validatedData = inventorySchema.parse(body);

    // Generate ID
    const itemId = `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create inventory item
    await executeTenantQuery(
      tenantSlug,
      `INSERT INTO inventory_items (id, name, category, quantity, unitPrice, totalValue, branchId, description, status, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [itemId, validatedData.name, validatedData.category, validatedData.quantity,
       validatedData.unitPrice, validatedData.quantity * validatedData.unitPrice,
       validatedData.branchId || null, validatedData.description || null, 'ACTIVE']
    );

    return c.json({
      success: true,
      message: 'Inventory item added successfully',
      data: { itemId }
    }, 201);

  } catch (error) {
    console.error('Add inventory error:', error);
    return c.json({
      success: false,
      message: 'Failed to add inventory item'
    }, 500);
  }
});

// ============================================================================
// EXPENSE MANAGEMENT
// ============================================================================

/**
 * GET /api/coaching/expenses
 * Get all expenses
 */
coaching.get('/expenses', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;

    const expenses = await executeTenantQuery(
      tenantSlug,
      `SELECT e.*, b.branchName, et.name as expenseTypeName
       FROM expenses e
       LEFT JOIN branches b ON e.branchId = b.id
       LEFT JOIN expenseTypes et ON e.expenseTypeId = et.id
       WHERE e.status != ?
       ORDER BY e.expenseDate DESC
       LIMIT ${limit} OFFSET ${offset}`,
      ['DELETED']
    );

    const totalResult = await executeTenantQuery(
      tenantSlug,
      `SELECT COUNT(*) as count FROM expenses WHERE status != ?`,
      ['DELETED']
    );

    return c.json({
      success: true,
      data: {
        expenses,
        pagination: {
          page,
          limit,
          total: totalResult[0]?.count || 0,
          totalPages: Math.ceil((totalResult[0]?.count || 0) / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get expenses error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch expenses'
    }, 500);
  }
});

/**
 * POST /api/coaching/expenses
 * Create new expense
 */
coaching.post('/expenses', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;
    const user = c.get('user');
    const body = await c.req.json();

    const expenseSchema = z.object({
      title: z.string().min(1, 'Title is required'),
      description: z.string().optional(),
      amount: z.number().min(0, 'Amount must be positive'),
      expenseDate: z.string().min(1, 'Expense date is required'),
      expenseTypeId: z.string().min(1, 'Expense type is required'),
      branchId: z.string().optional(),
      paymentMethod: z.enum(['CASH', 'CARD', 'BANK_TRANSFER', 'UPI', 'CHEQUE', 'ONLINE']),
      vendorName: z.string().optional(),
      receiptNumber: z.string().optional(),
    });

    const validatedData = expenseSchema.parse(body);

    // Generate ID
    const expenseId = `expense_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create expense
    await executeTenantQuery(
      tenantSlug,
      `INSERT INTO expenses (id, title, description, amount, expenseDate, expenseTypeId, branchId, paymentMethod, vendorName, receiptNumber, status, createdBy, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [expenseId, validatedData.title, validatedData.description || null, validatedData.amount,
       validatedData.expenseDate, validatedData.expenseTypeId, validatedData.branchId || null,
       validatedData.paymentMethod, validatedData.vendorName || null, validatedData.receiptNumber || null,
       'PENDING', user.id]
    );

    return c.json({
      success: true,
      message: 'Expense created successfully',
      data: { expenseId }
    }, 201);

  } catch (error) {
    console.error('Create expense error:', error);
    return c.json({
      success: false,
      message: 'Failed to create expense'
    }, 500);
  }
});

/**
 * GET /api/coaching/expense-types
 * Get all expense types
 */
coaching.get('/expense-types', async (c) => {
  try {
    const tenantInfo = getTenantInfo(c);
    const tenantSlug = tenantInfo.tenantSlug;

    const expenseTypes = await executeTenantQuery(
      tenantSlug,
      `SELECT * FROM expenseTypes WHERE status = ? ORDER BY name`,
      ['ACTIVE']
    );

    return c.json({
      success: true,
      data: { expenseTypes }
    });

  } catch (error) {
    console.error('Get expense types error:', error);
    return c.json({
      success: false,
      message: 'Failed to fetch expense types'
    }, 500);
  }
});

export default coaching;
