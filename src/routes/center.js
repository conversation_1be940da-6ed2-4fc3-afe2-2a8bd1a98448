import { Hono } from 'hono';
import { authMiddleware } from '../middleware/auth.js';
import { requireTenant } from '../middleware/tenant.js';
import { executeQuery } from '../config/database.js';
import { requirePermission, requireTenantAccess, requireUserManagement } from '../middleware/permissions.js';
import branchRoutes from './branches.js';

const center = new Hono();

// All center routes require tenant and authentication
center.use('*', requireTenant);
center.use('*', authMiddleware);
center.use('*', requireTenantAccess());

/**
 * GET /api/center/dashboard
 * Center dashboard with statistics
 */
center.get('/dashboard', async (c) => {
  try {
    const tenantId = c.get('getTenantId')();
    
    // Get center statistics
    const [
      studentStats,
      teacherStats,
      branchStats,
      attendanceStats
    ] = await Promise.all([
      executeQuery(
        'SELECT COUNT(*) as total, status FROM students WHERE tenant_id = ? GROUP BY status',
        [tenantId]
      ),
      executeQuery(
        'SELECT COUNT(*) as total, status FROM teachers WHERE tenant_id = ? GROUP BY status',
        [tenantId]
      ),
      executeQuery(
        'SELECT COUNT(*) as total FROM branches WHERE tenant_id = ?',
        [tenantId]
      ),
      executeQuery(`
        SELECT 
          COUNT(*) as total_records,
          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,
          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
        FROM attendance 
        WHERE tenant_id = ? AND date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      `, [tenantId])
    ]);
    
    return c.json({
      message: 'Center dashboard data',
      data: {
        students: studentStats,
        teachers: teacherStats,
        branches: branchStats[0]?.total || 0,
        attendance: attendanceStats[0] || { total_records: 0, present_count: 0, absent_count: 0 }
      }
    });
    
  } catch (error) {
    console.error('Center dashboard error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to fetch dashboard data'
    }, 500);
  }
});

/**
 * GET /api/center/profile
 * Get center profile information
 */
center.get('/profile', async (c) => {
  try {
    const tenant = c.get('tenant');
    
    return c.json({
      message: 'Center profile retrieved successfully',
      data: {
        id: tenant.id,
        name: tenant.name,
        subdomain: tenant.subdomain,
        logoUrl: tenant.logoUrl,
        themeColor: tenant.themeColor,
        status: tenant.status,
        subscription: tenant.subscription
      }
    });
    
  } catch (error) {
    console.error('Center profile error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to fetch center profile'
    }, 500);
  }
});

/**
 * PUT /api/center/profile
 * Update center profile
 */
center.put('/profile', async (c) => {
  try {
    const tenantId = c.get('getTenantId')();
    const userRole = c.get('userRole');
    
    // Only center admin can update profile
    if (!['admin', 'center_admin'].includes(userRole)) {
      return c.json({
        error: 'Forbidden',
        message: 'Only center administrators can update profile'
      }, 403);
    }
    
    const {
      name,
      logoUrl,
      themeColor,
      phone,
      address,
      website
    } = await c.req.json();
    
    // Build update query dynamically
    const updateFields = [];
    const updateValues = [];
    
    if (name) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    
    if (logoUrl) {
      updateFields.push('logo_url = ?');
      updateValues.push(logoUrl);
    }
    
    if (themeColor) {
      updateFields.push('theme_color = ?');
      updateValues.push(themeColor);
    }
    
    if (phone) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }
    
    if (address) {
      updateFields.push('address = ?');
      updateValues.push(address);
    }
    
    if (website) {
      updateFields.push('website = ?');
      updateValues.push(website);
    }
    
    if (updateFields.length === 0) {
      return c.json({
        error: 'Validation Error',
        message: 'No valid fields to update'
      }, 400);
    }
    
    updateFields.push('updated_at = NOW()');
    updateValues.push(tenantId);
    
    const updateQuery = `UPDATE centers SET ${updateFields.join(', ')} WHERE id = ?`;
    
    const result = await executeQuery(updateQuery, updateValues);
    
    if (result.affectedRows === 0) {
      return c.json({
        error: 'Not Found',
        message: 'Center not found'
      }, 404);
    }
    
    return c.json({
      message: 'Center profile updated successfully'
    });
    
  } catch (error) {
    console.error('Center profile update error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to update center profile'
    }, 500);
  }
});

/**
 * GET /api/center/subscription
 * Get subscription details
 */
center.get('/subscription', async (c) => {
  try {
    const tenant = c.get('tenant');
    
    if (!tenant.subscription) {
      return c.json({
        error: 'No Subscription',
        message: 'No active subscription found'
      }, 404);
    }
    
    return c.json({
      message: 'Subscription details retrieved successfully',
      data: tenant.subscription
    });
    
  } catch (error) {
    console.error('Center subscription error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to fetch subscription details'
    }, 500);
  }
});

/**
 * GET /api/center/features
 * Get available features for current plan
 */
center.get('/features', async (c) => {
  try {
    const features = c.get('getTenantFeatures')();
    const isSubscriptionActive = c.get('isSubscriptionActive')();
    
    return c.json({
      message: 'Features retrieved successfully',
      data: {
        features,
        isActive: isSubscriptionActive,
        hasFeature: (feature) => features.includes(feature)
      }
    });
    
  } catch (error) {
    console.error('Center features error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to fetch features'
    }, 500);
  }
});

/**
 * Student Management Routes
 */

/**
 * GET /api/center/students
 * Get all students for the center
 */
center.get('/students', requirePermission('center.students.view'), async (c) => {
  try {
    const tenant = c.get('tenant');
    const { page = 1, limit = 50, status = 'active' } = c.req.query();

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const studentsQuery = `
      SELECT
        u.id,
        u.name,
        u.email,
        u.phone,
        u.status,
        u.created_at,
        0 as enrolled_courses
      FROM users u
      WHERE u.tenant_id = ? AND u.role = 'student' AND u.status = ?
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const students = await executeQuery(studentsQuery, [tenant.id, status, parseInt(limit), offset]);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users
      WHERE tenant_id = ? AND role = 'student' AND status = ?
    `;

    const countResult = await executeQuery(countQuery, [tenant.id, status]);
    const total = countResult[0].total;

    return c.json({
      success: true,
      data: {
        students,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get students error:', error);
    return c.json({ success: false, message: 'Failed to fetch students' }, 500);
  }
});

/**
 * Course Management Routes
 */

/**
 * GET /api/center/courses
 * Get all courses for the center
 */
center.get('/courses', requirePermission('center.courses.view'), async (c) => {
  try {
    const tenant = c.get('tenant');
    const { page = 1, limit = 50, status = 'active' } = c.req.query();

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const coursesQuery = `
      SELECT
        c.id,
        c.name,
        c.description,
        c.duration_months,
        c.fee_monthly,
        c.status,
        c.created_at,
        0 as enrolled_students,
        0 as assigned_teachers
      FROM courses c
      WHERE c.tenant_id = ? AND c.status = ?
      ORDER BY c.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const courses = await executeQuery(coursesQuery, [tenant.id, status, parseInt(limit), offset]);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM courses
      WHERE tenant_id = ? AND status = ?
    `;

    const countResult = await executeQuery(countQuery, [tenant.id, status]);
    const total = countResult[0].total;

    return c.json({
      success: true,
      data: {
        courses,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get courses error:', error);
    return c.json({ success: false, message: 'Failed to fetch courses' }, 500);
  }
});

/**
 * POST /api/center/courses
 * Create a new course (restricted to center_owner and manager)
 */
center.post('/courses', requirePermission('center.courses.create'), async (c) => {
  try {
    const tenant = c.get('tenant');
    const { name, description, duration_months, fee_monthly } = await c.req.json();

    if (!name || !description) {
      return c.json({
        success: false,
        message: 'Name and description are required'
      }, 400);
    }

    const createCourseQuery = `
      INSERT INTO courses (name, description, duration_months, fee_monthly, tenant_id, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())
    `;

    const result = await executeQuery(createCourseQuery, [
      name, description, duration_months || 12, fee_monthly || 0, tenant.id
    ]);

    return c.json({
      success: true,
      message: 'Course created successfully',
      data: { course_id: result.insertId }
    }, 201);

  } catch (error) {
    console.error('Create course error:', error);
    return c.json({ success: false, message: 'Failed to create course' }, 500);
  }
});

/**
 * Staff Management Routes
 */

/**
 * GET /api/center/staff
 * Get all staff members for the center
 */
center.get('/staff', requirePermission('center.staff.view'), async (c) => {
  try {
    const tenant = c.get('tenant');
    const { page = 1, limit = 50, role } = c.req.query();

    const offset = (parseInt(page) - 1) * parseInt(limit);

    let whereClause = 'u.tenant_id = ? AND u.role IN (?, ?, ?) AND u.status = ?';
    let queryParams = [tenant.id, 'center_owner', 'manager', 'teacher', 'active'];

    if (role && ['center_owner', 'manager', 'teacher'].includes(role)) {
      whereClause = 'u.tenant_id = ? AND u.role = ? AND u.status = ?';
      queryParams = [tenant.id, role, 'active'];
    }

    const staffQuery = `
      SELECT
        u.id,
        u.name,
        u.email,
        u.phone,
        u.role,
        u.status,
        u.created_at,
        0 as assigned_courses
      FROM users u
      WHERE ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(parseInt(limit), offset);

    const staff = await executeQuery(staffQuery, queryParams);

    return c.json({
      success: true,
      data: { staff }
    });

  } catch (error) {
    console.error('Get staff error:', error);
    return c.json({ success: false, message: 'Failed to fetch staff' }, 500);
  }
});

/**
 * POST /api/center/staff
 * Create a new staff member
 */
center.post('/staff', authMiddleware, async (c) => {
  try {
    const user = c.get('user');
    const tenant = c.get('tenant');
    const data = await c.req.json();

    if (!tenant) {
      return c.json({
        success: false,
        message: 'Tenant not found'
      }, 400);
    }

    // Check permissions
    if (!['center_admin', 'admin', 'super_admin'].includes(user.role)) {
      return c.json({
        success: false,
        message: 'Insufficient permissions'
      }, 403);
    }

    // Validate required fields
    if (!data.name || !data.email || !data.role) {
      return c.json({
        success: false,
        message: 'Name, email, and role are required'
      }, 400);
    }

    // Check if email already exists
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE email = ?',
      [data.email]
    );

    if (existingUser.length > 0) {
      return c.json({
        success: false,
        message: 'Email already exists'
      }, 400);
    }

    // Generate a temporary password
    const tempPassword = 'TempPass123!';
    const bcrypt = await import('bcryptjs');
    const passwordHash = await bcrypt.hash(tempPassword, 10);

    const query = `
      INSERT INTO users (
        tenant_id, branch_id, email, password_hash, name, role,
        phone, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())
    `;

    const result = await executeQuery(query, [
      tenant.id,
      data.branch_id || null,
      data.email,
      passwordHash,
      data.name,
      data.role,
      data.phone || null
    ]);

    return c.json({
      success: true,
      message: 'Staff member created successfully',
      data: {
        id: result.insertId,
        name: data.name,
        email: data.email,
        tempPassword: tempPassword
      }
    }, 201);

  } catch (error) {
    console.error('Create staff error:', error);
    return c.json({
      success: false,
      message: 'Failed to create staff member'
    }, 500);
  }
});

// Branch management routes
center.route('/branches', branchRoutes);

export default center;
