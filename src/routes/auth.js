import { Hono } from 'hono';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { executeQuery } from '../config/database.js';
// import { strictRateLimit } from '../middleware/rateLimit.js';

// Disable rate limiting for development testing
const strictRateLimit = async (c, next) => {
  if (process.env.NODE_ENV === 'development') {
    await next();
  } else {
    const { strictRateLimit: actualRateLimit } = await import('../middleware/rateLimit.js');
    await actualRateLimit(c, next);
  }
};

const auth = new Hono();

/**
 * POST /api/auth/login
 * Super Admin login endpoint (root domain)
 */
auth.post('/login', strictRateLimit, async (c) => {
  try {
    const { email, password } = await c.req.json();
    console.log('🔐 Super Admin login attempt for:', email);

    if (!email || !password) {
      return c.json({
        error: 'Validation Error',
        message: 'Email and password are required'
      }, 400);
    }

    // Query super admin user only
    const userQuery = `
      SELECT
        u.id,
        u.email,
        u.name,
        u.password_hash,
        u.role,
        u.status,
        u.center_id,
        u.created_at
      FROM users u
      WHERE u.email = ? AND u.status = 'active' AND u.role = 'super_admin'
      LIMIT 1
    `;

    const queryParams = [email];
    
    const userResults = await executeQuery(userQuery, queryParams);
    
    if (userResults.length === 0) {
      return c.json({
        error: 'Authentication Failed',
        message: 'Invalid email or password'
      }, 401);
    }
    
    const user = userResults[0];
    
    // Check if user is active
    if (user.status !== 'active') {
      return c.json({
        error: 'Account Disabled',
        message: 'Your account has been disabled. Please contact support.'
      }, 403);
    }
    
    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    
    if (!isPasswordValid) {
      return c.json({
        error: 'Authentication Failed',
        message: 'Invalid email or password'
      }, 401);
    }
    
    // Ensure only super admin can login through this route
    if (user.role !== 'super_admin') {
      return c.json({
        error: 'Access Denied',
        message: 'This login is for super administrators only'
      }, 403);
    }

    // Generate JWT token
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      center_id: null
    };
    
    const token = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET || 'your-secret-key-for-development',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );
    
    // Update last login (if column exists)
    try {
      await executeQuery(
        'UPDATE users SET updated_at = NOW() WHERE id = ?',
        [user.id]
      );
    } catch (updateError) {
      console.log('Note: Could not update last login timestamp');
    }
    
    // Return success response
    return c.json({
      success: true,
      message: 'Super admin login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        center_id: null,
        center_slug: null,
        center_name: null
      }
    });
    
  } catch (error) {
    console.error('Login error:', error);
    console.error('Error stack:', error.stack);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to process login request',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, 500);
  }
});

/**
 * POST /api/auth/center-login
 * Center Admin/Teacher/Student login endpoint (subdomain)
 */
auth.post('/center-login', strictRateLimit, async (c) => {
  try {
    const { email, password, center_slug } = await c.req.json();
    console.log('🔐 Center login attempt for:', email, 'at center:', center_slug);

    if (!email || !password) {
      return c.json({
        error: 'Validation Error',
        message: 'Email and password are required'
      }, 400);
    }

    if (!center_slug) {
      return c.json({
        error: 'Validation Error',
        message: 'Center subdomain is required'
      }, 400);
    }

    // Query user with center information
    const userQuery = `
      SELECT
        u.id,
        u.email,
        u.name,
        u.password_hash,
        u.role,
        u.status,
        u.center_id,
        u.created_at,
        c.name as center_name,
        c.subdomain as center_slug
      FROM users u
      INNER JOIN centers c ON u.center_id = c.id
      WHERE u.email = ? AND u.status = 'active' AND c.subdomain = ?
      AND u.role IN ('center_admin', 'teacher', 'student')
      LIMIT 1
    `;

    const userResults = await executeQuery(userQuery, [email, center_slug]);

    if (userResults.length === 0) {
      return c.json({
        error: 'Authentication Failed',
        message: 'Invalid email, password, or center access'
      }, 401);
    }

    const user = userResults[0];

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);

    if (!isPasswordValid) {
      return c.json({
        error: 'Authentication Failed',
        message: 'Invalid email or password'
      }, 401);
    }

    // Generate JWT token
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      center_id: user.center_id,
      center_slug: user.center_slug
    };

    const token = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET || 'your-secret-key-for-development',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // Update last login (if column exists)
    try {
      await executeQuery(
        'UPDATE users SET updated_at = NOW() WHERE id = ?',
        [user.id]
      );
    } catch (updateError) {
      console.log('Note: Could not update last login timestamp');
    }

    // Return success response
    return c.json({
      success: true,
      message: 'Center login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        center_id: user.center_id,
        center_slug: user.center_slug,
        center_name: user.center_name
      }
    });

  } catch (error) {
    console.error('Center login error:', error);
    console.error('Error stack:', error.stack);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to process center login request',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, 500);
  }
});

/**
 * POST /api/auth/logout
 * User logout endpoint (client-side token removal)
 */
auth.post('/logout', async (c) => {
  return c.json({
    message: 'Logout successful'
  });
});

/**
 * POST /api/auth/forgot-password
 * Forgot password endpoint
 */
auth.post('/forgot-password', strictRateLimit, async (c) => {
  try {
    const { email } = await c.req.json();
    
    if (!email) {
      return c.json({
        error: 'Validation Error',
        message: 'Email is required'
      }, 400);
    }
    
    // Check if user exists
    const userResults = await executeQuery(
      'SELECT id, email, name FROM users WHERE email = ? AND status = "active" LIMIT 1',
      [email]
    );
    
    // Always return success to prevent email enumeration
    if (userResults.length === 0) {
      return c.json({
        message: 'If an account with that email exists, a password reset link has been sent.'
      });
    }
    
    const user = userResults[0];
    
    // Generate reset token
    const resetToken = jwt.sign(
      { userId: user.id, email: user.email, type: 'password_reset' },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
    
    // Store reset token in database
    await executeQuery(
      'UPDATE users SET reset_token = ?, reset_token_expires = DATE_ADD(NOW(), INTERVAL 1 HOUR) WHERE id = ?',
      [resetToken, user.id]
    );
    
    // TODO: Send email with reset link
    console.log(`Password reset token for ${email}: ${resetToken}`);
    
    return c.json({
      message: 'If an account with that email exists, a password reset link has been sent.'
    });
    
  } catch (error) {
    console.error('Forgot password error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to process password reset request'
    }, 500);
  }
});

/**
 * POST /api/auth/reset-password
 * Reset password endpoint
 */
auth.post('/reset-password', strictRateLimit, async (c) => {
  try {
    const { token, newPassword } = await c.req.json();
    
    if (!token || !newPassword) {
      return c.json({
        error: 'Validation Error',
        message: 'Token and new password are required'
      }, 400);
    }
    
    if (newPassword.length < 8) {
      return c.json({
        error: 'Validation Error',
        message: 'Password must be at least 8 characters long'
      }, 400);
    }
    
    // Verify reset token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'password_reset') {
      return c.json({
        error: 'Invalid Token',
        message: 'Invalid reset token'
      }, 400);
    }
    
    // Check if token exists and is not expired
    const userResults = await executeQuery(
      'SELECT id, email FROM users WHERE id = ? AND reset_token = ? AND reset_token_expires > NOW() LIMIT 1',
      [decoded.userId, token]
    );
    
    if (userResults.length === 0) {
      return c.json({
        error: 'Invalid Token',
        message: 'Invalid or expired reset token'
      }, 400);
    }
    
    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    // Update password and clear reset token
    await executeQuery(
      'UPDATE users SET password_hash = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?',
      [hashedPassword, decoded.userId]
    );
    
    return c.json({
      message: 'Password reset successful'
    });
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return c.json({
        error: 'Invalid Token',
        message: 'Invalid or expired reset token'
      }, 400);
    }
    
    console.error('Reset password error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to process password reset'
    }, 500);
  }
});

export default auth;
