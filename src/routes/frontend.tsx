import { Hono } from 'hono';
import { setCookie, getCookie } from 'hono/cookie';
import { executeQuery } from '../config/database.js';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { t, getTimeOfDay } from '../utils/i18n.js';
import { LoginPage } from '../components/LoginPage.tsx';
import { AdminDashboard, TenantDashboard } from '../components/Dashboard.tsx';
import { HomePage } from '../components/HomePage.tsx';

const frontend = new Hono();

/**
 * Frontend Routes
 * Handles server-side rendering with Hono JSX components and direct data fetching
 * No API calls - data is fetched directly on the server for SSR
 */

// Middleware to get user from JWT token
async function getUserFromToken(c) {
  try {
    const token = getCookie(c, 'auth_token');
    if (!token) return null;

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    const user = await executeQuery(
      'SELECT * FROM users WHERE id = ? AND status = "active"',
      [decoded.userId]
    );

    return user[0] || null;
  } catch (error) {
    console.error('Token verification error:', error);
    return null;
  }
}

// Login page (GET)
frontend.get('/login', async (c) => {
  try {
    // Get language and theme from context
    const language = c.get('language') || 'en';
    const theme = c.get('theme') || 'light';

    // Check if user is already logged in
    const user = await getUserFromToken(c);
    if (user) {
      const redirectUrl = user.role === 'super_admin' ? '/admin/dashboard' : '/dashboard';
      return c.redirect(redirectUrl);
    }

    const tenant = c.get('tenant');
    const error = c.req.query('error');
    const success = c.req.query('success');

    return c.html(
      <LoginPage 
        tenant={tenant} 
        error={error} 
        success={success}
        language={language} 
        theme={theme} 
      />
    );
  } catch (error) {
    console.error('Login page render error:', error);
    return c.text('Internal Server Error', 500);
  }
});

// Login form submission (POST)
frontend.post('/login', async (c) => {
  try {
    const tenant = c.get('tenant');
    const { email, password } = await c.req.json();

    if (!email || !password) {
      return c.redirect('/login?error=Email and password are required');
    }

    // Find user by email
    const users = await executeQuery(
      'SELECT * FROM users WHERE email = ? AND status = "active"',
      [email]
    );

    if (users.length === 0) {
      return c.redirect('/login?error=Invalid credentials');
    }

    const user = users[0];

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return c.redirect('/login?error=Invalid credentials');
    }

    // Check tenant access for non-super-admin users
    if (user.role !== 'super_admin' && tenant && user.tenant_id !== tenant.id) {
      return c.redirect('/login?error=Access denied for this tenant');
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email, role: user.role },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // Set auth cookie
    setCookie(c, 'auth_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 // 24 hours
    });

    const redirectUrl = user.role === 'super_admin' ? '/admin/dashboard' : '/dashboard';
    return c.redirect(redirectUrl);

  } catch (error) {
    console.error('Login error:', error);
    return c.redirect('/login?error=Login failed');
  }
});

// Logout
frontend.post('/logout', async (c) => {
  setCookie(c, 'auth_token', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 0
  });
  return c.redirect('/login');
});

// Dashboard page
frontend.get('/dashboard', async (c) => {
  try {
    const language = c.get('language') || 'en';
    const theme = c.get('theme') || 'light';
    const user = await getUserFromToken(c);
    const tenant = c.get('tenant');

    if (!user) {
      return c.redirect('/login');
    }

    // Fetch dashboard statistics directly from database
    const stats = await getDashboardStats(user, tenant);
    const recentActivity = await getRecentActivity(tenant);

    return c.html(
      <TenantDashboard 
        user={user}
        tenant={tenant}
        stats={stats}
        recentActivity={recentActivity}
        language={language}
        theme={theme}
      />
    );
  } catch (error) {
    console.error('Dashboard page render error:', error);
    return c.text('Internal Server Error', 500);
  }
});

// Admin dashboard
frontend.get('/admin/dashboard', async (c) => {
  try {
    const language = c.get('language') || 'en';
    const theme = c.get('theme') || 'light';
    const user = await getUserFromToken(c);

    if (!user || user.role !== 'super_admin') {
      return c.redirect('/login');
    }

    // Fetch admin statistics directly from database
    const stats = await getAdminStats();
    const recentActivity = await getRecentActivity();

    return c.html(
      <AdminDashboard 
        user={user}
        tenant={null}
        stats={stats}
        recentActivity={recentActivity}
        language={language}
        theme={theme}
      />
    );
  } catch (error) {
    console.error('Admin dashboard render error:', error);
    return c.text('Internal Server Error', 500);
  }
});

// Homepage
frontend.get('/', async (c) => {
  try {
    const language = c.get('language') || 'en';
    const theme = c.get('theme') || 'light';
    const tenant = c.get('tenant');
    const user = await getUserFromToken(c);

    // If user is logged in, redirect to dashboard
    if (user) {
      if (user.role === 'super_admin') {
        return c.redirect('/admin/dashboard');
      } else {
        return c.redirect('/dashboard');
      }
    }

    // Show homepage for non-logged-in users
    return c.html(
      <HomePage
        tenant={tenant}
        theme={theme}
        language={language}
      />
    );
  } catch (error) {
    console.error('Homepage render error:', error);
    return c.text('Internal Server Error', 500);
  }
});

/**
 * Helper function to get dashboard statistics from database
 */
async function getDashboardStats(user, tenant) {
  try {
    switch (user.role) {
      case 'super_admin':
        return await getAdminStats();

      case 'center_admin':
      case 'admin':
        const centerStatsQuery = `
          SELECT
            (SELECT COUNT(*) FROM users WHERE tenant_id = ? AND role = 'student' AND status = 'active') as totalStudents,
            (SELECT COUNT(*) FROM courses WHERE tenant_id = ? AND status = 'active') as activeCourses,
            (SELECT COUNT(*) FROM users WHERE tenant_id = ? AND role IN ('teacher', 'admin') AND status = 'active') as staffMembers,
            (SELECT COALESCE(SUM(amount), 0) FROM payments WHERE tenant_id = ? AND status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())) as monthlyRevenue
        `;
        
        const centerStats = await executeQuery(centerStatsQuery, [tenant?.id, tenant?.id, tenant?.id, tenant?.id]);
        return centerStats[0] || {};

      default:
        return {};
    }
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return {};
  }
}

/**
 * Helper function to get admin statistics from database
 */
async function getAdminStats() {
  try {
    const adminStatsQuery = `
      SELECT
        (SELECT COUNT(*) FROM centers WHERE status IN ('active', 'trial')) as totalCenters,
        (SELECT COUNT(*) FROM subscriptions WHERE status = 'active') as activeSubscriptions,
        (SELECT COUNT(*) FROM users WHERE status = 'active') as totalUsers,
        (SELECT COUNT(*) FROM centers WHERE status = 'active') as activeCenters,
        (SELECT COALESCE(SUM(amount), 0) FROM payments WHERE status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())) as monthlyRevenue
    `;
    
    const adminStats = await executeQuery(adminStatsQuery);
    return adminStats[0] || {
      totalCenters: 0,
      activeSubscriptions: 0,
      totalUsers: 0,
      activeCenters: 0,
      monthlyRevenue: 0
    };
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return {
      totalCenters: 0,
      activeSubscriptions: 0,
      totalUsers: 0,
      activeCenters: 0,
      monthlyRevenue: 0
    };
  }
}

/**
 * Helper function to get recent activity from database
 */
async function getRecentActivity(tenant = null) {
  try {
    let query = `
      SELECT 
        'New student enrolled' as title,
        CONCAT(u.name, ' enrolled in Mathematics course') as description,
        DATE_FORMAT(u.created_at, '%M %d, %Y at %h:%i %p') as time
      FROM users u 
      WHERE u.role = 'student' 
      ${tenant ? 'AND u.tenant_id = ?' : ''}
      ORDER BY u.created_at DESC 
      LIMIT 5
    `;
    
    const params = tenant ? [tenant.id] : [];
    const activities = await executeQuery(query, params);
    
    return activities || [];
  } catch (error) {
    console.error('Error fetching recent activity:', error);
    return [
      {
        title: 'New student enrolled',
        description: 'John Doe enrolled in Mathematics course',
        time: '2 hours ago'
      },
      {
        title: 'Payment received',
        description: 'Monthly subscription payment processed',
        time: '4 hours ago'
      },
      {
        title: 'Class schedule updated',
        description: 'Physics class rescheduled for tomorrow',
        time: '1 day ago'
      }
    ];
  }
}

export { frontend };
