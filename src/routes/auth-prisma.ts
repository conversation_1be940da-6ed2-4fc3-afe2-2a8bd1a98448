import { Hono } from 'hono';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { getCentralPrisma } from '../config/prisma.js';
import { getTenantPrismaClient } from '../utils/tenant-database.js';

const auth = new Hono();

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
  loginType: z.enum(['super_admin', 'center_admin']).optional().default('center_admin'),
});

const JWT_SECRET = process.env.JWT_SECRET || 'default-secret';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

/**
 * POST /api/auth/login
 * Login for both super admin and center admin
 */
auth.post('/login', async (c) => {
  try {
    const body = await c.req.json();
    const { email, password, loginType } = loginSchema.parse(body);

    if (loginType === 'super_admin') {
      // Super admin login (central database)
      const centralPrisma = getCentralPrisma();
      const user = await centralPrisma.centerUser.findFirst({
        where: {
          email,
          role: 'CENTER_ADMIN', // For now, we'll use CENTER_ADMIN as super admin
          status: 'ACTIVE'
        }
      });

      if (!user) {
        return c.json({
          success: false,
          message: 'Invalid credentials'
        }, 401);
      }

      const isValidPassword = await bcrypt.compare(password, user.passwordHash);
      if (!isValidPassword) {
        return c.json({
          success: false,
          message: 'Invalid credentials'
        }, 401);
      }

      // Generate JWT token
      const token = jwt.sign(
        {
          userId: user.id,
          email: user.email,
          role: 'super_admin',
          centerId: user.centerId,
          loginType: 'super_admin'
        },
        JWT_SECRET,
        { expiresIn: JWT_EXPIRES_IN }
      );

      // Update last login
      await centralPrisma.centerUser.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() }
      });

      return c.json({
        success: true,
        message: 'Login successful',
        data: {
          token,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: 'super_admin',
            centerId: user.centerId,
          }
        }
      });

    } else {
      // Center admin login (tenant database)
      // Get tenant slug from subdomain or header
      let tenantSlug = c.req.header('x-tenant-slug');
      
      if (!tenantSlug) {
        const host = c.req.header('host') || '';
        const parts = host.split('.');
        if (parts.length >= 2) {
          tenantSlug = parts[0].split(':')[0];
        }
      }

      if (!tenantSlug) {
        return c.json({
          success: false,
          message: 'Tenant not specified. Please use subdomain or x-tenant-slug header.'
        }, 400);
      }

      // Verify tenant exists and is active
      const centralPrisma = getCentralPrisma();
      const center = await centralPrisma.center.findUnique({
        where: {
          subdomain: tenantSlug,
          status: { in: ['ACTIVE', 'TRIAL'] }
        }
      });

      if (!center) {
        return c.json({
          success: false,
          message: 'Coaching center not found or inactive'
        }, 404);
      }

      // Get tenant database
      try {
        const tenantPrisma = getTenantPrismaClient(tenantSlug);
        
        const user = await tenantPrisma.user.findUnique({
          where: {
            email,
            status: 'ACTIVE'
          },
          include: {
            branch: true
          }
        });

        if (!user) {
          return c.json({
            success: false,
            message: 'Invalid credentials'
          }, 401);
        }

        const isValidPassword = await bcrypt.compare(password, user.passwordHash);
        if (!isValidPassword) {
          return c.json({
            success: false,
            message: 'Invalid credentials'
          }, 401);
        }

        // Generate JWT token
        const token = jwt.sign(
          {
            userId: user.id,
            email: user.email,
            role: user.role.toLowerCase(),
            centerId: center.id,
            tenantSlug: tenantSlug,
            branchId: user.branchId,
            loginType: 'center_admin'
          },
          JWT_SECRET,
          { expiresIn: JWT_EXPIRES_IN }
        );

        return c.json({
          success: true,
          message: 'Login successful',
          data: {
            token,
            user: {
              id: user.id,
              name: user.name,
              email: user.email,
              role: user.role.toLowerCase(),
              centerId: center.id,
              tenantSlug: tenantSlug,
              branchId: user.branchId,
              branch: user.branch,
            },
            center: {
              id: center.id,
              name: center.name,
              subdomain: center.subdomain,
            }
          }
        });

      } catch (error) {
        console.error(`Failed to connect to tenant database for ${tenantSlug}:`, error);
        return c.json({
          success: false,
          message: 'Unable to access coaching center data. Please contact support.'
        }, 503);
      }
    }

  } catch (error) {
    console.error('Login error:', error);
    
    if (error instanceof z.ZodError) {
      return c.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, 400);
    }
    
    return c.json({
      success: false,
      message: 'Login failed'
    }, 500);
  }
});

/**
 * POST /api/auth/logout
 * Logout user
 */
auth.post('/logout', async (c) => {
  // For JWT-based auth, logout is handled client-side by removing the token
  return c.json({
    success: true,
    message: 'Logout successful'
  });
});

/**
 * GET /api/auth/me
 * Get current user info
 */
auth.get('/me', async (c) => {
  try {
    const authHeader = c.req.header('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        message: 'No token provided'
      }, 401);
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, JWT_SECRET) as any;

    if (decoded.loginType === 'super_admin') {
      // Get user from central database
      const centralPrisma = getCentralPrisma();
      const user = await centralPrisma.centerUser.findUnique({
        where: { id: decoded.userId },
        include: {
          center: true
        }
      });

      if (!user) {
        return c.json({
          success: false,
          message: 'User not found'
        }, 404);
      }

      return c.json({
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: 'super_admin',
            centerId: user.centerId,
          },
          center: user.center
        }
      });

    } else {
      // Get user from tenant database
      const tenantPrisma = getTenantPrismaClient(decoded.tenantSlug);
      
      const user = await tenantPrisma.user.findUnique({
        where: { id: decoded.userId },
        include: {
          branch: true
        }
      });

      if (!user) {
        return c.json({
          success: false,
          message: 'User not found'
        }, 404);
      }

      const centralPrisma = getCentralPrisma();
      const center = await centralPrisma.center.findUnique({
        where: { id: decoded.centerId }
      });

      return c.json({
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role.toLowerCase(),
            centerId: decoded.centerId,
            tenantSlug: decoded.tenantSlug,
            branchId: user.branchId,
            branch: user.branch,
          },
          center
        }
      });
    }

  } catch (error) {
    console.error('Get user info error:', error);
    return c.json({
      success: false,
      message: 'Invalid token'
    }, 401);
  }
});

export default auth;
