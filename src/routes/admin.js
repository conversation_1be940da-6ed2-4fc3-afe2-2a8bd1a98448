import { Hono } from 'hono';
import { authMiddleware, requireSuperAdmin } from '../middleware/auth.js';
import { executeQuery } from '../config/database.js';
import bcrypt from 'bcryptjs';

const admin = new Hono();

// All admin routes require authentication
admin.use('*', authMiddleware);

/**
 * GET /api/admin/dashboard
 * Admin dashboard with system statistics
 */
admin.get('/dashboard', requireSuperAdmin, async (c) => {
  try {
    // Get system statistics
    const [
      centerStats,
      userStats,
      subscriptionStats,
      revenueStats
    ] = await Promise.all([
      executeQuery('SELECT COUNT(*) as total, status FROM centers GROUP BY status'),
      executeQuery('SELECT COUNT(*) as total, role FROM users GROUP BY role'),
      executeQuery('SELECT COUNT(*) as total, status FROM subscriptions GROUP BY status'),
      executeQuery(`
        SELECT 
          SUM(amount) as total_revenue,
          COUNT(*) as total_payments
        FROM payments 
        WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      `)
    ]);
    
    return c.json({
      message: 'Admin dashboard data',
      data: {
        centers: centerStats,
        users: userStats,
        subscriptions: subscriptionStats,
        revenue: revenueStats[0] || { total_revenue: 0, total_payments: 0 }
      }
    });
    
  } catch (error) {
    console.error('Admin dashboard error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to fetch dashboard data'
    }, 500);
  }
});

/**
 * GET /api/admin/dashboard-stats
 * Get dashboard statistics for super admin
 */
admin.get('/dashboard-stats', requireSuperAdmin, async (c) => {
  try {
    const statsQuery = `
      SELECT
        (SELECT COUNT(*) FROM centers WHERE status IN ('active', 'trial')) as totalCenters,
        (SELECT COUNT(*) FROM subscriptions WHERE status = 'active') as activeSubscriptions,
        (SELECT COUNT(*) FROM users WHERE status = 'active') as totalUsers,
        (SELECT COUNT(*) FROM centers WHERE status = 'active') as activeCenters
    `;

    const stats = await executeQuery(statsQuery);

    return c.json({
      success: true,
      data: {
        totalCenters: stats[0]?.totalCenters || 0,
        activeSubscriptions: stats[0]?.activeSubscriptions || 0,
        totalUsers: stats[0]?.totalUsers || 0,
        activeCenters: stats[0]?.activeCenters || 0,
        monthlyRevenue: 0 // Would calculate from payments/subscriptions
      }
    });

  } catch (error) {
    console.error('Get admin stats error:', error);
    return c.json({ success: false, message: 'Failed to fetch admin statistics' }, 500);
  }
});

/**
 * User Management Routes
 */

/**
 * GET /api/admin/users
 * List all users with filtering and pagination
 */
admin.get('/users', requireSuperAdmin, async (c) => {
  try {
    const { role, tenant_id, status, page = 1, limit = 50 } = c.req.query();

    let whereConditions = [];
    let queryParams = [];

    if (role) {
      whereConditions.push('u.role = ?');
      queryParams.push(role);
    }

    if (tenant_id) {
      whereConditions.push('u.tenant_id = ?');
      queryParams.push(tenant_id);
    }

    if (status) {
      whereConditions.push('u.status = ?');
      queryParams.push(status);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const usersQuery = `
      SELECT
        u.id,
        u.name,
        u.email,
        u.role,
        u.status,
        u.tenant_id,
        u.created_at,
        u.updated_at,
        c.name as center_name,
        c.subdomain as center_subdomain
      FROM users u
      LEFT JOIN centers c ON u.tenant_id = c.id
      ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(parseInt(limit), offset);

    const users = await executeQuery(usersQuery, queryParams);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      ${whereClause}
    `;

    const countResult = await executeQuery(countQuery, queryParams.slice(0, -2));
    const total = countResult[0].total;

    return c.json({
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    return c.json({ success: false, message: 'Failed to fetch users' }, 500);
  }
});

/**
 * POST /api/admin/users
 * Create a new user
 */
admin.post('/users', requireSuperAdmin, async (c) => {
  try {
    const { name, email, password, role, tenant_id, phone, address } = await c.req.json();

    // Validate required fields
    if (!name || !email || !password || !role) {
      return c.json({
        success: false,
        message: 'Name, email, password, and role are required'
      }, 400);
    }

    // Validate role
    const validRoles = ['super_admin', 'center_admin', 'admin', 'teacher', 'student'];
    if (!validRoles.includes(role)) {
      return c.json({
        success: false,
        message: 'Invalid role specified'
      }, 400);
    }

    // Check if email already exists
    const existingUserQuery = 'SELECT id FROM users WHERE email = ?';
    const existingUsers = await executeQuery(existingUserQuery, [email]);

    if (existingUsers.length > 0) {
      return c.json({
        success: false,
        message: 'Email already exists'
      }, 400);
    }

    // Validate tenant_id for non-super_admin roles
    if (role !== 'super_admin' && !tenant_id) {
      return c.json({
        success: false,
        message: 'Tenant ID is required for non-admin users'
      }, 400);
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create user
    const createUserQuery = `
      INSERT INTO users (name, email, password_hash, role, tenant_id, phone, address, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())
    `;

    const result = await executeQuery(createUserQuery, [
      name, email, passwordHash, role, tenant_id || null, phone || null, address || null
    ]);

    // Get the created user (without password)
    const userQuery = `
      SELECT
        u.id,
        u.name,
        u.email,
        u.role,
        u.status,
        u.tenant_id,
        u.phone,
        u.address,
        u.created_at,
        c.name as center_name
      FROM users u
      LEFT JOIN centers c ON u.tenant_id = c.id
      WHERE u.id = ?
    `;

    const users = await executeQuery(userQuery, [result.insertId]);

    return c.json({
      success: true,
      message: 'User created successfully',
      data: { user: users[0] }
    }, 201);

  } catch (error) {
    console.error('Create user error:', error);
    return c.json({ success: false, message: 'Failed to create user' }, 500);
  }
});

/**
 * GET /api/admin/centers
 * List all centers with pagination
 */
admin.get('/centers', requireSuperAdmin, async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const status = c.req.query('status');
    const search = c.req.query('search');
    
    const offset = (page - 1) * limit;
    
    let whereClause = '1=1';
    const queryParams = [];
    
    if (status) {
      whereClause += ' AND c.status = ?';
      queryParams.push(status);
    }
    
    if (search) {
      whereClause += ' AND (c.name LIKE ? OR c.subdomain LIKE ? OR c.email LIKE ?)';
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }
    
    const centersQuery = `
      SELECT 
        c.id,
        c.name,
        c.subdomain,
        c.email,
        c.phone,
        c.status,
        c.created_at,
        s.status as subscription_status,
        s.expires_at,
        p.name as plan_name
      FROM centers c
      LEFT JOIN subscriptions s ON c.id = s.tenant_id AND s.status = 'active'
      LEFT JOIN plans p ON s.plan_id = p.id
      WHERE ${whereClause}
      ORDER BY c.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const countQuery = `
      SELECT COUNT(*) as total
      FROM centers c
      WHERE ${whereClause}
    `;
    
    const [centers, countResult] = await Promise.all([
      executeQuery(centersQuery, [...queryParams, limit, offset]),
      executeQuery(countQuery, queryParams)
    ]);
    
    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);
    
    return c.json({
      message: 'Centers retrieved successfully',
      data: centers,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
    
  } catch (error) {
    console.error('Admin centers list error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to fetch centers'
    }, 500);
  }
});

/**
 * POST /api/admin/centers
 * Create new center
 */
admin.post('/centers', requireSuperAdmin, async (c) => {
  try {
    const {
      name,
      subdomain,
      email,
      phone,
      address,
      planId
    } = await c.req.json();
    
    // Validation
    if (!name || !subdomain || !email || !planId) {
      return c.json({
        error: 'Validation Error',
        message: 'Name, subdomain, email, and plan ID are required'
      }, 400);
    }
    
    // Check if subdomain is already taken
    const existingCenter = await executeQuery(
      'SELECT id FROM centers WHERE subdomain = ? LIMIT 1',
      [subdomain]
    );
    
    if (existingCenter.length > 0) {
      return c.json({
        error: 'Validation Error',
        message: 'Subdomain is already taken'
      }, 400);
    }
    
    // Check if email is already taken
    const existingEmail = await executeQuery(
      'SELECT id FROM centers WHERE email = ? LIMIT 1',
      [email]
    );
    
    if (existingEmail.length > 0) {
      return c.json({
        error: 'Validation Error',
        message: 'Email is already registered'
      }, 400);
    }
    
    // Create center
    const centerResult = await executeQuery(
      `INSERT INTO centers (name, subdomain, email, phone, address, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [name, subdomain, email, phone || null, address || null]
    );
    
    const centerId = centerResult.insertId;
    
    // Create subscription
    const trialDays = parseInt(process.env.TRIAL_PERIOD_DAYS || '7');
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + trialDays);
    
    await executeQuery(
      `INSERT INTO subscriptions (tenant_id, plan_id, status, expires_at, auto_renew, created_at, updated_at)
       VALUES (?, ?, 'active', ?, 1, NOW(), NOW())`,
      [centerId, planId, expiresAt]
    );
    
    return c.json({
      message: 'Center created successfully',
      data: {
        id: centerId,
        name,
        subdomain,
        email,
        status: 'active'
      }
    }, 201);
    
  } catch (error) {
    console.error('Admin create center error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to create center'
    }, 500);
  }
});

/**
 * PUT /api/admin/centers/:id/status
 * Update center status (suspend/activate)
 */
admin.put('/centers/:id/status', requireSuperAdmin, async (c) => {
  try {
    const centerId = c.req.param('id');
    const { status } = await c.req.json();
    
    if (!['active', 'suspended', 'inactive'].includes(status)) {
      return c.json({
        error: 'Validation Error',
        message: 'Invalid status. Must be active, suspended, or inactive'
      }, 400);
    }
    
    const result = await executeQuery(
      'UPDATE centers SET status = ?, updated_at = NOW() WHERE id = ?',
      [status, centerId]
    );
    
    if (result.affectedRows === 0) {
      return c.json({
        error: 'Not Found',
        message: 'Center not found'
      }, 404);
    }
    
    return c.json({
      message: 'Center status updated successfully',
      data: { id: centerId, status }
    });
    
  } catch (error) {
    console.error('Admin update center status error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to update center status'
    }, 500);
  }
});

/**
 * GET /api/admin/plans
 * List all subscription plans
 */
admin.get('/plans', requireSuperAdmin, async (c) => {
  try {
    const plans = await executeQuery(
      'SELECT * FROM plans ORDER BY created_at DESC'
    );

    return c.json({
      message: 'Plans retrieved successfully',
      data: plans
    });

  } catch (error) {
    console.error('Admin plans list error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to fetch plans'
    }, 500);
  }
});

/**
 * POST /api/admin/plans
 * Create new subscription plan
 */
admin.post('/plans', requireSuperAdmin, async (c) => {
  try {
    const {
      name,
      description,
      features,
      maxStudents,
      maxTeachers,
      maxBranches,
      priceMonthly,
      priceWeekly
    } = await c.req.json();

    // Validation
    if (!name || !description) {
      return c.json({
        error: 'Validation Error',
        message: 'Name and description are required'
      }, 400);
    }

    // Check if plan name already exists
    const existingPlan = await executeQuery(
      'SELECT id FROM plans WHERE name = ? LIMIT 1',
      [name]
    );

    if (existingPlan.length > 0) {
      return c.json({
        error: 'Validation Error',
        message: 'Plan name already exists'
      }, 400);
    }

    // Create plan
    const result = await executeQuery(
      `INSERT INTO plans (name, description, features, max_students, max_teachers, max_branches, price_monthly, price_weekly, is_active, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())`,
      [
        name,
        description,
        JSON.stringify(features || []),
        maxStudents || 0,
        maxTeachers || 0,
        maxBranches || 1,
        priceMonthly || 0.00,
        priceWeekly || 0.00
      ]
    );

    const planId = result.insertId;

    return c.json({
      message: 'Plan created successfully',
      data: {
        id: planId,
        name,
        description,
        features: features || [],
        maxStudents: maxStudents || 0,
        maxTeachers: maxTeachers || 0,
        maxBranches: maxBranches || 1,
        priceMonthly: priceMonthly || 0.00,
        priceWeekly: priceWeekly || 0.00,
        isActive: true
      }
    }, 201);

  } catch (error) {
    console.error('Admin create plan error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to create plan'
    }, 500);
  }
});

/**
 * PUT /api/admin/plans/:id
 * Update subscription plan
 */
admin.put('/plans/:id', requireSuperAdmin, async (c) => {
  try {
    const planId = c.req.param('id');
    const updateData = await c.req.json();

    // Build update query dynamically
    const updateFields = [];
    const updateValues = [];

    if (updateData.name) {
      updateFields.push('name = ?');
      updateValues.push(updateData.name);
    }

    if (updateData.description) {
      updateFields.push('description = ?');
      updateValues.push(updateData.description);
    }

    if (updateData.features) {
      updateFields.push('features = ?');
      updateValues.push(JSON.stringify(updateData.features));
    }

    if (updateData.maxStudents !== undefined) {
      updateFields.push('max_students = ?');
      updateValues.push(updateData.maxStudents);
    }

    if (updateData.maxTeachers !== undefined) {
      updateFields.push('max_teachers = ?');
      updateValues.push(updateData.maxTeachers);
    }

    if (updateData.maxBranches !== undefined) {
      updateFields.push('max_branches = ?');
      updateValues.push(updateData.maxBranches);
    }

    if (updateData.priceMonthly !== undefined) {
      updateFields.push('price_monthly = ?');
      updateValues.push(updateData.priceMonthly);
    }

    if (updateData.priceWeekly !== undefined) {
      updateFields.push('price_weekly = ?');
      updateValues.push(updateData.priceWeekly);
    }

    if (updateFields.length === 0) {
      return c.json({
        error: 'Validation Error',
        message: 'No valid fields to update'
      }, 400);
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(planId);

    const updateQuery = `UPDATE plans SET ${updateFields.join(', ')} WHERE id = ?`;

    const result = await executeQuery(updateQuery, updateValues);

    if (result.affectedRows === 0) {
      return c.json({
        error: 'Not Found',
        message: 'Plan not found'
      }, 404);
    }

    return c.json({
      message: 'Plan updated successfully',
      data: { id: planId, ...updateData }
    });

  } catch (error) {
    console.error('Admin update plan error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to update plan'
    }, 500);
  }
});

/**
 * DELETE /api/admin/plans/:id
 * Delete subscription plan (soft delete)
 */
admin.delete('/plans/:id', requireSuperAdmin, async (c) => {
  try {
    const planId = c.req.param('id');

    const result = await executeQuery(
      'UPDATE plans SET is_active = 0, updated_at = NOW() WHERE id = ?',
      [planId]
    );

    if (result.affectedRows === 0) {
      return c.json({
        error: 'Not Found',
        message: 'Plan not found'
      }, 404);
    }

    return c.json({
      message: 'Plan deleted successfully',
      data: { id: planId, isActive: false }
    });

  } catch (error) {
    console.error('Admin delete plan error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to delete plan'
    }, 500);
  }
});

/**
 * PUT /api/admin/plans/:id/status
 * Update plan status (activate/deactivate)
 */
admin.put('/plans/:id/status', requireSuperAdmin, async (c) => {
  try {
    const planId = c.req.param('id');
    const { isActive } = await c.req.json();

    if (typeof isActive !== 'boolean') {
      return c.json({
        error: 'Validation Error',
        message: 'isActive must be a boolean value'
      }, 400);
    }

    const result = await executeQuery(
      'UPDATE plans SET is_active = ?, updated_at = NOW() WHERE id = ?',
      [isActive ? 1 : 0, planId]
    );

    if (result.affectedRows === 0) {
      return c.json({
        error: 'Not Found',
        message: 'Plan not found'
      }, 404);
    }

    return c.json({
      message: 'Plan status updated successfully',
      data: { id: planId, isActive }
    });

  } catch (error) {
    console.error('Admin update plan status error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Unable to update plan status'
    }, 500);
  }
});

export default admin;
