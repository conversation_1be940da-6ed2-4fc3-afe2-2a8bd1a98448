import { Hono } from 'hono';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import { getCentralPrisma } from '../config/prisma.js';
import { createTenantDatabase } from '../utils/tenant-database.js';
import { authMiddleware, requireSuperAdmin } from '../middleware/auth-prisma.js';

const admin = new Hono();
const centralPrisma = getCentralPrisma();

// All admin routes require authentication
admin.use('*', authMiddleware);

// Validation schemas
const createCenterSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  subdomain: z.string().min(1, 'Subdomain is required').regex(/^[a-zA-Z0-9-]+$/, 'Invalid subdomain format'),
  email: z.string().email('Invalid email format'),
  phone: z.string().optional(),
  address: z.string().optional(),
  planId: z.string().optional(),
  adminName: z.string().min(1, 'Admin name is required'),
  adminEmail: z.string().email('Invalid admin email format'),
  adminPassword: z.string().min(6, 'Password must be at least 6 characters'),
});

/**
 * GET /api/admin/dashboard
 * Admin dashboard with system statistics
 */
admin.get('/dashboard', requireSuperAdmin, async (c) => {
  try {
    const [
      totalCenters,
      activeCenters,
      totalSubscriptions,
      activeSubscriptions,
      totalUsers,
      recentCenters
    ] = await Promise.all([
      centralPrisma.center.count(),
      centralPrisma.center.count({ where: { status: 'ACTIVE' } }),
      centralPrisma.subscription.count(),
      centralPrisma.subscription.count({ where: { status: 'ACTIVE' } }),
      centralPrisma.centerUser.count(),
      centralPrisma.center.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          subscriptions: {
            where: { status: 'ACTIVE' },
            include: { plan: true },
            take: 1
          },
          _count: {
            select: {
              centerUsers: true,
              subscriptions: true
            }
          }
        }
      })
    ]);

    return c.json({
      success: true,
      data: {
        stats: {
          totalCenters,
          activeCenters,
          totalSubscriptions,
          activeSubscriptions,
          totalUsers,
        },
        recentCenters
      }
    });

  } catch (error) {
    console.error('Get admin dashboard error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch dashboard data' 
    }, 500);
  }
});

/**
 * GET /api/admin/centers
 * List all centers with pagination
 */
admin.get('/centers', requireSuperAdmin, async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const status = c.req.query('status');
    const search = c.req.query('search');
    
    const skip = (page - 1) * limit;
    
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { subdomain: { contains: search } },
        { email: { contains: search } }
      ];
    }
    
    const [centers, total] = await Promise.all([
      centralPrisma.center.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          subscriptions: {
            where: { status: 'ACTIVE' },
            include: { plan: true },
            take: 1
          },
          _count: {
            select: {
              centerUsers: true
            }
          }
        }
      }),
      centralPrisma.center.count({ where })
    ]);
    
    return c.json({
      success: true,
      data: {
        centers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get centers error:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch centers' 
    }, 500);
  }
});

/**
 * POST /api/admin/centers
 * Create new coaching center with database provisioning
 */
admin.post('/centers', requireSuperAdmin, async (c) => {
  try {
    const body = await c.req.json();
    const data = createCenterSchema.parse(body);
    
    // Check if subdomain is already taken
    const existingCenter = await centralPrisma.center.findUnique({
      where: { subdomain: data.subdomain }
    });
    
    if (existingCenter) {
      return c.json({
        success: false,
        message: 'Subdomain is already taken'
      }, 400);
    }
    
    // Check if email is already taken
    const existingEmail = await centralPrisma.center.findUnique({
      where: { email: data.email }
    });
    
    if (existingEmail) {
      return c.json({
        success: false,
        message: 'Email is already registered'
      }, 400);
    }

    // Check if admin email is already taken
    const existingAdminEmail = await centralPrisma.centerUser.findFirst({
      where: { email: data.adminEmail }
    });
    
    if (existingAdminEmail) {
      return c.json({
        success: false,
        message: 'Admin email is already registered'
      }, 400);
    }

    // Start transaction
    const result = await centralPrisma.$transaction(async (tx) => {
      // Create center
      const center = await tx.center.create({
        data: {
          name: data.name,
          subdomain: data.subdomain,
          email: data.email,
          phone: data.phone,
          address: data.address,
          status: 'TRIAL',
          databaseName: `${data.subdomain}_coaching`,
          databaseCreated: false,
        }
      });

      // Create admin user in central database
      const hashedPassword = await bcrypt.hash(data.adminPassword, 12);
      const adminUser = await tx.centerUser.create({
        data: {
          centerId: center.id,
          name: data.adminName,
          email: data.adminEmail,
          passwordHash: hashedPassword,
          role: 'CENTER_ADMIN',
          status: 'ACTIVE',
        }
      });

      // Create default subscription plan if not provided
      let planId = data.planId;
      if (!planId) {
        const defaultPlan = await tx.subscriptionPlan.findFirst({
          where: { slug: 'trial' }
        });
        
        if (!defaultPlan) {
          // Create a default trial plan
          const newPlan = await tx.subscriptionPlan.create({
            data: {
              name: 'Trial Plan',
              slug: 'trial',
              description: 'Free trial plan',
              price: 0,
              currency: 'BDT',
              billingInterval: 'MONTHLY',
              trialDays: 7,
              maxStudents: 50,
              maxTeachers: 5,
              maxBranches: 1,
            }
          });
          planId = newPlan.id;
        } else {
          planId = defaultPlan.id;
        }
      }

      // Create subscription
      const trialEnd = new Date();
      trialEnd.setDate(trialEnd.getDate() + 7); // 7 days trial

      const subscription = await tx.subscription.create({
        data: {
          centerId: center.id,
          planId: planId,
          status: 'TRIAL',
          currentPeriodStart: new Date(),
          currentPeriodEnd: trialEnd,
          trialStart: new Date(),
          trialEnd: trialEnd,
        }
      });

      return { center, adminUser, subscription };
    });

    // Create tenant database
    try {
      console.log(`🔄 Creating tenant database for ${data.subdomain}...`);
      
      await createTenantDatabase(data.subdomain, {
        name: data.adminName,
        email: data.adminEmail,
        password: data.adminPassword,
      });

      // Update center to mark database as created
      await centralPrisma.center.update({
        where: { id: result.center.id },
        data: { databaseCreated: true }
      });

      console.log(`✅ Tenant database created successfully for ${data.subdomain}`);
    } catch (dbError) {
      console.error(`❌ Failed to create tenant database for ${data.subdomain}:`, dbError);
      
      // Rollback center creation if database creation fails
      await centralPrisma.center.delete({
        where: { id: result.center.id }
      });
      
      return c.json({
        success: false,
        message: 'Failed to create coaching center database. Please try again.'
      }, 500);
    }
    
    return c.json({
      success: true,
      message: 'Coaching center created successfully',
      data: {
        center: result.center,
        admin: {
          id: result.adminUser.id,
          name: result.adminUser.name,
          email: result.adminUser.email,
        },
        subscription: result.subscription,
      }
    }, 201);
    
  } catch (error) {
    console.error('Admin create center error:', error);
    
    if (error instanceof z.ZodError) {
      return c.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, 400);
    }
    
    return c.json({
      success: false,
      message: 'Unable to create coaching center'
    }, 500);
  }
});

export default admin;
