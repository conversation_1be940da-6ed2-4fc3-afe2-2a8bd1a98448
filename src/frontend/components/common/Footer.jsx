/** @jsx h */
import { h } from 'preact';

/**
 * Footer Component
 * Simple footer with copyright and links
 */
export function Footer({ tenant }) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-white border-t border-gray-200 py-4 px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
        <div className="text-sm text-gray-500">
          © {currentYear} {tenant?.name || 'Teaching Center SaaS Platform'}. All rights reserved.
        </div>
        
        <div className="flex items-center space-x-6 text-sm text-gray-500">
          <a href="/help" className="hover:text-gray-700 transition-colors">
            Help
          </a>
          <a href="/privacy" className="hover:text-gray-700 transition-colors">
            Privacy
          </a>
          <a href="/terms" className="hover:text-gray-700 transition-colors">
            Terms
          </a>
          {tenant && (
            <span className="text-xs text-gray-400">
              Powered by Teaching Center Platform
            </span>
          )}
        </div>
      </div>
    </footer>
  );
}
