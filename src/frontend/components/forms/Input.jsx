/** @jsx h */
import { h } from 'preact';
import { forwardRef } from 'preact/compat';

/**
 * Input Component
 * Reusable input field with validation states
 */
export const Input = forwardRef(({ 
  label,
  error,
  help,
  className = '',
  required = false,
  type = 'text',
  ...props 
}, ref) => {
  const inputClasses = `form-input ${error ? 'form-input-error' : ''} ${className}`;

  return (
    <div className="space-y-1">
      {label && (
        <label className="form-label">
          {label}
          {required && <span className="text-error-500 ml-1">*</span>}
        </label>
      )}
      
      <input
        ref={ref}
        type={type}
        className={inputClasses}
        {...props}
      />
      
      {error && (
        <p className="form-error">{error}</p>
      )}
      
      {help && !error && (
        <p className="form-help">{help}</p>
      )}
    </div>
  );
});

Input.displayName = 'Input';
