/** @jsx h */
import { h } from 'preact';
import { useState } from 'preact/hooks';
import { AuthLayout } from '../layouts/AuthLayout.jsx';
import { Input } from '../components/forms/Input.jsx';
import { Button } from '../components/forms/Button.jsx';

/**
 * Login Page Component
 * Handles user authentication for both admin and tenant users
 */
export function LoginPage({ tenant = null, error = null }) {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(formData)
      });

      if (response.redirected) {
        // Server handled redirect, follow it
        window.location.href = response.url;
      } else if (response.ok) {
        // Manual redirect based on response
        window.location.href = '/dashboard';
      } else {
        // Handle error - server will redirect to login with error param
        window.location.reload();
      }
    } catch (error) {
      console.error('Login error:', error);
      setErrors({
        general: 'Network error. Please check your connection and try again.'
      });
      setLoading(false);
    }
  };

  const pageTitle = tenant ? `Sign In - ${tenant.name}` : 'Admin Sign In';
  const pageDescription = tenant 
    ? `Sign in to your ${tenant.name} account`
    : 'Sign in to the Teaching Center admin panel';

  return (
    <AuthLayout 
      title={pageTitle}
      description={pageDescription}
      tenant={tenant}
    >
      <div className="space-y-6">
        {/* Page header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">
            {tenant ? 'Welcome back' : 'Admin Sign In'}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {tenant 
              ? 'Sign in to access your dashboard'
              : 'Sign in to manage the platform'
            }
          </p>
        </div>

        {/* Error messages */}
        {(error || errors.general) && (
          <div className="bg-error-50 border border-error-200 rounded-lg p-4">
            <div className="flex">
              <svg className="h-5 w-5 text-error-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="ml-3">
                <p className="text-sm text-error-700">
                  {error || errors.general}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Login form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="Email address"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            error={errors.email}
            required
            autoComplete="email"
            placeholder="Enter your email"
          />

          <Input
            label="Password"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleInputChange}
            error={errors.password}
            required
            autoComplete="current-password"
            placeholder="Enter your password"
          />

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                Remember me
              </label>
            </div>

            <a href="/forgot-password" className="text-sm text-primary-600 hover:text-primary-500">
              Forgot your password?
            </a>
          </div>

          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={loading}
            className="w-full"
          >
            {loading ? 'Signing in...' : 'Sign in'}
          </Button>
        </form>

        {/* Additional links */}
        <div className="text-center space-y-2">
          {!tenant && (
            <p className="text-sm text-gray-600">
              Don't have admin access?{' '}
              <a href="/contact" className="text-primary-600 hover:text-primary-500">
                Contact support
              </a>
            </p>
          )}
          
          {tenant && (
            <p className="text-sm text-gray-600">
              New to {tenant.name}?{' '}
              <a href="/register" className="text-primary-600 hover:text-primary-500">
                Create an account
              </a>
            </p>
          )}
        </div>
      </div>
    </AuthLayout>
  );
}
