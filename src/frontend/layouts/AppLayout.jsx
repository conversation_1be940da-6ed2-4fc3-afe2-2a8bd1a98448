/** @jsx h */
import { h } from 'preact';
import { BaseLayout } from './BaseLayout.jsx';
import { Header } from '../components/common/Header.jsx';
import { Sidebar } from '../components/common/Sidebar.jsx';
import { Footer } from '../components/common/Footer.jsx';

/**
 * Application Layout Component
 * Provides the main dashboard layout with header, sidebar, and content area
 */
export function AppLayout({ 
  children, 
  title,
  description,
  tenant,
  user,
  sidebarCollapsed = false,
  showSidebar = true,
  className = ''
}) {
  return (
    <BaseLayout 
      title={title} 
      description={description} 
      tenant={tenant} 
      user={user}
      className="bg-gray-50"
    >
      <div className="flex h-screen bg-gray-50">
        {/* Sidebar */}
        {showSidebar && (
          <Sidebar 
            user={user} 
            tenant={tenant} 
            collapsed={sidebarCollapsed}
          />
        )}
        
        {/* Main content area */}
        <div className={`flex-1 flex flex-col overflow-hidden ${showSidebar ? 'ml-0' : ''}`}>
          {/* Header */}
          <Header 
            user={user} 
            tenant={tenant} 
            showSidebarToggle={showSidebar}
          />
          
          {/* Main content */}
          <main className={`flex-1 overflow-y-auto bg-gray-50 ${className}`}>
            <div className="container mx-auto px-4 py-6 max-w-7xl">
              {children}
            </div>
          </main>
          
          {/* Footer */}
          <Footer tenant={tenant} />
        </div>
      </div>
    </BaseLayout>
  );
}
