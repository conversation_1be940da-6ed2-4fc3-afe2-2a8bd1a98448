/** @jsx h */
import { h } from 'preact';
import { BaseLayout } from './BaseLayout.jsx';

/**
 * Authentication Layout Component
 * Provides a clean layout for login, register, and other auth pages
 */
export function AuthLayout({ 
  children, 
  title,
  description,
  tenant,
  className = ''
}) {
  return (
    <BaseLayout 
      title={title} 
      description={description} 
      tenant={tenant}
      className="bg-gradient-to-br from-primary-50 to-secondary-100"
    >
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Logo and tenant branding */}
          <div className="text-center">
            {tenant?.logo_url ? (
              <img 
                src={tenant.logo_url} 
                alt={`${tenant.name} logo`}
                className="mx-auto h-16 w-auto"
              />
            ) : (
              <div className="mx-auto h-16 w-16 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-2xl font-bold">
                  {tenant?.name?.charAt(0) || 'T'}
                </span>
              </div>
            )}
            
            <h1 className="mt-4 text-3xl font-bold text-gray-900">
              {tenant?.name || 'Teaching Center'}
            </h1>
            
            <p className="mt-2 text-sm text-gray-600">
              {tenant ? 'Sign in to your account' : 'Platform Administration'}
            </p>
          </div>
          
          {/* Auth form container */}
          <div className={`bg-white rounded-lg shadow-medium p-8 ${className}`}>
            {children}
          </div>
          
          {/* Footer links */}
          <div className="text-center text-sm text-gray-500">
            <p>
              © 2024 Teaching Center SaaS Platform. All rights reserved.
            </p>
            {tenant && (
              <p className="mt-1">
                Powered by Teaching Center Platform
              </p>
            )}
          </div>
        </div>
      </div>
    </BaseLayout>
  );
}
