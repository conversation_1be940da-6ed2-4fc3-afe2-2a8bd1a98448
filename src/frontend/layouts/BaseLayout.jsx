/** @jsx h */
import { h } from 'preact';

/**
 * Base Layout Component
 * Provides the fundamental HTML structure for all pages
 */
export function BaseLayout({ 
  children, 
  title = 'Teaching Center SaaS', 
  description = 'Comprehensive teaching center management platform',
  tenant = null,
  user = null,
  className = ''
}) {
  const pageTitle = tenant ? `${title} - ${tenant.name}` : title;
  
  return (
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content={description} />
        <title>{pageTitle}</title>
        
        {/* Preload fonts */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link 
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" 
          rel="stylesheet" 
        />
        
        {/* Favicon */}
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        
        {/* CSS */}
        <link rel="stylesheet" href="/styles/main.css" />
        
        {/* Tenant-specific theme */}
        {tenant?.theme_color && (
          <style dangerouslySetInnerHTML={{
            __html: `
              :root {
                --tenant-primary: ${tenant.theme_color};
              }
              .tenant-primary { color: var(--tenant-primary); }
              .tenant-bg-primary { background-color: var(--tenant-primary); }
              .tenant-border-primary { border-color: var(--tenant-primary); }
            `
          }} />
        )}
      </head>
      <body className={`min-h-screen bg-gray-50 ${className}`}>
        {/* Skip to main content for accessibility */}
        <a 
          href="#main-content" 
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-lg z-50"
        >
          Skip to main content
        </a>
        
        {/* Main content */}
        <div id="main-content" className="min-h-screen">
          {children}
        </div>
        
        {/* Global data for client-side hydration */}
        <script 
          type="application/json" 
          id="global-data"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              tenant: tenant ? {
                id: tenant.id,
                name: tenant.name,
                subdomain: tenant.subdomain,
                theme_color: tenant.theme_color,
                logo_url: tenant.logo_url
              } : null,
              user: user ? {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role,
                center_id: user.center_id
              } : null
            })
          }}
        />
        
        {/* Client-side JavaScript */}
        <script type="module" src="/js/main.js"></script>
      </body>
    </html>
  );
}
