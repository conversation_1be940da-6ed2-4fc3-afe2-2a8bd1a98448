import type { FC } from 'hono/jsx';
import { DashboardLayout } from './DashboardLayout.tsx';
import { t, getTimeOfDay } from '../utils/i18n.js';

interface DashboardProps {
  user: any;
  tenant?: any;
  theme?: string;
  language?: string;
  stats?: any;
  recentActivity?: any[];
}

interface StatsCardProps {
  title: string;
  value: string | number;
  color: string;
  icon?: string;
}

const StatsCard: FC<StatsCardProps> = ({ title, value, color, icon = '📊' }) => {
  const colorClasses = {
    blue: 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
    green: 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400',
    purple: 'bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400',
    orange: 'bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400',
    pink: 'bg-pink-50 dark:bg-pink-900/20 text-pink-600 dark:text-pink-400',
    indigo: 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400'
  };

  return (
    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p class="text-3xl font-bold text-gray-900 dark:text-white mt-2">{value}</p>
        </div>
        <div class={`p-3 rounded-xl ${colorClasses[color as keyof typeof colorClasses] || colorClasses.blue}`}>
          <div class="text-2xl">{icon}</div>
        </div>
      </div>
    </div>
  );
};

interface QuickActionProps {
  title: string;
  description: string;
  href: string;
  icon?: string;
}

const QuickAction: FC<QuickActionProps> = ({ title, description, href, icon = '⚡' }) => {
  return (
    <a
      href={href}
      class="block p-6 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1"
    >
      <div class="flex items-start space-x-4">
        <div class="text-3xl">{icon}</div>
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-white text-lg">{title}</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">{description}</p>
        </div>
      </div>
    </a>
  );
};

export const AdminDashboard: FC<DashboardProps> = ({ 
  user, 
  tenant, 
  theme = 'light', 
  language = 'en', 
  stats,
  recentActivity = []
}) => {
  const greeting = getTimeOfDay(language);
  
  return (
    <DashboardLayout
      title={t('admin_dashboard', language)}
      user={user}
      tenant={tenant}
      theme={theme}
      language={language}
    >
      {/* Welcome Section */}
      <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white mb-8 shadow-lg">
        <h1 class="text-3xl font-bold mb-3">
          {greeting}, {user.name}!
        </h1>
        <p class="text-blue-100 text-lg">
          {t('welcome_admin', language)}
        </p>
      </div>

      {/* Statistics Grid */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatsCard 
          title={t('total_centers', language)}
          value={stats?.totalCenters || 0}
          color="blue"
          icon="🏢"
        />
        <StatsCard 
          title={t('active_subscriptions', language)}
          value={stats?.activeSubscriptions || 0}
          color="green"
          icon="✅"
        />
        <StatsCard 
          title={t('monthly_revenue', language)}
          value={`$${stats?.monthlyRevenue || 0}`}
          color="purple"
          icon="💰"
        />
        <StatsCard 
          title={t('total_users', language)}
          value={stats?.totalUsers || 0}
          color="orange"
          icon="👥"
        />
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Quick Actions */}
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
          <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">
            {t('quick_actions', language)}
          </h2>
          <div class="space-y-4">
            <QuickAction
              title={t('centers', language)}
              description={t('manage_centers_desc', language)}
              href="/admin/centers"
              icon="🏢"
            />
            <QuickAction
              title={t('center_details', language)}
              description={t('view_center_details_desc', language)}
              href="/admin/center-details"
              icon="📋"
            />
            <QuickAction
              title={t('subscriptions', language)}
              description={t('manage_subscriptions_desc', language)}
              href="/admin/subscriptions"
              icon="💳"
            />
          </div>
        </div>

        {/* Recent Activity */}
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
          <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">
            {t('recent_activity', language)}
          </h2>
          <div class="space-y-4">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity, index) => (
                <div key={index} class="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div class="flex-1">
                    <p class="font-medium text-gray-900 dark:text-white">{activity.title}</p>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mt-1">{activity.description}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">{activity.time}</p>
                  </div>
                </div>
              ))
            ) : (
              <div class="text-center py-12">
                <div class="text-4xl mb-4">📊</div>
                <p class="text-gray-500 dark:text-gray-400">{t('no_recent_activity', language)}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export const TenantDashboard: FC<DashboardProps> = ({ 
  user, 
  tenant, 
  theme = 'light', 
  language = 'en', 
  stats,
  recentActivity = []
}) => {
  const greeting = getTimeOfDay(language);
  
  return (
    <DashboardLayout 
      title={t('dashboard', language)} 
      user={user} 
      tenant={tenant} 
      theme={theme} 
      language={language}
    >
      {/* Welcome Section */}
      <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white mb-8 shadow-lg">
        <h1 class="text-3xl font-bold mb-3">
          {greeting}, {user.name}!
        </h1>
        <p class="text-blue-100 text-lg">
          {t('welcome_back', language)} {tenant?.name}
        </p>
      </div>

      {/* Statistics Grid */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatsCard 
          title={t('total_students', language)}
          value={stats?.totalStudents || 0}
          color="blue"
          icon="👨‍🎓"
        />
        <StatsCard 
          title={t('active_courses', language)}
          value={stats?.activeCourses || 0}
          color="green"
          icon="📚"
        />
        <StatsCard 
          title={t('total_teachers', language)}
          value={stats?.totalTeachers || 0}
          color="purple"
          icon="👩‍🏫"
        />
        <StatsCard 
          title={t('monthly_revenue', language)}
          value={`$${stats?.monthlyRevenue || 0}`}
          color="orange"
          icon="💰"
        />
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Quick Actions */}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">
            {t('quick_actions', language)}
          </h2>
          <div class="space-y-4">
            <QuickAction
              title={t('students', language)}
              description={t('manage_students_desc', language)}
              href="/students"
              icon="👨‍🎓"
            />
            <QuickAction
              title={t('courses', language)}
              description={t('manage_courses_desc', language)}
              href="/courses"
              icon="📚"
            />
            <QuickAction
              title={t('teachers', language)}
              description={t('manage_teachers_desc', language)}
              href="/teachers"
              icon="👩‍🏫"
            />
            <QuickAction
              title={t('branches', language)}
              description={t('manage_branches_desc', language)}
              href="/branches"
              icon="🏢"
            />
          </div>
        </div>

        {/* Recent Activity */}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">
            {t('recent_activity', language)}
          </h2>
          <div class="space-y-4">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity, index) => (
                <div key={index} class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div class="text-sm">
                    <p class="font-medium text-gray-900">{activity.title}</p>
                    <p class="text-gray-600">{activity.description}</p>
                    <p class="text-xs text-gray-500 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))
            ) : (
              <div class="text-center py-8">
                <p class="text-gray-500">{t('no_recent_activity', language)}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};
