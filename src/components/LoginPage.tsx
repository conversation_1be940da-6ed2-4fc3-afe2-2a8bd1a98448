import type { FC } from 'hono/jsx';
import { FrontendLayout } from './FrontendLayout.tsx';
import { t } from '../utils/i18n.js';

interface LoginPageProps {
  tenant?: any;
  theme?: string;
  language?: string;
  error?: string;
  success?: string;
}

export const LoginPage: FC<LoginPageProps> = ({ 
  tenant, 
  theme = 'light', 
  language = 'en', 
  error, 
  success 
}) => {
  return (
    <FrontendLayout title={t('signin', language)} tenant={tenant} theme={theme} language={language} showFooter={false}>
      <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
          <div class="text-center">
            {tenant?.logo_url ? (
              <img 
                src={tenant.logo_url} 
                alt={tenant.name} 
                class="h-16 w-auto mx-auto mb-4"
              />
            ) : (
              <div class="h-16 w-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span class="text-2xl font-bold text-white">
                  {tenant?.name?.charAt(0) || 'TC'}
                </span>
              </div>
            )}
            
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white">
              Sign In
            </h2>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Access your coaching management dashboard
            </p>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl">
              <p class="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}
          
          {success && (
            <div class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl">
              <p class="text-sm text-green-600 dark:text-green-400">{success}</p>
            </div>
          )}

          {/* Login Form */}
          <div class="bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
            <form id="loginForm" method="post" action="/login" class="space-y-6">
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email Address *
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autocomplete="email"
                  required
                  placeholder="Enter your email"
                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-600 focus:border-blue-600 transition-all duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Password *
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autocomplete="current-password"
                  required
                  placeholder="Enter your password"
                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-600 focus:border-blue-600 transition-all duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label for="remember-me" class="ml-3 text-sm text-gray-700 dark:text-gray-300">
                    Remember me
                  </label>
                </div>

                <div class="text-sm">
                  <a href="/forgot-password" class="text-blue-600 hover:text-blue-500 transition-colors">
                    Forgot password?
                  </a>
                </div>
              </div>

              <button
                type="submit"
                class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] shadow-lg hover:shadow-xl"
              >
                <span class="btn-text">Sign In</span>
                <span class="btn-loading hidden">
                  <span class="spinner -ml-1 mr-3"></span>
                  Signing in...
                </span>
              </button>
            </form>

            {/* Additional Links */}
            <div class="mt-6 text-center">
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Need help?{' '}
                <a href="/contact" class="text-blue-600 hover:text-blue-500 transition-colors">
                  Contact Support
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* JavaScript for form handling */}
      <script dangerouslySetInnerHTML={{
        __html: `
          // Form submission handling
          document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            
            // Show loading state
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');
            
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
            submitBtn.disabled = true;
            
            // Submit form directly to login POST route
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/login';
            
            // Add form data
            for (const [key, value] of formData.entries()) {
              const input = document.createElement('input');
              input.type = 'hidden';
              input.name = key;
              input.value = value;
              form.appendChild(input);
            }
            
            document.body.appendChild(form);
            form.submit();
          });
        `
      }} />
    </FrontendLayout>
  );
};
