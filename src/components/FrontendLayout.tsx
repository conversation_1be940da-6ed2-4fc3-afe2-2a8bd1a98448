import type { FC, PropsWithChildren } from 'hono/jsx';
import { getThemeConfig } from '../utils/theme.js';

interface FrontendLayoutProps {
  title: string;
  tenant?: any;
  theme?: string;
  language?: string;
  showNavbar?: boolean;
  showFooter?: boolean;
}

export const FrontendLayout: FC<PropsWithChildren<FrontendLayoutProps>> = ({ 
  title, 
  children, 
  tenant = null, 
  theme = 'light', 
  language = 'en',
  showNavbar = true,
  showFooter = true
}) => {
  const pageTitle = tenant ? `${title} - ${tenant.name}` : `${title} - Teaching Center SaaS`;
  const themeColor = tenant?.theme_color || '#2563eb';
  const themeConfig = getThemeConfig(theme);

  return (
    <html lang={language} class={themeConfig.class}>
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{pageTitle}</title>
        <meta name="description" content="Modern coaching management SaaS platform for educational institutions" />
        <link rel="stylesheet" href="/styles/main.css" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link 
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" 
          rel="stylesheet" 
        />
        <style dangerouslySetInnerHTML={{
          __html: `
            :root { 
              --tenant-primary: ${themeColor}; 
              --theme-background: ${themeConfig.colors.background};
              --theme-text: ${themeConfig.colors.text};
              --theme-surface: ${themeConfig.colors.surface};
              --theme-border: ${themeConfig.colors.border};
            }
            
            .tenant-primary { color: var(--tenant-primary); }
            .tenant-bg-primary { background-color: var(--tenant-primary); }

            /* Smooth transitions */
            * {
              transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
            }

            /* Custom scrollbar */
            ::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }
            
            ::-webkit-scrollbar-track {
              background: ${theme === 'dark' ? '#1f2937' : '#f3f4f6'};
            }
            
            ::-webkit-scrollbar-thumb {
              background: ${theme === 'dark' ? '#4b5563' : '#d1d5db'};
              border-radius: 3px;
            }
            
            ::-webkit-scrollbar-thumb:hover {
              background: ${theme === 'dark' ? '#6b7280' : '#9ca3af'};
            }

            /* Loading spinner */
            .spinner {
              display: inline-block;
              width: 16px;
              height: 16px;
              border: 2px solid currentColor;
              border-radius: 50%;
              border-top-color: transparent;
              animation: spin 1s linear infinite;
            }

            @keyframes spin {
              to { transform: rotate(360deg); }
            }

            /* Gradient animations */
            @keyframes gradient {
              0% { background-position: 0% 50%; }
              50% { background-position: 100% 50%; }
              100% { background-position: 0% 50%; }
            }

            .animate-gradient {
              background-size: 200% 200%;
              animation: gradient 6s ease infinite;
            }
          `
        }} />
      </head>
      <body class="min-h-screen" style={`background-color: ${themeConfig.colors.background}; color: ${themeConfig.colors.text};`}>
        {showNavbar && <Navbar theme={theme} language={language} tenant={tenant} />}
        
        <main class="min-h-screen">
          {children}
        </main>
        
        {showFooter && <Footer theme={theme} language={language} />}

        {/* Theme and Language Switch Scripts */}
        <script dangerouslySetInnerHTML={{
          __html: `
            // Theme switching function
            function switchTheme() {
              const currentTheme = document.documentElement.classList.contains('theme-dark') ? 'dark' : 'light';
              const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
              
              // Set theme cookie
              document.cookie = \`theme=\${newTheme}; path=/; max-age=31536000\`; // 1 year
              
              // Apply theme immediately
              document.documentElement.classList.remove('theme-light', 'theme-dark');
              document.documentElement.classList.add(\`theme-\${newTheme}\`);
              
              // Update theme button icons
              const themeButtons = document.querySelectorAll('[data-theme-toggle]');
              themeButtons.forEach(btn => {
                const icon = btn.querySelector('.theme-icon');
                if (icon) {
                  icon.textContent = newTheme === 'dark' ? '☀️' : '🌙';
                }
              });
              
              // Reload page to apply server-side theme changes
              setTimeout(() => window.location.reload(), 100);
            }
            
            // Language switching function
            function switchLanguage() {
              const currentLang = document.documentElement.lang || 'en';
              const newLang = currentLang === 'en' ? 'bn' : 'en';
              
              // Set language cookie
              document.cookie = \`language=\${newLang}; path=/; max-age=31536000\`; // 1 year
              
              // Reload page to apply new language
              window.location.reload();
            }

            // Mobile menu toggle
            function toggleMobileMenu() {
              const menu = document.getElementById('mobile-menu');
              const isHidden = menu.classList.contains('hidden');
              
              if (isHidden) {
                menu.classList.remove('hidden');
                menu.classList.add('block');
              } else {
                menu.classList.add('hidden');
                menu.classList.remove('block');
              }
            }
          `
        }} />
      </body>
    </html>
  );
};

const Navbar: FC<{ theme: string; language: string; tenant?: any }> = ({ theme, language, tenant }) => {
  return (
    <nav class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          {/* Logo */}
          <div class="flex items-center">
            <a href="/" class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">TC</span>
              </div>
              <span class="text-xl font-bold text-gray-900 dark:text-white">
                {tenant?.name || 'TeachingCenter'}
              </span>
            </a>
          </div>

          {/* Desktop Navigation */}
          <div class="hidden md:flex items-center space-x-8">
            <a href="/" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Home
            </a>
            <a href="#features" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Features
            </a>
            <a href="#pricing" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Pricing
            </a>
            <a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Contact
            </a>
          </div>

          {/* Right side buttons */}
          <div class="flex items-center space-x-4">
            {/* Language Toggle */}
            <button 
              onclick="switchLanguage()" 
              class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              title="Switch Language"
            >
              <span class="text-sm font-medium">
                {language === 'en' ? '🇺🇸' : '🇧🇩'}
              </span>
            </button>

            {/* Theme Toggle */}
            <button 
              onclick="switchTheme()" 
              data-theme-toggle
              class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              title="Toggle Theme"
            >
              <span class="theme-icon text-lg">
                {theme === 'dark' ? '☀️' : '🌙'}
              </span>
            </button>

            {/* Login Button */}
            <a 
              href="/login" 
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Sign In
            </a>

            {/* Mobile menu button */}
            <button 
              onclick="toggleMobileMenu()"
              class="md:hidden p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex flex-col space-y-4">
            <a href="/" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Home
            </a>
            <a href="#features" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Features
            </a>
            <a href="#pricing" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Pricing
            </a>
            <a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
              Contact
            </a>
          </div>
        </div>
      </div>
    </nav>
  );
};

const Footer: FC<{ theme: string; language: string }> = ({ theme, language }) => {
  return (
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div class="col-span-1 md:col-span-2">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">TC</span>
              </div>
              <span class="text-xl font-bold">TeachingCenter</span>
            </div>
            <p class="text-gray-400 mb-4 max-w-md">
              Modern coaching management SaaS platform designed for educational institutions. 
              Streamline your operations with our comprehensive solution.
            </p>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </a>
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li><a href="#features" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
              <li><a href="#pricing" class="text-gray-400 hover:text-white transition-colors">Pricing</a></li>
              <li><a href="/login" class="text-gray-400 hover:text-white transition-colors">Sign In</a></li>
              <li><a href="#contact" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 class="text-lg font-semibold mb-4">Support</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 TeachingCenter SaaS Platform. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};
