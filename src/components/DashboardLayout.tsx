import type { FC, PropsWithChildren } from 'hono/jsx';
import { getThemeConfig } from '../utils/theme.js';

interface DashboardLayoutProps {
  title: string;
  user: any;
  tenant?: any;
  theme?: string;
  language?: string;
}

export const DashboardLayout: FC<PropsWithChildren<DashboardLayoutProps>> = ({ 
  title, 
  children, 
  user,
  tenant = null, 
  theme = 'light', 
  language = 'en'
}) => {
  const pageTitle = tenant ? `${title} - ${tenant.name}` : `${title} - Teaching Center`;
  const themeColor = tenant?.theme_color || '#2563eb';
  const themeConfig = getThemeConfig(theme);

  return (
    <html lang={language} class={themeConfig.class}>
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{pageTitle}</title>
        <link rel="stylesheet" href="/styles/main.css" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link 
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" 
          rel="stylesheet" 
        />
        <style dangerouslySetInnerHTML={{
          __html: `
            :root { 
              --tenant-primary: ${themeColor}; 
              --theme-background: ${themeConfig.colors.background};
              --theme-text: ${themeConfig.colors.text};
              --theme-surface: ${themeConfig.colors.surface};
              --theme-border: ${themeConfig.colors.border};
            }
            
            .tenant-primary { color: var(--tenant-primary); }
            .tenant-bg-primary { background-color: var(--tenant-primary); }

            /* Smooth transitions */
            * {
              transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
            }

            /* Custom scrollbar */
            ::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }
            
            ::-webkit-scrollbar-track {
              background: ${theme === 'dark' ? '#1f2937' : '#f3f4f6'};
            }
            
            ::-webkit-scrollbar-thumb {
              background: ${theme === 'dark' ? '#4b5563' : '#d1d5db'};
              border-radius: 3px;
            }
            
            ::-webkit-scrollbar-thumb:hover {
              background: ${theme === 'dark' ? '#6b7280' : '#9ca3af'};
            }

            /* Loading spinner */
            .spinner {
              display: inline-block;
              width: 16px;
              height: 16px;
              border: 2px solid currentColor;
              border-radius: 50%;
              border-top-color: transparent;
              animation: spin 1s linear infinite;
            }

            @keyframes spin {
              to { transform: rotate(360deg); }
            }
          `
        }} />
      </head>
      <body class="min-h-screen bg-gray-50 dark:bg-gray-900" style={`background-color: ${themeConfig.colors.background}; color: ${themeConfig.colors.text};`}>
        {/* Header */}
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
              {/* Left side - Logo and Title */}
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">
                      {tenant?.name?.charAt(0) || 'TC'}
                    </span>
                  </div>
                  <span class="text-xl font-bold text-gray-900 dark:text-white">
                    {tenant?.name || 'TeachingCenter'}
                  </span>
                </div>
                <div class="hidden md:block h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
                <h1 class="hidden md:block text-lg font-semibold text-gray-900 dark:text-white">
                  {title}
                </h1>
              </div>
              
              {/* Right side - User menu and controls */}
              <div class="flex items-center space-x-4">
                {/* Theme Toggle */}
                <button 
                  onclick="switchTheme()" 
                  data-theme-toggle
                  class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  title="Toggle Theme"
                >
                  <span class="theme-icon text-lg">
                    {theme === 'dark' ? '☀️' : '🌙'}
                  </span>
                </button>

                {/* Language Toggle */}
                <button 
                  onclick="switchLanguage()" 
                  class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  title="Switch Language"
                >
                  <span class="text-sm font-medium">
                    {language === 'en' ? '🇺🇸' : '🇧🇩'}
                  </span>
                </button>

                {/* User Menu */}
                <div class="flex items-center space-x-3">
                  <div class="hidden md:block text-right">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {user.name}
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 capitalize">
                      {user.role.replace('_', ' ')}
                    </div>
                  </div>
                  
                  <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                    <span class="text-white text-sm font-medium">
                      {user.name?.charAt(0)?.toUpperCase() || 'U'}
                    </span>
                  </div>
                  
                  <form method="post" action="/logout" class="inline">
                    <button 
                      type="submit"
                      class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
                      title="Sign Out"
                    >
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                      </svg>
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          {children}
        </main>

        {/* Scripts */}
        <script dangerouslySetInnerHTML={{
          __html: `
            // Theme switching function
            function switchTheme() {
              const currentTheme = document.documentElement.classList.contains('theme-dark') ? 'dark' : 'light';
              const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
              
              // Set theme cookie
              document.cookie = \`theme=\${newTheme}; path=/; max-age=31536000\`; // 1 year
              
              // Apply theme immediately
              document.documentElement.classList.remove('theme-light', 'theme-dark');
              document.documentElement.classList.add(\`theme-\${newTheme}\`);
              
              // Update theme button icons
              const themeButtons = document.querySelectorAll('[data-theme-toggle]');
              themeButtons.forEach(btn => {
                const icon = btn.querySelector('.theme-icon');
                if (icon) {
                  icon.textContent = newTheme === 'dark' ? '☀️' : '🌙';
                }
              });
              
              // Reload page to apply server-side theme changes
              setTimeout(() => window.location.reload(), 100);
            }
            
            // Language switching function
            function switchLanguage() {
              const currentLang = document.documentElement.lang || 'en';
              const newLang = currentLang === 'en' ? 'bn' : 'en';
              
              // Set language cookie
              document.cookie = \`language=\${newLang}; path=/; max-age=31536000\`; // 1 year
              
              // Reload page to apply new language
              window.location.reload();
            }
          `
        }} />
      </body>
    </html>
  );
};
