import { getCentralPrisma } from '../config/prisma.js';
import bcrypt from 'bcryptjs';

const centralPrisma = getCentralPrisma();

async function seedCentralDatabase() {
  try {
    console.log('🌱 Starting central database seeding...');

    // Create default subscription plans
    const trialPlan = await centralPrisma.subscriptionPlan.upsert({
      where: { slug: 'trial' },
      update: {},
      create: {
        name: 'Trial Plan',
        slug: 'trial',
        description: 'Free trial plan for 7 days',
        price: 0,
        currency: 'BDT',
        billingInterval: 'MONTHLY',
        trialDays: 7,
        maxStudents: 50,
        maxTeachers: 5,
        maxBranches: 1,
        hasInventory: false,
        hasExams: true,
        hasReports: true,
        hasAPI: false,
        isActive: true,
      }
    });

    const basicPlan = await centralPrisma.subscriptionPlan.upsert({
      where: { slug: 'basic' },
      update: {},
      create: {
        name: 'Basic Plan',
        slug: 'basic',
        description: 'Basic plan for small coaching centers',
        price: 2000,
        currency: 'BDT',
        billingInterval: 'MONTHLY',
        trialDays: 7,
        maxStudents: 200,
        maxTeachers: 10,
        maxBranches: 2,
        hasInventory: true,
        hasExams: true,
        hasReports: true,
        hasAPI: false,
        isActive: true,
        isPopular: true,
      }
    });

    const proPlan = await centralPrisma.subscriptionPlan.upsert({
      where: { slug: 'pro' },
      update: {},
      create: {
        name: 'Pro Plan',
        slug: 'pro',
        description: 'Professional plan for growing coaching centers',
        price: 5000,
        currency: 'BDT',
        billingInterval: 'MONTHLY',
        trialDays: 7,
        maxStudents: 500,
        maxTeachers: 25,
        maxBranches: 5,
        hasInventory: true,
        hasExams: true,
        hasReports: true,
        hasAPI: true,
        isActive: true,
      }
    });

    console.log('✅ Subscription plans created/updated');

    // Create a demo coaching center for testing
    const demoCenter = await centralPrisma.center.upsert({
      where: { subdomain: 'demo' },
      update: {},
      create: {
        name: 'Demo Coaching Center',
        subdomain: 'demo',
        email: '<EMAIL>',
        phone: '+8801234567890',
        address: '123 Demo Street, Dhaka, Bangladesh',
        description: 'Demo coaching center for testing purposes',
        status: 'ACTIVE',
        databaseName: 'demo_coaching',
        databaseCreated: false,
      }
    });

    // Create super admin user for the demo center
    const hashedPassword = await bcrypt.hash('SuperAdmin123!', 12);

    // Check if super admin already exists
    const existingSuperAdmin = await centralPrisma.centerUser.findFirst({
      where: {
        email: '<EMAIL>',
        centerId: demoCenter.id
      }
    });

    let superAdmin;
    if (!existingSuperAdmin) {
      superAdmin = await centralPrisma.centerUser.create({
        data: {
          centerId: demoCenter.id,
          name: 'Super Admin',
          email: '<EMAIL>',
          passwordHash: hashedPassword,
          role: 'CENTER_ADMIN',
          status: 'ACTIVE',
        }
      });
    } else {
      superAdmin = existingSuperAdmin;
    }

    // Create subscription for demo center
    const existingSubscription = await centralPrisma.subscription.findFirst({
      where: {
        centerId: demoCenter.id,
        planId: trialPlan.id
      }
    });

    let demoSubscription;
    if (!existingSubscription) {
      demoSubscription = await centralPrisma.subscription.create({
        data: {
          centerId: demoCenter.id,
          planId: trialPlan.id,
          status: 'TRIAL',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
          trialStart: new Date(),
          trialEnd: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        }
      });
    } else {
      demoSubscription = existingSubscription;
    }

    console.log('✅ Demo coaching center created/updated');
    console.log('');
    console.log('🔑 Test Credentials:');
    console.log('Super Admin Email: <EMAIL>');
    console.log('Super Admin Password: SuperAdmin123!');
    console.log('');
    console.log('🌐 Test URLs:');
    console.log('Admin Login: http://localhost:3004/api/auth/login');
    console.log('API Health: http://localhost:3004/health');
    console.log('API Info: http://localhost:3004/api');
    console.log('');
    console.log('📊 Created Plans:');
    console.log(`- Trial Plan (${trialPlan.id}): ${trialPlan.name}`);
    console.log(`- Basic Plan (${basicPlan.id}): ${basicPlan.name}`);
    console.log(`- Pro Plan (${proPlan.id}): ${proPlan.name}`);

  } catch (error) {
    console.error('❌ Error seeding central database:', error);
    throw error;
  } finally {
    await centralPrisma.$disconnect();
  }
}

// Run the seed function
seedCentralDatabase()
  .then(() => {
    console.log('✅ Central database seeding completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Central database seeding failed:', error);
    process.exit(1);
  });
