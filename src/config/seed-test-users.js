/**
 * Seed test users for role-based testing
 */

import bcrypt from 'bcryptjs';
import { executeQuery, initializeDatabase } from './database.js';

export async function seedTestUsers() {
  try {
    console.log('🌱 Seeding test users...');
    
    // Test users data
    const testUsers = [
      {
        name: 'Center Owner',
        email: '<EMAIL>',
        password: 'Owner123!',
        role: 'center_owner',
        tenant_id: 1, // Sunrise Academy
        phone: '+1234567890'
      },
      {
        name: 'Test Manager',
        email: '<EMAIL>',
        password: 'Manager123!',
        role: 'manager',
        tenant_id: 1, // Sunrise Academy
        phone: '+1234567891'
      },
      {
        name: 'Test Teacher',
        email: '<EMAIL>',
        password: 'Teacher123!',
        role: 'teacher',
        tenant_id: 1, // Sunrise Academy
        phone: '+1234567892'
      },
      {
        name: 'Test Student',
        email: '<EMAIL>',
        password: 'Student123!',
        role: 'student',
        tenant_id: 1, // Sunrise Academy
        phone: '+1234567893'
      }
    ];
    
    for (const userData of testUsers) {
      // Check if user already exists
      const existingUserQuery = 'SELECT id FROM users WHERE email = ?';
      const existingUsers = await executeQuery(existingUserQuery, [userData.email]);
      
      if (existingUsers.length > 0) {
        console.log(`👤 User ${userData.email} already exists, skipping...`);
        continue;
      }
      
      // Hash password
      const passwordHash = await bcrypt.hash(userData.password, 12);
      
      // Create user
      const createUserQuery = `
        INSERT INTO users (name, email, password_hash, role, tenant_id, phone, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())
      `;
      
      await executeQuery(createUserQuery, [
        userData.name,
        userData.email,
        passwordHash,
        userData.role,
        userData.tenant_id,
        userData.phone
      ]);
      
      console.log(`✅ Created ${userData.role}: ${userData.email}`);
    }
    
    // Create some test courses
    await seedTestCourses();
    
    console.log('🎉 Test users seeded successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding test users:', error);
    throw error;
  }
}

async function seedTestCourses() {
  try {
    console.log('📚 Seeding test courses...');
    
    const testCourses = [
      {
        name: 'Mathematics Grade 10',
        description: 'Advanced mathematics for grade 10 students',
        duration_months: 6,
        fee_monthly: 500,
        tenant_id: 1
      },
      {
        name: 'Physics Grade 11',
        description: 'Physics fundamentals for grade 11 students',
        duration_months: 8,
        fee_monthly: 600,
        tenant_id: 1
      },
      {
        name: 'Chemistry Grade 12',
        description: 'Advanced chemistry for grade 12 students',
        duration_months: 10,
        fee_monthly: 700,
        tenant_id: 1
      }
    ];
    
    for (const courseData of testCourses) {
      // Check if course already exists
      const existingCourseQuery = 'SELECT id FROM courses WHERE name = ? AND tenant_id = ?';
      const existingCourses = await executeQuery(existingCourseQuery, [courseData.name, courseData.tenant_id]);
      
      if (existingCourses.length > 0) {
        console.log(`📖 Course ${courseData.name} already exists, skipping...`);
        continue;
      }
      
      // Create course
      const createCourseQuery = `
        INSERT INTO courses (name, description, duration_months, fee_monthly, tenant_id, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())
      `;

      await executeQuery(createCourseQuery, [
        courseData.name,
        courseData.description,
        courseData.duration_months,
        courseData.fee_monthly,
        courseData.tenant_id
      ]);
      
      console.log(`✅ Created course: ${courseData.name}`);
    }
    
  } catch (error) {
    console.error('❌ Error seeding test courses:', error);
    throw error;
  }
}

// Run seeder if this file is executed directly
if (import.meta.main) {
  initializeDatabase()
    .then(() => seedTestUsers())
    .then(() => {
      console.log('✅ Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}
