import { PrismaClient } from '@prisma/client';

// Central database client (singleton)
let centralPrisma: PrismaClient | null = null;

/**
 * Get the central database Prisma client
 */
export function getCentralPrisma(): PrismaClient {
  if (!centralPrisma) {
    centralPrisma = new PrismaClient({
      log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
      errorFormat: 'pretty',
    });
  }
  return centralPrisma;
}

/**
 * Initialize the central database connection
 */
export async function initializeCentralDatabase(): Promise<void> {
  try {
    const prisma = getCentralPrisma();
    await prisma.$connect();
    console.log('✅ Central database connected successfully');
  } catch (error) {
    console.error('❌ Central database connection failed:', error);
    throw error;
  }
}

/**
 * Close the central database connection
 */
export async function closeCentralDatabase(): Promise<void> {
  if (centralPrisma) {
    await centralPrisma.$disconnect();
    centralPrisma = null;
    console.log('✅ Central database connection closed');
  }
}

/**
 * Health check for central database
 */
export async function checkCentralDatabaseHealth(): Promise<boolean> {
  try {
    const prisma = getCentralPrisma();
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('❌ Central database health check failed:', error);
    return false;
  }
}

export default getCentralPrisma;
