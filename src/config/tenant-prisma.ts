// This file handles tenant-specific Prisma clients
// Since we can't dynamically generate Prisma clients for different schemas,
// we'll use raw SQL for tenant database operations

import mysql from 'mysql2/promise';

// Cache for tenant database connections
const tenantConnections = new Map<string, mysql.Connection>();

/**
 * Get a MySQL connection for a specific tenant database
 */
export async function getTenantConnection(subdomain: string): Promise<mysql.Connection> {
  // Check if we already have a connection for this tenant
  if (tenantConnections.has(subdomain)) {
    const connection = tenantConnections.get(subdomain)!;
    try {
      // Test the connection
      await connection.ping();
      return connection;
    } catch (error) {
      // Connection is dead, remove it from cache
      tenantConnections.delete(subdomain);
    }
  }

  const databaseName = `${subdomain}_coaching`;
  
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASS || '',
    database: databaseName,
    charset: 'utf8mb4',
  });

  // Cache the connection
  tenantConnections.set(subdomain, connection);
  
  return connection;
}

/**
 * Execute a query on a tenant database
 */
export async function executeTenantQuery(
  subdomain: string, 
  query: string, 
  params: any[] = []
): Promise<any> {
  const connection = await getTenantConnection(subdomain);
  const [rows] = await connection.execute(query, params);
  return rows;
}

/**
 * Close all tenant database connections
 */
export async function closeAllTenantConnections(): Promise<void> {
  for (const [subdomain, connection] of tenantConnections.entries()) {
    try {
      await connection.end();
      console.log(`✅ Closed connection for tenant: ${subdomain}`);
    } catch (error) {
      console.error(`❌ Error closing connection for tenant ${subdomain}:`, error);
    }
  }
  tenantConnections.clear();
}

/**
 * Create a user in tenant database
 */
export async function createTenantUser(subdomain: string, userData: {
  name: string;
  email: string;
  passwordHash: string;
  role: string;
  status: string;
  branchId?: string;
}): Promise<any> {
  const query = `
    INSERT INTO users (id, name, email, passwordHash, role, status, branchId, createdAt, updatedAt)
    VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
  `;
  
  const userId = generateId();
  const params = [
    userId,
    userData.name,
    userData.email,
    userData.passwordHash,
    userData.role,
    userData.status,
    userData.branchId || null
  ];
  
  await executeTenantQuery(subdomain, query, params);
  
  // Return the created user
  const selectQuery = 'SELECT * FROM users WHERE id = ?';
  const [user] = await executeTenantQuery(subdomain, selectQuery, [userId]);
  return user;
}

/**
 * Create a branch in tenant database
 */
export async function createTenantBranch(subdomain: string, branchData: {
  branchCode: string;
  branchName: string;
  status: string;
}): Promise<any> {
  const query = `
    INSERT INTO branches (id, branchCode, branchName, status, createdAt, updatedAt)
    VALUES (?, ?, ?, ?, NOW(), NOW())
  `;
  
  const branchId = generateId();
  const params = [
    branchId,
    branchData.branchCode,
    branchData.branchName,
    branchData.status
  ];
  
  await executeTenantQuery(subdomain, query, params);
  
  // Return the created branch
  const selectQuery = 'SELECT * FROM branches WHERE id = ?';
  const [branch] = await executeTenantQuery(subdomain, selectQuery, [branchId]);
  return branch;
}

/**
 * Create a subject in tenant database
 */
export async function createTenantSubject(subdomain: string, subjectData: {
  name: string;
  code?: string;
}): Promise<any> {
  const query = `
    INSERT INTO subjects (id, name, code, status, createdAt, updatedAt)
    VALUES (?, ?, ?, 'ACTIVE', NOW(), NOW())
  `;
  
  const subjectId = generateId();
  const params = [
    subjectId,
    subjectData.name,
    subjectData.code || null
  ];
  
  await executeTenantQuery(subdomain, query, params);
  
  // Return the created subject
  const selectQuery = 'SELECT * FROM subjects WHERE id = ?';
  const [subject] = await executeTenantQuery(subdomain, selectQuery, [subjectId]);
  return subject;
}

/**
 * Update user with branch assignment
 */
export async function updateTenantUserBranch(subdomain: string, userId: string, branchId: string): Promise<void> {
  const query = 'UPDATE users SET branchId = ?, updatedAt = NOW() WHERE id = ?';
  await executeTenantQuery(subdomain, query, [branchId, userId]);
}

/**
 * Get user by email from tenant database
 */
export async function getTenantUserByEmail(subdomain: string, email: string): Promise<any> {
  const query = `
    SELECT u.*, b.branchName, b.branchCode 
    FROM users u 
    LEFT JOIN branches b ON u.branchId = b.id 
    WHERE u.email = ? AND u.status = 'ACTIVE'
  `;
  const [user] = await executeTenantQuery(subdomain, query, [email]);
  return user;
}

/**
 * Get user by ID from tenant database
 */
export async function getTenantUserById(subdomain: string, userId: string): Promise<any> {
  const query = `
    SELECT u.*, b.branchName, b.branchCode 
    FROM users u 
    LEFT JOIN branches b ON u.branchId = b.id 
    WHERE u.id = ? AND u.status = 'ACTIVE'
  `;
  const [user] = await executeTenantQuery(subdomain, query, [userId]);
  return user;
}

/**
 * Generate a unique ID (similar to Prisma's cuid)
 */
function generateId(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 15);
  return `cmd${timestamp}${randomPart}`;
}

/**
 * Check if tenant database exists and has tables
 */
export async function checkTenantDatabaseReady(subdomain: string): Promise<boolean> {
  try {
    const connection = await getTenantConnection(subdomain);
    
    // Check if users table exists
    const [tables] = await connection.execute(
      "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users'",
      [`${subdomain}_coaching`]
    );
    
    return Array.isArray(tables) && tables.length > 0;
  } catch (error) {
    console.error(`Error checking tenant database readiness for ${subdomain}:`, error);
    return false;
  }
}
