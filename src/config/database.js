import mysql from 'mysql2/promise';

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || '',
  database: process.env.DB_NAME || 'coaching',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  charset: 'utf8mb4'
};

// Test database configuration
const testDbConfig = {
  ...dbConfig,
  database: process.env.DB_NAME_TEST || 'coaching_test',
  user: process.env.DB_USER_TEST || dbConfig.user,
  password: process.env.DB_PASS_TEST || dbConfig.password
};

// Create connection pools
let pool;
let testPool;

// Initialize main database pool
export const initializeDatabase = async () => {
  try {
    pool = mysql.createPool(dbConfig);
    console.log(dbConfig);
    
    // Test the connection
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();
    
    return pool;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    throw error;
  }
};

// Initialize test database pool
export const initializeTestDatabase = async () => {
  try {
    testPool = mysql.createPool(testDbConfig);
    
    // Test the connection
    const connection = await testPool.getConnection();
    console.log('✅ Test database connected successfully');
    connection.release();
    
    return testPool;
  } catch (error) {
    console.error('❌ Test database connection failed:', error.message);
    throw error;
  }
};

// Get database pool
export const getDatabase = () => {
  if (!pool) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return pool;
};

// Get test database pool
export const getTestDatabase = () => {
  if (!testPool) {
    throw new Error('Test database not initialized. Call initializeTestDatabase() first.');
  }
  return testPool;
};

// Execute query with error handling
export const executeQuery = async (query, params = [], useTestDb = false) => {
  const db = useTestDb ? getTestDatabase() : getDatabase();
  // console.log(db.getDatabase());
  try {
    const [results] = await db.execute(query, params);
    return results;
  } catch (error) {
    console.error('Database query error:', {
      query: query.substring(0, 100) + '...',
      error: error.message
    });
    throw error;
  }
};

// Execute transaction
export const executeTransaction = async (queries, useTestDb = false) => {
  const db = useTestDb ? getTestDatabase() : getDatabase();
  const connection = await db.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const results = [];
    for (const { query, params } of queries) {
      const [result] = await connection.execute(query, params || []);
      results.push(result);
    }
    
    await connection.commit();
    return results;
  } catch (error) {
    await connection.rollback();
    console.error('Transaction error:', error.message);
    throw error;
  } finally {
    connection.release();
  }
};

// Close database connections
export const closeDatabase = async () => {
  try {
    if (pool) {
      await pool.end();
      console.log('✅ Database connection closed');
    }
    if (testPool) {
      await testPool.end();
      console.log('✅ Test database connection closed');
    }
  } catch (error) {
    console.error('❌ Error closing database connections:', error.message);
  }
};

// Tenant-specific database operations
export const getTenantDatabase = (tenantId) => {
  // For now, we'll use a single database with tenant_id column
  // In the future, this could be extended to support separate databases per tenant
  return getDatabase();
};

// Check if database exists
export const checkDatabaseExists = async (databaseName) => {
  try {
    const tempConfig = { ...dbConfig };
    delete tempConfig.database;
    
    const tempPool = mysql.createPool(tempConfig);
    const [rows] = await tempPool.execute(
      'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
      [databaseName]
    );
    
    await tempPool.end();
    return rows.length > 0;
  } catch (error) {
    console.error('Error checking database existence:', error.message);
    return false;
  }
};

// Create database if it doesn't exist
export const createDatabaseIfNotExists = async (databaseName) => {
  try {
    const tempConfig = { ...dbConfig };
    delete tempConfig.database;
    
    const tempPool = mysql.createPool(tempConfig);
    await tempPool.execute(`CREATE DATABASE IF NOT EXISTS \`${databaseName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    
    await tempPool.end();
    console.log(`✅ Database '${databaseName}' created or already exists`);
  } catch (error) {
    console.error(`❌ Error creating database '${databaseName}':`, error.message);
    throw error;
  }
};

export default {
  initializeDatabase,
  initializeTestDatabase,
  getDatabase,
  getTestDatabase,
  executeQuery,
  executeTransaction,
  closeDatabase,
  getTenantDatabase,
  checkDatabaseExists,
  createDatabaseIfNotExists
};
