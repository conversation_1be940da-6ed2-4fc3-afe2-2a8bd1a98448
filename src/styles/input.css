@tailwind base;
@tailwind components;
@tailwind utilities;

/* Tailwind CSS utilities */
.min-h-screen { min-height: 100vh; }
.flex { display: flex; }
.hidden { display: none; }
.lg\\:flex { display: flex; }
.lg\\:hidden { display: none; }
.lg\\:w-1\\/2 { width: 50%; }
.flex-1 { flex: 1 1 0%; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.from-blue-600 { --tw-gradient-from: #2563eb var(--tw-gradient-from-position); --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.via-purple-600 { --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position); --tw-gradient-stops: var(--tw-gradient-from), #9333ea var(--tw-gradient-via-position), var(--tw-gradient-to); }
.to-blue-800 { --tw-gradient-to: #1e40af var(--tw-gradient-to-position); }
.relative { position: relative; }
.absolute { position: absolute; }
.inset-0 { inset: 0px; }
.top-20 { top: 5rem; }
.left-20 { left: 5rem; }
.bottom-20 { bottom: 5rem; }
.right-20 { right: 5rem; }
.top-1\\/2 { top: 50%; }
.left-1\\/3 { left: 33.333333%; }
.z-10 { z-index: 10; }
.overflow-hidden { overflow: hidden; }
.bg-black { background-color: rgb(0 0 0); }
.bg-opacity-20 { background-color: rgb(0 0 0 / 0.2); }
.bg-white { background-color: rgb(255 255 255); }
.bg-opacity-10 { background-color: rgb(255 255 255 / 0.1); }
.bg-purple-300 { background-color: rgb(196 181 253); }
.bg-blue-300 { background-color: rgb(147 197 253); }
.bg-opacity-15 { background-color: rgb(147 197 253 / 0.15); }
.text-white { color: rgb(255 255 255); }
.text-blue-100 { color: rgb(219 234 254); }
.p-12 { padding: 3rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.lg\\:px-20 { padding-left: 5rem; padding-right: 5rem; }
.xl\\:px-24 { padding-left: 6rem; padding-right: 6rem; }
.text-center { text-align: center; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.tracking-tight { letter-spacing: -0.025em; }
.leading-6 { line-height: 1.5rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mt-6 { margin-top: 1.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-8 { margin-top: 2rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.ml-3 { margin-left: 0.75rem; }
.w-full { width: 100%; }
.w-32 { width: 8rem; }
.w-40 { width: 10rem; }
.w-24 { width: 6rem; }
.w-96 { width: 24rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.h-32 { height: 8rem; }
.h-40 { height: 10rem; }
.h-24 { height: 6rem; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.rounded-full { border-radius: 9999px; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-md { border-radius: 0.375rem; }
.blur-xl { filter: blur(24px); }
.blur-lg { filter: blur(16px); }
.border-0 { border-width: 0px; }
.border { border-width: 1px; }
.border-gray-300 { border-color: rgb(209 213 219); }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-1\\.5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }
.p-1 { padding: 0.25rem; }
.shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
.ring-1 { box-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.ring-inset { --tw-ring-inset: inset; }
.ring-gray-300 { --tw-ring-color: rgb(209 213 219); }
.ring-gray-600 { --tw-ring-color: rgb(75 85 99); }
.ring-primary-600 { --tw-ring-color: rgb(37 99 235); }
.ring-primary-500 { --tw-ring-color: rgb(59 130 246); }
.ring-blue-600 { --tw-ring-color: rgb(37 99 235); }
.focus\\:ring-2:focus { box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.focus\\:ring-inset:focus { --tw-ring-inset: inset; }
.focus\\:ring-primary-600:focus { --tw-ring-color: rgb(37 99 235); }
.focus\\:ring-blue-600:focus { --tw-ring-color: rgb(37 99 235); }
.focus-visible\\:outline:focus-visible { outline: 2px solid transparent; outline-offset: 2px; }
.focus-visible\\:outline-2:focus-visible { outline-width: 2px; }
.focus-visible\\:outline-offset-2:focus-visible { outline-offset: 2px; }
.focus-visible\\:outline-primary-600:focus-visible { outline-color: rgb(37 99 235); }
.focus-visible\\:outline-blue-600:focus-visible { outline-color: rgb(37 99 235); }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.block { display: block; }
.inline-flex { display: inline-flex; }
.space-y-6 > :not([hidden]) ~ :not([hidden]) { margin-top: 1.5rem; }
.space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem; }
.bg-primary-600 { background-color: rgb(37 99 235); }
.bg-blue-600 { background-color: rgb(37 99 235); }
.hover\\:bg-primary-500:hover { background-color: rgb(59 130 246); }
.hover\\:bg-blue-500:hover { background-color: rgb(59 130 246); }
.text-blue-600 { color: rgb(37 99 235); }
.hover\\:text-blue-500:hover { color: rgb(59 130 246); }
.disabled\\:opacity-50:disabled { opacity: 0.5; }
.disabled\\:cursor-not-allowed:disabled { cursor: not-allowed; }
.animate-spin { animation: spin 1s linear infinite; }
@keyframes spin { to { transform: rotate(360deg); } }
.-ml-1 { margin-left: -0.25rem; }
.mr-3 { margin-right: 0.75rem; }

/* Custom component styles */
@layer components {
  /* Form components */
  .form-input {
    @apply block w-full rounded-lg border-0 py-3 px-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6 transition-all duration-200;
  }
  
  .form-input:focus {
    @apply outline-none ring-2 ring-primary-600;
  }
  
  .form-label {
    @apply block text-sm font-medium leading-6 text-gray-900 mb-2;
  }
  
  .form-error {
    @apply mt-2 text-sm text-red-600;
  }
  
  /* Button components */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-semibold transition-all duration-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white shadow-sm hover:bg-primary-500 focus-visible:outline-primary-600;
  }
  
  .btn-secondary {
    @apply bg-white text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:outline-gray-600;
  }
  
  .btn-success {
    @apply bg-success-600 text-white shadow-sm hover:bg-success-500 focus-visible:outline-success-600;
  }
  
  .btn-warning {
    @apply bg-warning-600 text-white shadow-sm hover:bg-warning-500 focus-visible:outline-warning-600;
  }
  
  .btn-error {
    @apply bg-error-600 text-white shadow-sm hover:bg-error-500 focus-visible:outline-error-600;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* Card components */
  .card {
    @apply bg-white shadow-soft rounded-lg border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* Navigation components */
  .nav-link {
    @apply block px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200;
  }
  
  .nav-link.active {
    @apply bg-primary-100 text-primary-700;
  }
  
  /* Table components */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50;
  }
  
  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .table tbody tr:nth-child(even) {
    @apply bg-gray-50;
  }
  
  /* Alert components */
  .alert {
    @apply rounded-lg p-4 mb-4;
  }
  
  .alert-success {
    @apply bg-success-50 text-success-800 border border-success-200;
  }
  
  .alert-warning {
    @apply bg-warning-50 text-warning-800 border border-warning-200;
  }
  
  .alert-error {
    @apply bg-error-50 text-error-800 border border-error-200;
  }
  
  .alert-info {
    @apply bg-primary-50 text-primary-800 border border-primary-200;
  }
}

/* Dark mode styles */
@layer components {
  .dark .form-input {
    @apply bg-gray-800 text-white ring-gray-600 placeholder:text-gray-400;
  }
  
  .dark .form-input:focus {
    @apply ring-primary-500;
  }
  
  .dark .form-label {
    @apply text-gray-200;
  }
  
  .dark .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .dark .card-header {
    @apply border-gray-700;
  }
  
  .dark .card-footer {
    @apply border-gray-700 bg-gray-900;
  }
  
  .dark .nav-link {
    @apply text-gray-300 hover:text-white hover:bg-gray-700;
  }
  
  .dark .nav-link.active {
    @apply bg-primary-900 text-primary-200;
  }
  
  .dark .table th {
    @apply bg-gray-800 text-gray-300;
  }
  
  .dark .table td {
    @apply text-gray-200;
  }
  
  .dark .table tbody tr:nth-child(even) {
    @apply bg-gray-800;
  }
}

/* Custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .bg-gradient-radial {
    background-image: radial-gradient(var(--tw-gradient-stops));
  }
  
  .bg-gradient-conic {
    background-image: conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops));
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
  
  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
  }
}
