/**
 * Theme utility for dark mode and light mode support
 */

import { getCookie } from 'hono/cookie';

// Default theme
const DEFAULT_THEME = 'light';

// Supported themes
const SUPPORTED_THEMES = ['light', 'dark'];

/**
 * Detect theme from request
 */
export function detectTheme(c) {
  // Priority order: query param > cookie > default
  let theme = c.req.query('theme') || 
              getCookie(c, 'theme') ||
              DEFAULT_THEME;
  
  // Validate and fallback to default if not supported
  if (!SUPPORTED_THEMES.includes(theme)) {
    theme = DEFAULT_THEME;
  }
  
  return theme;
}

/**
 * Get theme configuration
 */
export function getThemeConfig(theme = DEFAULT_THEME) {
  const themes = {
    light: {
      name: 'Light',
      class: 'theme-light',
      colors: {
        primary: '#2563eb',
        primaryHover: '#1d4ed8',
        secondary: '#64748b',
        background: '#ffffff',
        surface: '#f8fafc',
        text: '#1e293b',
        textSecondary: '#64748b',
        border: '#e2e8f0',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
        info: '#3b82f6'
      },
      shadows: {
        sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
        md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
        lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)'
      }
    },
    dark: {
      name: 'Dark',
      class: 'theme-dark',
      colors: {
        primary: '#3b82f6',
        primaryHover: '#2563eb',
        secondary: '#94a3b8',
        background: '#0f172a',
        surface: '#1e293b',
        text: '#f1f5f9',
        textSecondary: '#94a3b8',
        border: '#334155',
        success: '#22c55e',
        warning: '#eab308',
        error: '#f87171',
        info: '#60a5fa'
      },
      shadows: {
        sm: '0 1px 2px 0 rgb(0 0 0 / 0.3)',
        md: '0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)',
        lg: '0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3)'
      }
    }
  };
  
  return themes[theme] || themes[DEFAULT_THEME];
}

/**
 * Generate CSS variables for theme
 */
export function generateThemeCSS(theme = DEFAULT_THEME) {
  const config = getThemeConfig(theme);
  
  const cssVars = Object.entries(config.colors)
    .map(([key, value]) => `--color-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value};`)
    .join('\n    ');
  
  const shadowVars = Object.entries(config.shadows)
    .map(([key, value]) => `--shadow-${key}: ${value};`)
    .join('\n    ');
  
  return `
  .${config.class} {
    ${cssVars}
    ${shadowVars}
  }`;
}

/**
 * Get all theme CSS
 */
export function getAllThemeCSS() {
  return SUPPORTED_THEMES.map(theme => generateThemeCSS(theme)).join('\n');
}

/**
 * Generate theme-aware CSS classes
 */
export function getThemeClasses(theme = DEFAULT_THEME) {
  const config = getThemeConfig(theme);
  
  return {
    // Background classes
    'bg-primary': `background-color: ${config.colors.primary}`,
    'bg-secondary': `background-color: ${config.colors.secondary}`,
    'bg-surface': `background-color: ${config.colors.surface}`,
    'bg-background': `background-color: ${config.colors.background}`,
    
    // Text classes
    'text-primary': `color: ${config.colors.primary}`,
    'text-secondary': `color: ${config.colors.textSecondary}`,
    'text-default': `color: ${config.colors.text}`,
    
    // Border classes
    'border-default': `border-color: ${config.colors.border}`,
    
    // Status classes
    'text-success': `color: ${config.colors.success}`,
    'text-warning': `color: ${config.colors.warning}`,
    'text-error': `color: ${config.colors.error}`,
    'text-info': `color: ${config.colors.info}`,
    
    'bg-success': `background-color: ${config.colors.success}`,
    'bg-warning': `background-color: ${config.colors.warning}`,
    'bg-error': `background-color: ${config.colors.error}`,
    'bg-info': `background-color: ${config.colors.info}`,
    
    // Shadow classes
    'shadow-sm': `box-shadow: ${config.shadows.sm}`,
    'shadow-md': `box-shadow: ${config.shadows.md}`,
    'shadow-lg': `box-shadow: ${config.shadows.lg}`
  };
}

/**
 * Check if a theme is supported
 */
export function isThemeSupported(theme) {
  return SUPPORTED_THEMES.includes(theme);
}

/**
 * Get list of supported themes
 */
export function getSupportedThemes() {
  return SUPPORTED_THEMES.map(theme => ({
    value: theme,
    label: getThemeConfig(theme).name,
    class: getThemeConfig(theme).class
  }));
}

/**
 * Create a theme-aware style generator
 */
export function createThemeStyleGenerator(theme) {
  const config = getThemeConfig(theme);
  
  return {
    colors: config.colors,
    shadows: config.shadows,
    css: (styles) => {
      return Object.entries(styles)
        .map(([property, value]) => {
          // Replace color variables
          const processedValue = value.replace(/var\(--color-(\w+)\)/g, (match, colorName) => {
            const colorKey = colorName.replace(/-([a-z])/g, (m, letter) => letter.toUpperCase());
            return config.colors[colorKey] || match;
          });
          
          return `${property}: ${processedValue}`;
        })
        .join('; ');
    }
  };
}

/**
 * Export constants
 */
export { DEFAULT_THEME, SUPPORTED_THEMES };
