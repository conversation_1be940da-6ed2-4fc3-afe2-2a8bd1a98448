import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import { join } from 'path';
import bcrypt from 'bcryptjs';
import {
  createTenantUser,
  createTenantBranch,
  createTenantSubject,
  updateTenantUserBranch,
  checkTenantDatabaseReady,
  getTenantUserByEmail,
  getTenantUserById
} from '../config/tenant-prisma.js';

// Central database client
const centralPrisma = new PrismaClient();

// Cache for tenant database connections
const tenantConnections = new Map<string, PrismaClient>();

/**
 * Create a dedicated database for a coaching center
 */
export async function createTenantDatabase(subdomain: string, adminData: {
  name: string;
  email: string;
  password: string;
}): Promise<{
  databaseName: string;
  created: boolean;
  migrated: boolean;
  seeded: boolean;
}> {
  const databaseName = `${subdomain}_coaching`;
  
  console.log(`🔍 Creating tenant database: ${databaseName}`);
  
  try {
    // Step 1: Create the database
    console.log(`📊 Creating database: ${databaseName}`);
    await createDatabase(databaseName);
    
    // Step 2: Run migrations on the new database
    console.log(`🔄 Running migrations on: ${databaseName}`);
    await runMigrationsOnTenantDatabase(databaseName);
    
    // Step 3: Seed the database with initial data
    console.log(`🌱 Seeding database: ${databaseName}`);
    await seedTenantDatabase(subdomain, databaseName, adminData);
    
    console.log(`✅ Tenant database created successfully: ${databaseName}`);
    
    return {
      databaseName,
      created: true,
      migrated: true,
      seeded: true
    };
  } catch (error) {
    console.error(`❌ Error creating tenant database ${databaseName}:`, error);
    throw error;
  }
}

/**
 * Create a new MySQL database
 */
async function createDatabase(databaseName: string): Promise<void> {
  try {
    // Create database using raw MySQL connection
    const mysql = await import('mysql2/promise');
    
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || '',
    });
    
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${databaseName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    await connection.end();
    
    console.log(`✅ Database ${databaseName} created successfully`);
  } catch (error) {
    console.error(`❌ Error creating database ${databaseName}:`, error);
    throw error;
  }
}

/**
 * Run Prisma migrations on a tenant database
 */
async function runMigrationsOnTenantDatabase(databaseName: string): Promise<void> {
  try {
    // Get the base database URL and replace the database name
    const baseUrl = process.env.DATABASE_URL || 'mysql://root:BD4321*BD@localhost:3306/coaching_central';
    const tenantDatabaseUrl = baseUrl.replace(/\/[^\/]+(\?|$)/, `/${databaseName}$1`);
    
    // Store original DATABASE_URL
    const originalDatabaseUrl = process.env.DATABASE_URL;
    
    console.log(`🔄 Running migrations on ${databaseName}...`);
    
    // Set environment variable for this migration
    process.env.TENANT_DATABASE_URL = tenantDatabaseUrl;
    
    try {
      // Use prisma db push with the tenant schema for development
      execSync(`npx prisma db push --schema=prisma/tenant-schema.prisma --force-reset`, {
        stdio: 'inherit',
        env: { 
          ...process.env, 
          DATABASE_URL: tenantDatabaseUrl,
          TENANT_DATABASE_URL: tenantDatabaseUrl
        }
      });

      console.log(`✅ Schema pushed to ${databaseName}`);
    } finally {
      // Restore original DATABASE_URL
      process.env.DATABASE_URL = originalDatabaseUrl;
      delete process.env.TENANT_DATABASE_URL;
    }
  } catch (error) {
    console.error(`❌ Error running migrations on ${databaseName}:`, error);
    throw error;
  }
}

/**
 * Seed a tenant database with initial data
 */
async function seedTenantDatabase(
  subdomain: string,
  databaseName: string,
  adminData: { name: string; email: string; password: string }
): Promise<void> {
  try {
    // Wait a moment for the database to be ready
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if database is ready
    const isReady = await checkTenantDatabaseReady(subdomain);
    if (!isReady) {
      throw new Error(`Tenant database ${databaseName} is not ready`);
    }

    // Hash the admin password
    const hashedPassword = await bcrypt.hash(adminData.password, 12);

    // Create default branch first
    const defaultBranch = await createTenantBranch(subdomain, {
      branchCode: 'MAIN',
      branchName: 'Main Branch',
      status: 'ACTIVE',
    });

    // Create the admin user
    const adminUser = await createTenantUser(subdomain, {
      name: adminData.name,
      email: adminData.email,
      passwordHash: hashedPassword,
      role: 'CENTER_ADMIN',
      status: 'ACTIVE',
      branchId: defaultBranch.id,
    });

    // Create default subjects
    const defaultSubjects = [
      { name: 'Mathematics', code: 'MATH' },
      { name: 'English', code: 'ENG' },
      { name: 'Science', code: 'SCI' },
      { name: 'Bangla', code: 'BAN' },
    ];

    for (const subject of defaultSubjects) {
      await createTenantSubject(subdomain, subject);
    }

    console.log(`✅ Tenant database ${databaseName} seeded successfully`);
  } catch (error) {
    console.error(`❌ Error seeding tenant database ${databaseName}:`, error);
    throw error;
  }
}

/**
 * Get a Prisma-like client for tenant database operations
 * This is a wrapper around raw SQL operations to provide a familiar interface
 */
export function getTenantPrismaClient(subdomain: string): any {
  return {
    user: {
      findUnique: async ({ where, include }: any) => {
        if (where.email) {
          return await getTenantUserByEmail(subdomain, where.email);
        } else if (where.id) {
          return await getTenantUserById(subdomain, where.id);
        }
        throw new Error('Invalid where clause for user.findUnique');
      },
      create: async ({ data }: any) => {
        return await createTenantUser(subdomain, data);
      },
      count: async ({ where }: any) => {
        let query = 'SELECT COUNT(*) as count FROM users WHERE 1=1';
        const params: any[] = [];

        if (where?.role) {
          query += ' AND role = ?';
          params.push(where.role);
        }
        if (where?.status) {
          query += ' AND status = ?';
          params.push(where.status);
        }

        const result = await import('../config/tenant-prisma.js').then(m =>
          m.executeTenantQuery(subdomain, query, params)
        );
        return result[0]?.count || 0;
      },
      findMany: async ({ where, take, skip, orderBy, include }: any) => {
        let query = 'SELECT u.*, b.branchName, b.branchCode FROM users u LEFT JOIN branches b ON u.branchId = b.id WHERE 1=1';
        const params: any[] = [];

        if (where?.role) {
          query += ' AND u.role = ?';
          params.push(where.role);
        }
        if (where?.status) {
          query += ' AND u.status = ?';
          params.push(where.status);
        }
        if (where?.OR) {
          const orConditions = where.OR.map((condition: any) => {
            if (condition.name?.contains) {
              params.push(`%${condition.name.contains}%`);
              return 'u.name LIKE ?';
            }
            if (condition.email?.contains) {
              params.push(`%${condition.email.contains}%`);
              return 'u.email LIKE ?';
            }
            if (condition.phone?.contains) {
              params.push(`%${condition.phone.contains}%`);
              return 'u.phone LIKE ?';
            }
            return '1=0';
          }).join(' OR ');
          query += ` AND (${orConditions})`;
        }

        if (orderBy?.createdAt) {
          query += ` ORDER BY u.createdAt ${orderBy.createdAt.toUpperCase()}`;
        }

        if (take) {
          query += ` LIMIT ${take}`;
        }
        if (skip) {
          query += ` OFFSET ${skip}`;
        }

        return await import('../config/tenant-prisma.js').then(m =>
          m.executeTenantQuery(subdomain, query, params)
        );
      }
    },
    course: {
      count: async ({ where }: any) => {
        let query = 'SELECT COUNT(*) as count FROM courses WHERE 1=1';
        const params: any[] = [];

        if (where?.status) {
          query += ' AND status = ?';
          params.push(where.status);
        }

        const result = await import('../config/tenant-prisma.js').then(m =>
          m.executeTenantQuery(subdomain, query, params)
        );
        return result[0]?.count || 0;
      },
      findMany: async ({ where, include, orderBy }: any) => {
        let query = `
          SELECT c.*, s.name as subjectName, s.code as subjectCode,
                 b.branchName, b.branchCode
          FROM courses c
          LEFT JOIN subjects s ON c.subjectId = s.id
          LEFT JOIN branches br ON c.branchId = br.id
          WHERE 1=1
        `;
        const params: any[] = [];

        if (where?.status) {
          query += ' AND c.status = ?';
          params.push(where.status);
        }

        if (orderBy?.createdAt) {
          query += ` ORDER BY c.createdAt ${orderBy.createdAt.toUpperCase()}`;
        }

        return await import('../config/tenant-prisma.js').then(m =>
          m.executeTenantQuery(subdomain, query, params)
        );
      }
    },
    branch: {
      count: async ({ where }: any) => {
        let query = 'SELECT COUNT(*) as count FROM branches WHERE 1=1';
        const params: any[] = [];

        if (where?.status) {
          query += ' AND status = ?';
          params.push(where.status);
        }

        const result = await import('../config/tenant-prisma.js').then(m =>
          m.executeTenantQuery(subdomain, query, params)
        );
        return result[0]?.count || 0;
      },
      findMany: async ({ where, include, orderBy }: any) => {
        let query = 'SELECT * FROM branches WHERE 1=1';
        const params: any[] = [];

        if (where?.status) {
          query += ' AND status = ?';
          params.push(where.status);
        }

        if (orderBy?.createdAt) {
          query += ` ORDER BY createdAt ${orderBy.createdAt.toUpperCase()}`;
        }

        return await import('../config/tenant-prisma.js').then(m =>
          m.executeTenantQuery(subdomain, query, params)
        );
      }
    },
    $queryRaw: async (query: any) => {
      return await import('../config/tenant-prisma.js').then(m =>
        m.executeTenantQuery(subdomain, 'SELECT 1', [])
      );
    },
    $disconnect: async () => {
      // No-op for now, connections are managed separately
    }
  };
}

/**
 * Check if a tenant database exists
 */
export async function checkTenantDatabaseExists(subdomain: string): Promise<boolean> {
  try {
    const databaseName = `${subdomain}_coaching`;
    const mysql = await import('mysql2/promise');
    
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || '',
    });
    
    const [rows] = await connection.execute(
      'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
      [databaseName]
    );
    
    await connection.end();
    return Array.isArray(rows) && rows.length > 0;
  } catch (error) {
    console.error('Error checking tenant database existence:', error);
    return false;
  }
}

/**
 * Get a real Prisma client for tenant database
 */
export function getRealTenantPrismaClient(subdomain: string): PrismaClient {
  const databaseName = `${subdomain}_coaching`;

  // Check if we already have a connection for this tenant
  if (tenantConnections.has(subdomain)) {
    return tenantConnections.get(subdomain)!;
  }

  // Create new Prisma client for tenant database
  const tenantPrisma = new PrismaClient({
    datasources: {
      db: {
        url: `mysql://root:BD4321*BD@localhost:3306/${databaseName}`
      }
    },
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
  });

  // Cache the connection
  tenantConnections.set(subdomain, tenantPrisma);

  return tenantPrisma;
}

/**
 * Close all tenant database connections
 */
export async function closeAllTenantConnections(): Promise<void> {
  for (const [subdomain, prisma] of tenantConnections.entries()) {
    try {
      await prisma.$disconnect();
      console.log(`✅ Closed connection for tenant: ${subdomain}`);
    } catch (error) {
      console.error(`❌ Error closing connection for tenant ${subdomain}:`, error);
    }
  }
  tenantConnections.clear();
}

export { centralPrisma };
