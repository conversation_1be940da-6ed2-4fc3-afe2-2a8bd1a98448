import sharp from 'sharp'
import { promises as fs } from 'fs'
import path from 'path'
import crypto from 'crypto'

// Ensure uploads directory exists
const UPLOADS_DIR = path.join(process.cwd(), 'uploads')
const IMAGES_DIR = path.join(UPLOADS_DIR, 'images')

export async function ensureUploadDirectories() {
  try {
    await fs.access(UPLOADS_DIR)
  } catch {
    await fs.mkdir(UPLOADS_DIR, { recursive: true })
  }
  
  try {
    await fs.access(IMAGES_DIR)
  } catch {
    await fs.mkdir(IMAGES_DIR, { recursive: true })
  }
}

export interface ImageUploadOptions {
  maxWidth?: number
  maxHeight?: number
  quality?: number
  format?: 'webp' | 'jpeg' | 'png'
  prefix?: string
}

export interface ImageUploadResult {
  filename: string
  originalName: string
  size: number
  width: number
  height: number
  format: string
  path: string
  url: string
}

/**
 * Process and save uploaded image
 * @param buffer - Image buffer from multer
 * @param originalName - Original filename
 * @param options - Processing options
 * @returns Promise<ImageUploadResult>
 */
export async function processAndSaveImage(
  buffer: Buffer,
  originalName: string,
  options: ImageUploadOptions = {}
): Promise<ImageUploadResult> {
  const {
    maxWidth = 1200,
    maxHeight = 1200,
    quality = 80,
    format = 'webp',
    prefix = 'img'
  } = options

  // Ensure directories exist
  await ensureUploadDirectories()

  // Generate unique filename
  const timestamp = Date.now()
  const randomString = crypto.randomBytes(8).toString('hex')
  const filename = `${prefix}_${timestamp}_${randomString}.${format}`
  const filepath = path.join(IMAGES_DIR, filename)

  // Process image with sharp
  const image = sharp(buffer)
  const metadata = await image.metadata()

  // Resize if needed
  let processedImage = image
  if (metadata.width && metadata.height) {
    if (metadata.width > maxWidth || metadata.height > maxHeight) {
      processedImage = image.resize(maxWidth, maxHeight, {
        fit: 'inside',
        withoutEnlargement: true
      })
    }
  }

  // Convert to specified format with quality
  let outputBuffer: Buffer
  let outputMetadata: sharp.OutputInfo

  switch (format) {
    case 'webp':
      const webpResult = await processedImage.webp({ quality }).toBuffer({ resolveWithObject: true })
      outputBuffer = webpResult.data
      outputMetadata = webpResult.info
      break
    case 'jpeg':
      const jpegResult = await processedImage.jpeg({ quality }).toBuffer({ resolveWithObject: true })
      outputBuffer = jpegResult.data
      outputMetadata = jpegResult.info
      break
    case 'png':
      const pngResult = await processedImage.png({ quality }).toBuffer({ resolveWithObject: true })
      outputBuffer = pngResult.data
      outputMetadata = pngResult.info
      break
    default:
      throw new Error(`Unsupported format: ${format}`)
  }

  // Save to disk
  await fs.writeFile(filepath, outputBuffer)

  // Return result
  return {
    filename,
    originalName,
    size: outputBuffer.length,
    width: outputMetadata.width || 0,
    height: outputMetadata.height || 0,
    format,
    path: filepath,
    url: `/uploads/images/${filename}`
  }
}

/**
 * Delete image file
 * @param filename - Image filename to delete
 */
export async function deleteImage(filename: string): Promise<void> {
  if (!filename) return
  
  try {
    const filepath = path.join(IMAGES_DIR, filename)
    await fs.unlink(filepath)
  } catch (error) {
    console.error('Error deleting image:', error)
    // Don't throw error if file doesn't exist
  }
}

/**
 * Get image info
 * @param filename - Image filename
 * @returns Promise<ImageUploadResult | null>
 */
export async function getImageInfo(filename: string): Promise<ImageUploadResult | null> {
  if (!filename) return null
  
  try {
    const filepath = path.join(IMAGES_DIR, filename)
    const stats = await fs.stat(filepath)
    const image = sharp(filepath)
    const metadata = await image.metadata()
    
    return {
      filename,
      originalName: filename,
      size: stats.size,
      width: metadata.width || 0,
      height: metadata.height || 0,
      format: metadata.format || 'unknown',
      path: filepath,
      url: `/uploads/images/${filename}`
    }
  } catch (error) {
    console.error('Error getting image info:', error)
    return null
  }
}

/**
 * Validate image file
 * @param buffer - Image buffer
 * @param originalName - Original filename
 * @returns Promise<boolean>
 */
export async function validateImage(buffer: Buffer, originalName: string): Promise<boolean> {
  try {
    // Check file extension
    const ext = path.extname(originalName).toLowerCase()
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff']
    
    if (!allowedExtensions.includes(ext)) {
      return false
    }

    // Check if buffer is a valid image using sharp
    const image = sharp(buffer)
    const metadata = await image.metadata()
    
    // Must have width and height
    return !!(metadata.width && metadata.height)
  } catch (error) {
    return false
  }
}

/**
 * Get file size in human readable format
 * @param bytes - File size in bytes
 * @returns string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
