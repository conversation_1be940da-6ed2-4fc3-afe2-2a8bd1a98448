import { PrismaClient } from '@prisma/client';

// Cache for tenant database connections
const tenantConnections = new Map<string, PrismaClient>();

/**
 * Get a properly configured Prisma client for tenant database
 */
export function getTenantPrismaClient(subdomain: string): PrismaClient {
  const databaseName = `${subdomain}_coaching`;
  
  // Check if we already have a connection for this tenant
  if (tenantConnections.has(subdomain)) {
    return tenantConnections.get(subdomain)!;
  }
  
  // Create new Prisma client for tenant database
  const tenantPrisma = new PrismaClient({
    datasources: {
      db: {
        url: `mysql://root:BD4321*BD@localhost:3306/${databaseName}`
      }
    },
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
  });
  
  // Cache the connection
  tenantConnections.set(subdomain, tenantPrisma);
  
  return tenantPrisma;
}

/**
 * Close all tenant database connections
 */
export async function closeAllTenantConnections(): Promise<void> {
  for (const [subdomain, prisma] of tenantConnections.entries()) {
    try {
      await prisma.$disconnect();
      console.log(`✅ Closed connection for tenant: ${subdomain}`);
    } catch (error) {
      console.error(`❌ Error closing connection for tenant ${subdomain}:`, error);
    }
  }
  tenantConnections.clear();
}
