/**
 * Internationalization (i18n) utility for multi-language support
 * Supports English (en) and Bengali (bn) languages
 */

import fs from 'fs';
import path from 'path';
import { getCookie } from 'hono/cookie';

// Cache for loaded translations
const translationsCache = new Map();

// Default language
const DEFAULT_LANGUAGE = 'en';

// Supported languages
const SUPPORTED_LANGUAGES = ['en', 'bn'];

/**
 * Load translations for a specific language
 */
function loadTranslations(language) {
  if (translationsCache.has(language)) {
    return translationsCache.get(language);
  }
  
  try {
    const filePath = path.join(process.cwd(), 'src', 'locales', `${language}.json`);
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(fileContent);
    
    translationsCache.set(language, translations);
    return translations;
  } catch (error) {
    console.error(`Failed to load translations for language: ${language}`, error);
    
    // Fallback to default language if not already trying default
    if (language !== DEFAULT_LANGUAGE) {
      return loadTranslations(DEFAULT_LANGUAGE);
    }
    
    return {};
  }
}

/**
 * Get nested value from object using dot notation
 * Example: getNestedValue(obj, 'user.profile.name')
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Replace placeholders in translation string
 * Example: replacePlaceholders("Hello {name}!", { name: "John" }) => "Hello John!"
 */
function replacePlaceholders(text, params = {}) {
  if (!text || typeof text !== 'string') {
    return text;
  }
  
  return text.replace(/\{(\w+)\}/g, (match, key) => {
    return params[key] !== undefined ? params[key] : match;
  });
}

/**
 * Get translation for a key in specified language
 */
export function t(key, params = {}, language = DEFAULT_LANGUAGE) {
  // Validate language
  if (!SUPPORTED_LANGUAGES.includes(language)) {
    language = DEFAULT_LANGUAGE;
  }
  
  const translations = loadTranslations(language);
  const translation = getNestedValue(translations, key);
  
  if (translation === undefined) {
    // Fallback to default language if translation not found
    if (language !== DEFAULT_LANGUAGE) {
      const defaultTranslations = loadTranslations(DEFAULT_LANGUAGE);
      const defaultTranslation = getNestedValue(defaultTranslations, key);
      
      if (defaultTranslation !== undefined) {
        return replacePlaceholders(defaultTranslation, params);
      }
    }
    
    // Return key if no translation found
    console.warn(`Translation not found for key: ${key} in language: ${language}`);
    return key;
  }
  
  return replacePlaceholders(translation, params);
}

/**
 * Get all translations for a specific namespace
 */
export function getNamespace(namespace, language = DEFAULT_LANGUAGE) {
  if (!SUPPORTED_LANGUAGES.includes(language)) {
    language = DEFAULT_LANGUAGE;
  }
  
  const translations = loadTranslations(language);
  return getNestedValue(translations, namespace) || {};
}

/**
 * Check if a language is supported
 */
export function isLanguageSupported(language) {
  return SUPPORTED_LANGUAGES.includes(language);
}

/**
 * Get list of supported languages
 */
export function getSupportedLanguages() {
  return [...SUPPORTED_LANGUAGES];
}

/**
 * Get language info
 */
export function getLanguageInfo(language) {
  const languageInfo = {
    en: {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      direction: 'ltr'
    },
    bn: {
      code: 'bn',
      name: 'Bengali',
      nativeName: 'বাংলা',
      direction: 'ltr'
    }
  };
  
  return languageInfo[language] || languageInfo[DEFAULT_LANGUAGE];
}

/**
 * Get time of day greeting based on current time
 */
export function getTimeOfDay(language = DEFAULT_LANGUAGE) {
  const hour = new Date().getHours();
  
  if (hour < 12) {
    return t('time.morning', {}, language);
  } else if (hour < 17) {
    return t('time.afternoon', {}, language);
  } else if (hour < 21) {
    return t('time.evening', {}, language);
  } else {
    return t('time.night', {}, language);
  }
}

/**
 * Format date according to language locale
 */
export function formatDate(date, language = DEFAULT_LANGUAGE) {
  const dateObj = new Date(date);
  
  const locales = {
    en: 'en-US',
    bn: 'bn-BD'
  };
  
  const locale = locales[language] || locales[DEFAULT_LANGUAGE];
  
  return dateObj.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Format number according to language locale
 */
export function formatNumber(number, language = DEFAULT_LANGUAGE) {
  const locales = {
    en: 'en-US',
    bn: 'bn-BD'
  };
  
  const locale = locales[language] || locales[DEFAULT_LANGUAGE];
  
  return new Intl.NumberFormat(locale).format(number);
}

/**
 * Format currency according to language locale
 */
export function formatCurrency(amount, currency = 'BDT', language = DEFAULT_LANGUAGE) {
  const locales = {
    en: 'en-US',
    bn: 'bn-BD'
  };
  
  const locale = locales[language] || locales[DEFAULT_LANGUAGE];
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency
  }).format(amount);
}

/**
 * Create a translation function bound to a specific language
 */
export function createTranslator(language) {
  return (key, params = {}) => t(key, params, language);
}

/**
 * Middleware to detect and set language from request
 */
export function detectLanguage(c) {
  // Priority order: query param > header > cookie > default
  let language = c.req.query('lang') ||
                 c.req.header('Accept-Language')?.split(',')[0]?.split('-')[0] ||
                 getCookie(c, 'language') ||
                 DEFAULT_LANGUAGE;
  
  // Validate and fallback to default if not supported
  if (!isLanguageSupported(language)) {
    language = DEFAULT_LANGUAGE;
  }
  
  return language;
}

/**
 * Export constants
 */
export { DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES };
