import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';
import type { FC } from 'hono/jsx';

// Initialize database connection
import { initializeDatabase } from './config/database.js';

// Import middleware
import { tenantMiddleware } from './middleware/tenant.js';
import { authMiddleware } from './middleware/auth.js';
import { rateLimitMiddleware } from './middleware/rateLimit.js';
import { i18nMiddleware, createLanguageRoutes } from './middleware/i18n.js';
import { themeMiddleware, createThemeRoutes } from './middleware/theme.js';

// Import routes
import authRoutes from './routes/auth.js';
import adminRoutes from './routes/admin.js';
import centerRoutes from './routes/center.js';
import { api } from './routes/api.js';
import { staticRoutes } from './routes/static.js';

// Initialize Hono app
const app = new Hono();

// Global middleware
app.use('*', logger());
app.use('*', prettyJSON());
app.use('*', secureHeaders());
app.use('*', cors({
  origin: (origin) => {
    // Allow requests from subdomains
    if (!origin) return origin;

    const baseDomain = process.env.BASE_DOMAIN || 'localhost';
    const allowedOrigins = [
      `http://${baseDomain}:${process.env.PORT || 3000}`,
      `https://${baseDomain}`,
      `http://*.${baseDomain}:${process.env.PORT || 3000}`,
      `https://*.${baseDomain}`,
      // Add frontend development servers
      'http://localhost:5173',
      'http://localhost:5174',
      'http://abc.localhost:5173',
      'http://abc.localhost:5174'
    ];

    const isAllowed = allowedOrigins.some(allowed => {
      if (allowed.includes('*')) {
        const pattern = allowed.replace('*', '.*');
        return new RegExp(pattern).test(origin);
      }
      return allowed === origin;
    });

    return isAllowed ? origin : null;
  },
  credentials: true,
  allowHeaders: ['Content-Type', 'Authorization', 'X-Tenant-ID', 'X-Tenant'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']
}));

// Rate limiting
app.use('*', rateLimitMiddleware);

// Internationalization middleware
app.use('*', i18nMiddleware);

// Theme middleware
app.use('*', themeMiddleware);

// Tenant resolution middleware (for subdomain-based multi-tenancy)
app.use('*', tenantMiddleware);

// Health check endpoint
app.get('/health', (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  });
});

// Static file routes (serve before other routes)
app.route('/', staticRoutes);

// API routes
app.route('/api/auth', authRoutes);
app.route('/api/admin', adminRoutes);
app.route('/api/center', centerRoutes);
app.route('/api/i18n', createLanguageRoutes());
app.route('/api/theme', createThemeRoutes());

// API routes
app.route('/api', api);

// 404 handler
app.notFound((c) => {
  return c.json({
    error: 'Not Found',
    message: 'The requested resource was not found',
    path: c.req.path
  }, 404);
});

// Error handler
app.onError((err, c) => {
  console.error('Application Error:', err);
  
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return c.json({
    error: 'Internal Server Error',
    message: isDevelopment ? err.message : 'Something went wrong',
    ...(isDevelopment && { stack: err.stack })
  }, 500);
});

// Initialize database and start server
async function startServer() {
  try {
    // Initialize database connection
    await initializeDatabase();
    console.log('✅ Database initialized successfully');

    const port = parseInt(process.env.PORT || '3000');

    console.log(`🚀 Starting Teaching Center SaaS Platform...`);
    console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🌐 Base Domain: ${process.env.BASE_DOMAIN || 'localhost'}`);
    console.log(`🔗 Server will be available at: http://${process.env.BASE_DOMAIN || 'localhost'}:${port}`);

    console.log(`✅ Server is running on http://localhost:${port}`);
    console.log(`📚 API Documentation: http://localhost:${port}/health`);
    console.log(`🏫 Multi-tenant access: http://[subdomain].${process.env.BASE_DOMAIN || 'localhost'}:${port}`);

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer();

// Bun server export
export default {
  port: parseInt(process.env.PORT || '3000'),
  fetch: app.fetch,
};
