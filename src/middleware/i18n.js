/**
 * Internationalization middleware for language detection and setting
 */

import { Hono } from 'hono';
import { setCookie } from 'hono/cookie';
import { detectLanguage, createTranslator, getLanguageInfo, SUPPORTED_LANGUAGES } from '../utils/i18n.js';

/**
 * Language detection and setup middleware
 */
export function i18nMiddleware(c, next) {
  // Detect language from request
  const language = detectLanguage(c);
  
  // Set language in context
  c.set('language', language);
  c.set('languageInfo', getLanguageInfo(language));
  
  // Create translator function bound to detected language
  const t = createTranslator(language);
  c.set('t', t);
  
  // Add language switching helper
  c.set('switchLanguage', (newLanguage) => {
    if (SUPPORTED_LANGUAGES.includes(newLanguage)) {
      c.set('language', newLanguage);
      c.set('languageInfo', getLanguageInfo(newLanguage));
      c.set('t', createTranslator(newLanguage));
      return true;
    }
    return false;
  });
  
  return next();
}

/**
 * API endpoint for language switching
 */
export function createLanguageRoutes() {
  const routes = new Hono();
  
  // Get supported languages
  routes.get('/languages', (c) => {
    const languages = SUPPORTED_LANGUAGES.map(lang => ({
      code: lang,
      ...getLanguageInfo(lang)
    }));
    
    return c.json({
      success: true,
      data: {
        languages,
        current: c.get('language')
      }
    });
  });
  
  // Set language preference
  routes.post('/language', async (c) => {
    try {
      const { language } = await c.req.json();
      
      if (!SUPPORTED_LANGUAGES.includes(language)) {
        return c.json({
          success: false,
          message: 'Unsupported language'
        }, 400);
      }
      
      // Set language cookie (expires in 1 year)
      setCookie(c, 'language', language, {
        maxAge: 365 * 24 * 60 * 60, // 1 year
        httpOnly: false, // Allow JavaScript access for frontend
        sameSite: 'lax'
      });
      
      return c.json({
        success: true,
        message: 'Language preference updated',
        data: {
          language,
          languageInfo: getLanguageInfo(language)
        }
      });
      
    } catch (error) {
      return c.json({
        success: false,
        message: 'Invalid request'
      }, 400);
    }
  });
  
  return routes;
}

export default i18nMiddleware;
