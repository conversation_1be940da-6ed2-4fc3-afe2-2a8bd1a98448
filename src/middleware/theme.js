/**
 * Theme middleware for dark mode and light mode support
 */

import { Hono } from 'hono';
import { setCookie } from 'hono/cookie';
import { detectTheme, getThemeConfig, getSupportedThemes, SUPPORTED_THEMES } from '../utils/theme.js';

/**
 * Theme detection and setup middleware
 */
export function themeMiddleware(c, next) {
  // Detect theme from request
  const theme = detectTheme(c);
  
  // Set theme in context
  c.set('theme', theme);
  c.set('themeConfig', getThemeConfig(theme));
  
  // Add theme switching helper
  c.set('switchTheme', (newTheme) => {
    if (SUPPORTED_THEMES.includes(newTheme)) {
      c.set('theme', newTheme);
      c.set('themeConfig', getThemeConfig(newTheme));
      return true;
    }
    return false;
  });
  
  return next();
}

/**
 * API endpoint for theme switching
 */
export function createThemeRoutes() {
  const routes = new Hono();
  
  // Get supported themes
  routes.get('/themes', (c) => {
    const themes = getSupportedThemes();
    
    return c.json({
      success: true,
      data: {
        themes,
        current: c.get('theme')
      }
    });
  });
  
  // Set theme preference
  routes.post('/theme', async (c) => {
    try {
      const { theme } = await c.req.json();
      
      if (!SUPPORTED_THEMES.includes(theme)) {
        return c.json({
          success: false,
          message: 'Unsupported theme'
        }, 400);
      }
      
      // Set theme cookie (expires in 1 year)
      setCookie(c, 'theme', theme, {
        maxAge: 365 * 24 * 60 * 60, // 1 year
        httpOnly: false, // Allow JavaScript access for frontend
        sameSite: 'lax'
      });
      
      return c.json({
        success: true,
        message: 'Theme preference updated',
        data: {
          theme,
          themeConfig: getThemeConfig(theme)
        }
      });
      
    } catch (error) {
      return c.json({
        success: false,
        message: 'Invalid request'
      }, 400);
    }
  });
  
  return routes;
}

export default themeMiddleware;
