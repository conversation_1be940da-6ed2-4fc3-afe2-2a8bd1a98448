import { Context, Next } from 'hono';
import { PrismaClient } from '@prisma/client';
import { getCentralPrisma } from '../config/prisma.js';
import { getTenantPrismaClient, checkTenantDatabaseExists, getRealTenantPrismaClient } from '../utils/tenant-database.js';
import { getTenantPrismaClient as getNewTenantPrismaClient } from '../utils/tenant-prisma-client.js';

export interface TenantContext {
  tenantId: string;
  tenantSlug: string;
  tenant: any;
  db: PrismaClient | null; // Can be null in development mode when tenant database is not available
}

declare module 'hono' {
  interface ContextVariableMap {
    tenant: TenantContext;
  }
}

/**
 * Extract tenant slug from host header
 */
function extractTenantFromHost(host: string): string | null {
  // Handle subdomain-based tenancy
  // Examples: abc.localhost:3002 -> abc, demo.example.com -> demo
  const parts = host.split('.');
  
  if (parts.length >= 2) {
    const subdomain = parts[0];
    
    // Skip common non-tenant subdomains
    const skipSubdomains = ['www', 'api', 'admin', 'app', 'mail', 'ftp'];
    if (skipSubdomains.includes(subdomain.toLowerCase())) {
      return null;
    }
    
    // Remove port if present
    const cleanSubdomain = subdomain.split(':')[0];
    
    // Validate subdomain format (alphanumeric and hyphens only)
    if (/^[a-zA-Z0-9-]+$/.test(cleanSubdomain) && cleanSubdomain.length > 0) {
      return cleanSubdomain.toLowerCase();
    }
  }
  
  return null;
}

/**
 * Middleware to resolve tenant from subdomain and set up tenant-specific database connection
 */
export const tenantMiddleware = async (c: Context, next: Next) => {
  try {
    // Skip tenant resolution for OPTIONS requests (CORS preflight)
    if (c.req.method === 'OPTIONS') {
      return await next();
    }

    // Skip tenant resolution for health check and API info endpoints
    const path = c.req.path;
    if (path === '/health' || path === '/api' || path.startsWith('/api/admin')) {
      return await next();
    }

    // Try to get tenant slug from custom header first (for API calls)
    let tenantSlug = c.req.header('x-tenant-slug');

    // If not found in header, extract from host (for browser requests)
    if (!tenantSlug) {
      const host = c.req.header('host') || '';
      tenantSlug = extractTenantFromHost(host);
    }

    if (!tenantSlug) {
      return c.json({
        error: 'Tenant not found',
        message: 'Unable to determine tenant from request. Please check your subdomain or x-tenant-slug header.',
        code: 'TENANT_NOT_FOUND'
      }, 400);
    }

    // Get central database client
    const centralPrisma = getCentralPrisma();

    // Look up tenant in central database
    const tenant = await centralPrisma.center.findUnique({
      where: { 
        subdomain: tenantSlug,
        status: { in: ['ACTIVE', 'TRIAL'] }
      },
      include: {
        subscriptions: {
          where: { status: 'ACTIVE' },
          include: { plan: true },
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });

    if (!tenant) {
      return c.json({
        error: 'Tenant not found',
        message: `No active coaching center found for subdomain: ${tenantSlug}`,
        code: 'TENANT_NOT_FOUND'
      }, 404);
    }

    // Check if tenant database exists
    const databaseExists = await checkTenantDatabaseExists(tenantSlug);
    
    let tenantDb: PrismaClient | null = null;
    
    if (databaseExists) {
      try {
        tenantDb = getNewTenantPrismaClient(tenantSlug);
        // Test the connection
        await tenantDb.$queryRaw`SELECT 1`;
      } catch (error) {
        console.error(`Failed to connect to tenant database for ${tenantSlug}:`, error);
        tenantDb = null;
      }
    }

    // Create tenant context
    const tenantContext: TenantContext = {
      tenantId: tenant.id,
      tenantSlug: tenantSlug,
      tenant: tenant,
      db: tenantDb
    };

    c.set('tenant', tenantContext);

    // Update last activity
    await centralPrisma.center.update({
      where: { id: tenant.id },
      data: { updatedAt: new Date() }
    }).catch(err => {
      console.error('Failed to update tenant last activity:', err);
    });

    console.log(`✅ Tenant resolved: ${tenantSlug} (${tenant.id})`);
    
    await next();
  } catch (error) {
    console.error('Tenant middleware error:', error);
    return c.json({
      error: 'Internal server error',
      message: 'Failed to resolve tenant',
      code: 'TENANT_RESOLUTION_ERROR'
    }, 500);
  }
};

/**
 * Middleware to require tenant database connection
 */
export const requireTenantDatabase = async (c: Context, next: Next) => {
  const tenant = c.get('tenant') as TenantContext;
  
  if (!tenant) {
    return c.json({
      error: 'Tenant not found',
      message: 'Tenant context not available',
      code: 'TENANT_NOT_FOUND'
    }, 400);
  }

  if (!tenant.db) {
    return c.json({
      error: 'Database not available',
      message: `Tenant database not available for ${tenant.tenantSlug}. Please contact support.`,
      code: 'TENANT_DATABASE_NOT_AVAILABLE'
    }, 503);
  }

  await next();
};

/**
 * Helper function to get tenant database from context
 */
export function getTenantDb(c: Context): PrismaClient {
  const tenant = c.get('tenant') as TenantContext;
  
  if (!tenant || !tenant.db) {
    throw new Error('Tenant database not available');
  }
  
  return tenant.db;
}

/**
 * Helper function to get tenant info from context
 */
export function getTenantInfo(c: Context): TenantContext {
  const tenant = c.get('tenant') as TenantContext;
  
  if (!tenant) {
    throw new Error('Tenant context not available');
  }
  
  return tenant;
}
