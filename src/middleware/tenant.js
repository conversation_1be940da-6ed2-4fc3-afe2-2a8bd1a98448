import { executeQuery } from '../config/database.js';

/**
 * Middleware to resolve tenant from subdomain
 * Extracts tenant information from subdomain and adds it to context
 */
export const tenantMiddleware = async (c, next) => {
  try {
    const host = c.req.header('host') || '';
    const baseDomain = process.env.BASE_DOMAIN || 'localhost';
    const port = process.env.PORT || '3000';

    // Debug logging
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Tenant Debug - Host:', host);
    }

    // Extract subdomain from host or custom header (for testing)
    let subdomain = null;
    let tenantInfo = null;

    // Check for custom tenant header (for testing)
    const customTenant = c.req.header('x-tenant-subdomain');
    if (customTenant) {
      subdomain = customTenant;
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Tenant Debug - Using custom header subdomain:', subdomain);
      }
    }
    
    // Handle different host formats
    if (host.includes(':')) {
      // Development: subdomain.localhost:3000
      const hostWithoutPort = host.split(':')[0];
      const parts = hostWithoutPort.split('.');
      
      if (parts.length > 1 && parts[0] !== 'www') {
        subdomain = parts[0];
      }
    } else {
      // Production: subdomain.domain.com
      const parts = host.split('.');

      if (parts.length > 2 && parts[0] !== 'www') {
        subdomain = parts[0];
      }
    }

    // Debug logging
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Tenant Debug - Extracted subdomain:', subdomain);
    }
    
    // If subdomain exists, fetch tenant information
    if (subdomain) {
      try {
        const tenantQuery = `
          SELECT
            c.id,
            c.name,
            c.subdomain,
            c.logo_url,
            c.theme_color,
            c.status,
            c.created_at,
            c.updated_at,
            s.id as subscription_id,
            s.plan_id,
            s.status as subscription_status,
            s.expires_at,
            s.auto_renew,
            p.name as plan_name,
            p.features,
            p.max_students,
            p.max_teachers,
            p.max_branches,
            p.price_monthly,
            p.price_weekly
          FROM centers c
          LEFT JOIN subscriptions s ON c.id = s.tenant_id AND s.status = 'active'
          LEFT JOIN plans p ON s.plan_id = p.id
          WHERE c.subdomain = ? AND c.status IN ('active', 'trial')
          LIMIT 1
        `;

        // Debug logging
        if (process.env.NODE_ENV === 'development') {
          console.log('🔍 Tenant Debug - Querying for subdomain:', subdomain);
        }

        const tenantResults = await executeQuery(tenantQuery, [subdomain]);

        // Debug logging
        if (process.env.NODE_ENV === 'development') {
          console.log('🔍 Tenant Debug - Query results:', tenantResults.length, 'rows');
        }
        
        if (tenantResults.length > 0) {
          const tenant = tenantResults[0];
          
          // Check if subscription is expired
          const now = new Date();
          const expiresAt = new Date(tenant.expires_at);
          
          if (tenant.subscription_status === 'active' && expiresAt > now) {
            tenantInfo = {
              id: tenant.id,
              name: tenant.name,
              subdomain: tenant.subdomain,
              logoUrl: tenant.logo_url,
              themeColor: tenant.theme_color,
              status: tenant.status,
              subscription: {
                id: tenant.subscription_id,
                planId: tenant.plan_id,
                status: tenant.subscription_status,
                expiresAt: tenant.expires_at,
                autoRenew: tenant.auto_renew,
                plan: {
                  name: tenant.plan_name,
                  features: tenant.features ? (typeof tenant.features === 'string' ? JSON.parse(tenant.features) : tenant.features) : [],
                  maxStudents: tenant.max_students,
                  maxTeachers: tenant.max_teachers,
                  maxBranches: tenant.max_branches,
                  priceMonthly: tenant.price_monthly,
                  priceWeekly: tenant.price_weekly
                }
              }
            };
          } else {
            // Subscription expired or inactive
            tenantInfo = {
              id: tenant.id,
              name: tenant.name,
              subdomain: tenant.subdomain,
              status: 'expired',
              subscription: null
            };
          }
        }
      } catch (error) {
        console.error('Error fetching tenant information:', error);
        // Continue without tenant info - will be handled by route handlers
      }
    }
    
    // Add tenant information to context
    c.set('tenant', tenantInfo);
    c.set('subdomain', subdomain);
    c.set('isMultiTenant', !!subdomain);
    
    // Add helper functions to context
    c.set('getTenantId', () => tenantInfo?.id || null);
    c.set('getTenantFeatures', () => tenantInfo?.subscription?.plan?.features || []);
    c.set('hasFeature', (feature) => {
      const features = tenantInfo?.subscription?.plan?.features || [];
      return features.includes(feature);
    });
    c.set('isSubscriptionActive', () => {
      return tenantInfo?.subscription?.status === 'active';
    });
    
    await next();
  } catch (error) {
    console.error('Tenant middleware error:', error);
    
    return c.json({
      error: 'Tenant Resolution Error',
      message: 'Unable to resolve tenant information'
    }, 500);
  }
};

/**
 * Middleware to require valid tenant
 * Use this for routes that must have a valid tenant
 */
export const requireTenant = async (c, next) => {
  const tenant = c.get('tenant');
  
  if (!tenant) {
    return c.json({
      error: 'Tenant Required',
      message: 'This resource requires a valid tenant subdomain'
    }, 400);
  }
  
  if (tenant.status === 'expired') {
    return c.json({
      error: 'Subscription Expired',
      message: 'Your subscription has expired. Please renew to continue using the service.',
      tenant: {
        name: tenant.name,
        subdomain: tenant.subdomain
      }
    }, 402); // Payment Required
  }
  
  await next();
};

/**
 * Middleware to require specific feature
 * Use this for routes that require premium features
 */
export const requireFeature = (feature) => {
  return async (c, next) => {
    const hasFeature = c.get('hasFeature');
    
    if (!hasFeature(feature)) {
      const tenant = c.get('tenant');
      
      return c.json({
        error: 'Feature Not Available',
        message: `This feature (${feature}) is not available in your current plan`,
        tenant: {
          name: tenant?.name,
          plan: tenant?.subscription?.plan?.name
        },
        requiredFeature: feature
      }, 403);
    }
    
    await next();
  };
};

/**
 * Middleware to add tenant context to database queries
 * Automatically adds tenant_id to query context
 */
export const addTenantContext = async (c, next) => {
  const tenantId = c.get('getTenantId')();
  
  if (tenantId) {
    c.set('tenantId', tenantId);
    
    // Helper function to add tenant filter to queries
    c.set('addTenantFilter', (baseQuery, alias = '') => {
      const prefix = alias ? `${alias}.` : '';
      return `${baseQuery} AND ${prefix}tenant_id = ${tenantId}`;
    });
  }
  
  await next();
};

export default {
  tenantMiddleware,
  requireTenant,
  requireFeature,
  addTenantContext
};
