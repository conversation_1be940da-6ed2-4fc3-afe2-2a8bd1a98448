/**
 * Permission-based middleware for role and feature access control
 */

/**
 * Role hierarchy and permissions configuration
 */
const ROLE_HIERARCHY = {
  super_admin: 100,
  center_admin: 80,  // Center owner/admin
  admin: 60,         // Manager/admin
  teacher: 40,
  student: 20
};

const PERMISSIONS = {
  // Admin permissions
  'admin.dashboard': ['super_admin'],
  'admin.centers.view': ['super_admin'],
  'admin.centers.create': ['super_admin'],
  'admin.centers.edit': ['super_admin'],
  'admin.centers.delete': ['super_admin'],
  'admin.plans.view': ['super_admin'],
  'admin.plans.create': ['super_admin'],
  'admin.plans.edit': ['super_admin'],
  'admin.plans.delete': ['super_admin'],
  'admin.users.view': ['super_admin'],
  'admin.users.create': ['super_admin'],
  'admin.users.edit': ['super_admin'],
  'admin.users.delete': ['super_admin'],
  
  // Center management permissions
  'center.dashboard': ['super_admin', 'center_admin', 'admin', 'teacher'],
  'center.profile.view': ['super_admin', 'center_admin', 'admin'],
  'center.profile.edit': ['super_admin', 'center_admin'],
  'center.settings': ['super_admin', 'center_admin'],

  // Staff management permissions
  'center.staff.view': ['super_admin', 'center_admin', 'admin'],
  'center.staff.create': ['super_admin', 'center_admin', 'admin'],
  'center.staff.edit': ['super_admin', 'center_admin', 'admin'],
  'center.staff.delete': ['super_admin', 'center_admin'],

  // Course management permissions
  'center.courses.view': ['super_admin', 'center_admin', 'admin', 'teacher'],
  'center.courses.create': ['super_admin', 'center_admin', 'admin'],
  'center.courses.edit': ['super_admin', 'center_admin', 'admin'],
  'center.courses.delete': ['super_admin', 'center_admin'],

  // Student management permissions
  'center.students.view': ['super_admin', 'center_admin', 'admin', 'teacher'],
  'center.students.create': ['super_admin', 'center_admin', 'admin'],
  'center.students.edit': ['super_admin', 'center_admin', 'admin'],
  'center.students.delete': ['super_admin', 'center_admin'],
  
  // Teacher-specific permissions
  'teacher.classes.view': ['super_admin', 'center_admin', 'admin', 'teacher'],
  'teacher.students.view': ['super_admin', 'center_admin', 'admin', 'teacher'],
  'teacher.attendance.mark': ['super_admin', 'center_admin', 'admin', 'teacher'],
  'teacher.grades.enter': ['super_admin', 'center_admin', 'admin', 'teacher'],

  // Student-specific permissions
  'student.profile.view': ['super_admin', 'center_admin', 'admin', 'teacher', 'student'],
  'student.courses.view': ['super_admin', 'center_admin', 'admin', 'teacher', 'student'],
  'student.grades.view': ['super_admin', 'center_admin', 'admin', 'teacher', 'student'],

  // Financial permissions
  'center.finance.view': ['super_admin', 'center_admin'],
  'center.finance.edit': ['super_admin', 'center_admin'],
  'center.reports.view': ['super_admin', 'center_admin', 'admin'],
};

/**
 * Check if a user has a specific permission
 */
function hasPermission(userRole, permission) {
  const allowedRoles = PERMISSIONS[permission];
  if (!allowedRoles) {
    return false; // Permission doesn't exist
  }
  
  return allowedRoles.includes(userRole);
}

/**
 * Check if a user role has higher or equal hierarchy level than required role
 */
function hasRoleLevel(userRole, requiredRole) {
  const userLevel = ROLE_HIERARCHY[userRole] || 0;
  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;
  
  return userLevel >= requiredLevel;
}

/**
 * Middleware to require specific permission
 */
export function requirePermission(permission) {
  return async (c, next) => {
    const user = c.get('user');
    
    if (!user) {
      return c.json({ 
        success: false, 
        message: 'Authentication required' 
      }, 401);
    }
    
    if (!hasPermission(user.role, permission)) {
      return c.json({ 
        success: false, 
        message: 'Insufficient permissions',
        required_permission: permission,
        user_role: user.role
      }, 403);
    }
    
    await next();
  };
}

/**
 * Middleware to require minimum role level
 */
export function requireRole(requiredRole) {
  return async (c, next) => {
    const user = c.get('user');
    
    if (!user) {
      return c.json({ 
        success: false, 
        message: 'Authentication required' 
      }, 401);
    }
    
    if (!hasRoleLevel(user.role, requiredRole)) {
      return c.json({ 
        success: false, 
        message: 'Insufficient role level',
        required_role: requiredRole,
        user_role: user.role
      }, 403);
    }
    
    await next();
  };
}

/**
 * Middleware to check if user can access tenant-specific resources
 */
export function requireTenantAccess() {
  return async (c, next) => {
    const user = c.get('user');
    const tenant = c.get('tenant');
    
    if (!user) {
      return c.json({ 
        success: false, 
        message: 'Authentication required' 
      }, 401);
    }
    
    // Super admin can access any tenant
    if (user.role === 'super_admin') {
      await next();
      return;
    }
    
    // Other users must belong to the same tenant
    if (!tenant || user.tenant_id !== tenant.id) {
      return c.json({ 
        success: false, 
        message: 'Access denied to this tenant',
        user_tenant: user.tenant_id,
        requested_tenant: tenant?.id
      }, 403);
    }
    
    await next();
  };
}

/**
 * Middleware to check if user can manage other users
 */
export function requireUserManagement() {
  return async (c, next) => {
    const user = c.get('user');
    
    if (!user) {
      return c.json({ 
        success: false, 
        message: 'Authentication required' 
      }, 401);
    }
    
    // Only super_admin, center_admin, and admin can manage users
    const allowedRoles = ['super_admin', 'center_admin', 'admin'];
    
    if (!allowedRoles.includes(user.role)) {
      return c.json({ 
        success: false, 
        message: 'User management access denied',
        user_role: user.role
      }, 403);
    }
    
    await next();
  };
}

/**
 * Get user permissions for frontend
 */
export function getUserPermissions(userRole) {
  const userPermissions = [];
  
  for (const [permission, allowedRoles] of Object.entries(PERMISSIONS)) {
    if (allowedRoles.includes(userRole)) {
      userPermissions.push(permission);
    }
  }
  
  return userPermissions;
}

/**
 * Export permission constants for use in other modules
 */
export { PERMISSIONS, ROLE_HIERARCHY, hasPermission, hasRoleLevel };
