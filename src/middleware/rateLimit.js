/**
 * Simple in-memory rate limiting middleware for Bun/Hono
 * In production, consider using Redis for distributed rate limiting
 */

// In-memory store for rate limiting
const rateLimitStore = new Map();

/**
 * Rate limiting middleware
 */
export const rateLimitMiddleware = async (c, next) => {
  try {
    const windowMs = parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'); // 15 minutes
    const maxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '300');
    
    // Get client identifier (IP address or user ID if authenticated)
    const clientIp = c.req.header('x-forwarded-for') || 
                     c.req.header('x-real-ip') || 
                     c.env?.ip || 
                     'unknown';
    
    const userId = c.get('userId');
    const identifier = userId || clientIp;
    
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get or create rate limit data for this identifier
    let rateLimitData = rateLimitStore.get(identifier);
    
    if (!rateLimitData) {
      rateLimitData = {
        requests: [],
        resetTime: now + windowMs
      };
      rateLimitStore.set(identifier, rateLimitData);
    }
    
    // Remove old requests outside the current window
    rateLimitData.requests = rateLimitData.requests.filter(timestamp => timestamp > windowStart);
    
    // Check if rate limit exceeded
    if (rateLimitData.requests.length >= maxRequests) {
      const retryAfter = Math.ceil((rateLimitData.resetTime - now) / 1000);
      
      return c.json({
        error: 'Rate Limit Exceeded',
        message: `Too many requests. Try again in ${retryAfter} seconds.`,
        retryAfter: retryAfter,
        limit: maxRequests,
        windowMs: windowMs
      }, 429, {
        'Retry-After': retryAfter.toString(),
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': rateLimitData.resetTime.toString()
      });
    }
    
    // Add current request timestamp
    rateLimitData.requests.push(now);
    
    // Update reset time if needed
    if (now > rateLimitData.resetTime) {
      rateLimitData.resetTime = now + windowMs;
    }
    
    // Add rate limit headers
    const remaining = Math.max(0, maxRequests - rateLimitData.requests.length);
    
    c.header('X-RateLimit-Limit', maxRequests.toString());
    c.header('X-RateLimit-Remaining', remaining.toString());
    c.header('X-RateLimit-Reset', rateLimitData.resetTime.toString());
    
    await next();
  } catch (error) {
    console.error('Rate limit middleware error:', error);
    // Continue without rate limiting if there's an error
    await next();
  }
};

/**
 * Stricter rate limiting for sensitive endpoints
 */
export const strictRateLimit = async (c, next) => {
  try {
    const windowMs = 300000; // 5 minutes
    const maxRequests = 5; // Only 5 requests per 5 minutes
    
    const clientIp = c.req.header('x-forwarded-for') || 
                     c.req.header('x-real-ip') || 
                     c.env?.ip || 
                     'unknown';
    
    const userId = c.get('userId');
    const identifier = `strict_${userId || clientIp}`;
    
    const now = Date.now();
    const windowStart = now - windowMs;
    
    let rateLimitData = rateLimitStore.get(identifier);
    
    if (!rateLimitData) {
      rateLimitData = {
        requests: [],
        resetTime: now + windowMs
      };
      rateLimitStore.set(identifier, rateLimitData);
    }
    
    rateLimitData.requests = rateLimitData.requests.filter(timestamp => timestamp > windowStart);
    
    if (rateLimitData.requests.length >= maxRequests) {
      const retryAfter = Math.ceil((rateLimitData.resetTime - now) / 1000);
      
      return c.json({
        error: 'Rate Limit Exceeded',
        message: `Too many sensitive requests. Try again in ${retryAfter} seconds.`,
        retryAfter: retryAfter
      }, 429, {
        'Retry-After': retryAfter.toString()
      });
    }
    
    rateLimitData.requests.push(now);
    
    if (now > rateLimitData.resetTime) {
      rateLimitData.resetTime = now + windowMs;
    }
    
    await next();
  } catch (error) {
    console.error('Strict rate limit middleware error:', error);
    await next();
  }
};

/**
 * Clean up old rate limit data periodically
 */
export const cleanupRateLimitStore = () => {
  const now = Date.now();
  const cleanupThreshold = 3600000; // 1 hour
  
  for (const [identifier, data] of rateLimitStore.entries()) {
    if (now - data.resetTime > cleanupThreshold) {
      rateLimitStore.delete(identifier);
    }
  }
};

// Clean up every 10 minutes
setInterval(cleanupRateLimitStore, 600000);

export default {
  rateLimitMiddleware,
  strictRateLimit,
  cleanupRateLimitStore
};
