import jwt from 'jsonwebtoken';
import { executeQuery } from '../config/database.js';

/**
 * Middleware to verify JW<PERSON> token and authenticate user
 */
export const authMiddleware = async (c, next) => {
  try {
    const authHeader = c.req.header('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        error: 'Unauthorized',
        message: 'No valid authorization token provided'
      }, 401);
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Fetch user information from database
    const userQuery = `
      SELECT 
        u.id,
        u.email,
        u.name,
        u.role,
        u.status,
        u.tenant_id,
        u.branch_id,
        u.last_login,
        u.created_at,
        u.updated_at
      FROM users u
      WHERE u.id = ? AND u.status = 'active'
      LIMIT 1
    `;
    
    const userResults = await executeQuery(userQuery, [decoded.userId]);
    
    if (userResults.length === 0) {
      return c.json({
        error: 'Unauthorized',
        message: 'User not found or inactive'
      }, 401);
    }
    
    const user = userResults[0];
    
    // Check if user belongs to the current tenant (if multi-tenant request)
    const tenantId = c.get('getTenantId')();
    if (tenantId && user.tenant_id !== tenantId) {
      return c.json({
        error: 'Forbidden',
        message: 'User does not belong to this tenant'
      }, 403);
    }
    
    // Add user information to context
    c.set('user', user);
    c.set('userId', user.id);
    c.set('userRole', user.role);
    c.set('userTenantId', user.tenant_id);
    c.set('userBranchId', user.branch_id);
    
    await next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return c.json({
        error: 'Unauthorized',
        message: 'Invalid token'
      }, 401);
    }
    
    if (error.name === 'TokenExpiredError') {
      return c.json({
        error: 'Unauthorized',
        message: 'Token expired'
      }, 401);
    }
    
    console.error('Auth middleware error:', error);
    return c.json({
      error: 'Authentication Error',
      message: 'Unable to authenticate user'
    }, 500);
  }
};

/**
 * Middleware to require specific role
 */
export const requireRole = (allowedRoles) => {
  return async (c, next) => {
    const userRole = c.get('userRole');
    
    if (!allowedRoles.includes(userRole)) {
      return c.json({
        error: 'Forbidden',
        message: `Access denied. Required roles: ${allowedRoles.join(', ')}`
      }, 403);
    }
    
    await next();
  };
};

/**
 * Middleware to require admin role
 */
export const requireAdmin = requireRole(['admin', 'super_admin']);

/**
 * Middleware to require super admin role
 */
export const requireSuperAdmin = requireRole(['super_admin']);

/**
 * Optional auth middleware - doesn't fail if no token provided
 */
export const optionalAuth = async (c, next) => {
  try {
    const authHeader = c.req.header('authorization');
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      const userQuery = `
        SELECT 
          u.id,
          u.email,
          u.name,
          u.role,
          u.status,
          u.tenant_id,
          u.branch_id
        FROM users u
        WHERE u.id = ? AND u.status = 'active'
        LIMIT 1
      `;
      
      const userResults = await executeQuery(userQuery, [decoded.userId]);
      
      if (userResults.length > 0) {
        const user = userResults[0];
        c.set('user', user);
        c.set('userId', user.id);
        c.set('userRole', user.role);
        c.set('userTenantId', user.tenant_id);
        c.set('userBranchId', user.branch_id);
      }
    }
    
    await next();
  } catch (error) {
    // Ignore auth errors for optional auth
    await next();
  }
};

export default {
  authMiddleware,
  requireRole,
  requireAdmin,
  requireSuperAdmin,
  optionalAuth
};
