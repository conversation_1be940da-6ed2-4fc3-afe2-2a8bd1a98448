import jwt from 'jsonwebtoken';
import { Context, Next } from 'hono';
import { getCentralPrisma } from '../config/prisma.js';
import { getTenantPrismaClient } from '../utils/tenant-database.js';
import { executeTenantQuery } from '../config/tenant-prisma.js';

const JWT_SECRET = process.env.JWT_SECRET || 'default-secret';

interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  centerId?: string;
  tenantSlug?: string;
  branchId?: string;
  loginType: 'super_admin' | 'center_admin' | 'teacher';
}

declare module 'hono' {
  interface ContextVariableMap {
    user: any;
    userRole: string;
  }
}

/**
 * Middleware to verify JWT token and authenticate user
 */
export const authMiddleware = async (c: Context, next: Next) => {
  try {
    const authHeader = c.req.header('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({
        success: false,
        message: 'No valid authorization token provided'
      }, 401);
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify JWT token
    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    
    let user = null;
    
    if (decoded.loginType === 'super_admin') {
      // Get user from central database
      const centralPrisma = getCentralPrisma();
      user = await centralPrisma.centerUser.findUnique({
        where: { 
          id: decoded.userId,
          status: 'ACTIVE'
        },
        include: {
          center: true
        }
      });
      
      if (!user) {
        return c.json({
          success: false,
          message: 'User not found or inactive'
        }, 401);
      }
      
      // Set user context for super admin
      c.set('user', {
        id: user.id,
        email: user.email,
        name: user.name,
        role: 'super_admin',
        centerId: user.centerId,
        center: user.center,
        loginType: 'super_admin'
      });
      c.set('userRole', 'super_admin');
      
    } else {
      // Get user from tenant database
      if (!decoded.tenantSlug) {
        return c.json({
          success: false,
          message: 'Tenant information missing from token'
        }, 401);
      }
      
      try {
        // Get user from tenant database using direct query
        const users = await executeTenantQuery(
          decoded.tenantSlug,
          'SELECT u.*, b.branchName, b.branchCode FROM users u LEFT JOIN branches b ON u.branchId = b.id WHERE u.id = ? AND u.status = ?',
          [decoded.userId, 'ACTIVE']
        );

        user = users[0] || null;
        
        if (!user) {
          return c.json({
            success: false,
            message: 'User not found or inactive'
          }, 401);
        }
        
        // Set user context for tenant user (center admin, teacher, etc.)
        c.set('user', {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          centerId: decoded.centerId,
          tenantSlug: decoded.tenantSlug,
          branchId: user.branchId,
          branchName: user.branchName,
          branchCode: user.branchCode,
          loginType: decoded.loginType
        });
        c.set('userRole', user.role);
        
      } catch (error) {
        console.error(`Failed to authenticate user in tenant ${decoded.tenantSlug}:`, error);
        return c.json({
          success: false,
          message: 'Unable to verify user credentials'
        }, 503);
      }
    }
    
    await next();
    
  } catch (error) {
    console.error('Auth middleware error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return c.json({
        success: false,
        message: 'Invalid token'
      }, 401);
    }
    
    if (error.name === 'TokenExpiredError') {
      return c.json({
        success: false,
        message: 'Token expired'
      }, 401);
    }
    
    return c.json({
      success: false,
      message: 'Authentication failed'
    }, 500);
  }
};

/**
 * Middleware to require super admin role
 */
export const requireSuperAdmin = async (c: Context, next: Next) => {
  const user = c.get('user');
  
  if (!user || user.role !== 'super_admin') {
    return c.json({
      success: false,
      message: 'Super admin access required'
    }, 403);
  }
  
  await next();
};

/**
 * Middleware to require center admin role
 */
export const requireCenterAdmin = async (c: Context, next: Next) => {
  const user = c.get('user');
  
  if (!user || !['center_admin', 'super_admin'].includes(user.role)) {
    return c.json({
      success: false,
      message: 'Center admin access required'
    }, 403);
  }
  
  await next();
};

/**
 * Middleware to require teacher role or higher
 */
export const requireTeacher = async (c: Context, next: Next) => {
  const user = c.get('user');
  
  if (!user || !['teacher', 'center_admin', 'super_admin'].includes(user.role)) {
    return c.json({
      success: false,
      message: 'Teacher access required'
    }, 403);
  }
  
  await next();
};

/**
 * Helper function to get user from token (for use in routes)
 */
export const getUserFromToken = async (c: Context) => {
  try {
    const authHeader = c.req.header('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    
    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    
    if (decoded.loginType === 'super_admin') {
      const centralPrisma = getCentralPrisma();
      return await centralPrisma.centerUser.findUnique({
        where: { 
          id: decoded.userId,
          status: 'ACTIVE'
        },
        include: {
          center: true
        }
      });
    } else {
      if (!decoded.tenantSlug) return null;
      
      const tenantPrisma = getTenantPrismaClient(decoded.tenantSlug);
      return await tenantPrisma.user.findUnique({
        where: { 
          id: decoded.userId,
          status: 'ACTIVE'
        },
        include: {
          branch: true
        }
      });
    }
  } catch (error) {
    console.error('Get user from token error:', error);
    return null;
  }
};
