import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize database connections
import { initializeCentralDatabase, checkCentralDatabaseHealth } from './config/prisma.js';

// Import middleware
import { tenantMiddleware, requireTenantDatabase } from './middleware/tenant-prisma.js';
import { authMiddleware } from './middleware/auth-prisma.js';
import { rateLimitMiddleware } from './middleware/rateLimit.js';
import { i18nMiddleware, createLanguageRoutes } from './middleware/i18n.js';
import { themeMiddleware, createThemeRoutes } from './middleware/theme.js';

// Import routes
import adminRoutes from './routes/admin-prisma.js';
import authRoutes from './routes/auth-prisma.js';
import coachingRoutes from './routes/coaching-complete.js';

const app = new Hono();

// Global middleware
app.use('*', logger());
app.use('*', prettyJSON());
app.use('*', secureHeaders());
app.use('*', cors({
  origin: (origin) => {
    // Allow requests from subdomains
    if (!origin) return origin;

    const baseDomain = process.env.BASE_DOMAIN || 'localhost';
    const port = process.env.PORT || '3002';
    const allowedOrigins = [
      `http://${baseDomain}:${port}`,
      `https://${baseDomain}`,
      `http://*.${baseDomain}:${port}`,
      `https://*.${baseDomain}`,
      // Add frontend development servers
      'http://localhost:5175',
      'http://localhost:5176',
      'http://localhost:5177',
      'http://localhost:5178',
      'http://localhost:5174',
      `http://abc.${baseDomain}:5175`,
      `http://abc.${baseDomain}:5176`,
      `http://abc.${baseDomain}:5177`,
      `http://abc.${baseDomain}:5178`,
      `http://abc.${baseDomain}:5174`
    ];

    // Check if origin matches any allowed pattern
    return allowedOrigins.some(allowed => {
      if (allowed.includes('*')) {
        const pattern = allowed.replace('*', '.*');
        return new RegExp(`^${pattern}$`).test(origin);
      }
      return allowed === origin;
    }) ? origin : false;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Tenant-Slug'],
  credentials: true,
}));

// Rate limiting
app.use('*', rateLimitMiddleware);

// Internationalization and theme middleware
app.use('*', i18nMiddleware);
app.use('*', themeMiddleware);

// Health check endpoint
app.get('/health', async (c) => {
  const centralDbHealth = await checkCentralDatabaseHealth();
  
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    environment: process.env.NODE_ENV || 'development',
    database: {
      central: centralDbHealth ? 'connected' : 'disconnected'
    }
  });
});

// API info endpoint
app.get('/api', (c) => {
  return c.json({
    name: 'Coaching Center Management System API',
    version: '2.0.0',
    description: 'Multi-tenant coaching center management system built with Hono and Prisma',
    architecture: 'Multi-database per tenant',
    endpoints: {
      health: '/health',
      admin: '/api/admin/*',
      auth: '/api/auth/*',
      coaching: '/api/coaching/*',
    },
    tenant_access: 'Use subdomain (e.g., abc.localhost:3002) or X-Tenant-Slug header'
  });
});

// Language and theme routes
app.route('/api/language', createLanguageRoutes());
app.route('/api/theme', createThemeRoutes());

// Admin routes (central database, no tenant context needed)
app.route('/api/admin', adminRoutes);

// Authentication routes (can work with both central and tenant contexts)
app.route('/api/auth', authRoutes);

// Tenant-aware routes (require tenant resolution)
app.use('/api/coaching/*', tenantMiddleware);
app.use('/api/coaching/*', requireTenantDatabase);
app.route('/api/coaching', coachingRoutes);

// Error handler
app.onError((err, c) => {
  console.error('Application Error:', err);
  
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return c.json({
    error: 'Internal Server Error',
    message: isDevelopment ? err.message : 'Something went wrong',
    ...(isDevelopment && { stack: err.stack })
  }, 500);
});

// Initialize database and start server
async function startServer() {
  try {
    // Initialize central database connection
    await initializeCentralDatabase();
    console.log('✅ Central database initialized successfully');

    const port = parseInt(process.env.PORT || '3002');

    console.log(`🚀 Starting Coaching Center Management System...`);
    console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🌐 Base Domain: ${process.env.BASE_DOMAIN || 'localhost'}`);
    console.log(`🔗 Server will be available at: http://${process.env.BASE_DOMAIN || 'localhost'}:${port}`);
    console.log(`🏫 Multi-tenant access: http://[subdomain].${process.env.BASE_DOMAIN || 'localhost'}:${port}`);
    console.log(`📊 Architecture: Multi-database per tenant with Prisma ORM`);

    console.log(`✅ Server is running on http://localhost:${port}`);
    console.log(`📚 API Documentation: http://localhost:${port}/api`);

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer();

// Bun server export
export default {
  port: parseInt(process.env.PORT || '3004'),
  fetch: app.fetch,
};
