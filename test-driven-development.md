
## ✅ Playwright MCP TDD Test Case List (Admin, Center, Student, etc.)

### 🔐 A. Authentication & Access

1. ✅ Admin login (valid, invalid credentials)
2. ✅ Admin logout
3. ✅ Center owner login (valid, invalid)
4. ✅ Blocked user cannot log in
5. ✅ Forgot password flow
6. ✅ Session timeout & token renewal

---

### 🛠️ B. Subscription & Plan Management

7. ✅ Add new subscription plan (Free, Basic, Premium)
8. ✅ Edit plan pricing/duration/features
9. ✅ Delete a plan
10. ✅ Activate/Deactivate a plan
11. ✅ Plan feature toggle visibility for center

---

### 🏫 C. Center Management

12. ✅ Create new center (basic info, logo, domain)
13. ✅ Assign subscription plan to center
14. ✅ Center subdomain routing works (`abc.domain.com`)
15. ✅ Login to center subdomain
16. ✅ Edit center profile info
17. ✅ Suspend/ban a center
18. ✅ Center renewal reminder/expired flow

---

### 🌐 D. Multi-Tenancy & Domain Routing

19. ✅ Subdomain route isolation
20. ✅ Prevent cross-tenant data leak
21. ✅ Center-specific theme/logo loads correctly

---

### 🧍 E. User Roles & Permissions

22. ✅ Create users (admin, teacher, HR, accountant)
23. ✅ Role-based access control (CRUD restrictions)
24. ✅ User impersonation by superadmin
25. ✅ Staff login only sees allowed modules

---

### 🏢 F. Branch Management

26. ✅ Add new branch to center
27. ✅ Edit branch info
28. ✅ Delete branch
29. ✅ Assign admins/staff to branch

---

### 📚 G. Course Management

30. ✅ Create course (name, fee, duration)
31. ✅ Edit course
32. ✅ Delete course
33. ✅ Assign course to teacher
34. ✅ Batch-wise course listing

---

### 👩‍🏫 H. Teacher Management

35. ✅ Add teacher profile
36. ✅ Assign subjects
37. ✅ View teacher list
38. ✅ Deactivate teacher
39. ✅ Teacher dashboard access

---

### 👨‍🎓 I. Student Management

40. ✅ Add student with guardian info
41. ✅ Import students (CSV/Excel)
42. ✅ Student list filter by course/batch
43. ✅ Student profile update
44. ✅ View student performance report
45. ✅ View payment history per student

---

### 🗓️ J. Class Scheduling

46. ✅ Create class schedule
47. ✅ Prevent double-booking of teacher/room
48. ✅ View calendar by week/month
49. ✅ Reschedule a class
50. ✅ Auto-schedule suggestion (optional)

---

### 📝 K. Attendance Management

51. ✅ Mark student attendance (manual)
52. ✅ Teacher attendance
53. ✅ Import biometric data
54. ✅ Generate attendance report
55. ✅ SMS on absence sent

---

### 🧑‍💼 L. HRM & Payroll

56. ✅ Add employee record
57. ✅ Set salary structure
58. ✅ Leave approval workflow
59. ✅ Monthly payroll auto-calculation
60. ✅ Download payslip as PDF

---

### 💰 M. Accounting Module

61. ✅ Add income/expense
62. ✅ Fee collection entry
63. ✅ Staff salary payment
64. ✅ Export reports (Excel/PDF)
65. ✅ Filter by date/branch/type

---

### 📦 N. Inventory Module

66. ✅ Add inventory item
67. ✅ Stock in/out transactions
68. ✅ Item usage tracking
69. ✅ Expiry alert (optional)
70. ✅ Inventory report by category

---

### 📑 O. Requisition Module

71. ✅ Submit requisition request
72. ✅ Approve/reject flow
73. ✅ Link request to inventory
74. ✅ Track status of requisition
75. ✅ Notification on approval

---

### 🧪 P. Result Management

76. ✅ Enter marks for student (MCQ/Written)
77. ✅ Auto-calculation of total/grade
78. ✅ Generate report card
79. ✅ Share result with guardian
80. ✅ Filter results by batch/subject

---

### 💬 Q. Notification System

81. ✅ Send SMS (manual, auto on absence)
82. ✅ Push notification to app
83. ✅ WhatsApp message preview
84. ✅ Notification logs per center

---

### 🌐 R. Public Admission Page

85. ✅ Admission form loads correctly
86. ✅ Submit new admission
87. ✅ Payment integration works (bKash, Nagad, card)
88. ✅ Auto-create student profile post-payment

---

### 🖼️ S. ID & Certificate Generator

89. ✅ Generate student ID card
90. ✅ Print teacher ID card
91. ✅ Issue course certificate
92. ✅ Download as PDF

---

### 📂 T. Document Management

93. ✅ Upload student/teacher documents
94. ✅ View/download documents
95. ✅ Set visibility (admin-only, all staff)
96. ✅ Delete expired files

---

### ⚙️ U. Settings & Configurations

97. ✅ Center-specific logo, theme, language
98. ✅ Enable/disable modules per plan
99. ✅ Update institute timezone, currency
100. ✅ Language toggle (English/Bengali)

---

### 🧾 V. Invoicing & Billing

101. ✅ Generate invoice for plan
102. ✅ View payment history
103. ✅ Download invoice PDF
104. ✅ Alert on due invoice

---

### 📈 W. Dashboard & Reports

105. ✅ Admin dashboard with stats
106. ✅ Filter reports by branch/date
107. ✅ Attendance chart
108. ✅ Income/expense overview

---

### 🚫 X. Security & Restrictions

109. ✅ Invalid token redirect to login
110. ✅ Password policy enforcement
111. ✅ Blocked user access
112. ✅ Rate limit sensitive routes

---

### 📱 Y. Mobile Compatibility

113. ✅ Mobile view responsive for all modules
114. ✅ Push notifications via Firebase
115. ✅ Student can check schedule, results from mobile

---

### ♻️ Z. Backup & Recovery (Optional Premium)

116. ✅ Data backup trigger
117. ✅ Tenant-specific DB dump
118. ✅ Restore backup
119. ✅ Email backup link to center owner

---

