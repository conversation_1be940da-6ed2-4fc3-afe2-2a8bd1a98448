// This is the Prisma schema file for tenant databases (coaching center specific data)
// Each coaching center gets its own database with this schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// TENANT DATABASE MODELS (Coaching Center Specific)
// ============================================================================

model User {
  id                    String            @id @default(cuid())
  name                  String
  email                 String            @unique
  passwordHash          String
  phone                 String?
  address               String?           @db.Text
  dateOfBirth           DateTime?
  gender                Gender?
  bloodGroup            BloodGroup?
  role                  UserRole
  status                UserStatus        @default(ACTIVE)
  
  // Profile
  profileImage          String?
  emergencyContact      String?
  parentName            String?
  parentPhone           String?
  admissionDate         DateTime?
  
  // Employee specific
  employeeId            String?
  qualification         String?           @db.Text
  experienceYears       Int?
  salary                Decimal?          @db.Decimal(10, 2)
  
  // Branch assignment
  branchId              String?
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  branch                Branch?           @relation(fields: [branchId], references: [id])
  teacherBatches        Batch[]           @relation("TeacherBatches")
  studentEnrollments    Enrollment[]
  attendanceRecords     Attendance[]
  examResults           ExamResult[]
  feeRecords            FeeRecord[]
  
  @@map("users")
}

model Branch {
  id                    String            @id @default(cuid())
  branchCode            String            @unique
  branchName            String
  address               String?           @db.Text
  phone                 String?
  email                 String?
  managerId             String?
  establishmentDate     DateTime?
  settings              Json?
  status                BranchStatus      @default(ACTIVE)
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  users                 User[]
  courses               Course[]
  batches               Batch[]
  rooms                 Room[]
  inventory             InventoryItem[]
  expenses              Expense[]
  
  @@map("branches")
}

model Subject {
  id                    String            @id @default(cuid())
  name                  String
  code                  String?
  description           String?           @db.Text
  status                SubjectStatus     @default(ACTIVE)
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  courses               Course[]
  
  @@map("subjects")
}

model Course {
  id                    String            @id @default(cuid())
  branchId              String?
  subjectId             String?
  name                  String
  code                  String?
  description           String?           @db.Text
  durationMonths        Int?
  fee                   Decimal?          @db.Decimal(10, 2)
  status                CourseStatus      @default(ACTIVE)
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  branch                Branch?           @relation(fields: [branchId], references: [id])
  subject               Subject?          @relation(fields: [subjectId], references: [id])
  batches               Batch[]
  enrollments           Enrollment[]
  
  @@map("courses")
}

model Batch {
  id                    String            @id @default(cuid())
  branchId              String?
  courseId              String
  teacherId             String?
  name                  String
  startDate             DateTime?
  endDate               DateTime?
  scheduleDays          Json?             // ["monday", "wednesday", "friday"]
  startTime             String?           // "09:00"
  endTime               String?           // "10:30"
  maxStudents           Int               @default(30)
  status                BatchStatus       @default(ACTIVE)
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  branch                Branch?           @relation(fields: [branchId], references: [id])
  course                Course            @relation(fields: [courseId], references: [id])
  teacher               User?             @relation("TeacherBatches", fields: [teacherId], references: [id])
  enrollments           Enrollment[]
  attendanceRecords     Attendance[]
  schedules             Schedule[]
  
  @@map("batches")
}

model Enrollment {
  id                    String            @id @default(cuid())
  studentId             String
  courseId              String
  batchId               String?
  enrollmentDate        DateTime          @default(now())
  status                EnrollmentStatus  @default(ACTIVE)
  
  // Fee details
  courseFee             Decimal?          @db.Decimal(10, 2)
  discount              Decimal?          @db.Decimal(10, 2)
  finalFee              Decimal?          @db.Decimal(10, 2)
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  student               User              @relation(fields: [studentId], references: [id])
  course                Course            @relation(fields: [courseId], references: [id])
  batch                 Batch?            @relation(fields: [batchId], references: [id])
  feeRecords            FeeRecord[]
  
  @@map("enrollments")
}

model Room {
  id                    String            @id @default(cuid())
  branchId              String
  roomCode              String
  roomName              String
  roomType              RoomType          @default(CLASSROOM)
  capacity              Int               @default(0)
  equipment             String?           @db.Text
  notes                 String?           @db.Text
  status                RoomStatus        @default(AVAILABLE)
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  branch                Branch            @relation(fields: [branchId], references: [id])
  schedules             Schedule[]
  
  @@unique([branchId, roomCode])
  @@map("rooms")
}

model Schedule {
  id                    String            @id @default(cuid())
  batchId               String
  roomId                String?
  dayOfWeek             DayOfWeek
  startTime             String            // "09:00"
  endTime               String            // "10:30"
  isActive              Boolean           @default(true)
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  batch                 Batch             @relation(fields: [batchId], references: [id])
  room                  Room?             @relation(fields: [roomId], references: [id])
  
  @@map("schedules")
}

model Attendance {
  id                    String            @id @default(cuid())
  studentId             String
  batchId               String
  date                  DateTime
  status                AttendanceStatus  @default(PRESENT)
  notes                 String?

  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt

  // Relationships
  student               User              @relation(fields: [studentId], references: [id])
  batch                 Batch             @relation(fields: [batchId], references: [id])

  @@unique([studentId, batchId, date])
  @@map("attendance")
}

model Exam {
  id                    String            @id @default(cuid())
  name                  String
  description           String?           @db.Text
  examDate              DateTime
  duration              Int               // in minutes
  totalMarks            Decimal           @db.Decimal(10, 2)
  passingMarks          Decimal           @db.Decimal(10, 2)
  examType              ExamType          @default(WRITTEN)
  status                ExamStatus        @default(SCHEDULED)

  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt

  // Relationships
  results               ExamResult[]

  @@map("exams")
}

model ExamResult {
  id                    String            @id @default(cuid())
  examId                String
  studentId             String
  marksObtained         Decimal           @db.Decimal(10, 2)
  grade                 String?
  remarks               String?           @db.Text

  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt

  // Relationships
  exam                  Exam              @relation(fields: [examId], references: [id])
  student               User              @relation(fields: [studentId], references: [id])

  @@unique([examId, studentId])
  @@map("exam_results")
}

model FeeRecord {
  id                    String            @id @default(cuid())
  enrollmentId          String
  studentId             String
  amount                Decimal           @db.Decimal(10, 2)
  feeType               FeeType           @default(MONTHLY)
  dueDate               DateTime
  paidDate              DateTime?
  status                FeeStatus         @default(PENDING)
  paymentMethod         String?
  transactionId         String?
  discount              Decimal?          @db.Decimal(10, 2)
  lateFee               Decimal?          @db.Decimal(10, 2)
  notes                 String?           @db.Text

  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt

  // Relationships
  enrollment            Enrollment        @relation(fields: [enrollmentId], references: [id])
  student               User              @relation(fields: [studentId], references: [id])

  @@map("fee_records")
}

model InventoryItem {
  id                    String            @id @default(cuid())
  branchId              String
  name                  String
  description           String?           @db.Text
  category              String?
  sku                   String?           @unique
  quantity              Int               @default(0)
  minQuantity           Int               @default(0)
  unitPrice             Decimal?          @db.Decimal(10, 2)
  sellingPrice          Decimal?          @db.Decimal(10, 2)
  status                InventoryStatus   @default(ACTIVE)

  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt

  // Relationships
  branch                Branch            @relation(fields: [branchId], references: [id])

  @@map("inventory_items")
}

model Expense {
  id                    String            @id @default(cuid())
  branchId              String
  title                 String
  description           String?           @db.Text
  amount                Decimal           @db.Decimal(10, 2)
  category              String
  expenseDate           DateTime
  paymentMethod         String?
  receipt               String?
  approvedBy            String?
  status                ExpenseStatus     @default(PENDING)

  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt

  // Relationships
  branch                Branch            @relation(fields: [branchId], references: [id])

  @@map("expenses")
}

// ============================================================================
// ENUMS
// ============================================================================

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum BloodGroup {
  A_POSITIVE
  A_NEGATIVE
  B_POSITIVE
  B_NEGATIVE
  AB_POSITIVE
  AB_NEGATIVE
  O_POSITIVE
  O_NEGATIVE
}

enum UserRole {
  CENTER_ADMIN
  TEACHER
  STUDENT
  STAFF
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum BranchStatus {
  ACTIVE
  INACTIVE
}

enum SubjectStatus {
  ACTIVE
  INACTIVE
}

enum CourseStatus {
  ACTIVE
  INACTIVE
}

enum BatchStatus {
  ACTIVE
  COMPLETED
  CANCELLED
}

enum EnrollmentStatus {
  ACTIVE
  COMPLETED
  CANCELLED
  TRANSFERRED
}

enum RoomType {
  CLASSROOM
  LAB
  LIBRARY
  OFFICE
  MEETING_ROOM
  OTHER
}

enum RoomStatus {
  AVAILABLE
  OCCUPIED
  MAINTENANCE
  UNAVAILABLE
}

enum DayOfWeek {
  SUNDAY
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum ExamType {
  WRITTEN
  ORAL
  PRACTICAL
  ONLINE
}

enum ExamStatus {
  SCHEDULED
  ONGOING
  COMPLETED
  CANCELLED
}

enum FeeType {
  ADMISSION
  MONTHLY
  EXAM
  CERTIFICATE
  OTHER
}

enum FeeStatus {
  PENDING
  PAID
  OVERDUE
  WAIVED
}

enum InventoryStatus {
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
}

enum ExpenseStatus {
  PENDING
  APPROVED
  REJECTED
  PAID
}
