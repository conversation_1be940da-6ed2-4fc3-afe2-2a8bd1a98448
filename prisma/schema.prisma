// This is your Prisma schema file for the central database
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// CENTRAL DATABASE MODELS (System Administration)
// ============================================================================

model Center {
  id                    String            @id @default(cuid())
  name                  String
  subdomain             String            @unique
  email                 String            @unique
  phone                 String?
  address               String?           @db.Text
  description           String?           @db.Text
  logoUrl               String?
  website               String?
  status                CenterStatus      @default(ACTIVE)
  
  // Database info
  databaseName          String?           // e.g., "abc_coaching"
  databaseCreated       Boolean           @default(false)
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  subscriptions         Subscription[]
  centerUsers           CenterUser[]
  domains               Domain[]
  
  @@map("centers")
}

model CenterUser {
  id                    String            @id @default(cuid())
  centerId              String
  name                  String
  email                 String
  passwordHash          String
  phone                 String?
  role                  CenterUserRole    @default(CENTER_ADMIN)
  status                UserStatus        @default(ACTIVE)
  
  // Profile
  profileImage          String?
  lastLoginAt           DateTime?
  emailVerifiedAt       DateTime?
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  center                Center            @relation(fields: [centerId], references: [id], onDelete: Cascade)
  
  @@unique([email, centerId])
  @@map("center_users")
}

model SubscriptionPlan {
  id                    String            @id @default(cuid())
  name                  String
  slug                  String            @unique
  description           String?           @db.Text
  price                 Decimal           @db.Decimal(10, 2)
  currency              String            @default("BDT")
  billingInterval       BillingInterval   @default(MONTHLY)
  trialDays             Int               @default(7)
  
  // Features
  maxStudents           Int               @default(100)
  maxTeachers           Int               @default(10)
  maxBranches           Int               @default(1)
  hasInventory          Boolean           @default(false)
  hasExams              Boolean           @default(true)
  hasReports            Boolean           @default(true)
  hasAPI                Boolean           @default(false)
  
  // Status
  isActive              Boolean           @default(true)
  isPopular             Boolean           @default(false)
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  subscriptions         Subscription[]
  
  @@map("subscription_plans")
}

model Subscription {
  id                    String                @id @default(cuid())
  centerId              String
  planId                String
  status                SubscriptionStatus    @default(TRIAL)
  
  // Billing
  currentPeriodStart    DateTime
  currentPeriodEnd      DateTime
  cancelAtPeriodEnd     Boolean               @default(false)
  canceledAt            DateTime?
  
  // Trial
  trialStart            DateTime?
  trialEnd              DateTime?
  
  // Timestamps
  createdAt             DateTime              @default(now())
  updatedAt             DateTime              @updatedAt
  
  // Relationships
  center                Center                @relation(fields: [centerId], references: [id], onDelete: Cascade)
  plan                  SubscriptionPlan      @relation(fields: [planId], references: [id])
  payments              Payment[]
  
  @@map("subscriptions")
}

model Payment {
  id                    String            @id @default(cuid())
  subscriptionId        String
  amount                Decimal           @db.Decimal(10, 2)
  currency              String            @default("BDT")
  status                PaymentStatus     @default(PENDING)
  
  // Payment details
  paymentMethod         String?
  transactionId         String?
  gatewayResponse       Json?
  
  // Timestamps
  paidAt                DateTime?
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  subscription          Subscription      @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  
  @@map("payments")
}

model Domain {
  id                    String            @id @default(cuid())
  centerId              String
  domain                String            @unique
  isVerified            Boolean           @default(false)
  isPrimary             Boolean           @default(false)
  
  // SSL
  sslEnabled            Boolean           @default(false)
  sslExpiresAt          DateTime?
  
  // Timestamps
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  
  // Relationships
  center                Center            @relation(fields: [centerId], references: [id], onDelete: Cascade)
  
  @@map("domains")
}

// ============================================================================
// ENUMS
// ============================================================================

enum CenterStatus {
  ACTIVE
  SUSPENDED
  INACTIVE
  TRIAL
}

enum CenterUserRole {
  CENTER_ADMIN
  MANAGER
  STAFF
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum BillingInterval {
  MONTHLY
  YEARLY
}

enum SubscriptionStatus {
  TRIAL
  ACTIVE
  PAST_DUE
  CANCELED
  UNPAID
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}
