# Server Configuration
PORT=3000
NODE_ENV=development
BASE_DOMAIN=localhost

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=coaching
DB_USER=root
DB_PASS=BD4321*BD

# Test Database
DB_NAME_TEST=coaching_test
DB_USER_TEST=root
DB_PASS_TEST=BD4321*BD

# JWT Configuration
JWT_SECRET=teaching_center_jwt_secret_key_development_only_change_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_PROJECT_ID=teaching-center-saas
FIREBASE_MESSAGING_SENDER_ID=123456789
FIREBASE_APP_ID=your_firebase_app_id

# SMS Gateway Configuration
SMS_API_KEY=your_sms_api_key
SMS_API_URL=https://api.smsgateway.com/send
SMS_SENDER_ID=TEACHING

# Payment Gateway Configuration
# SSLCommerz
SSLCOMMERZ_STORE_ID=your_store_id
SSLCOMMERZ_STORE_PASSWORD=your_store_password
SSLCOMMERZ_IS_LIVE=false

# bKash
BKASH_APP_KEY=your_bkash_app_key
BKASH_APP_SECRET=your_bkash_app_secret
BKASH_USERNAME=your_bkash_username
BKASH_PASSWORD=your_bkash_password
BKASH_IS_LIVE=false

# Nagad
NAGAD_MERCHANT_ID=your_nagad_merchant_id
NAGAD_MERCHANT_PRIVATE_KEY=your_nagad_private_key
NAGAD_PGP_PUBLIC_KEY=your_nagad_pgp_public_key
NAGAD_IS_LIVE=false

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document
UPLOAD_PATH=./uploads

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=<EMAIL>

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=300

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_key

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# Notification Configuration
NOTIFICATION_ENABLED=true
PUSH_NOTIFICATION_ENABLED=true
SMS_NOTIFICATION_ENABLED=true
EMAIL_NOTIFICATION_ENABLED=true

# Multi-tenancy Configuration
SUBDOMAIN_ENABLED=true
DEFAULT_PLAN=free
TRIAL_PERIOD_DAYS=7

# Feature Flags
FEATURE_BIOMETRIC_ATTENDANCE=false
FEATURE_WHATSAPP_INTEGRATION=false
FEATURE_ADVANCED_REPORTS=true
FEATURE_MOBILE_APP=false

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs

# Cache Configuration
CACHE_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Monitoring
MONITORING_ENABLED=false
SENTRY_DSN=your_sentry_dsn
