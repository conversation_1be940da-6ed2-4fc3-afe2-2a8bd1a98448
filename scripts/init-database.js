import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import mysql from 'mysql2/promise';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || ''
};

async function initializeDatabase() {
  let connection;
  
  try {
    console.log('🔄 Connecting to MySQL server...');
    connection = await mysql.createConnection(dbConfig);
    
    // Create database if it doesn't exist
    const dbName = process.env.DB_NAME || 'coaching';
    console.log(`🔄 Creating database '${dbName}' if it doesn't exist...`);
    await connection.query(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);

    // Use the database
    await connection.query(`USE \`${dbName}\``);
    console.log(`✅ Connected to database '${dbName}'`);
    
    // Read and execute schema file
    const schemaPath = join(__dirname, 'create-schema.sql');
    console.log('Reading schema from:', schemaPath);
    const schema = readFileSync(schemaPath, 'utf8');
    console.log('Schema file size:', schema.length, 'characters');

    console.log('🔄 Creating tables and inserting initial data...');

    // Remove comments and split into statements more carefully
    const cleanedSchema = schema
      .split('\n')
      .filter(line => !line.trim().startsWith('--'))
      .join('\n');

    // Split by semicolon followed by newline to avoid splitting within statements
    const statements = cleanedSchema
      .split(/;\s*\n/)
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0)
      .map(stmt => stmt.endsWith(';') ? stmt : stmt + ';'); // Ensure each statement ends with semicolon

    console.log(`Found ${statements.length} SQL statements to execute`);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        console.log(`Executing statement ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`);
        try {
          await connection.query(statement);
        } catch (error) {
          console.error(`Error executing statement ${i + 1}:`, error.message);
          console.error('Statement:', statement);
          throw error;
        }
      }
    }

    console.log('✅ Database schema created successfully');
    
    // Create test database if in development
    if (process.env.NODE_ENV === 'development') {
      const testDbName = process.env.DB_NAME_TEST || 'coaching_test';
      console.log(`🔄 Creating test database '${testDbName}'...`);
      await connection.query(`CREATE DATABASE IF NOT EXISTS \`${testDbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);

      // Use test database and create schema
      await connection.query(`USE \`${testDbName}\``);

      // Execute schema statements for test database
      for (const statement of statements) {
        if (statement.trim()) {
          await connection.query(statement);
        }
      }
      console.log(`✅ Test database '${testDbName}' created successfully`);
    }
    
    console.log('🎉 Database initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeDatabase();
}

export default initializeDatabase;
