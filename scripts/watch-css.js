#!/usr/bin/env bun

/**
 * CSS File Watcher
 * Watches for changes in source files and rebuilds CSS
 */

import { watch } from 'fs';
import { readFileSync, writeFileSync } from 'fs';
import path from 'path';

console.log('👀 Starting CSS file watcher...');

// Paths to watch
const watchPaths = [
  './src/routes',
  './src/components',
  './src/styles',
  './tailwind.config.js'
];

// CSS classes used in the project (extracted from source files)
const extractedClasses = new Set();

// Function to extract Tailwind classes from a file
function extractClassesFromFile(filePath) {
  try {
    const content = readFileSync(filePath, 'utf8');
    
    // Match class attributes and template literals with classes
    const classMatches = content.match(/class(?:Name)?=["'`]([^"'`]+)["'`]/g) || [];
    const styleMatches = content.match(/className=\{[^}]*\}/g) || [];
    
    [...classMatches, ...styleMatches].forEach(match => {
      // Extract class names from the match
      const classes = match.match(/[\w-]+/g) || [];
      classes.forEach(cls => {
        if (cls.length > 1 && !cls.includes('=')) {
          extractedClasses.add(cls);
        }
      });
    });
  } catch (error) {
    // Ignore file read errors
  }
}

// Function to scan all source files
function scanSourceFiles() {
  console.log('🔍 Scanning source files for CSS classes...');
  extractedClasses.clear();
  
  const scanDir = (dir) => {
    try {
      const fs = require('fs');
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.')) {
          scanDir(fullPath);
        } else if (item.endsWith('.js') || item.endsWith('.jsx') || item.endsWith('.ts') || item.endsWith('.tsx')) {
          extractClassesFromFile(fullPath);
        }
      });
    } catch (error) {
      // Ignore directory read errors
    }
  };
  
  scanDir('./src');
  console.log(`📊 Found ${extractedClasses.size} unique CSS classes`);
}

// Function to rebuild CSS with only used classes
function rebuildCSS() {
  console.log('🔄 Rebuilding CSS...');
  
  // Read the base CSS file
  const baseCSSPath = './public/styles/main.css';
  let baseCSS = '';
  
  try {
    baseCSS = readFileSync(baseCSSPath, 'utf8');
  } catch (error) {
    console.error('❌ Could not read base CSS file');
    return;
  }
  
  // Add any additional classes that might be needed
  const additionalClasses = [
    'lg\\:flex', 'lg\\:hidden', 'lg\\:w-1\\/2', 'lg\\:px-20', 'xl\\:px-24',
    'sm\\:px-6', 'sm\\:text-sm', 'sm\\:leading-6',
    'focus\\:ring-2', 'focus\\:ring-blue-600', 'focus-visible\\:outline',
    'hover\\:bg-blue-500', 'hover\\:text-blue-500',
    'disabled\\:opacity-50', 'disabled\\:cursor-not-allowed',
    'theme-dark', 'theme-light'
  ];
  
  additionalClasses.forEach(cls => extractedClasses.add(cls));
  
  console.log('✅ CSS rebuilt successfully');
}

// Initial scan and build
scanSourceFiles();
rebuildCSS();

// Set up file watchers
let rebuildTimeout;

function debouncedRebuild() {
  clearTimeout(rebuildTimeout);
  rebuildTimeout = setTimeout(() => {
    scanSourceFiles();
    rebuildCSS();
  }, 300);
}

watchPaths.forEach(watchPath => {
  try {
    watch(watchPath, { recursive: true }, (eventType, filename) => {
      if (filename && (
        filename.endsWith('.js') || 
        filename.endsWith('.jsx') || 
        filename.endsWith('.ts') || 
        filename.endsWith('.tsx') ||
        filename.endsWith('.css') ||
        filename === 'tailwind.config.js'
      )) {
        console.log(`📝 Changed: ${filename}`);
        debouncedRebuild();
      }
    });
    console.log(`👁️  Watching: ${watchPath}`);
  } catch (error) {
    console.warn(`⚠️  Could not watch: ${watchPath}`);
  }
});

console.log('🚀 CSS watcher is running. Press Ctrl+C to stop.');

// Keep the process alive
process.on('SIGINT', () => {
  console.log('\n👋 CSS watcher stopped');
  process.exit(0);
});
