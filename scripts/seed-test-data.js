import bcrypt from 'bcryptjs';
import { executeQuery, initializeDatabase, initializeTestDatabase } from '../src/config/database.js';

async function seedTestData() {
  try {
    console.log('🔄 Initializing database connections...');
    await initializeDatabase();
    await initializeTestDatabase();
    
    console.log('🔄 Seeding test data...');
    
    // Seed both main and test databases
    const seedMainDb = process.env.NODE_ENV === 'development';
    const useTestDb = true;
    
    // Create test centers
    console.log('📍 Creating test centers...');
    
    // Sunrise Academy
    const sunriseResult = await executeQuery(
      `INSERT INTO centers (name, subdomain, email, phone, address, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [
        'Sunrise Academy',
        'sunriseacademy',
        '<EMAIL>',
        '02-9876543',
        '123 Main Street, Dhaka'
      ],
      useTestDb
    );
    
    const sunriseId = sunriseResult.insertId;
    
    // ABC Coaching
    const abcResult = await executeQuery(
      `INSERT INTO centers (name, subdomain, email, phone, address, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [
        'ABC Coaching Center',
        'abccoaching',
        '<EMAIL>',
        '02-1234567',
        '456 Secondary Road, Chittagong'
      ],
      useTestDb
    );
    
    const abcId = abcResult.insertId;
    
    // Test Center (Expired)
    const testResult = await executeQuery(
      `INSERT INTO centers (name, subdomain, email, phone, address, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [
        'Test Center',
        'testcenter',
        '<EMAIL>',
        '02-7654321',
        '789 Test Avenue, Sylhet'
      ],
      useTestDb
    );
    
    const testId = testResult.insertId;
    
    // Create subscriptions
    console.log('💳 Creating subscriptions...');
    
    // Sunrise Academy - Premium (Active)
    const premiumPlanResult = await executeQuery(
      'SELECT id FROM plans WHERE name = "Premium" LIMIT 1',
      [],
      useTestDb
    );
    const premiumPlanId = premiumPlanResult[0].id;
    
    const futureDate = new Date();
    futureDate.setMonth(futureDate.getMonth() + 1);
    
    await executeQuery(
      `INSERT INTO subscriptions (center_id, plan_id, status, expires_at, auto_renew, created_at, updated_at)
       VALUES (?, ?, 'active', ?, 1, NOW(), NOW())`,
      [sunriseId, premiumPlanId, futureDate],
      useTestDb
    );
    
    // ABC Coaching - Basic (Active)
    const basicPlanResult = await executeQuery(
      'SELECT id FROM plans WHERE name = "Basic" LIMIT 1',
      [],
      useTestDb
    );
    const basicPlanId = basicPlanResult[0].id;
    
    await executeQuery(
      `INSERT INTO subscriptions (center_id, plan_id, status, expires_at, auto_renew, created_at, updated_at)
       VALUES (?, ?, 'active', ?, 1, NOW(), NOW())`,
      [abcId, basicPlanId, futureDate],
      useTestDb
    );
    
    // Test Center - Free (Expired)
    const freePlanResult = await executeQuery(
      'SELECT id FROM plans WHERE name = "Free" LIMIT 1',
      [],
      useTestDb
    );
    const freePlanId = freePlanResult[0].id;
    
    const pastDate = new Date();
    pastDate.setMonth(pastDate.getMonth() - 1);
    
    await executeQuery(
      `INSERT INTO subscriptions (center_id, plan_id, status, expires_at, auto_renew, created_at, updated_at)
       VALUES (?, ?, 'expired', ?, 0, NOW(), NOW())`,
      [testId, freePlanId, pastDate],
      useTestDb
    );
    
    // Create test users
    console.log('👥 Creating test users...');
    
    const saltRounds = 12;
    
    // Hash passwords
    const ownerPassword = await bcrypt.hash('Owner123!', saltRounds);
    const managerPassword = await bcrypt.hash('Manager123!', saltRounds);
    const teacherPassword = await bcrypt.hash('Teacher123!', saltRounds);
    const hrPassword = await bcrypt.hash('HR123!', saltRounds);
    const accountantPassword = await bcrypt.hash('Account123!', saltRounds);
    const studentPassword = await bcrypt.hash('Student123!', saltRounds);
    const blockedPassword = await bcrypt.hash('Blocked123!', saltRounds);
    
    // Sunrise Academy Users
    const users = [
      // Center Owner
      {
        tenant_id: sunriseId,
        email: '<EMAIL>',
        password_hash: ownerPassword,
        name: 'Sunrise Owner',
        role: 'center_admin',
        phone: '***********'
      },
      // Manager
      {
        tenant_id: sunriseId,
        email: '<EMAIL>',
        password_hash: managerPassword,
        name: 'Branch Manager',
        role: 'admin',
        phone: '***********'
      },
      // Teachers
      {
        tenant_id: sunriseId,
        email: '<EMAIL>',
        password_hash: teacherPassword,
        name: 'Karim Ahmed',
        role: 'teacher',
        phone: '***********'
      },
      {
        tenant_id: sunriseId,
        email: '<EMAIL>',
        password_hash: teacherPassword,
        name: 'Fatima Khan',
        role: 'teacher',
        phone: '***********'
      },
      // HR Manager
      {
        tenant_id: sunriseId,
        email: '<EMAIL>',
        password_hash: hrPassword,
        name: 'HR Manager',
        role: 'hr',
        phone: '***********'
      },
      // Accountant
      {
        tenant_id: sunriseId,
        email: '<EMAIL>',
        password_hash: accountantPassword,
        name: 'Accountant',
        role: 'accountant',
        phone: '***********'
      },
      // Students
      {
        tenant_id: sunriseId,
        email: '<EMAIL>',
        password_hash: studentPassword,
        name: 'Rahul Ahmed',
        role: 'student',
        phone: '***********'
      },
      {
        tenant_id: sunriseId,
        email: '<EMAIL>',
        password_hash: studentPassword,
        name: 'Fatima Rahman',
        role: 'student',
        phone: '***********'
      },
      // Blocked User
      {
        tenant_id: sunriseId,
        email: '<EMAIL>',
        password_hash: blockedPassword,
        name: 'Blocked User',
        role: 'teacher',
        status: 'blocked',
        phone: '***********'
      }
    ];
    
    for (const user of users) {
      await executeQuery(
        `INSERT INTO users (tenant_id, email, password_hash, name, role, status, phone, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          user.tenant_id,
          user.email,
          user.password_hash,
          user.name,
          user.role,
          user.status || 'active',
          user.phone
        ],
        useTestDb
      );
    }
    
    console.log('✅ Test data seeded successfully!');
    console.log('');
    console.log('🔑 Test Credentials:');
    console.log('Super Admin: <EMAIL> / SuperAdmin123!');
    console.log('Center Owner: <EMAIL> / Owner123!');
    console.log('Manager: <EMAIL> / Manager123!');
    console.log('Teacher: <EMAIL> / Teacher123!');
    console.log('HR: <EMAIL> / HR123!');
    console.log('Accountant: <EMAIL> / Account123!');
    console.log('Student: <EMAIL> / Student123!');
    console.log('');
    console.log('🌐 Test Subdomains:');
    console.log('http://sunriseacademy.localhost:3000');
    console.log('http://abccoaching.localhost:3000');
    console.log('http://testcenter.localhost:3000 (expired)');
    
  } catch (error) {
    console.error('❌ Error seeding test data:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedTestData();
}

export default seedTestData;
