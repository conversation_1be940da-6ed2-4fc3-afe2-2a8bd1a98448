#!/usr/bin/env bun

/**
 * CSS Build Script with File Watching
 * Compiles Tailwind CSS and watches for changes
 */

import { watch } from 'fs';
import { spawn } from 'child_process';
import path from 'path';

const isWatch = process.argv.includes('--watch');

console.log('🎨 Building CSS...');

function buildCSS() {
  return new Promise((resolve, reject) => {
    const args = [
      '-i', './src/styles/input.css',
      '-o', './public/styles/main.css',
      '--minify'
    ];

    if (isWatch) {
      args.push('--watch');
    }

    const process = spawn('npx', ['tailwindcss', ...args], {
      stdio: 'inherit'
    });

    process.on('close', (code) => {
      if (code === 0) {
        console.log('✅ CSS built successfully');
        resolve();
      } else {
        console.error('❌ CSS build failed');
        reject(new Error(`CSS build failed with code ${code}`));
      }
    });
  });
}

// Initial build
try {
  await buildCSS();
} catch (error) {
  console.error('Failed to build CSS:', error);
  process.exit(1);
}

// Watch mode
if (isWatch) {
  console.log('👀 Watching for file changes...');
  
  const watchPaths = [
    './src',
    './tailwind.config.js',
    './src/styles/input.css'
  ];

  let buildTimeout;
  
  function debouncedBuild() {
    clearTimeout(buildTimeout);
    buildTimeout = setTimeout(async () => {
      console.log('🔄 Files changed, rebuilding CSS...');
      try {
        await buildCSS();
      } catch (error) {
        console.error('Failed to rebuild CSS:', error);
      }
    }, 300);
  }

  watchPaths.forEach(watchPath => {
    watch(watchPath, { recursive: true }, (eventType, filename) => {
      if (filename && (
        filename.endsWith('.js') || 
        filename.endsWith('.jsx') || 
        filename.endsWith('.ts') || 
        filename.endsWith('.tsx') ||
        filename.endsWith('.css') ||
        filename.endsWith('.html') ||
        filename === 'tailwind.config.js'
      )) {
        console.log(`📝 Changed: ${filename}`);
        debouncedBuild();
      }
    });
  });

  console.log('🚀 CSS watcher is running. Press Ctrl+C to stop.');
  
  // Keep the process alive
  process.on('SIGINT', () => {
    console.log('\n👋 CSS watcher stopped');
    process.exit(0);
  });
}
