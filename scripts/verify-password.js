import bcrypt from 'bcryptjs';

const password = 'SuperAdmin123!';
const hash = '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Dh/Ey2';

console.log('Testing password:', password);
console.log('Against hash:', hash);

const isValid = await bcrypt.compare(password, hash);
console.log('Password valid:', isValid);

// Let's also generate a new hash for comparison
const newHash = await bcrypt.hash(password, 12);
console.log('New hash:', newHash);

const isNewValid = await bcrypt.compare(password, newHash);
console.log('New hash valid:', isNewValid);
