-- Teaching Center SaaS Database Schema
-- Multi-tenant architecture with tenant_id for data isolation

-- Plans table (subscription plans)
CREATE TABLE IF NOT EXISTS plans (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  features <PERSON><PERSON><PERSON>,
  max_students INT DEFAULT 0,
  max_teachers INT DEFAULT 0,
  max_branches INT DEFAULT 1,
  price_monthly DECIMAL(10,2) DEFAULT 0.00,
  price_weekly DECIMAL(10,2) DEFAULT 0.00,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Centers table (tenants)
CREATE TABLE IF NOT EXISTS centers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(255) NOT NULL,
  subdomain VARCHAR(100) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  address TEXT,
  website VARCHAR(255),
  logo_url VARCHAR(500),
  theme_color VARCHAR(7) DEFAULT '#3B82F6',
  status ENUM('active', 'suspended', 'inactive', 'trial') DEFAULT 'trial',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_subdomain (subdomain),
  INDEX idx_email (email),
  INDEX idx_status (status)
);

-- Subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  center_id INT NOT NULL,
  plan_id INT NOT NULL,
  status ENUM('active', 'expired', 'cancelled', 'pending') DEFAULT 'pending',
  expires_at TIMESTAMP NULL,
  auto_renew BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
  FOREIGN KEY (plan_id) REFERENCES plans(id),
  INDEX idx_center_status (center_id, status),
  INDEX idx_expires_at (expires_at)
);

-- Users table (multi-role: super_admin, admin, center_admin, teacher, hr, accountant, student)
CREATE TABLE IF NOT EXISTS users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT NULL, -- NULL for super_admin
  branch_id INT NULL,
  email VARCHAR(255) NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  role ENUM('super_admin', 'admin', 'center_admin', 'teacher', 'hr', 'accountant', 'student') NOT NULL,
  status ENUM('active', 'inactive', 'blocked') DEFAULT 'active',
  phone VARCHAR(20),
  address TEXT,
  date_of_birth DATE,
  gender ENUM('male', 'female', 'other'),
  profile_image VARCHAR(500),
  last_login TIMESTAMP NULL,
  reset_token VARCHAR(500) NULL,
  reset_token_expires TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_email_tenant (email, tenant_id),
  FOREIGN KEY (tenant_id) REFERENCES centers(id) ON DELETE CASCADE,
  INDEX idx_email (email),
  INDEX idx_tenant_role (tenant_id, role),
  INDEX idx_status (status)
);

-- Branches table
CREATE TABLE IF NOT EXISTS branches (
  id INT PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT NOT NULL,
  name VARCHAR(255) NOT NULL,
  address TEXT,
  phone VARCHAR(20),
  manager_id INT NULL,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (tenant_id) REFERENCES centers(id) ON DELETE CASCADE,
  FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_tenant (tenant_id),
  INDEX idx_status (status)
);

-- Courses table
CREATE TABLE IF NOT EXISTS courses (
  id INT PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  duration_months INT DEFAULT 12,
  fee_monthly DECIMAL(10,2) DEFAULT 0.00,
  max_students INT DEFAULT 30,
  syllabus TEXT,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (tenant_id) REFERENCES centers(id) ON DELETE CASCADE,
  INDEX idx_tenant (tenant_id),
  INDEX idx_status (status)
);

-- Teachers table
CREATE TABLE IF NOT EXISTS teachers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT NOT NULL,
  user_id INT NOT NULL,
  employee_id VARCHAR(50),
  subjects JSON,
  qualifications TEXT,
  experience_years INT DEFAULT 0,
  salary DECIMAL(10,2) DEFAULT 0.00,
  join_date DATE,
  status ENUM('active', 'inactive', 'on_leave') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (tenant_id) REFERENCES centers(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_employee_id_tenant (employee_id, tenant_id),
  INDEX idx_tenant (tenant_id),
  INDEX idx_status (status)
);

-- Students table
CREATE TABLE IF NOT EXISTS students (
  id INT PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT NOT NULL,
  user_id INT NULL, -- NULL if student doesn't have login access
  student_id VARCHAR(50) NOT NULL,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  date_of_birth DATE,
  gender ENUM('male', 'female', 'other'),
  address TEXT,
  guardian_name VARCHAR(255),
  guardian_phone VARCHAR(20),
  guardian_email VARCHAR(255),
  guardian_relation VARCHAR(50),
  admission_date DATE,
  status ENUM('active', 'inactive', 'graduated', 'dropped') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (tenant_id) REFERENCES centers(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  UNIQUE KEY unique_student_id_tenant (student_id, tenant_id),
  INDEX idx_tenant (tenant_id),
  INDEX idx_status (status),
  INDEX idx_guardian_phone (guardian_phone)
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  center_id INT NOT NULL,
  student_id INT NULL,
  amount DECIMAL(10,2) NOT NULL,
  payment_type ENUM('subscription', 'admission', 'monthly_fee', 'other') NOT NULL,
  payment_method ENUM('cash', 'bkash', 'nagad', 'rocket', 'card', 'bank') NOT NULL,
  transaction_id VARCHAR(255),
  status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
  payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
  FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE SET NULL,
  INDEX idx_center (center_id),
  INDEX idx_status (status),
  INDEX idx_payment_date (payment_date)
);

-- Attendance table
CREATE TABLE IF NOT EXISTS attendance (
  id INT PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT NOT NULL,
  student_id INT NULL,
  teacher_id INT NULL,
  date DATE NOT NULL,
  status ENUM('present', 'absent', 'late', 'excused') NOT NULL,
  notes TEXT,
  marked_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (tenant_id) REFERENCES centers(id) ON DELETE CASCADE,
  FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
  FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
  FOREIGN KEY (marked_by) REFERENCES users(id) ON DELETE SET NULL,
  UNIQUE KEY unique_attendance (tenant_id, student_id, teacher_id, date),
  INDEX idx_tenant_date (tenant_id, date),
  INDEX idx_student_date (student_id, date)
);

-- Insert default plans
INSERT IGNORE INTO plans (name, description, features, max_students, max_teachers, max_branches, price_monthly, price_weekly) VALUES
('Free', 'Basic features for small centers', '["basic_student_management", "basic_attendance", "basic_reports"]', 50, 5, 1, 0.00, 0.00),
('Basic', 'Essential features for growing centers', '["student_management", "teacher_management", "attendance", "basic_reports", "sms_notifications"]', 200, 20, 3, 499.00, 149.00),
('Premium', 'All features for large institutions', '["student_management", "teacher_management", "attendance", "advanced_reports", "sms_notifications", "whatsapp_integration", "payroll", "inventory", "requisition", "result_management", "id_card_generator", "certificate_generator", "document_management", "backup_recovery"]', 1000, 100, 10, 999.00, 299.00);

-- Insert super admin user (password: SuperAdmin123!)
INSERT IGNORE INTO users (tenant_id, email, password_hash, name, role, status) VALUES
(NULL, '<EMAIL>', '$2b$12$/wwPcUnB3Pja4IRblWOeB.dPEViIOBxqYncrtzQ35HJ4p.gRqBm72', 'Super Administrator', 'super_admin', 'active');
