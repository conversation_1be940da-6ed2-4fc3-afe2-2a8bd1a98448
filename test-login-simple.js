// Test login without tenant_slug
const testLoginSimple = async () => {
  try {
    console.log('🔐 Testing simple login (no tenant_slug)...');
    
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    const data = await response.json();
    
    console.log('📊 Response Status:', response.status);
    console.log('📊 Response Data:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success) {
      console.log('✅ LOGIN SUCCESSFUL!');
      console.log('👤 User:', data.user.name);
      console.log('📧 Email:', data.user.email);
      console.log('🏢 Center:', data.user.center_name);
      console.log('🔑 Role:', data.user.role);
      console.log('🌐 Subdomain:', data.user.center_slug);
      console.log('🎫 Token:', data.token ? 'Generated' : 'Missing');
      
    } else {
      console.log('❌ LOGIN FAILED!');
      console.log('Error:', data.error || data.message);
      console.log('Details:', data.details);
    }
    
  } catch (error) {
    console.error('❌ Network Error:', error.message);
  }
};

testLoginSimple();
