-- MySQL dump 10.13  Distrib 8.0.36, for Linux (x86_64)
--
-- Host: localhost    Database: coaching
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.24.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `assignments`
--

DROP TABLE IF EXISTS `assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assignments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `batch_id` int NOT NULL,
  `teacher_id` int NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `due_date` date DEFAULT NULL,
  `max_marks` int DEFAULT '100',
  `status` enum('active','completed','cancelled') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  KEY `batch_id` (`batch_id`),
  KEY `teacher_id` (`teacher_id`),
  CONSTRAINT `assignments_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `assignments_ibfk_2` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE CASCADE,
  CONSTRAINT `assignments_ibfk_3` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `assignments`
--

LOCK TABLES `assignments` WRITE;
/*!40000 ALTER TABLE `assignments` DISABLE KEYS */;
INSERT INTO `assignments` VALUES (1,1,1,2,'Calculus Problem Set 1','Solve the given calculus problems focusing on derivatives and integrals','2024-02-01',100,'active','2025-07-09 12:29:48','2025-07-09 12:29:48',1),(2,1,1,1,'Math Assignment 1','Solve algebra problems from chapter 5','2025-01-20',100,'active','2025-07-10 15:32:31','2025-07-10 15:32:31',1),(3,1,2,1,'Physics Lab Report','Complete lab report on motion experiments','2025-01-25',50,'active','2025-07-10 15:32:31','2025-07-10 15:32:31',1);
/*!40000 ALTER TABLE `assignments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `attendance`
--

DROP TABLE IF EXISTS `attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attendance` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `student_id` int NOT NULL,
  `batch_id` int NOT NULL,
  `date` date NOT NULL,
  `status` enum('present','absent','late','excused') NOT NULL,
  `notes` text,
  `marked_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_student_date_batch` (`student_id`,`date`,`batch_id`),
  KEY `center_id` (`center_id`),
  KEY `batch_id` (`batch_id`),
  KEY `marked_by` (`marked_by`),
  CONSTRAINT `attendance_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `attendance_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `attendance_ibfk_3` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE CASCADE,
  CONSTRAINT `attendance_ibfk_4` FOREIGN KEY (`marked_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `attendance`
--

LOCK TABLES `attendance` WRITE;
/*!40000 ALTER TABLE `attendance` DISABLE KEYS */;
INSERT INTO `attendance` VALUES (1,1,3,1,'2024-01-15','present',NULL,2,'2025-07-09 12:29:48','2025-07-09 12:29:48',1),(2,1,3,1,'2024-01-17','present',NULL,2,'2025-07-09 12:29:48','2025-07-09 12:29:48',1),(3,1,3,1,'2024-01-19','absent',NULL,2,'2025-07-09 12:29:48','2025-07-09 12:29:48',1),(10,1,3,1,'2024-01-20','present','Good participation',2,'2025-07-09 16:07:16','2025-07-09 16:07:16',1),(11,1,3,1,'2024-01-21','late','Traffic delay',2,'2025-07-09 16:07:16','2025-07-09 16:07:16',1),(12,1,3,1,'2024-01-22','present','Excellent work',2,'2025-07-09 16:07:16','2025-07-09 16:07:16',1),(13,1,3,2,'2024-01-20','present','Active in class',2,'2025-07-09 16:07:16','2025-07-09 16:07:16',1),(14,1,3,2,'2024-01-21','present','Good performance',2,'2025-07-09 16:07:16','2025-07-09 16:07:16',1),(15,1,3,2,'2024-01-22','absent','Family emergency',2,'2025-07-09 16:07:16','2025-07-09 16:07:16',1);
/*!40000 ALTER TABLE `attendance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `batches`
--

DROP TABLE IF EXISTS `batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `batches` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `course_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `teacher_id` int DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `schedule_days` json DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `max_students` int DEFAULT '30',
  `status` enum('active','completed','cancelled') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  KEY `course_id` (`course_id`),
  KEY `teacher_id` (`teacher_id`),
  CONSTRAINT `batches_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `batches_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `batches_ibfk_3` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `batches`
--

LOCK TABLES `batches` WRITE;
/*!40000 ALTER TABLE `batches` DISABLE KEYS */;
INSERT INTO `batches` VALUES (1,1,1,'JEE Morning Batch',2,'2024-01-01','2024-12-31','[\"monday\", \"wednesday\", \"friday\"]','09:00:00','12:00:00',30,'active','2025-07-09 12:29:48','2025-07-09 12:29:48',1),(2,1,2,'NEET Evening Batch',2,'2024-01-01','2024-12-31','[\"tuesday\", \"thursday\", \"saturday\"]','14:00:00','17:00:00',25,'active','2025-07-09 12:29:48','2025-07-09 12:29:48',1);
/*!40000 ALTER TABLE `batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `branches`
--

DROP TABLE IF EXISTS `branches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `branches` (
  `id` int NOT NULL AUTO_INCREMENT,
  `branch_code` varchar(20) NOT NULL,
  `branch_name` varchar(255) NOT NULL,
  `address` text,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `manager_id` int DEFAULT NULL,
  `parent_branch_id` int DEFAULT NULL,
  `establishment_date` date DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `settings` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `branch_code` (`branch_code`),
  KEY `manager_id` (`manager_id`),
  KEY `parent_branch_id` (`parent_branch_id`),
  KEY `idx_branch_code` (`branch_code`),
  KEY `idx_status` (`status`),
  CONSTRAINT `branches_ibfk_1` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `branches_ibfk_2` FOREIGN KEY (`parent_branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `branches`
--

LOCK TABLES `branches` WRITE;
/*!40000 ALTER TABLE `branches` DISABLE KEYS */;
INSERT INTO `branches` VALUES (1,'MAIN','Main Branch','Head Office Location',NULL,NULL,NULL,NULL,'2025-07-09','active',NULL,'2025-07-09 17:26:09','2025-07-09 17:26:09'),(3,'DHAN','Dhanmondi Branch','Road 27, Dhanmondi, Dhaka-1209',NULL,NULL,NULL,NULL,'2023-01-15','active',NULL,'2025-07-09 17:30:07','2025-07-09 17:30:07'),(4,'GULS','Gulshan Branch','Road 11, Gulshan-2, Dhaka-1212',NULL,NULL,NULL,NULL,'2023-06-01','active',NULL,'2025-07-09 17:30:07','2025-07-09 17:30:07'),(5,'UTTE','Uttara Branch','Sector 7, Uttara, Dhaka-1230',NULL,NULL,NULL,NULL,'2024-01-01','active',NULL,'2025-07-09 17:30:07','2025-07-09 17:30:07'),(11,'TEST','Test Branch','Test Address','123456789','<EMAIL>',NULL,NULL,'2025-07-10','active','{}','2025-07-10 06:16:44','2025-07-10 06:16:44');
/*!40000 ALTER TABLE `branches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `center_settings`
--

DROP TABLE IF EXISTS `center_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `center_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL DEFAULT '1',
  `setting_key` varchar(255) NOT NULL,
  `setting_value` text,
  `setting_type` enum('text','number','boolean','json','image') DEFAULT 'text',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_center_setting` (`center_id`,`setting_key`),
  KEY `idx_center_settings` (`center_id`,`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `center_settings`
--

LOCK TABLES `center_settings` WRITE;
/*!40000 ALTER TABLE `center_settings` DISABLE KEYS */;
INSERT INTO `center_settings` VALUES (1,1,'coaching_center_name','ABC Coaching Center - Updated','text','2025-07-10 06:17:54','2025-07-11 07:04:50'),(2,1,'address','123 Main Street, City, State 12345','text','2025-07-10 06:17:54','2025-07-11 07:04:50'),(3,1,'phone','******-888-7777','text','2025-07-10 06:17:54','2025-07-11 09:26:08'),(4,1,'email','<EMAIL>','text','2025-07-10 06:17:54','2025-07-11 07:04:50'),(5,1,'facebook_link','https://facebook.com/abccoaching','text','2025-07-10 06:17:54','2025-07-10 06:17:54'),(6,1,'whatsapp_number','******-123-4567','text','2025-07-10 06:17:54','2025-07-11 07:04:50'),(7,1,'invoice_logo','/uploads/images/data:image/webp;base64,UklGRlBYAABXRUJQVlA4WAoAAAAgAAAAHwMAKwEASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDggYlYAABBGAZ0BKiADLAE+bTSVSCQioiEjcyuogA2JaW78Rvltb2bvGzeF14xh/Fn9L/H331fG/0X+w/2H/F/5H+4f/D1v/Fflv63/cP8d/lv7X/7/9n8Qv+R/e/IL5//Rf8z0O/jX13+2f2b/Of6j/EfuD9zv1//B/2z9s/8l6S/FX+l/v/7pf5n5BfyD+Uf3n+2ftr/YP3w9zn7Nd0HvH/E/8XqEevv0X/Tf3f/Hf+7/aejn/h/4b1O/Qf7j/t/7r+T/2A/yr+kf6j+6f4f9lPm//n+Fd6j7An9Y/z/quf2P/d/z/+n/Zj3Yfpn+o/8v+j/Kr7DP57/cf99/jf89/9P9H/////5Yi2BQpPguamLfoSTFCk+C5qYt+hJMUKT4Lmpi36EkxQpPguamLfoSTFCk+C5qYt+hJMUKT4Lmpi36DQAw1DzROMmHsKmSq9YawwmUsYv3lk+evo7EhKffZm1zMPdTLxNAVMyiki15C8t98PSTGoVXd15aiPWzXNpVXfoJNk8LzKL7/Gcy6q+cbQ0PRAnkdG2nuRHBFNPLhNdQkwRzbBmQI90kVpiFgWaT+AAciEGKBqtc59hoKVKnRHlGI9kllXMmUYU2yPho4OmHgPVuzzwOgrPFnNWG5hSTdViKiZY9k7Ms5/1WBDQlGiaKrhBZWLRkAxTh2PziO+mhGcP8q20Kz11Rua/E2wITvAJyWXf0sdTSrq+mvcZMtxMQLAUDHueXLV9AxT0nN+vSzlWfS4HnO3J7RcRRF8/Rs9U/+GgkSpdEJm+lUfyP1hjORkFpBQeZRoNzrPsVDBst+NE0Ul5JMK7rXHq2UFbYKVIwh0MMcK76g4k6T/JvtNmOaFVzP4eqv62KkTn8DYr1WjwpO2T2GBq8weFiCDZHxNXaz8mgtRWybxN8sg3eTNQdgto/9hqoNNZ1AjPOpJAEZKR5F6EbGbb26QdT0ElYUH12otIzqGTWSdZ784aZs7wQOm6UEhuwAMwOXBo1jJ6v2jNBcW68ebwWpoTTzT7koXfrwHL0R+8/ztGv00ErqRmfn6dapr1/6ZDX2mm8xEcs5y+KsYJ3Vke0nxLXPzIoKf/X/Z9Cj4tiZGh3GRXvDQ5dpCoss4BIXL1kRFvNPWLETOhcLg8G2Yov5oXuTX1GD3/Aofmfm2lCJUYY4uhkq/HAgMU6dSiuzjOn4Dlc+tVZS+uLdG1ZRpj0FVTHx98y90vOqCs2a9DG2vgpSAJ2LNnTek3b3SkGKCg5qWBOezhU39EcB57nqDXYs6rNQEzm+UQ07GUCHSexznExaKXBnJMUKT4LjUO/Y94siUFbX3YFquqC5VOWZtifpakaV1n0WbwdiSdgJsPTI8nqTkjAf3IZ4DJamGfy/+2mFtATkVlBTyvKeeKEeDRK7zkL8xvgGTGsK3L0V1/4BJnzx8qtrXsUVcl/XCJKGof28u4xld/44yqOS4Gq4eLpesLfsDuj0FufJy01HOG44+WN/7/WKLpo/+hNWR1yfE8cKWdmgmPQi/NexY/jlz34OhzUxb9CSSB8li+edUSFB5TWNTcXvuOa9OpI1yxgoLWWPx8X15u6wzDC7t/VK0pIqYObWI9sn31K03b5/pREMSNthZYp5/5wnNC8/7u3534MYrBeq2xccFKflyNakVIb4oUnwXNSQz4YHljUElfu1bzUo5XMKDjA2P6v8cx0WAWNfnzBdwf6c3cp+q2Ic96kLo4yfspOfc0IZiwrCtoU03jDlRMdEzG1+Rl7nWYJp3fR6wSGKF51wc3d3Hks3PLqjD4VlBSkYZ8n35zB0Rtd4CaDF8ndxAMbhpjOFplN8S9FApLXsm3n4qQTu+ydlaTu7R9tIXhUNdTH8Rr8iyX8p/NT9/adilnGz7XdQgpNw8dKGBbR52Fl/ii3MFB4xmdpy4n4/+/XqSZITGiTFCk+C5qYt+g3BeplQsI3C8foIEFe6OaMsKKUk+v0X788OoFMtTxeCr7JQJ3uqYaOL4xCopqZGoYqpS7ib86gcBkdJDFx9YaId718goqqpYxuTmeo82dZHIinCJ8FzUxb9CSSCrhzg9dsrnT3C+JVYNumnKJiSdZN5eoirE1bQt/2ntTYw1ncXpU27d8m7dmn8i3wLnXeaATAyEzXbIlGuaolVxcsE6mHiAQqQ/Mj41N1j3ThA1J7+cyZUuPZmXwG+4hnesAoTkk8H53heIMqK2ZcHIDbT1hSa9TrcRSZs92T7LRe4B/AHsGPcX+5vY+zpj8GXFTRy6m6RO/61PtK5vaOaPffvA93zHW/v9j09oG+o6DYTOE8WZJN2UbNP0uWYtTP3dZg1gU4fEMe4uww30ijegind6vJvmJYf7/BK+WQKraozoKTcpsiL6QqspNIbzyUG0EkDEb1EREYf5QGNTbvhxf8yZJuVYuIfQMR3FljVqwvWEIb5Bop0P/5cKYcPLKWwl8aoOidzUrWc7XbhWj/KQirE92tcGWURsGxC9kD+Cr2qP0vhtlKX/NS8F19QCBuUbAiVNI5IYLU7yDeM0vXgEM+o3EQxlZfcChYFwx24WsHK99Q4a9qK4Z+cphoLsKY3GwfXVCZnaF8LNv7QoOFmGuD6+6hrpB4JphMevX7fU+vcwVGBBrvahfuvZSDX7W9e+8/lvFPmqY8zP5+nRaAieYnvwXje/IhA2PQip8Pk0yTnaNqst4zkF7VJZKU0ud29k+uaWYIG60Z0SitjoTxAP/t/Rd9fIfvoHzFGoYUe1SFMSR4kjFwv6bF2mDw76nyPdHVgh+1UZBaBLRADrQEep2fgvmEUrK3IGkxxSpx68mSe00DZWOTkC0OGlH/oCsFGYGL1n1Jo12SVt+v5prjY5JwB80CvYuAjIJY5YcmVW3bKO58kGIuV5volzQSwdD9B6HyIb4xbbYyOlCYad/aeg06Zl7Gxa1q31DTGrHKBGJT+UGUKRdYN7JYAWaS4uYQtjv/fPZm2jGSJjpzeek619g6MSy1CVx80Fwgg06c1e62H0oavyLPlS1x3aHefeWb94nz7E/vfije3GF30Gs5ZOrmYA+e2keaGay4iFZPDcgzQ2ouVRlxp1wOOcxBvrfYkH9LkUEzi9JpgjABtd4TLV0z0LclT7J7Z+dp3ksHHT/09Kc0Ka0TdYRjGaP1vFRN2+FNkm40O7x89+Hp7U3dPuaoHAm04DwxoaxGCyAxxVlWp2mFdLR5u/O+YWz4aaM7r4n+NPAZYhCzfG/buh8RJ82FCO5K0dDMYqgxYw9UGVpCxd//FKyY1qK6GtZaBAsMoVSCSLcsTb/iyJzprMRXBPwmFfUgpPguamLfoSTFjM6aM1NPorrqo42WZn9CHx4xwM+SNcuwCUnCaYdCItO4I1t5ylxnyD39YtGamLfoSTFCk+C5qYt+hJMUKT4Lmpi35ouMwyaYwtY9QdGZDY6fI9bE6aM1MW/QkmKFJ8FzUxb9CSYoUnwXNTFv0JJihSfBc1MW/QkmKFJ8FzUxb9CSYoUnvQAA/v+INz9jyYAAAAAAA8jDNjt0YysoNxsA30g2p/heKke1bp1c8G1KCH+7sYwTFuc+PqDvhVzYAFzB7JOxDSuXmiXJzMIfl8yS3V30QKLaC9c6GHRoWi/p8T2wEI4wymSbun+OIYoxQKDib0atKVAq/RbiKvmhdH0e7+8ME6p3EKxRqy5LyFglcgwVWzVC6Nn/Fjr11zoHmfM9fkVUoTgpn4Z/0EsiLciBmOsy+rFeajT+8uzgfpRXQSfkBZHzVbFHhuUC3eD8p5sL9DkmUN52VOu5yJ8fPlzZJBGVqm3BPUPq7zrquA7YQMpMeS0bmsWUIbpLak00446wZOHGZtl9/reIA83YQHzYBHMj/vINi/MRzbrFz9rEG2pg17wacy6hjKRxnOf1RfG7qizF0fVpWx8kWzA2cZN4xyx/4wMy+9wT74q8mORI0/f4I86LLbtZLb3m6y/y0G/D2z+XGt6X408JWHM1DXxWaXw8hpzzjf/nvA81MxaSHSIVKqggG5mUPVB7yz10BPK3zb/B2YmZD/c9KuuuJrP3b0i/HcbrjXT9luCpMLgtE+rJWGMPfVkOdkeUcxUburLvWJvKg60cCGhe1FaP4/JKn8M+tPPPNWS6scGnfkngyTuzqH+gzUcyk8/JAv8omDlQNp9PujaZqzNcbWGAaP9l+ptypU9ttVM8DtPLhHty4yljJqTjwHnIpAkf1n8oAZHhAhXT+pVy5u2uAt9toYACer+8thtX9eKB1QDSbGmWmBHQPkMopEo4LdZ96LHiNk38/JcK+uZl/zeJSv2pftu9p4mhCgi/EPYkp5jYaceqWXbVPo/3t3rnpHfvd+XxS9Fj+Vv4Nw3jVsyriKpN1+bEa5cUwB+jmZZ/tBL8yqLn3QT39tEIe7CMiBh3Gmz9gLOPJaZrXDEzmWyxqPib/pQCrjWq9rF7wtxn2E5TR0/omUEPShTZtS6tGU8PtE/0ihmwV59nCFL5kCSKYTV7CQZBDfrjGF60g9zTPwHmJcQtYIViCaq0No9mkmlvGYz5DC5f+VQnp+hol1ffR8wVjLziuUDF++cGJVbm7xuzN/nvgN7UZB7phppcE99SKH8DLshaAtsCpFquVKEtMl66SveiguqSixERcCETM2XTwIXhGBzP0X1sex7G1LGkD+AvJmeiwifg7pA3azN3R77rkAr7jzK1eVeOf47QcrKm5wPs106zJrLknh9ZrQQFwCB2EcyWoHddldEbxp5I4t4jFww/BxS3nrdiIs2I3o3mIKQ9ytCsJLNTixh8ujBc4H1qJg27FHlWhNdI+ATRMaQ4Hnv+h+BElwgpUu9WJwsWsoKwHeuonGITjh06EoT3yTwnNWjhBprjvHKragl4P3BsLU92giO3rKm26hpwbajMPNdYOPBYC46509oKaKyHF9nC8Z/cpS/wQ6OBjPj13DH6c5BHBQD+d0SeZsuk27NljrKvHP8dmbSHhJBSqozuMGDEhpXLzRLk2AbA3En4Yv2zYbv34rtXls06KMWoGrJG/XlitsV8lryX4MmvOL7/2nDoiEuZDQEXOC2fy5842XLo4+wzj/dQYFC7BtUNKcJKmcTeoenU2N0VmnWdypv/As2K94mIJhdQ0ua92qssugrvkJg3kgUuWiVkNGYqxQ8pDqST5vO6XqbnOdEoT7EXQiXZMIM3Kq4XD7C0sLQ0vDs+xafyxnRyqjx461l3a8x1ejBkXM/6ZfBJil/F4i7plip2NFJvH1DID/FqDqi3HSmqDzmLluFcfh0q06/+/T2va7DBJhGtNZV+2YKuzS6ShCFZbhRHjMZN+YBbTG+s4wlwrc2Gd0YFfAPa3gNn9RXX7NY5eC+ATUR4TuLrQO3pGCsr51275dhHarX2xlk8vOK8fLQmomLQV7rcCR5y001p/Xf2Kyhm9z0V+4Ou0nvRDsxrLcv/6VTkWlhrJ0ReorpGXRx90DW8+O6yek0UB7j7KRVQ5XyG7NKuaGVUhDy2+7sB7ZxmSj0KLSEQwZAZ2rXKEEkicOphlRAgVw7ieIhAh+f1yhgLokKM6Vx0HchRHQ7+0RDPt0VAycRekMru5sO7HvP6T1xvD4V4W7SPwt9wxopUu/WTl4BDaE/F9n4sTrCpkaFJJ8TBVSY+qq62gWJA4pzr8dOo3ud1/7MynnzxN/gyvcl+dfoPhlNVscNfjyhdF1hYotzdy0DndgaOQTNFYQc/FkLsbGCeptEtSueRdn0AGPVl5fgw3iFUZCKUobBfXoQ8WFTypGai0wIFTPn2aVjGSRRq02mJ8/zadmM3qoVpeBeAL0TD6gx0itzSDY9g3l5ppTUgvsZOUrHcR/JN/XRM8e49OFH6hAWGPRgzgDqvqt7hL/EBy5n4BLGoVyWRXZve2W0iSgzNQUK8/nwN2LRA1l3waHkRe7uqn6GDpHv5Qj+iHXAgKfnb12TlmNovt+Ig6Eq4svQsVGIFsAy6rTb0UCBiIynsil5ZY3wu1p3Vg88UC6v0L/9CLhtPWtIagv4WbfHIEd5PMsw7FVpTnRZT2UnsFh0XQQqv8k2XOCV19G+w2IxnOKKGrt0HGAJJF6KzPEDnJxqUOLoKUWvFRFAo690hz7S8S/uKdfVhBdgXCbL3yly8LuVxAO12cXdMSH9gHsFKIDozr1qjktB04gXP81LXxSgFB2E9isY9Barj5gkXDSnWpl2cmr1C2+p9Efez8h1UB/ceonQNWBCAqJbBwp32L47eGtebyhlkRbfz/YHsGifgvdAXd0bPo5EaJJB9I/45dUTXEOH0I4kPqqtAG78SdLceJM6Fsuve/Z8S+SZoTIdEG1Bo7GrjXzdqxU34YhauEbeG9lVSwRemh+RC1Q4O2E+2LoA9rzXVm+hCKEkcer+BVC++VeXWZTNNWqrE90L6nG7gdaKuPLZjiwQMMI2sCv2nLuKzDRItg3U6vUySzdBuPZqL2MPW/CPKBSqTj9QhEK/dJndaAW0xjuVuER/3uVrlt6ulVagmQMTAye1t/4Xvs0u8mtLPsZJdqjIK8uDBM2pAua2xy6qsyzymKDiEZmK6bSFCzHVA9LO+TwAsuugqXc6TfiUOeh1FYE16XUbCAb174fD0Gtx1ecRMoWhH7EO3dBrMIjsxtvA6gArK7xwIJ+TQ9EPBpd+xZhIF/PNDjklBGrd+Nfmy/B0UbSasDnE7If3bPzomzk9yP5ocwkt3Bnf9JZPw5FDAkzZLMAk0ZyYKUxsz3bEJNDi+8xnBGrzRtQUZsBulSG0aVdBHMUA6TqZe7h5XmnwQxcjg2Ut+Evq24VWU2sDueGXHFcaMKGaij5+FWxTw0osFLHFonU6E9mSQKNTcQYku5yJ4AeHUwiOwMxvJdLyne6CbPI/bqkN6A+amVFUQ/qfOWa5dqJJytlkMkL5fw7IgaDlvCvKtU6EIoTUvNnwGm2SLDWkThUi6MIJKMj8ONJCyuasLkeBF08IGLR8qmqgT3Ds3QSIkMVojqmYF+jtBF+/kX5oZfFdk92uTm4fbLSDCcF3bAfC2IJw1TH6ZH7UqbLU+FoURYDQoE2OKjFRjScTt9tnsvnVLCIvsBkzA7odZ1u5uD3zWTw57lTrZFAD4/j5B3ik2DlGO2h8FMpmFWPsP5E3MhNL5fHL5/3dcwD21kn+6dskiwrgnox0DBHISpvM3uVTw7/cqtW9BvhYNUV/1B1KFVQozrq8RuGMtQTf2S0jgk6omCtby9mfxZ8rSUKYfyVGh11Dmm8gt4E6NzVsOEq41nCl5xjySF9a+wj7Te3I8Y+RuN8SYRtI4RkCx2i7+pyqAoMhGTgzR3ocF9gs9FJXG+kkrRdhhhcT458z0v8IrJeFCAER2zT4knu5do/++3OlapI39qGUA26OFpfGqovizhMXPSZTvw43M1Xu5NNEcnA1jELk4QPrDpubaZINoRPCFS7EoruRVtzG6cziBqWromzPpK+VfEc2o3vaXjPrx/s9Y5dfmLBMzyJef9nFQIfO5toTYBRD8DT2lrR3ylSbIKWwBv34GdBEAKCjpltpqoFzIIxCfi/2OoSie3JfIDGH3rxJkKIqXkVbFA+zL78J/Buu8aFcI8jIGCKMA67AtTlvuyCbAJqLMw04Dx/C1WoIPrj+P+WETjO5KoYyOzb/jezwbGHCfQBl4IVDqeatEGguNAbbhKi6aokiIiKRvM/7SvkllHTxBR9fMrScb9SJZDZBuSXTk0T4r1x34iDsFdYq5rcnuHtcPkUqG1D0Bk8kaFtKcsr/OIN1mR7LOCG1AeOeK32/eYGooPHiw0GDWsak/Tgs1mLS/EoRo2ssZ/IYzPjd57YMzgrPZQKkU5ACiwaN0R47f0kHFXx1I1XjnykjJ/3n+GLA79vujg9IoeuRivyF+japZjbMcqZIDncG7GOxegDUYWbslxYwoXxZ0nt2XR9dbKuyl7ifuPcwnLiXWlNDioZ4lu5d+4zl8v/b9TaRCOZ65fx6aDphmEVUJRZQ+6GsfH83YD4xKJ0OiVhFiKYiHsQoSTgWR4kT/xhpY15z4W2r3Inu1V7yYdswZcrSYx6Pr1Rgmbh0wEAmEYSp36KSUcqkeF0u4hZj24YNLUpzcb+7FLU0WlGQvPNpuzlkbXWq7xuwL0pVe79njulakXFkOolIHqrydsAS3fEx+WtSCZ1H1z/U0TmuyBo/8EeOtT1AUlKVzSDgP+eTCj7FAAPlDRArTeLvYyndP2rl7QmnBz0wcIBw4yu6TT78bwBnPagMZKCyGWrnlHvJNwqVjkHt7xGn4rkEecnm1VsP0yIE5jl8sCGa5T4QbpdjRHjaPl2UReJ+BQCvAs90CcL28oUy3X5ZTJE1OCcYYB5zNkW/zQl7wN34rMp1SNjADIO2+WYR+7bjt/JoReFkM0Ruy3iRETObxVmxr7DjgPnG6kAGNf0iMguIRU1BRyaABVqPFVCtFd+gurQcNfKz2XrEEfUjZJ81zjCT2s+3QRL48nZzg/D2dfaQu/vaIwwbxJv6TVQWLiyakHzvdZdnQQHSUSvcJx4pXCYz/N/1DxMFBQ7L952F5IXhGgv46VRUAJ7S4LkeUCkZbVr4EsW6kEAcyjSxXI0E97kUNCexUHqyrEDRgK53TrQbUJILFWhUM7MeiYd4YX5Cy0NvTbFXRswfOHtyQb9zhhjQazBcgAviQDMuHZB+9ycY1m8w3B6BBDTPtT9yq0TlduPG572llBbSlNyYT3xJ2aBJC5RAATN9m0U5wtqpNiL1r9pEuH+K6XDNqMoq7B9lRgERdlUgX3553G+OIbMCWjuL31lvWZ21vnEkj9iH0lNwuPDQZdJwS5hfXBR6pMZfCO4zHHOYIPZodaBf/q0LnkBKfGn5lIgpUATIzp22N8HZWgjYo9SRhl6z3/843XH7zUy7toXu3Fivva2XQoRhz3dbOf0m7leDZby+OKTD3YAM7Jf17nLo2Rklr3BfxQV53v7ygVUe5MVeJMNqjtNZcr+yVN+gpaZA4pcD8E3zE28rpDmRsNlyntsCO/dH2qc9fVxAZMKE6JvDDqn0bvhQgtWNBozNsRYHapnFIlZP7HSXHNBTanWnxi2ey9ZmZZ5gXAfs6Of9gyGLNvv9XHbbmBt+1sdba3g4sxHQjaAOs/qqtWpw+/aSucekSVgyApmqzDdK3r1UI4+Q31AMzSuJo9GnJWSzVs66j5epvYb7kz9WXYK526ZwYDj0kMZEu4Yl9EzheV4e8AiUJZoV7KH1SkCrZOlugOiIjShHRScofbyNBpmTv0qWO2MV06UiMNgrvPgJHe9aViuZlzUY3hdErqlODvVjqqsTe3pqIKWA+WIQ03ZTLCNmN7/sWD9h3JP4jZo1PqPe9w8GOh5RsV27K13I/DYn0d2a+4ZRijEAX175qZyUuRll+5t0nbopakoOYK6tpuZiZXuOaL9hD0Jx8a3v1g0+tk4HKabYi4QYa3qK45vZPjE03ERWV5rtMUFA0IZJuQfsxWXiqro0m09Nm9p5i10oM1ePs7aWirKa+rPZzq8g6egj40bkZAWSpqDsHLUvBvIdzorvx3KfyaSDJgC6j/4wq6grbxvT+/yZ+uY8bJ/1b3UMJRhgro2eVS8sbbJtjNQ9jj7aYd8RhiOt3M6odQXwehCmwuHXybiw0/hO+szzflE10nbtVODuUXtirfeeO5apknPesGdTdE7Ntcg+CMyZHOChE/DaIsV2I+c/eh0iVcwt1x1IMY1JDx2AlzI+ecpgrneiSIpVzCzdP5JzBxmfZKaU1E9VmZOIcPxDaJOhEVDhtG4lNaVaKXtTVBvJzA3Rls+utlawl4DD9NQqPu+b2AlPQtjTTIprXwKCevHH1Syf1oGseFXp73fB9Jrw2jxUOReHnjBGekN0pisHlgOclki6+n32aN1m4BItXy6ZCUFHbqRQ5mEe8g1cgOwwj0hm0vuUCGCiKsHMDiV9hGHPPZ8ETemw8YmqxkYIsKvmgV9q13xoLte+GzfBM6sfbiB8JH7PjhYNZnxZAhx7CrghvxaF8kaTZs+Fk1CJFROayHE/4+jzgxWydep8+LUXWC79osS0PROCZclJhpPe6uYsBOXkLVejIkljryo8rpwaDuLKnX6wKbSLtXJp1TEDQ9lKif1tJDi4R9TsWQ0MO5ftPtnWe6ou6j4T48rQ9+qL2vIiW+l2jalymijWJMxnnHfDTrYY7kXai5gDV42WkjIcpIPUi4ene47XoHcytRAIU0KU9gOLC/SVsTej8OCi3JnKihvGtTKVN1+nDZGQNVABc/WLlwpL+Bb0kTfu8zPqw7op5XvqqlX6SS8qAF5fyGVLHKHFXIuXaxt1FqImxxrQtHsEV6uyfYaQ5Bs5qHN7AX2HRFWyQYskVE2P0C/kVAsJwFE3F7U9KHLnrAjEPzBAApQ67upf6nytmovrORDlNZO6U+WU1KjcVI32sDth4IA5G/ewWuXW/FWrDPpRt/eW1oW6mwolYqwa91aIY1tVuiy0ZQOY9nZ18kAylPY7JZ+UAPqqDmBEmT1jMSWMW7lZNDUCR6ZBj+Q1X6Ahj/378l5h1pk6c5KmtpjlRXgChLnsVDFGbdNleqhver/ZMKXZgINUS18uEYjx/vAlw8p9zPe4GuNm7jMmbliN6sxd29A2aX6yNmJ0fCw1/uLp+l1lIIgfni8PNHMC3XhJ5bDWIgpNjo0qzU2nayZkoXFseqCTgQJWoBPnG3uMaBPbdUXiAvdqlenxHe8xL5UzORcRtKy90bS1vhUXsHsUQEIoOfItrs10n6dWl7V40FazqiGwFDBQwBc7JcSQsqkRHnumaFGBSwR2pMuqcUkld4nGmlodiqB4Xu4eecLRj5xvVH94EswD2ZLR4Ezww+hhdirDMWrJ5ZbSYZ7ff5Qn3dI4Ghf8ea2Sd5dVc/KQS4uK1JqRHB9n/pteiLsuuyb2GWqlO7YGNul4jTOoRNwZanc58mEDOJyc6VWYeOZwHZMrqMBkMXCVKmQ7mCU596sLkf4P6SsqazfGU7Lr12AdiJQ+YFB6het31nda9w7HHnWkLsnnRIyUESpohxc6OVyy9cNR4nS6sDzqrh6LGalwYE3gmLP5K0uDdW7jJ4othbCvqkZszQwAt5+TS2pyDI9PO+r6OvFzkJkYn5Vfnu1lkbh6epELFpHxOtJ6qETxW6mgtwso4X1zlyjRTsJ2NqKGBVT39OWkGp+wcqP2DC+mJN6/Y2+Rlj13BEzUQ65Sd8UudXTO/6rqoGAxSGFou0lbNAGf7wQ+QG4ZfYXXqQfSexSAPld8ley0YefK9LZ8PYW5biKkoZF61FfRiNQf34491cLQukAjQd/WvLEl11lh7Gtefl1vZ7wzg252NIqFazdLpTkNbJ47SIm+TBY2Es5AmPGitvT+bi0m4HRy6wA8JTQMk9DG6w6EDd/ozc5efAq7xc2PXRbzINn/IpXNJpyeA41gLOCrqfkIH9/avmOW2vnFUze8dOgKqFpC5z4smrxTSs1pA6Cy1/k63jKdMLdTwhPd+i/GxtGBxz0WGEjas+yOHAna34PFfGDCZXX/rIrSFsx9BwKq6w+aHuAJbOQmyNs0kYWHTRRT96Tlnkz0jYKxc1sSiVAWyQQn0Ln5EBVgzWmyZOZ+JExS1KUlXBz6gcSNhhPT7xGWYaurLDuQ1lay5TNDVjzg5ld4YU8qyCDYAZZnSdmh4DGaOnjoMfA9JcgUtLPUt1IrXQz2WckzyDH8eMvmysVXX7GzCIAZYD80vTRNZgm+teGBAqa55RXrIiCdJw+IfNxpkJDFEp0o4iPYNOm3XP4ZR41eYulubFfNbGOGO2WkPJ3g4lWuD7LHsU3W5JFmrDvKxoWy/SFl47UGNBud8lflitFnuC05IHsBGYOHy8aBdAbEmvYGJnYjKUE2Z+m0xenidTnxCuV1mrejVA9SKJz0VHUHs/Q7NtX80ZhxviP6zLfNKYqYMMskolO6BRz6OVEu9njEvpjCXqNFJ/6ONzbhAsQF+f0L5gpRBSHzs0YnQn9wB9zoSwmZIJk8hXY9bXs7evJv/vtr8lFJMoOsRCVeWEISv9KxILSafISS2zrNKBysgYhKAGJpMU5Zh5p5N3zOkvmv4bV3521zxcKQHyrFIZMKjw1rxbLrot1JkxsOlF+UgXQc5aBvHTHrAJeFPQxU9aqiUoBy1dRv5kx8f9NESRM9O16zNINlu6q/B9DYAJwqKb++3nK84xeMO48DEo+w0W54vJBmdB/DnBIaSRtf9DF0qZSPo8QyUu27vBplNXWk2Hk8QQWlHZfaounAV+MVdyTV1U5qvdzGm+OSOsOLNNm331gw7ZOaLE/FKOTL1nxmOJb78EyJESBTG/6D3OWdTx9cmakJ8z5vgULymD279I7rxTfW5KIfHw6OdZmfraaNxvOVyOYKn4w3QODQ2soLtYYl5OSPFx21X1zrAqlumNlBmajjkPD/SSVYRKNdbC+72pXAcYOUU73PiEYSO5NiiKxF9hut9dfIkQUe/jjSXVFcY93VTRYLM+Fztrrp3VTfS16sBxTnYpA9xaliLDU8FxemV8K2xmZhy3uOSPNCBvGFxtlicgX5VleSsz9q6+8sSBuMumvVCyGaJ7ukRrH6oabI/KxGfLj5VBIJjIpSIglSxyNWrXRo84X57B9nMsFRG2g6W6b1eIorCQ/TPSHHhPJxAjSpulz6aeAR8w+lXS5/ZkW8KnLTR6mRcrkH3aIkIhaOU4oJBAk5z9G4b2La9ZmIZYSIZ8K4T1FMbY/7lWfzAc21w4yUKDk1EOSLlRu5bVr2dHh8tBGGY25Y9ytm22HoXbQvgUiYR+K0uidwZgPBl+pkmdaEr9utccd1BJ6CAKyIy2GYtUAwZpk7IMIshtbKB5xvUtTy4xFKndEFhg5boQCJQp+GL/Od7+mriVywkrGEWP6otyCjfSF1ebrcrR0SfMH2BV1yLIPSiTd3SFa1zA3gFOzFcE2tXAxdlul3K544Dab45DZGsZdulwy1rqVyUC3Byw9s1kjy95tBGYMB7WTUhfoAlssZ/hvstV96c6W6DlUTX10+uJUxJyPPnMvdJVz9u9pk9hUU8Wq9AG04xoXONveN7rntnQkB3TtBrWAS07J6upd0KLZ+mKaDeaiMbYOifVBQBtsLXovvBxXJQtQgX+zdiPBwEmTmFGAuw/YJ+Gq1G3Zvnoc8h5G1EiGor+8REE53U/EB6dsdVbTrlGhHlxffqh6U6NexrdCny/yiimWenHNYgptSXrOIRkCTiyxNJ46dYR0Ckx00IYUkPo9e2tUYyZd9ndd8sB4wuh8llIH9/bdILJJvcvt5Fce/QiwVQc4ADkTPOjHIFJaJBk76lV1JK4/mNjK+nJF5ipyN4boN+wZ3bX4gj2xphbZCVvpLGuzuE6um72u13VgV5XVLMeohKp52bb2jX8TGS8PaJfSoi1PFHztaRp4468qbYBIURGEZHUSaI3r7154XLT15wxHa4PST7WOBNM8eQvcGyRaIMpF4HevNTcfbrKqoXdAa0cvy55q1TNeOEENxvREq3DtKGL/gRJh5pyd7OF7p67Jzq1cGklckcpRGQI3Ih7ixingBDqIT861eeilHKVmJ6p6cKziE2C7LB5SP4LLtLJgfaaqUkSEl8qIaRVLpjxQen3gNUWyV5OtQ0oyeMBDX1CuiDi7XVd6gQIhjG2Bo0/8irsKbtRJDEwAFo5yqhihoJh9hTqtZYTjmc4pacAm4L/iRjr8lmO2kLfHTBrA1VgbenqdEvUiQKL28McSCl1avdyOw1B3XI4waB6xWbFGcS/u6kyZDPMKskJmg10JQhXBa3N2E+R1ZSmm+eU7GMVkCOiNyuWYpKtc1W4ildR8TTk8uZHCN5oP9oh8c1bB39higMo9BY7TdRnm7iYZVAx/xyEbjiUsdtoX2HzPN7LXmftVH63+q48HUHZfnUh7lMkluuXy1+0zpizV8lhxemyRD/oPAi5UZuXRVZuvjHLyxucrvPjJRmu4UQ6cdiC4Ye3JzKi59zzdxHcx4JJkE3uItoUJeHYr/WUEu3dz0xmWgiynhufPNql5GQnMmZmIYF18pJ+8VDal+I74E787T914VPKd/MxBtfRwCIWPWp5Uq6c4xutO+aMzaIP/0jUuiHEbw/RBdkwWZzfhqmoHy+v15iEz4k74iTPaBm4F+f4ThmtJgOMKxdt7PJq3j/rw9lu89yvkV+GmL/++QOetjoahRuASgkv0Ug0pH5ogi8kj69Ps7R2/qmucs6SH6Caf50J69HyJMIo9Zx7IGhIjh5fDGwJvK5wi6rohNfA2Sj3Kf5JrMoM1Ro4piu9HeHnxleXbiffFWobRL9BUvkp8h4Cx+uhJahD0xBAfprKA0UE9llalrv7oKg9JHNkSFgDdAwcBQL2RVEu3FPQCU2hgT2FhQu5dnvJUOolELkxIVPLAE9o6+30q5rX2ms5pyB1kKcJ130jnSl++93QN8GkkAlp0bokgv1qJix0V3Vv7KyxeY/fiGV1UDLLMH9qH1LjE6MaPltRw2xOhobL1mWJM+iklYtErjN2xSXyVLEMGGsFuqr+Ld3K+mjb6NyFR5+v9koHoM4necgLdFDzQZrAGuCBZ+f43QjgxQcYCn7SwviZcy+oAZNczOzmpkB6Y88DPzqvAhTiQEBc9A229Ay2zzvc4/8j7y/cu5KrpLYFdCgE4SV+QFlD3MnUwtgXBakXsv4pI6exA6sLJro6HQY76HzPb4ho3sT2eE/a1bjdCQ4J9q9JBQ9/jG59pIAt7zTUCJ6zqUVfYY+KyHJ8vvecLZowlMR7P/hZFyEazCSDicJRVjpU1a9VBF92Gxkfi/JYmbeNx4s8+U9BPNXOvVlDJPYxfQh799OUfJT9pXXOk2yDmSSFM3c9rhvCfrCD3spQjTPz+FFuU3nAdtU42rdMD4Xa6ysOcme7OInTA6Q3GNzfTY1bEU6fZf3G37F/ta+s6fNB25u+rZrf/dlMV4VimvmuZcEnrJ5UmUU/z9cBS1BTXyR+bA4yNEq0XRxTj5k4AAzLOAKtW22KB9yXcfvjNmKOdOv2ycfAYIEaCFZhHtpcT65eTBXov6VGURsxinH79kwRAjNIHSgyAQ3wPg/xEpbIyHj5VXP1sYO/jzkauDV4KxOuEL4j89c77T6MtdVO9VsZPjAwc3BBNJPM9JUlbbSxYGvHz2/2oqLFqbLD9bt8UWTE7Y2DIoSZwpUCnQtQrOPT3bsJhDNLOO/jp3kTNUCWwvFY8MCdsZ70Lr1SsDjvFBeJPVTdAJH0pGGXMb3WX2oQ8eetI1OUExJyi3X/249WKJCoWgvMm2XM3+JLvDBcfEgrVST75m2VWAmtpuchcfD+OmT+OTPXsiUqQqY3MKKGxeITEk8cO1Lge+saeOPF83OgzYOb64tCvvx7ufAmqqM0XIuuT2zjyOTOpxl4+aHyJQHsK2KzCVrAJqIyPIUfFCBcanX7QD8qhsjewX65mCOkGAYfexi9cR15AO2z4gfMuq5Kb/ZPIfpx0CKpN1Qdu/JvsjuYLorFkH0C+nRyZD2iRRRl2LdBzSEkGU1srDuvdXg343EEUxYEIlIem0VoccXGncbme4j29zp/MRgRUhT8tKnOI/hEX70BN98T3Z4LodjZ5VTvUi33pQmaIPC2uqOwrZ6PXTC1E39xKsebWcm3JfyoUq8VW6zJMjYyMCyZkomwrRvTG+zbPQ/mlKYuqHo7YM5axjAERVMfZecN3+RAqzYJcfyf03KcL0xfIoGAoXMZxyCBKK0uPr+YvoT6oSYkwOPCyf12FqzYdXerRjYN+SkjyOIyRLBIGu/9YW7X/iem4IrTOzRWRa8Rgi5+SRIOlPxx9+55dP+pUxMtTQUiB+gJfl0v3v+6yqIIKds9vUXodloJEb8775m9TPab/MRHOvdRE31vsDBtEOg4E+qjYLgp3KXHQzNxCRFbgoZKcLwaDitZL2KC9go3cy8B2RJkQQy19dP9E21Y9S5AZO0xs0HaVfXvGxkeXfe18VRulTDU9mfQqK9hQGuyBbDJqUYcjagz6Tv/cZfFNn3/VILbxv4oi/UAr1dPebEFfqNUxWj7lPSnsnSHM9OUWSlzqC5hYonDo+ImhRJ+drd0gW28Qdz4BMzaB4a3KsiUulEZb9F4jgeNfZ8Fb7miu+YGNM+Hwc6vHzzOzd33Ba4hmPGL1oqkKUAsNMnEhl8WjBS4vbTODeP2ZwBkg1vgx9W83FSVjr6+pJ/Nvg3MGqzz9un7DWuRPsve/1K90gSgomgX3u2QjC0bleKXwshp+ty/lmIz76IFVj6Hz036ZZrTf/grL2Hm865e1oGHy7+KRG3Ew0pkK8zhZA9SQGS3DhVcWClVk833757wDsKQrmJEgmVVFNYsbCO83MWWmp3ld9thsURr5MrdJGw7Y2wyda5h3HWwCxtGneMmirqGx44HSdamYTXvU6ddqOJAdAd8wNPqftOCmjEMZVNY7t/dBTU/B7UKbxLBidjMwMwD6757xGtV3AdidTF+eFst6A8SkHajCWbVGKyuHX9xK5+XxIxvEzkX1G4UJ4gTemPNTQjexcZ1Gbyj/oy031pz4sfrBBJwjaI9C9L3TKa323HGYdL+h7/I27foNRRAwSqSK2Ngha/IkI3/hswLcfyjTY2A8urMWWrhoF15GfXzRM11mG64TrqmBjSIT6rz9nyA/00xQEKCrO3zVM2D5XuoBg4A0Ugr44wJpPo/VmcdGOWrhSU26rZmUbN3Uhxl56Au3AyVUnDj6qEECUtWbszGk0Lg7KwV1UQM2qktRo1V0Y1RKo0/I4mh4zXy4SWBduTWddzdLrfc+1kFfq0qQPF83s+nfvPRocMA+2AR+6Hs5Kwd08XzCyHjNTpEvXnPLMc0z1vQ3vmG3Rdph0wXmOClKqgZnD3IdnQQUDx9PHKnt7nSMtWuUT9uQZmolpTjEN5V/qR0nk5pK624Lgep+2lJlyu5w7o/x3qhlBFW0KaYGj7Q6ZbdXa5ba9+nw3K/q6CiJF8IZTEbqPPhdUsDtfK5JB3aUIExfBxpAWFiZmW8OpiT1uLyd4CSuC3G/CizA0CPCvZSj6AN49udKTyYa8PZatFKyTG1nGTLTegfVTF78PKQZOPHYYDDmQqUvvZQiIheqPBZBGyAQiWAz9IJiI5bRBjpUe1Fp2vbOAhOLEXZwzLQzD1ynG/OSyeCbVy+HFeWv0e1pgXrYujLXCGJT4eaoiw3SBgMh/gV0xIBBV7ZQecqhy9NC2iwf29peofwdG6mQVwwItGKlRQv3IEjDdlz0C7snwLEzv6EfSvnOtm/a+VkzO8dJTAQSuiHO5tIZlKX0arfbUOPoazYStd4zASFSPtMK1HID6qkXAAWYZQAD+JTLG5KaMDoEQBqvwtUMRbWeSEdj89GTwWuXW1jzSFWsa50Fryd6kGj/haywD1nUQxhOg7jp4saU0gA0BRpHKE+6Tt41t4pn7BKtbk4352tpvh4xAD9/aJV5VcFMCrGGxicSC+C2wmRUz4k8ACZSLtri2EltW89BlD9APcGH0h5iWXF9VitZWYv/DDqEYQ2b/xjZiVMJP6SY9tusmJhI1HBuAyt7ehexLP9ZekaVDBP11m+UIi4ua5dxrCSIRENSXOIeJ3v1QGE1h7zlLqs7mXg6WXbFjTePJm8tfHHVw8t5nc6J20fvqIjI047+CYwOlUtP/BM3dhzm2HwoCME/4dvpaQG/3dgrlhkCD3wGLnyC/zXipWOgcEwbnns879Ygon+mphZ8OEv+fl5oPbUkKS671QygWKhM0AnhH6aOtFI79nkzcIP6jJEUHBebGvfybYW4+i7cO8onEWAksHAI38jakccx19XDPolczZSS13iKJWvG9XkanX+pXuA4NR4tHFdtoPhWtzgZt3285+1tc8vHBKEPlyXT5ZNAqaGI6Yjop08NFR1Ii3ZqlGupdUIgiCl1ZdVgYf+xcVLN4X5D+QdTWydGEJ+dteNJLzlIG49LDq3FgHaDbx7NzABI0iGMvLK78xcNFJQvuveKT9C7wrJSI7/xVVKlqt/mcqzCZDJLu9Yjzkp7AXgeKMzvvtD+NOQFeTYdD642+W21yQNjGWvGFrqqngMyglRWcpjgXVFcgrovzr71IAFaHN2HGMjqiKTjW8tqSxvVmnEe5FhVLAfx9MCMT/0/bhSX+0hG+yYWjQSjJOCDeNdNDmD4pRyM+iSmS5Y6FgwsPgydSSsem9wL6MHUQEHmF9nl1Px3iOD5B1G19kdQfUDlUf5r2fpnkcc7JukPnA3/LQfL/u+ThjZJo96IZAAmlIsjRtex9aTazT11iEz5bhSZoQtnQiQYczaWoQki+OtBacLGJHi+uMrXIO1pQx/l874xNPL83kniLbHqdztcQHWXXMWmrUrfLZAXIWI+sCtPvfA0GimHk/wR6q+oT0vWfmNCUwsCUrVdTsZjn0sb8f8HnXw+ue6GhEvnorzRcNM90TUDS9+2XKNlHWe73IcBLroAPZ2Bp6vsABleEkUd70ME4cZrq96Xqhv/MetBQhH/ns1BbE+5SyZoIgmowZMfyFtXOylCm1RRGWqqdjOMuk+A4pBfiK4BbM2qmcjJNrcylQJ/X5/f7PiO+ZvkKDUug4robR0FvW5IoldtL9/Yueyjqpra3vwpO5+zASH9taPEitu+OJFIInacBd3h92j2RqevvV7cJMNZg470P9ySs74UGrHwFpDxFRlkcqLQlibaRJ+qAJ/sieyAQwcBXJV1Uay/RvSSUqy7TU0PcBn6tVh2WlwbGXwboVidb43ASImD52nhhWPqmQ12uMW/DpUQ3IrFpsJC6J3SBjMTAn52TUiNkuAM5QF+5vHqAKwndS4BRw6KpZnuRh5ZzmUc0PsXNwh4grPOxvArH7UUJGaNUWfDrviA3SxJWkquc57Huef1BKTL2tgEFjVkMSA6m4XIaQXztkOLxPq7PrW58GjNoFKBQ/19O/uoN032BmTQCQD9heeIUPy+HuyouHwHypzz61FQQBDaHwgftloDimgE6rBotoE0fscFnLMOtUEaffK+ObtUtMGha3e4N5lXI+OpWLm0I1F9NplEotAQ2oD1nftrt3JA2IyfwL8wnv05J6cBOEcxm2tyuKrozadYaIK38PwhahZoJMrtB4dsG/Wr6/jQblxvXxY4OpNoetPx7BsvYSEXvN3r89SFiONLngo+XU9DTZFTH5sjyTwfLdAn9qfPLfFBERUwXCqkb8vAsUijnGfKhsQTnXbVCNOOFuuC+Np+K+Kgs+zkLgkxat8PuztNq5GLIL1tyQ9WbDx3kd6HNFFaLVWIha8cI1Gzy8NO0+wlcGh27/jgyNF+e0NruReGdD8zjmoYawq+juITVVxN3MhzA5NaYsehoNbH1IZWpe9gK9Jq1BH3y+Ss3CM6oxkW6B4isEf6IWhgFjuuDqnw8JrT4DqrgmYLXvf9vDzbjZsOaUNzX8BLNOFoDs/PYa+8gAsG4wLplPqU69fXAfkm2QRINGaefIR+Vi52dyJ1/oPcfpCETwN9O2Ma2IcViXc5b/TmUqimAAcKmckg5SPJAvj40C5cjPd8uzfZ6uVEZ/cvgCiAYUyCjvHr+8nmCxYRTX7ehVNTlFvT8X+PE6+RCqKQE65fHt2k9fCXkRw49cRTLMh+OOhZjWkFDVSEmEQjR5H37fkArhmovW6WaAKFjL1zT3xIkDruW9L6XFP3TOiznGvpHISyqtCktK2CkXG2McYSuJJAvj44R2me/cz1IkuQvY6l9DiBYT8+q2BkZUr4rCv7t/+Na2n8MlgPGxtcibwNF+4TcTTXRHTtHNsZECyzgnacoQqnMgPCjLnZtiF1pO4bVk+7yhaIVb6+EweIxnUozvPpaNL0O4rfiB8UzuK0123+9NH4S5T+6j+iQCga426eVtgWExfTXJ1xJ7dtDPVjX4z1B2H+fVeLKW40p+7ivtcmVH9nDsi02przfYalFj2LM3ZJBE5yrr7XCHeuZRL2nxmuOTE6wzmdjBZ/2dIoab3FM9wbapfXLrs5RVADZRbcKu8ombaHzQP3fLvyB8AFyD3XO6ewJcPeS/FbPy2L9tAPPng6U8T/WxoAwILVXaQ4J6wz40OQpDytpxolR8u3MHAX9uNmlo2Xp2SL9JtrfGuuiuz4oYu9tSbEMdPhVeu2h5pGYaQQ6LOZVFoWEk5snTgV8AcwYe5STMSVcJos9ko9LhHmZtRbNZIXVKbYOAxv34qsD5LmKSbSE32YMnCYuFC9u/GELq+KENWgkXz5leZpsZRZGPLhMzM2Hia4Xg6n1I1FA6CGIbxp8oxwl7iwsGI5R4emMtzs5AglMjUtM+ViOeaCzK4Oug1YXqpVtXzM5V31Zrrt2BkPHarrXjbDzwHfp4+AmWeAimsvbv8J41NT9VqHGwFUOQ8siWFlyK6xRc3HIwuj1Z8z7g2Q2SHEOp0r20LUyDr/swA2F4+j4F83ZeinVHsAM6eBDfu7PAI6mVEozC0PXhMLwSNNukzO5W5z0+WQ4XiqJ/6ZtmH/IaTKmPz2boxwwGLptwk+XncZKdxFWfNyTpFYk5qeTWe1eH+JDD8GSqZIu+BgP5w1YcplZ/OulkGIwdcPIZETKdAUVNwfJzDWzJHNQgyHqeQPqhpASXpIrcJ3i5BIlHeDiYlwXqQNks8Jz4MIiruAVctP/DhgbUnINKJdhtCY/bk9e06julE5Jg1XDaQMwNfDlswrRYn3QYXwIrlvEkVrC6reyTjZcpVPiP0xp3aY7dGFLWjo+tGZ1IMquoUp30hIZb2CnzW8sFIz94aij+brulrhLpOr023H90l12xwvUpTGsCZRAG2Zcv+Woi1khqRxbyfpEDntDhOJKcdqJVSFpEXC9hcr7QGKeULpq39VgAmMmv11taUXrH9jwcCTm/C7wYX0xBargvsarE6JBKa/3MEy9by0fJVahxi5g/9bOSg+z0u0I/uTho7x8Bd+N+azjq349tIWRnRwtdIwOSPCvsM7ybSsclX+fzBYJS5U3yqihiSzM4jgXccxNASN55bHeoPaMyi06aZ5taHGQvct/8LLTqFp+Nd+KnnVryuIqlcHLT/3zFcImlcUFII1LGqbqtrRaYOo6LMZGRC6AZJrXZGNxVcPuP3YhZ6p76UD2HuqylgzQjgnpIMoeVvfSgtWBrzexIh18/bnR21FDsTpV19Tf8nZSc7a8W/JNcM6pBjESK+T6jG7iWSGXT01EP/WWP/FIOJ1bNLnuV8O+LiDpad1p5eFDQ2099hBjHkmUwoFEyn/1zPPnQH9NrdYXgZuSR6/7dVLR64OOgogLdZ64MsUOfxO0/nYMk0HptPfYQpNT9wQ4Ws8Np0ok8pmWQWCxfO5KrQDT2/JgdqjbKMSoDXQAEFb0hI8HUApAlqtjzRwavBZGqKYDl3M0p+5DpKmxqP+ZfVKBiXmZk2fLjiH6RZ4UauQ02HdVAegZetdFfG7Xr978Tr8kfrs1mk8gI0S/sQQ5YXwjxkfn0lVylsas3laVPGkH/7L9zaKy2ccrMjtTeedGzBEks9nHt5luzcAR3UeTPqSrDPhcB0pSadDQhjBtfH91oKKMI8dacq/9JoHCHk8LrAeitx5RGTBWCnpn4DHJBahXjmGsqpdLgG94IIzf+lCD5uRP8lKx5CMta3+17JJTOiuQ8b8G/NCsptrXhnGZ0sp7wQU109VPj/+WmwLDYMt3834ESnkS9Ah67liXzeEIfAME9LLSc+9guuiv2VD4Z4QfrzegrStWLtXC1vRwvMcLtUWCFvlMBTU9FTfjXi7JEdBQfWtFJ7MjdEtbr9d+wxsIzLdioi+BjlbJhLq8WEoVDDpneyZLoBRoASTaH1s1bloWwSy81K0p0k3Ln72ORNn1mYTslYMbdIwSwF6EehqKS6vlDRjPAvaw37hXv9eEaEvZ5kG5KEfeP4ZynQQQqLYAWMIEYLxpbS8tSkVeSe4YNhGrVWaLkGN9QZozygWIoBXvJiFdrsLZw+zgPpbSe+ALooU1FXYdaGPINZnFkvq7bJNmCpMhMgaatllrN5vSwjzNjZ5Mgyhfs4ZItIqTNeOSA5j/8GeX5y3++eeObKAAGK4LhVFgGoDPpMFcu5fm4FV0ZmbLRiiT0nAY2ODFNn+X4CbrKJxQEpHfONCf0S00VFa9MGFwjbPuVs68CLWIYYss3UUDLBt7h9e9FoNtk0TZ05NbqdURnDe1fxvfkTZbH/ZfsBwG09vMFPbZABUJvjihboBdQ08wTAKinxLEqpj9x4Cj8oRd6HRufA0Z5+cgtgD2osUdUpbPDTV/sSaeza2vBxrbO82O2EMUIeUWmAF9Hfu7fsmb00aCBllYbPhtaRmkUf4Rvdi/+ZerGSlmmmMqdQ0AHPdi0C/xJdR6jO7CS87hVCR/w73+ZzVvQUIKTloSMsDrr40BcGsEIta2S1kV4VwqyZ1MMjbSwt91ce/65VSWRIz2+MxR0zvtb/wKxSZ4kp9yC67QkG2PO+UhF5S4X61IFn1j9UpWmM3IJ4cODF9dXpRgHPmY4WcB+Om6iqM3izhNm7nJrTOufMUqzk1tSDdt9lzorALv8MgEFrk4ey8iwTOG074IFbUbVb/tzySGSI5UorLfpMv2pRXWZ9hgm6XSJxEJEAxsLUR2R1sYnZ284Q7OpEfLVckdosOaXAqcjQCLZsFJsXF0f4+t0Qd9aRWi32+4sF4THFTfO38feOdiwZkmEHZKqWn4K5iXSGeoiPNaNYryVKo07irij7XIpXHLLxYTnZLyUbBF3Wmkr+I+52Q9cf5Gn+beQqJfL5h+lj/fBvqIZlod6JYL/Wdreg6TNkGoXDw4Wj533NkobMJgF28aR17eieDBWwU3Woh9SrHYg8erJkfnYzTlfyXGzxYKeoWpN1B7I7Thq3kvfFtuKLmAF/i8zPTn01T+QDkiTezuZ5HPm+N4Mz8TXZDKEMNJVTc1WxUFxx+vkxdRAgn5y099kMsbfRHLtt+IqS/bJCwiJvIT3R9v73lEQtHQ24Hg6Yo4v5wttCNdqHPLZsXCB2OM6v4fLVZRcyhtxuoP0+jQVsPRVygjAyhu2WRjqaYdsgsIgnkCuxLfRwVFfDkAshBh3htYbrzKVss43IPrDuWIo4s6BgyQ8ucoAUA/3N3rY1pLsrBdB8AqyNY1A3ywAH8rQQS4yhAbWScfA3Cr1C03XOlb9NUw1UAwspyj0bXM8SRc1o1QA0qsahiePdjK3ztOYGiJbNW4BUEiPgvyakyDdu4aK9NLbb9uBJyPVAn9WLU+ChMGv7PcoTZIG+o6pIREQUSGBVSMoLwhghKY7Slk12ab0IC/xeEFwo3+ti7B4Ok8iYioa3OepMXsX8br+mUTJ02PhvADXr48hyR6OTPqmY6XQltG4JPw2ndV3c9TMJLbf/aZziYpmTAW6YENTEt7GjB8QHzeC5aOv7upSVk1dR11mJbEF4H3pKM/Q1qgKEKVR+gwajR5WgSxehDchyGZ9V1ItUiIf6CFUlNj9gTtSnmtDmcxRqSh6iimt5DBFlhhQqZCg446r9Nxirm9XRRtL9wYok3+PnAKdCYLJxzuqL8QzToneM9qvxKLtmVsqUYz5yO+jcHNgU3v/T8BcuftN8cR5kwyNI3ac+CiNUyFMpgJ3LJtboIZKq76vAeR9ueevQgaY1AzVClxFRYlXsU5ZH7CBB/pU2bIVy1xXScDXmpN3dAQ558lKfsMu/cDlKe5bewGpFWZOKxW9f/tcd5/IzYCufWcACLrJbv2+kUuZWyAFHJu8s5d7tay125qbN3PiXhPuRsj0k462ve5vXiYQAr1v7W50qmC1LSkCDrO4eLKWiiljkz1vGr275DDxPgfWt0KtqamqUhg3Jy8M3ELTnNQhW5H6dSF+q04pzMbXu1gu4kVpOsav65Ci5vLyM02GP6qvZOlP2bSq2x2kTNjmE5DDSnMEsBQabdNLMlokY/JyVe9ad0uApMuJp337aKAmvR83/gC5MpoyL0RHr2a4R3yZcNb1sG115L2jliN6Um2VXa7guHCVq9pfWJzXoOKa86cYC5xbd2LDKkvKeNxGd7bzHLuEVu/3whworxxGE4pkw9r/BwO3ajzWKc2KwcGG1GR83xc4mHe3xhkd0hucK/WrNsVE4k2lNW2bVY+vl0bcu4ppu652jvFo7Cj0oQCfp9NPOqgKlMz+cIuRyNRrMKm51la5NOSvx379WMGUOv8+njETN+p5Y9YGKQQjDgCDp6T+wYDM1479+mCovoQTCfLaJRJm/UQ0hwXVgcXmX6J7WbPor88FsDRFZj6jbHojSwrQ9vxdRW72jM1EoROC2hcSiL9f6FqjBT2/dKdNNY/NUV01vUvPJRlmMlhs4mHkE2/H7QlMcC+0gv1QJLQ22VY6O56AM70ecN1dQZQjbQ9qjO4bemVCrbBRSnome5FDwYwXgfHwE+b2M5lNtMekS48RMTOONqRiQOLWM8yIWBJMWuCXJX7FC86KnaHd1gvwSxhReTL4CDt/ePgnylhStBvJu+EZVQZGDrH2FzgVRTQJvk2rw2Z+9vOc05TV+5YXIG89bdGOrFxmfVUv4OrnBMWEVxsdpV48HRhjZ8MSaaxX1rtVHkpj86lHqpSXUfIRHuHr0Yj32fSTvRI60Q7KJfeNTg62Mfufz2bDtN8Rn4vGfypQSfYWEvbEEKxeVsie5riDZ+Q336HTp0gi5PbElxNJUS5pOcNs79W/dcoQDxI/B8hoo34L3hWdPMj5cxgrecbgYfbDJMnPe20nN+gp6nunNt0DvByF7dR0k4DiEihSFXYnj7FNw8TB2I+jQcn0qzChdzpF9DsYH/u6qtBuCrN4uYlLxS6JF3X+669l6HJmk+5xxc0ykEPkP6riUghmUy1Dvb5qktVNXL3xEpXX4VKIH6rZoeJxZWFpUwZGL9e4aKn1qxxRD7XfQHWxbAvBbDeAKdZK1pFpCcUDBPFiMI5s8CKPge+X2uHuhcyRWN/OeEkpmqOoKHxnBsokZnODfZZz+jaeBGc5XluBzsoRI4E1gCWOiV8ltvxuWF/dYiGmaLYRbp7JXoBzjwSseQSa/t8leLIzBr88pY+NCmSuZoDbWfoZFoBcw0cWVcj940gUvacWt2cZ+vaqy9QIYULcBnAZ+lcn4xdJ6p06Ejk6SsRAnQ9Ud+tCOULyiPdGGLHykgOXoPduf8F0+lKAfpmXzJBwsR8TDRgKkj/Cp9aHOwqsoES2HtaPUNu6FYCcIjrcx/LZLuX0htyE4IXb3DMB3ABmeWJ+jD4BfYd8jdOirx/9gIY6idCdDu+i8w9E6LsYFrsA4clZI5dfbyK+J4QFHMk+V5Jpa6kZhUtFdRqkGmK3C42J6L2dH9ZRtcy0diJpvD/n4XnxBgNnUtgQaiIKviVxNKOWRXk4CDG+Tn8YxDXkCY/0Slwhm2Km903AhVstXDion0jWdOJrWX3zEUC8NiAd9wjggpPzXSM8w2Ok2RmciE+mNG8uSlcm7wj1bPYz53dqr1jKvC+tPGBvOvxzEnHURAQJWuUYJTgUWQxjz5t0pmIjFPVtMlCLn0oBaLfG/CFiQ28+Qvs7kFenBzGEoaknnmE73aYYznTE+HF59c8FIi/52yGn+zWpvLR4YfQyDoYVMGsR2oF2V/egjhdm4xFnTzkCPWa1aucHVZHEz307xjXkqeVGALJUzXAiPXPwhKZUHxw43TCpoxHrOSHFzUUE4/QY3g86Xdx3o4qDOHDjnM5YMwZnuV6jqlqrtTF7qDT4vcYI1NaDLbo1Mk2HKM6m2Ls1opmIr9DdIoy8xM43E/TpuUZrSAxDdUlKa5ACRN2rDET7J8usrvztj8wicrERtAQTlP+YA40y0dEhGU0ZUzK5+d0mEhfqEpAvU8qYJ3ieD5IoLQEmm9QO9kEzTWwNCe7btaifC5mMvuqj+zSKila7EYtCLwMP7hVFcggVKXLFSZMVcetWQWpoWmpyeHHW/v3RKTY7bHGai06XxU7YyOsK+dBcC4oZE7GeDMF4/7zi2E6xiIpYg0Ip+vYdNHXoypHtzPnRGgwGkxRHftPuRuzYnMxGALK8o1CBAZSl4FGUKx9SV1ILh9Z8eFtRzc9TbLcBgKt49YtZDxKVjOcYDPm2i3RaJqPku7ag5SW6FTqTnRHwQ9TC/Wlt4HDCYqyv7RqLsbp+tGBAVWK3UaDaRAQM+7aJevvl+wcFe79N0DgcheYuj/pfyQQvrZ6VlKciXjgF2mLrcaerU60Z//nY0lUMU2VV5Mm/joElm+81kld+HP84dI34ZnCY1qHGevkicfn2HOF58msxVW7h06TIpgkyismOzgrWJKc+GPlLfT0VN77qLwiiCnRKTbU4MJCuprnoMBv2oXiBT73bj3WMzJ4vx243IHFXoVsyOroWK/m7K5ncsEcigBvWSPjV7MqzLKqh8I3lhgPKPOvvNnSUR0g1CGeb8oD1BNIvJGWODzYQ7lYP/qXztegClOQhZpm/AF5kVrPoStlnEj2A25OTDbjF0W5er5l3vKtvdC9DX5Ur5qJAJhHoh9TkLgc0FenBGTcBK4xcXOXeQTHUwpjb+caeTvbExN8TrarfqfG3IgchUV4gO5HdGNwZ+c5QnovwlJAyQyAjrqTc6dTaUMMcYL93DguTC+AKK+kp9cMponGCpRL6GLmibAjldo51+4P76/eBlw+cY6AmaJdBEsbJyEktwrE5VIMGQdvvrX40Hcf7kGVDfs/fKrNnUuhG6uRySNBLmC/HVo7m1QlElTNQEOTGf+qWe4Ha/siBP4iqxWx5Q+EkPMaA3AXG/FTFq4gXw9TtmhuzcGE4hBUg729sBMLe2HC8v6Dox0Ue5epEH2feE8Pv5zQdyNzIGq4gGFBTrknRuXVfqjrWlBpWcmwQrVAwkGKpTywX5Bw7IeeyW6WbmE1NrkT5NHjJl4wyve6Etz8qC3WDzDnu/rG557FPByk1Ce2xldhfld2paNa6V1rSfQJMZhVmrgTaCBukubaCmkKRtYr/FQEVH0CEor+KOeI7/Uh7SI59a+1Q21P8yRpMwguzUXwKsudoSB2q6ePX8nz42azep0xmN+yOIJz9bx49abjS5Rr4rm/MlzH2SY9MhPOu4/2/4AdkikcGmdFgrjmGd9Vtu6GgPYNFT1/ZW6bdOiavH/obN6/WpwxQ7g9xWp9RibC+8kHrh46J5CFApbYTKAcgyWrqRFEO0PLt7OrwialygsxL+S8gqIzgGRVKfmwZ64DloIpT1M2OEyI9brRIJ6+npbmxFjePmm2Re9/Q3u7ZzNxba5olEzzSg6WyHdapaOiMB8AOrqOr4BI+HDF8bDevuOjGBqghrA2BtdWEwp6GfttpGcbihx01KiAy3Qo1qsJU9tX6CbnnPB24tZC3bfdfef5oKbbQpCHI7bmNrIpX1h+ylQHXWj+sEkhxLKxtItrfR8IVveyBnXrjqquqCR9+NsuexMRpSXGJIi9kjxW9Qqo3Bl0fI9/0Nh2Ht1GNrFi3RtNnj93RcflzLB4S4OJvKXtkydmWwn7arYa8af3idP5KX86Bp6bnfEB7nNbThRsS3O1E0shYWlfit40BmbtDOyWrVckwfcepZ+0i0sRzFL9MSvV7xsDbT5JVOgHMfddE5lqeyzhdDhTQY4xCnJBwMP9OrQC3pzkLBp2/AhQbUEoinHpUh6HlLqs3M/9TFXBSEUDhaVMGS0mn5ZVUjc7B39TgkHfMAf4zFMWPpwC3EPgChPFeJVS3oQrjnIUr8qUCcK556fxjVqqAdZR9I01rfo6mvsIWVzm1eBU4WxWN9EqOIJNX8XP2XDmkFq7aM3II3CBCQZ4SRC3vXwdWbm+14u/gPmGIgtkONyNWGTvHCwb29PByxc0kM7M5WCvDqwgh5EJybXv1wU9Z0cUIySqAYdk0PjA0l8YAiyG95rkWeppAd3leowY1UQX/Ln3xibXJHmSjAW25MZC3/A2EdB3mU5mstaPxqoodyN8kVT5XsZ3B5L0oRiLKe8yhcnfolztgjLVC7oKBlbayRp8ozYLHL8RpF5GEMmULoQ2X6VywWYrR8+v8FDf8s6YwoRVmcoPheN1bC2lhCAY4ilq2GLYD9DcRmsKf8ggzJeJDwXv0X3NrJ0NR4cYltIMF/iIKKLasdHSjHDbMFx/fg6wjxVM85Io4qJkMj1DBCeuhT53a0oDtS535BBesZRyNpwP4YMnk7dZp0Y3qpbqu90f/uuOS71DVmVrqUcr/ANdToRt5z2+Q/tWcOIo6x04FQLR6I1aqHMvLa6D2nT2WA7f5y23NByK6aU2ffKXQLN6EUIicXRLLTRAV23mMMSQzZZ+HM9WiAsQmIiEYYEpSmpeGVRbRiYKhoPZMLE/oJMESnjwe97Ji5lHOwnYfBAcdASc2rdUAh+xVG1rpNP1CSDBYYX3Ba93cbI4hAZD5YTWv3kO4EyyiUVj0Kuue9V6+o/7O5psreWeaoP5jPAhRiqRbwn5abIwJ8QyRhkgjtbjf4IsRFo3FsW1E8hGAZjwEJKBDH2zESzJLqFwqCck9fHi6HNJEBE6xksuM0Pz7c6H6uQIp2O8RcYiOKoy9hL9uOZYF21Y5qs8qWW4vgCUgTh3vkLKbwgx+kO+GNmtnnwKF8I8R+jvkhmez47xpZC8DOQah+0j6cTEvzOERr0aChVqzFIh2YphRaVJo0E1TZ/O3z95y6D3neNU1eA21qAaTPEZYsnPyZP+3MjJBl3A3rC4YHBgMhqTaeyojK7FXfRf1cZJKjtHGX8ECbdSReOw8wYIISMKLGMDsUSeITgZu0fx4blDpGKBGtJ9CCJjXGE2FrZbntc/S4swBTvK0wLDaYaz5WH/LSEK9Mt2SmTgWCf6QXDdir8fQek+MINNrvtYbCi5sQMGbwwJfs7nxuGPujVXWH8QvSk6BKDJBHe4fjQmUCbPAD4DxSU53gZXDXq9S/UN/+WxHSEUDZESK9zDjWqcu5nQhfkGCubt4FkhNsHtr7+OjfbscRAAC6B6Znd9TKuWLzytxruOnFap688835DKenyT4gZTMMbNFar1VZDB00qAclVEyuOuwX5HqWWRfmrmIL4OqtjWNENnFLw4oGYVc+SjYt5LwI+krnp39yz+bdQE4Fit1yyalCYVXZKPmYjYex7PsF1/890790n/dYGv3WP1HP+EKwSDj1lFED6yRzgsz8KjjyxASLUXRylYo+4+yvQN+FJL5Eh1fjTMBi0IKWrsZBfWxmPPUry3UEmH7ISXMckEM5Wtbd0nsuCi9mBrDT8cZzuNiX5Kp9Pa309XTNgUJZAXp4EaVrbT+M06mivRu+BlMeKEcTdMDUrBICcPV6r0Mzg9tK1Wrtk0vCCb+R1LhFWHH+wkmkFqPsDuAAAFN6V5zup1rFVtC6rbkjBCruuYL0cpUsnrbzAhDpiw+BS7RKVvQCIsTRgQERhWXOMwoaTzFKXhTQBiyYyApa3fuiLnZ/AZ37s+Dc6yjd2k9WJZ1qrMpTj1OKd+G+UmiT5AANHU5dVNgn5xgsKffAg6/5lWwrAAAAAAAAAA=','image','2025-07-10 06:17:54','2025-07-11 09:26:08'),(8,1,'invoice_watermark','','image','2025-07-10 06:17:54','2025-07-10 06:17:54');
/*!40000 ALTER TABLE `center_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `centers`
--

DROP TABLE IF EXISTS `centers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `centers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `subdomain` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text,
  `description` text,
  `logo_url` varchar(500) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `status` enum('active','suspended','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subdomain` (`subdomain`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `centers`
--

LOCK TABLES `centers` WRITE;
/*!40000 ALTER TABLE `centers` DISABLE KEYS */;
INSERT INTO `centers` VALUES (1,'ABC Coaching Center','abc','<EMAIL>','******-567-8900','123 Main Street, City, State 12345','Excellence in education with personalized attention',NULL,NULL,'active','2025-07-09 12:29:48','2025-07-09 12:29:48');
/*!40000 ALTER TABLE `centers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `certificate_details`
--

DROP TABLE IF EXISTS `certificate_details`;
/*!50001 DROP VIEW IF EXISTS `certificate_details`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `certificate_details` AS SELECT 
 1 AS `id`,
 1 AS `certificate_number`,
 1 AS `certificate_title`,
 1 AS `course_name`,
 1 AS `completion_date`,
 1 AS `grade`,
 1 AS `percentage`,
 1 AS `language`,
 1 AS `status`,
 1 AS `issued_at`,
 1 AS `student_name`,
 1 AS `student_email`,
 1 AS `template_name`,
 1 AS `template_type`,
 1 AS `issued_by_name`,
 1 AS `center_name`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `certificate_templates`
--

DROP TABLE IF EXISTS `certificate_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `certificate_templates` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `template_type` enum('completion','achievement','participation','excellence','custom') DEFAULT 'completion',
  `background_color` varchar(7) DEFAULT '#ffffff',
  `border_style` enum('none','simple','elegant','modern','classic') DEFAULT 'elegant',
  `font_family` enum('serif','sans-serif','monospace') DEFAULT 'serif',
  `language` enum('bengali','english','both') DEFAULT 'both',
  `layout` enum('classic','modern','minimal','decorative') DEFAULT 'classic',
  `template_data` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `idx_center_templates` (`center_id`,`is_active`),
  KEY `idx_template_type` (`template_type`),
  CONSTRAINT `certificate_templates_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `certificate_templates_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `certificate_templates`
--

LOCK TABLES `certificate_templates` WRITE;
/*!40000 ALTER TABLE `certificate_templates` DISABLE KEYS */;
INSERT INTO `certificate_templates` VALUES (1,1,'Course Completion Certificate','completion','#ffffff','elegant','serif','both','classic',NULL,1,1,'2025-07-10 13:09:08','2025-07-10 13:09:08'),(2,1,'Achievement Certificate','achievement','#f8f9fa','modern','sans-serif','both','modern',NULL,1,1,'2025-07-10 13:09:08','2025-07-10 13:09:08'),(3,1,'Excellence Award','excellence','#fff8dc','','serif','both','decorative',NULL,1,1,'2025-07-10 13:09:08','2025-07-10 13:09:08'),(4,1,'Participation Certificate','participation','#f0f8ff','simple','sans-serif','both','minimal',NULL,1,1,'2025-07-10 13:09:08','2025-07-10 13:09:08');
/*!40000 ALTER TABLE `certificate_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `certificates`
--

DROP TABLE IF EXISTS `certificates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `certificates` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `student_id` int NOT NULL,
  `template_id` int NOT NULL,
  `certificate_number` varchar(50) DEFAULT NULL,
  `certificate_title` varchar(255) NOT NULL,
  `course_name` varchar(255) DEFAULT NULL,
  `completion_date` date DEFAULT NULL,
  `grade` varchar(10) DEFAULT NULL,
  `percentage` decimal(5,2) DEFAULT NULL,
  `custom_text` text,
  `language` enum('bengali','english') DEFAULT 'bengali',
  `certificate_data` json DEFAULT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `issued_by` int NOT NULL,
  `issued_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('draft','issued','revoked') DEFAULT 'issued',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `certificate_number` (`certificate_number`),
  KEY `template_id` (`template_id`),
  KEY `issued_by` (`issued_by`),
  KEY `idx_student_certificates` (`student_id`,`issued_at` DESC),
  KEY `idx_center_certificates` (`center_id`,`issued_at` DESC),
  KEY `idx_certificate_number` (`certificate_number`),
  KEY `idx_certificate_status` (`status`),
  CONSTRAINT `certificates_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `certificates_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `certificates_ibfk_3` FOREIGN KEY (`template_id`) REFERENCES `certificate_templates` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `certificates_ibfk_4` FOREIGN KEY (`issued_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `certificates`
--

LOCK TABLES `certificates` WRITE;
/*!40000 ALTER TABLE `certificates` DISABLE KEYS */;
INSERT INTO `certificates` VALUES (1,1,3,1,'MAIN-2025-0001','Certificate of Course Completion','Advanced Mathematics','2025-01-10','A+',95.50,NULL,'english',NULL,NULL,1,'2025-07-10 13:09:08','issued','2025-07-10 13:09:08','2025-07-10 13:09:08'),(2,1,3,2,'MAIN-2025-0002','Achievement Certificate','Science Olympiad','2025-01-15','Gold',98.00,NULL,'bengali',NULL,NULL,1,'2025-07-10 13:09:08','issued','2025-07-10 13:09:08','2025-07-10 13:09:08');
/*!40000 ALTER TABLE `certificates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `courses`
--

DROP TABLE IF EXISTS `courses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `courses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(50) DEFAULT NULL,
  `description` text,
  `duration_months` int DEFAULT NULL,
  `fee` decimal(10,2) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  CONSTRAINT `courses_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `courses`
--

LOCK TABLES `courses` WRITE;
/*!40000 ALTER TABLE `courses` DISABLE KEYS */;
INSERT INTO `courses` VALUES (1,1,'JEE Main Preparation','JEE001','Complete preparation for JEE Main examination',12,15000.00,'active','2025-07-09 12:29:48','2025-07-09 12:29:48',1),(2,1,'NEET Preparation','NEET001','Medical entrance exam preparation',12,18000.00,'active','2025-07-09 12:29:48','2025-07-09 12:29:48',1),(3,1,'Dolorem accusantium ','Et blanditiis ut qua','Et neque quas impedi',22,34.00,'active','2025-07-09 15:52:29','2025-07-09 15:52:29',1);
/*!40000 ALTER TABLE `courses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees`
--

DROP TABLE IF EXISTS `employees`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `employees` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `employee_id` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `position` varchar(100) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `status` enum('active','inactive','terminated') DEFAULT 'active',
  `address` text,
  `emergency_contact` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_id` (`employee_id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees`
--

LOCK TABLES `employees` WRITE;
/*!40000 ALTER TABLE `employees` DISABLE KEYS */;
INSERT INTO `employees` VALUES (1,1,'EMP001','Sarah Ahmed','<EMAIL>','***********','Senior Teacher','Academic','2023-01-15',35000.00,'active',NULL,NULL,'2025-07-09 16:09:59','2025-07-09 16:09:59'),(2,1,'EMP002','Karim Rahman','<EMAIL>','***********','Accountant','Finance','2023-03-01',28000.00,'active',NULL,NULL,'2025-07-09 16:09:59','2025-07-09 16:09:59'),(3,1,'EMP003','Fatima Khan','<EMAIL>','***********','Receptionist','Administration','2023-06-01',18000.00,'active',NULL,NULL,'2025-07-09 16:09:59','2025-07-09 16:09:59');
/*!40000 ALTER TABLE `employees` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `enrollments`
--

DROP TABLE IF EXISTS `enrollments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `enrollments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `student_id` int NOT NULL,
  `batch_id` int NOT NULL,
  `enrollment_date` date NOT NULL,
  `status` enum('active','completed','dropped','transferred') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_student_batch` (`student_id`,`batch_id`),
  KEY `center_id` (`center_id`),
  KEY `batch_id` (`batch_id`),
  CONSTRAINT `enrollments_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `enrollments_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `enrollments_ibfk_3` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `enrollments`
--

LOCK TABLES `enrollments` WRITE;
/*!40000 ALTER TABLE `enrollments` DISABLE KEYS */;
INSERT INTO `enrollments` VALUES (1,1,3,1,'2024-01-15','active','2025-07-09 12:29:48','2025-07-09 12:29:48');
/*!40000 ALTER TABLE `enrollments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exam_batches`
--

DROP TABLE IF EXISTS `exam_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exam_batches` (
  `id` int NOT NULL AUTO_INCREMENT,
  `exam_id` int NOT NULL,
  `batch_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_exam_batch` (`exam_id`,`batch_id`),
  KEY `batch_id` (`batch_id`),
  CONSTRAINT `exam_batches_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_batches_ibfk_2` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exam_batches`
--

LOCK TABLES `exam_batches` WRITE;
/*!40000 ALTER TABLE `exam_batches` DISABLE KEYS */;
INSERT INTO `exam_batches` VALUES (1,1,1,'2025-07-10 07:47:10'),(2,1,2,'2025-07-10 07:47:10'),(3,2,2,'2025-07-10 07:47:10'),(4,3,1,'2025-07-10 07:47:10');
/*!40000 ALTER TABLE `exam_batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exam_results`
--

DROP TABLE IF EXISTS `exam_results`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exam_results` (
  `id` int NOT NULL AUTO_INCREMENT,
  `exam_id` int NOT NULL,
  `student_id` int NOT NULL,
  `total_marks` decimal(10,2) NOT NULL DEFAULT '100.00',
  `batch_id` int NOT NULL,
  `obtained_marks` decimal(6,2) DEFAULT NULL,
  `percentage` decimal(5,2) DEFAULT NULL,
  `grade` varchar(5) DEFAULT NULL,
  `position_in_batch` int DEFAULT NULL,
  `position_overall` int DEFAULT NULL,
  `is_absent` tinyint(1) DEFAULT '0',
  `notes` text,
  `status` enum('draft','published') DEFAULT 'draft',
  `entered_by` int DEFAULT NULL,
  `entered_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_exam_student` (`exam_id`,`student_id`),
  KEY `batch_id` (`batch_id`),
  KEY `entered_by` (`entered_by`),
  KEY `idx_exam_results` (`exam_id`,`batch_id`),
  KEY `idx_student_results` (`student_id`,`exam_id`),
  CONSTRAINT `exam_results_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_results_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_results_ibfk_3` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_results_ibfk_4` FOREIGN KEY (`entered_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exam_results`
--

LOCK TABLES `exam_results` WRITE;
/*!40000 ALTER TABLE `exam_results` DISABLE KEYS */;
INSERT INTO `exam_results` VALUES (1,2,3,300.00,1,245.00,81.67,'A',NULL,NULL,0,NULL,'published',1,'2025-07-10 11:10:54','2025-07-10 11:10:54','2025-07-10 11:10:54'),(2,3,3,100.00,2,85.00,85.00,'A',NULL,NULL,0,NULL,'published',1,'2025-07-10 11:10:54','2025-07-10 11:10:54','2025-07-10 11:10:54'),(3,1,3,200.00,1,165.00,82.50,'A',NULL,NULL,0,NULL,'draft',1,'2025-07-10 11:10:54','2025-07-10 11:10:54','2025-07-10 11:10:54'),(10,1,1,100.00,1,85.00,85.00,'A',NULL,NULL,0,'Excellent performance','published',1,'2025-07-10 15:53:34','2025-07-10 15:53:34','2025-07-10 15:53:34'),(11,1,2,100.00,1,78.00,78.00,'B+',NULL,NULL,0,'Good work','published',1,'2025-07-10 15:53:34','2025-07-10 15:53:34','2025-07-10 15:53:34'),(12,2,1,100.00,2,92.00,92.00,'A+',NULL,NULL,0,'Outstanding','published',1,'2025-07-10 15:53:34','2025-07-10 15:53:34','2025-07-10 15:53:34'),(13,2,2,100.00,2,88.00,88.00,'A',NULL,NULL,0,'Very good','published',1,'2025-07-10 15:53:34','2025-07-10 15:53:34','2025-07-10 15:53:34');
/*!40000 ALTER TABLE `exam_results` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exam_statistics`
--

DROP TABLE IF EXISTS `exam_statistics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exam_statistics` (
  `id` int NOT NULL AUTO_INCREMENT,
  `exam_id` int NOT NULL,
  `batch_id` int DEFAULT NULL,
  `total_students` int DEFAULT '0',
  `present_students` int DEFAULT '0',
  `absent_students` int DEFAULT '0',
  `passed_students` int DEFAULT '0',
  `failed_students` int DEFAULT '0',
  `highest_marks` decimal(6,2) DEFAULT '0.00',
  `lowest_marks` decimal(6,2) DEFAULT '0.00',
  `average_marks` decimal(6,2) DEFAULT '0.00',
  `average_percentage` decimal(5,2) DEFAULT '0.00',
  `grade_distribution` json DEFAULT NULL,
  `calculated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_exam_batch_stats` (`exam_id`,`batch_id`),
  KEY `batch_id` (`batch_id`),
  CONSTRAINT `exam_statistics_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_statistics_ibfk_2` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exam_statistics`
--

LOCK TABLES `exam_statistics` WRITE;
/*!40000 ALTER TABLE `exam_statistics` DISABLE KEYS */;
/*!40000 ALTER TABLE `exam_statistics` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exam_supervisors`
--

DROP TABLE IF EXISTS `exam_supervisors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exam_supervisors` (
  `id` int NOT NULL AUTO_INCREMENT,
  `exam_id` int NOT NULL,
  `supervisor_id` int NOT NULL,
  `supervisor_role` enum('chief_supervisor','assistant_supervisor','invigilator') DEFAULT 'assistant_supervisor',
  `assigned_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_exam_supervisor` (`exam_id`,`supervisor_id`),
  KEY `supervisor_id` (`supervisor_id`),
  CONSTRAINT `exam_supervisors_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_supervisors_ibfk_2` FOREIGN KEY (`supervisor_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exam_supervisors`
--

LOCK TABLES `exam_supervisors` WRITE;
/*!40000 ALTER TABLE `exam_supervisors` DISABLE KEYS */;
INSERT INTO `exam_supervisors` VALUES (1,1,1,'chief_supervisor','2025-07-10 07:47:10'),(2,1,2,'assistant_supervisor','2025-07-10 07:47:10'),(3,2,2,'chief_supervisor','2025-07-10 07:47:10'),(4,3,1,'chief_supervisor','2025-07-10 07:47:10');
/*!40000 ALTER TABLE `exam_supervisors` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exams`
--

DROP TABLE IF EXISTS `exams`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exams` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL DEFAULT '1',
  `exam_name` varchar(255) NOT NULL,
  `exam_description` text,
  `exam_type` enum('midterm','final','quiz','mock_test','assignment','practical') DEFAULT 'midterm',
  `subject_id` int DEFAULT NULL,
  `exam_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `duration_minutes` int GENERATED ALWAYS AS (timestampdiff(MINUTE,concat(`exam_date`,_latin1' ',`start_time`),concat(`exam_date`,_latin1' ',`end_time`))) STORED,
  `room_id` int DEFAULT NULL,
  `total_marks` decimal(6,2) NOT NULL,
  `passing_marks` decimal(6,2) NOT NULL,
  `grading_scale` json DEFAULT NULL,
  `exam_instructions` text,
  `status` enum('draft','scheduled','in_progress','completed','results_published','cancelled') DEFAULT 'draft',
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `room_id` (`room_id`),
  KEY `created_by` (`created_by`),
  KEY `idx_exam_date` (`exam_date`,`status`),
  KEY `idx_exam_status` (`status`),
  KEY `idx_exam_subject` (`subject_id`),
  CONSTRAINT `exams_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `courses` (`id`) ON DELETE SET NULL,
  CONSTRAINT `exams_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE SET NULL,
  CONSTRAINT `exams_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exams`
--

LOCK TABLES `exams` WRITE;
/*!40000 ALTER TABLE `exams` DISABLE KEYS */;
INSERT INTO `exams` (`id`, `center_id`, `exam_name`, `exam_description`, `exam_type`, `subject_id`, `exam_date`, `start_time`, `end_time`, `room_id`, `total_marks`, `passing_marks`, `grading_scale`, `exam_instructions`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (1,1,'JEE Main Mock Test 1','First mock test for JEE Main preparation','mock_test',1,'2025-07-15','10:00:00','13:00:00',1,300.00,120.00,'{\"A\": 70, \"B\": 50, \"C\": 40, \"D\": 33, \"F\": 0, \"A+\": 80, \"B+\": 60}','Bring calculator, pen, and admit card. No mobile phones allowed.','scheduled',1,'2025-07-10 07:47:10','2025-07-10 07:47:10'),(2,1,'NEET Biology Test','Biology chapter test for NEET students','quiz',2,'2025-07-12','14:00:00','15:30:00',2,100.00,40.00,'{\"A\": 75, \"B\": 55, \"C\": 45, \"D\": 35, \"F\": 0, \"A+\": 85, \"B+\": 65}','Multiple choice questions only. Mark answers clearly.','completed',1,'2025-07-10 07:47:10','2025-07-10 07:47:10'),(3,1,'Mathematics Midterm','Midterm examination for mathematics','midterm',1,'2025-07-20','09:00:00','12:00:00',1,200.00,80.00,'{\"A\": 70, \"B\": 50, \"C\": 40, \"D\": 33, \"F\": 0, \"A+\": 80, \"B+\": 60}','Show all working steps. Partial marks will be awarded.','scheduled',1,'2025-07-10 07:47:10','2025-07-10 10:18:28'),(4,1,'Mathematics Midterm','Midterm examination for JEE Main Mathematics','midterm',1,'2025-07-20','09:00:00','12:00:00',NULL,200.00,80.00,NULL,NULL,'draft',1,'2025-07-10 11:10:54','2025-07-10 11:10:54'),(5,1,'JEE Main Mock Test 1','Full-length mock test for JEE Main','mock_test',1,'2025-07-15','10:00:00','13:00:00',NULL,300.00,120.00,NULL,NULL,'scheduled',1,'2025-07-10 11:10:54','2025-07-10 11:10:54');
/*!40000 ALTER TABLE `exams` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `expense_types`
--

DROP TABLE IF EXISTS `expense_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `expense_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `category` enum('infrastructure','utilities','salaries','marketing','supplies','maintenance','transport','other') DEFAULT 'other',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  CONSTRAINT `expense_types_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `expense_types`
--

LOCK TABLES `expense_types` WRITE;
/*!40000 ALTER TABLE `expense_types` DISABLE KEYS */;
INSERT INTO `expense_types` VALUES (1,1,'Electricity Bill','Monthly electricity expenses','utilities',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(2,1,'Water Bill','Monthly water expenses','utilities',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(3,1,'Internet & Phone','Internet and telephone bills','utilities',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(4,1,'Teacher Salaries','Monthly salary payments to teachers','salaries',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(5,1,'Staff Salaries','Monthly salary payments to staff','salaries',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(6,1,'Rent','Monthly building rent','infrastructure',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(7,1,'Maintenance','Building and equipment maintenance','maintenance',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(8,1,'Stationery & Supplies','Office and teaching supplies','supplies',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(9,1,'Marketing & Advertising','Promotional activities and advertising','marketing',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(10,1,'Transport','Vehicle fuel and maintenance','transport',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(11,1,'Equipment Purchase','Computers, projectors, furniture','infrastructure',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(12,1,'Software Licenses','Educational software and licenses','infrastructure',1,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(13,1,'Electric','','utilities',1,'2025-07-09 15:55:17','2025-07-09 15:55:17');
/*!40000 ALTER TABLE `expense_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `expenses`
--

DROP TABLE IF EXISTS `expenses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `expenses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `expense_type_id` int NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `amount` decimal(10,2) NOT NULL,
  `expense_date` date NOT NULL,
  `payment_method` enum('cash','card','bank_transfer','upi','cheque','online') NOT NULL,
  `payment_gateway_id` int DEFAULT NULL,
  `vendor_name` varchar(255) DEFAULT NULL,
  `vendor_contact` varchar(255) DEFAULT NULL,
  `receipt_number` varchar(100) DEFAULT NULL,
  `receipt_url` varchar(500) DEFAULT NULL,
  `notes` text,
  `status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `approved_by` int DEFAULT NULL,
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  KEY `expense_type_id` (`expense_type_id`),
  KEY `payment_gateway_id` (`payment_gateway_id`),
  KEY `approved_by` (`approved_by`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `expenses_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `expenses_ibfk_2` FOREIGN KEY (`expense_type_id`) REFERENCES `expense_types` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `expenses_ibfk_3` FOREIGN KEY (`payment_gateway_id`) REFERENCES `payment_gateways` (`id`) ON DELETE SET NULL,
  CONSTRAINT `expenses_ibfk_4` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `expenses_ibfk_5` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `expenses`
--

LOCK TABLES `expenses` WRITE;
/*!40000 ALTER TABLE `expenses` DISABLE KEYS */;
INSERT INTO `expenses` VALUES (1,1,1,'January Electricity Bill','Monthly electricity bill payment',8500.00,'2024-01-15','bank_transfer',NULL,'State Electricity Board','1800-123-456','EB001',NULL,NULL,'paid',NULL,1,'2025-07-09 12:51:11','2025-07-09 12:51:11',1),(2,1,4,'Teacher Salary - January','Monthly salary for Dr. Sarah Wilson',50000.00,'2024-01-31','bank_transfer',NULL,'Dr. Sarah Wilson','<EMAIL>','SAL001',NULL,NULL,'paid',NULL,1,'2025-07-09 12:51:11','2025-07-09 12:51:11',1),(3,1,6,'January Rent','Monthly building rent payment',25000.00,'2024-01-01','cheque',NULL,'Property Owner','**********','RENT001',NULL,NULL,'paid',NULL,1,'2025-07-09 12:51:11','2025-07-09 12:51:11',1),(4,1,8,'Office Supplies','Stationery and teaching materials',3500.00,'2024-01-20','cash',NULL,'Local Stationery Store','**********','STAT001',NULL,NULL,'paid',NULL,1,'2025-07-09 12:51:11','2025-07-09 12:51:11',1),(5,1,13,'january','',5000.00,'2025-07-09','cash',1,'','','',NULL,'','pending',NULL,1,'2025-07-09 15:55:51','2025-07-09 15:55:51',1);
/*!40000 ALTER TABLE `expenses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fees`
--

DROP TABLE IF EXISTS `fees`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fees` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `student_id` int NOT NULL,
  `batch_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `due_date` date NOT NULL,
  `status` enum('pending','paid','overdue','waived') DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  KEY `student_id` (`student_id`),
  KEY `batch_id` (`batch_id`),
  CONSTRAINT `fees_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fees_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fees_ibfk_3` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fees`
--

LOCK TABLES `fees` WRITE;
/*!40000 ALTER TABLE `fees` DISABLE KEYS */;
INSERT INTO `fees` VALUES (1,1,3,1,15000.00,'2024-02-01','paid','2025-07-09 12:29:48','2025-07-09 12:29:48',1),(2,1,3,1,5000.00,'2024-03-01','pending','2025-07-09 16:03:27','2025-07-09 16:03:27',1),(3,1,3,2,8000.00,'2024-03-15','pending','2025-07-09 16:03:27','2025-07-09 16:03:27',1),(4,1,3,1,12000.00,'2024-04-01','overdue','2025-07-09 16:03:27','2025-07-09 16:03:27',1);
/*!40000 ALTER TABLE `fees` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `id_card_details`
--

DROP TABLE IF EXISTS `id_card_details`;
/*!50001 DROP VIEW IF EXISTS `id_card_details`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `id_card_details` AS SELECT 
 1 AS `id`,
 1 AS `card_number`,
 1 AS `card_title`,
 1 AS `validity_date`,
 1 AS `emergency_contact`,
 1 AS `status`,
 1 AS `issued_at`,
 1 AS `student_name`,
 1 AS `student_email`,
 1 AS `student_roll`,
 1 AS `class_name`,
 1 AS `class_section`,
 1 AS `blood_group`,
 1 AS `profile_image_webp`,
 1 AS `template_name`,
 1 AS `card_type`,
 1 AS `layout`,
 1 AS `issued_by_name`,
 1 AS `center_name`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `id_card_templates`
--

DROP TABLE IF EXISTS `id_card_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `id_card_templates` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `card_type` enum('student','staff','visitor','temporary') DEFAULT 'student',
  `orientation` enum('portrait','landscape') DEFAULT 'portrait',
  `background_color` varchar(7) DEFAULT '#ffffff',
  `text_color` varchar(7) DEFAULT '#000000',
  `layout` enum('classic','modern','minimal','decorative') DEFAULT 'classic',
  `include_photo` tinyint(1) DEFAULT '1',
  `include_qr` tinyint(1) DEFAULT '1',
  `include_barcode` tinyint(1) DEFAULT '0',
  `template_data` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `idx_center_id_card_templates` (`center_id`,`is_active`),
  KEY `idx_card_type` (`card_type`),
  CONSTRAINT `id_card_templates_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `id_card_templates_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `id_card_templates`
--

LOCK TABLES `id_card_templates` WRITE;
/*!40000 ALTER TABLE `id_card_templates` DISABLE KEYS */;
INSERT INTO `id_card_templates` VALUES (1,1,'Classic Student ID','student','portrait','#ffffff','#000000','classic',1,1,0,NULL,1,1,'2025-07-10 13:33:39','2025-07-10 13:33:39'),(2,1,'Modern Student ID','student','portrait','#f8f9fa','#1e40af','modern',1,1,1,NULL,1,1,'2025-07-10 13:33:39','2025-07-10 13:33:39'),(3,1,'Minimal Student ID','student','portrait','#ffffff','#374151','minimal',1,0,0,NULL,1,1,'2025-07-10 13:33:39','2025-07-10 13:33:39'),(4,1,'Staff ID Card','staff','portrait','#fef3c7','#92400e','classic',1,1,1,NULL,1,1,'2025-07-10 13:33:39','2025-07-10 13:33:39'),(5,1,'Classic Student ID','student','portrait','#ffffff','#000000','classic',1,1,0,NULL,1,1,'2025-07-10 13:35:16','2025-07-10 13:35:16'),(6,1,'Modern Student ID','student','portrait','#f8f9fa','#1e40af','modern',1,1,1,NULL,1,1,'2025-07-10 13:35:16','2025-07-10 13:35:16'),(7,1,'Minimal Student ID','student','portrait','#ffffff','#374151','minimal',1,0,0,NULL,1,1,'2025-07-10 13:35:16','2025-07-10 13:35:16'),(8,1,'Staff ID Card','staff','portrait','#fef3c7','#92400e','classic',1,1,1,NULL,1,1,'2025-07-10 13:35:16','2025-07-10 13:35:16');
/*!40000 ALTER TABLE `id_card_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `id_cards`
--

DROP TABLE IF EXISTS `id_cards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `id_cards` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `student_id` int NOT NULL,
  `template_id` int NOT NULL,
  `card_number` varchar(50) DEFAULT NULL,
  `card_title` varchar(255) NOT NULL,
  `validity_date` date DEFAULT NULL,
  `emergency_contact` varchar(20) DEFAULT NULL,
  `custom_fields` json DEFAULT NULL,
  `qr_code_data` text,
  `barcode_data` varchar(100) DEFAULT NULL,
  `card_data` json DEFAULT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `issued_by` int NOT NULL,
  `issued_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('active','expired','revoked','lost') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `card_number` (`card_number`),
  KEY `template_id` (`template_id`),
  KEY `issued_by` (`issued_by`),
  KEY `idx_student_id_cards` (`student_id`,`issued_at` DESC),
  KEY `idx_center_id_cards` (`center_id`,`issued_at` DESC),
  KEY `idx_card_number` (`card_number`),
  KEY `idx_card_status` (`status`),
  CONSTRAINT `id_cards_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `id_cards_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `id_cards_ibfk_3` FOREIGN KEY (`template_id`) REFERENCES `id_card_templates` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `id_cards_ibfk_4` FOREIGN KEY (`issued_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `id_cards`
--

LOCK TABLES `id_cards` WRITE;
/*!40000 ALTER TABLE `id_cards` DISABLE KEYS */;
INSERT INTO `id_cards` VALUES (1,1,3,1,'MAIN-ID-2025-0001','Student ID Card','2025-12-31','+880 1234567890',NULL,'{\"center_id\": 1, \"issued_at\": \"2025-07-10 19:33:39.000000\", \"student_id\": 3, \"card_number\": \"MAIN-ID-2025-0001\", \"validity_date\": \"2025-12-31\"}','MAIN-ID-2025-0001',NULL,NULL,1,'2025-07-10 13:33:39','active','2025-07-10 13:33:39','2025-07-10 13:33:39'),(2,1,3,2,'MAIN-ID-2025-0002','Student Identification','2025-12-31','+880 1234567890',NULL,'{\"center_id\": 1, \"issued_at\": \"2025-07-10 19:33:39.000000\", \"student_id\": 3, \"card_number\": \"MAIN-ID-2025-0002\", \"validity_date\": \"2025-12-31\"}','MAIN-ID-2025-0002',NULL,NULL,1,'2025-07-10 13:33:39','active','2025-07-10 13:33:39','2025-07-10 13:33:39'),(3,1,3,1,'MAIN-ID-2025-0003','Student ID Card','2025-12-31','+880 1234567890',NULL,'{\"center_id\": 1, \"issued_at\": \"2025-07-10 19:35:16.000000\", \"student_id\": 3, \"card_number\": \"MAIN-ID-2025-0003\", \"validity_date\": \"2025-12-31\"}','MAIN-ID-2025-0003',NULL,NULL,1,'2025-07-10 13:35:16','active','2025-07-10 13:35:16','2025-07-10 13:35:16'),(4,1,3,2,'MAIN-ID-2025-0004','Student Identification','2025-12-31','+880 1234567890',NULL,'{\"center_id\": 1, \"issued_at\": \"2025-07-10 19:35:16.000000\", \"student_id\": 3, \"card_number\": \"MAIN-ID-2025-0004\", \"validity_date\": \"2025-12-31\"}','MAIN-ID-2025-0004',NULL,NULL,1,'2025-07-10 13:35:16','active','2025-07-10 13:35:16','2025-07-10 13:35:16');
/*!40000 ALTER TABLE `id_cards` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_categories`
--

DROP TABLE IF EXISTS `inventory_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_categories`
--

LOCK TABLES `inventory_categories` WRITE;
/*!40000 ALTER TABLE `inventory_categories` DISABLE KEYS */;
INSERT INTO `inventory_categories` VALUES (1,1,'Stationery','Office and classroom stationery items','active','2025-07-09 16:09:59','2025-07-09 16:09:59',1),(2,1,'Books','Textbooks and reference materials','active','2025-07-09 16:09:59','2025-07-09 16:09:59',1),(3,1,'Electronics','Electronic equipment and devices','active','2025-07-09 16:09:59','2025-07-09 16:09:59',1),(4,1,'Furniture','Classroom and office furniture','active','2025-07-09 16:09:59','2025-07-09 16:09:59',1);
/*!40000 ALTER TABLE `inventory_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_items`
--

DROP TABLE IF EXISTS `inventory_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `category_id` int NOT NULL,
  `item_code` varchar(100) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `unit` varchar(50) DEFAULT NULL,
  `current_stock` int DEFAULT '0',
  `minimum_stock` int DEFAULT '0',
  `unit_price` decimal(10,2) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_saleable` tinyint(1) DEFAULT '0',
  `cost_price` decimal(10,2) DEFAULT '0.00',
  `selling_price` decimal(10,2) DEFAULT '0.00',
  `barcode` varchar(100) DEFAULT NULL,
  `reorder_level` int DEFAULT '0',
  `supplier_item_code` varchar(100) DEFAULT NULL,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `item_code` (`item_code`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_items`
--

LOCK TABLES `inventory_items` WRITE;
/*!40000 ALTER TABLE `inventory_items` DISABLE KEYS */;
INSERT INTO `inventory_items` VALUES (1,1,1,'ST001','A4 Paper','White A4 printing paper','Ream',10,10,350.00,'active','2025-07-09 16:09:59','2025-07-10 16:41:08',1,280.00,420.00,'BC000001',10,NULL,1),(2,1,1,'ST002','Whiteboard Marker','Blue whiteboard marker','Piece',15,5,45.00,'active','2025-07-09 16:09:59','2025-07-09 16:43:23',1,36.00,54.00,'BC000002',5,NULL,1),(3,1,2,'BK001','Physics Textbook Class 12','HSC Physics textbook','Piece',49,20,450.00,'active','2025-07-09 16:09:59','2025-07-10 16:41:39',1,360.00,540.00,'BC000003',20,NULL,1),(4,1,3,'EL001','Projector','HD classroom projector','Piece',3,1,35000.00,'active','2025-07-09 16:09:59','2025-07-09 16:43:23',1,28000.00,42000.00,'BC000004',1,NULL,1),(5,1,4,'FU001','Student Chair','Plastic student chair','Piece',100,20,850.00,'active','2025-07-09 16:09:59','2025-07-09 16:43:23',1,680.00,1020.00,'BC000005',20,NULL,1);
/*!40000 ALTER TABLE `inventory_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_transactions`
--

DROP TABLE IF EXISTS `inventory_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_transactions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `item_id` int NOT NULL,
  `transaction_type` enum('in','out','adjustment') NOT NULL,
  `quantity` int NOT NULL,
  `unit_price` decimal(10,2) DEFAULT NULL,
  `total_amount` decimal(10,2) DEFAULT NULL,
  `reference_type` enum('purchase','sale','adjustment','damage','return') NOT NULL,
  `reference_id` varchar(100) DEFAULT NULL,
  `notes` text,
  `transaction_date` date NOT NULL,
  `processed_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_transactions`
--

LOCK TABLES `inventory_transactions` WRITE;
/*!40000 ALTER TABLE `inventory_transactions` DISABLE KEYS */;
INSERT INTO `inventory_transactions` VALUES (1,1,1,'out',10,350.00,3500.00,'sale','','','2025-07-09',1,'2025-07-09 16:34:00',1),(2,1,1,'out',10,350.00,3500.00,'sale','fgfgfg','','2025-07-09',1,'2025-07-09 16:34:24',1),(3,1,1,'out',1,420.00,420.00,'sale','2','Sale transaction','2025-07-09',1,'2025-07-09 17:00:01',1),(4,1,1,'in',10,350.00,3500.00,'purchase','','','2025-07-10',1,'2025-07-10 16:39:47',1),(5,1,3,'out',1,540.00,540.00,'sale','3','Sale transaction','2025-07-10',1,'2025-07-10 16:41:39',1);
/*!40000 ALTER TABLE `inventory_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_transfer_items`
--

DROP TABLE IF EXISTS `inventory_transfer_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_transfer_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `transfer_id` int NOT NULL,
  `item_id` int NOT NULL,
  `quantity_requested` int NOT NULL,
  `quantity_sent` int DEFAULT '0',
  `quantity_received` int DEFAULT '0',
  `unit_price` decimal(10,2) NOT NULL,
  `total_value` decimal(12,2) NOT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `item_id` (`item_id`),
  KEY `idx_transfer_item` (`transfer_id`,`item_id`),
  CONSTRAINT `inventory_transfer_items_ibfk_1` FOREIGN KEY (`transfer_id`) REFERENCES `inventory_transfers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `inventory_transfer_items_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `inventory_items` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_transfer_items`
--

LOCK TABLES `inventory_transfer_items` WRITE;
/*!40000 ALTER TABLE `inventory_transfer_items` DISABLE KEYS */;
/*!40000 ALTER TABLE `inventory_transfer_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_transfers`
--

DROP TABLE IF EXISTS `inventory_transfers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_transfers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `transfer_number` varchar(50) NOT NULL,
  `from_branch_id` int NOT NULL,
  `to_branch_id` int NOT NULL,
  `transfer_date` date NOT NULL,
  `status` enum('pending','approved','in_transit','received','cancelled') DEFAULT 'pending',
  `total_value` decimal(12,2) DEFAULT '0.00',
  `notes` text,
  `requested_by` int NOT NULL,
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `received_by` int DEFAULT NULL,
  `received_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transfer_number` (`transfer_number`),
  KEY `to_branch_id` (`to_branch_id`),
  KEY `requested_by` (`requested_by`),
  KEY `approved_by` (`approved_by`),
  KEY `received_by` (`received_by`),
  KEY `idx_transfer_number` (`transfer_number`),
  KEY `idx_branch_transfers` (`from_branch_id`,`to_branch_id`,`status`),
  CONSTRAINT `inventory_transfers_ibfk_1` FOREIGN KEY (`from_branch_id`) REFERENCES `branches` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `inventory_transfers_ibfk_2` FOREIGN KEY (`to_branch_id`) REFERENCES `branches` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `inventory_transfers_ibfk_3` FOREIGN KEY (`requested_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `inventory_transfers_ibfk_4` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `inventory_transfers_ibfk_5` FOREIGN KEY (`received_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_transfers`
--

LOCK TABLES `inventory_transfers` WRITE;
/*!40000 ALTER TABLE `inventory_transfers` DISABLE KEYS */;
/*!40000 ALTER TABLE `inventory_transfers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoices`
--

DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `invoices` (
  `id` int NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(50) NOT NULL,
  `student_id` int NOT NULL,
  `student_course_id` int NOT NULL,
  `invoice_type` enum('enrollment','monthly_fee','one_time_fee') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `discount_amount` decimal(10,2) DEFAULT '0.00',
  `tax_amount` decimal(10,2) DEFAULT '0.00',
  `total_amount` decimal(10,2) NOT NULL,
  `due_date` date NOT NULL,
  `payment_status` enum('pending','paid','overdue','cancelled') DEFAULT 'pending',
  `payment_date` date DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invoice_number` (`invoice_number`),
  KEY `student_course_id` (`student_course_id`),
  KEY `idx_student_invoices` (`student_id`,`payment_status`),
  KEY `idx_invoice_date` (`due_date`,`payment_status`),
  CONSTRAINT `invoices_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `invoices_ibfk_2` FOREIGN KEY (`student_course_id`) REFERENCES `student_courses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoices`
--

LOCK TABLES `invoices` WRITE;
/*!40000 ALTER TABLE `invoices` DISABLE KEYS */;
INSERT INTO `invoices` VALUES (1,'INV-1752142497138-1',3,1,'monthly_fee',18000.00,0.00,0.00,18000.00,'2025-07-17','pending',NULL,NULL,NULL,'2025-07-10 10:14:57','2025-07-10 10:14:57');
/*!40000 ALTER TABLE `invoices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `leave_requests`
--

DROP TABLE IF EXISTS `leave_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `leave_requests` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `employee_id` int NOT NULL,
  `leave_type` enum('sick','casual','annual','maternity','emergency') NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `days_requested` int NOT NULL,
  `reason` text,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `leave_requests`
--

LOCK TABLES `leave_requests` WRITE;
/*!40000 ALTER TABLE `leave_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `leave_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','warning','success','error') DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
INSERT INTO `notifications` VALUES (1,1,3,'Welcome to ABC Coaching','Welcome to our coaching center! Your classes start from tomorrow.','info',0,'2025-07-09 12:29:48'),(2,1,3,'Fee Payment Due','Your monthly fee payment is due on 1st February 2024.','warning',0,'2025-07-09 12:29:48');
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_gateways`
--

DROP TABLE IF EXISTS `payment_gateways`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment_gateways` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('razorpay','payu','stripe','paypal','phonepe','gpay','paytm','cash','bank_transfer','cheque','upi') NOT NULL,
  `api_key` varchar(500) DEFAULT NULL,
  `api_secret` varchar(500) DEFAULT NULL,
  `webhook_url` varchar(500) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `is_default` tinyint(1) DEFAULT '0',
  `configuration` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  CONSTRAINT `payment_gateways_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_gateways`
--

LOCK TABLES `payment_gateways` WRITE;
/*!40000 ALTER TABLE `payment_gateways` DISABLE KEYS */;
INSERT INTO `payment_gateways` VALUES (1,1,'Cash Payment','cash',NULL,NULL,NULL,1,1,NULL,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(2,1,'Bank Transfer','bank_transfer',NULL,NULL,NULL,1,0,NULL,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(3,1,'UPI Payment','upi',NULL,NULL,NULL,1,0,NULL,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(4,1,'Cheque Payment','cheque',NULL,NULL,NULL,1,0,NULL,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(5,1,'Razorpay','razorpay',NULL,NULL,NULL,0,0,NULL,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(6,1,'PayU','payu',NULL,NULL,NULL,0,0,NULL,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(7,1,'PhonePe','phonepe',NULL,NULL,NULL,0,0,NULL,'2025-07-09 12:51:11','2025-07-09 12:51:11'),(8,1,'Google Pay','gpay',NULL,NULL,NULL,0,0,NULL,'2025-07-09 12:51:11','2025-07-09 12:51:11');
/*!40000 ALTER TABLE `payment_gateways` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payments`
--

DROP TABLE IF EXISTS `payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `fee_id` int NOT NULL,
  `student_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method` enum('cash','card','bank_transfer','upi','cheque') NOT NULL,
  `payment_date` date NOT NULL,
  `transaction_id` varchar(255) DEFAULT NULL,
  `receipt_number` varchar(100) DEFAULT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  KEY `fee_id` (`fee_id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`fee_id`) REFERENCES `fees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `payments_ibfk_3` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payments`
--

LOCK TABLES `payments` WRITE;
/*!40000 ALTER TABLE `payments` DISABLE KEYS */;
INSERT INTO `payments` VALUES (1,1,1,3,15000.00,'upi','2024-01-20',NULL,'RCP001',NULL,'2025-07-09 12:29:48','2025-07-09 12:29:48');
/*!40000 ALTER TABLE `payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `plans`
--

DROP TABLE IF EXISTS `plans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `plans` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `duration_months` int DEFAULT '12',
  `max_students` int DEFAULT '-1',
  `max_teachers` int DEFAULT '-1',
  `max_courses` int DEFAULT '-1',
  `features` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `plans`
--

LOCK TABLES `plans` WRITE;
/*!40000 ALTER TABLE `plans` DISABLE KEYS */;
INSERT INTO `plans` VALUES (1,'Basic Plan','Perfect for small coaching centers',5000.00,12,100,5,10,'[\"Student Management\", \"Basic Reports\", \"Email Support\", \"Attendance Tracking\"]',1,'2025-07-09 12:29:48','2025-07-09 12:29:48'),(2,'Premium Plan','Ideal for growing coaching centers',12000.00,12,500,20,50,'[\"All Basic Features\", \"Advanced Analytics\", \"SMS Integration\", \"Priority Support\", \"Fee Management\", \"Assignment System\"]',1,'2025-07-09 12:29:48','2025-07-09 12:29:48'),(3,'Enterprise Plan','For large coaching institutions',25000.00,12,-1,-1,-1,'[\"All Premium Features\", \"Custom Branding\", \"API Access\", \"Dedicated Support\", \"Multi-location Support\", \"Advanced Reporting\"]',1,'2025-07-09 12:29:48','2025-07-09 12:29:48');
/*!40000 ALTER TABLE `plans` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_order_items`
--

DROP TABLE IF EXISTS `purchase_order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `purchase_order_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int NOT NULL,
  `item_id` int NOT NULL,
  `quantity_ordered` int NOT NULL,
  `quantity_received` int DEFAULT '0',
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(12,2) NOT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `item_id` (`item_id`),
  KEY `idx_po_item` (`purchase_order_id`,`item_id`),
  CONSTRAINT `purchase_order_items_ibfk_1` FOREIGN KEY (`purchase_order_id`) REFERENCES `purchase_orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `purchase_order_items_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `inventory_items` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_order_items`
--

LOCK TABLES `purchase_order_items` WRITE;
/*!40000 ALTER TABLE `purchase_order_items` DISABLE KEYS */;
INSERT INTO `purchase_order_items` VALUES (1,1,1,50,50,300.00,15000.00,NULL,'2025-07-09 16:43:23');
/*!40000 ALTER TABLE `purchase_order_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_orders`
--

DROP TABLE IF EXISTS `purchase_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `purchase_orders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `po_number` varchar(50) NOT NULL,
  `vendor_id` int NOT NULL,
  `order_date` date NOT NULL,
  `expected_delivery_date` date DEFAULT NULL,
  `actual_delivery_date` date DEFAULT NULL,
  `subtotal` decimal(12,2) NOT NULL DEFAULT '0.00',
  `tax_amount` decimal(10,2) DEFAULT '0.00',
  `discount_amount` decimal(10,2) DEFAULT '0.00',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `status` enum('draft','pending','approved','ordered','partially_received','received','cancelled') DEFAULT 'draft',
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `notes` text,
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `po_number` (`po_number`),
  KEY `approved_by` (`approved_by`),
  KEY `created_by` (`created_by`),
  KEY `idx_center_po` (`center_id`,`po_number`),
  KEY `idx_vendor_date` (`vendor_id`,`order_date`),
  CONSTRAINT `purchase_orders_ibfk_1` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `purchase_orders_ibfk_2` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `purchase_orders_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_orders`
--

LOCK TABLES `purchase_orders` WRITE;
/*!40000 ALTER TABLE `purchase_orders` DISABLE KEYS */;
INSERT INTO `purchase_orders` VALUES (1,1,'PO2024001',1,'2024-01-15','2024-01-25',NULL,15000.00,0.00,0.00,15000.00,'received',NULL,NULL,NULL,2,'2025-07-09 16:43:23','2025-07-09 16:43:23',1);
/*!40000 ALTER TABLE `purchase_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_payments`
--

DROP TABLE IF EXISTS `purchase_payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `purchase_payments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `purchase_order_id` int NOT NULL,
  `transaction_id` int DEFAULT NULL,
  `amount` decimal(12,2) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` enum('cash','cheque','bank_transfer','card') NOT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `notes` text,
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `purchase_order_id` (`purchase_order_id`),
  KEY `transaction_id` (`transaction_id`),
  KEY `created_by` (`created_by`),
  KEY `idx_center_payment` (`center_id`,`payment_date`),
  CONSTRAINT `purchase_payments_ibfk_1` FOREIGN KEY (`purchase_order_id`) REFERENCES `purchase_orders` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `purchase_payments_ibfk_2` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `purchase_payments_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_payments`
--

LOCK TABLES `purchase_payments` WRITE;
/*!40000 ALTER TABLE `purchase_payments` DISABLE KEYS */;
/*!40000 ALTER TABLE `purchase_payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `room_booking_attendees`
--

DROP TABLE IF EXISTS `room_booking_attendees`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `room_booking_attendees` (
  `id` int NOT NULL AUTO_INCREMENT,
  `booking_id` int NOT NULL,
  `user_id` int NOT NULL,
  `role` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_booking_attendee` (`booking_id`,`user_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `room_booking_attendees_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `room_bookings` (`id`) ON DELETE CASCADE,
  CONSTRAINT `room_booking_attendees_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `room_booking_attendees`
--

LOCK TABLES `room_booking_attendees` WRITE;
/*!40000 ALTER TABLE `room_booking_attendees` DISABLE KEYS */;
/*!40000 ALTER TABLE `room_booking_attendees` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `room_booking_audit_log`
--

DROP TABLE IF EXISTS `room_booking_audit_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `room_booking_audit_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `booking_id` int NOT NULL,
  `action_type` enum('create','update','cancel','approve','confirm') NOT NULL,
  `action_by` int DEFAULT NULL,
  `action_details` json DEFAULT NULL,
  `action_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `action_by` (`action_by`),
  KEY `idx_booking_audit` (`booking_id`,`action_time`),
  CONSTRAINT `room_booking_audit_log_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `room_bookings` (`id`) ON DELETE CASCADE,
  CONSTRAINT `room_booking_audit_log_ibfk_2` FOREIGN KEY (`action_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `room_booking_audit_log`
--

LOCK TABLES `room_booking_audit_log` WRITE;
/*!40000 ALTER TABLE `room_booking_audit_log` DISABLE KEYS */;
INSERT INTO `room_booking_audit_log` VALUES (1,4,'approve',1,'{\"new_end\": \"2025-07-10 23:46:00.000000\", \"old_end\": \"2025-07-10 23:46:00.000000\", \"new_start\": \"2025-07-10 22:45:00.000000\", \"old_start\": \"2025-07-10 22:45:00.000000\", \"new_status\": \"approved\", \"old_status\": \"pending\", \"new_room_id\": 4, \"old_room_id\": 4}','2025-07-10 16:46:04');
/*!40000 ALTER TABLE `room_booking_audit_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `room_bookings`
--

DROP TABLE IF EXISTS `room_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `room_bookings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL DEFAULT '1',
  `room_id` int NOT NULL,
  `booking_type` enum('class','exam','meeting','conference','training','workshop','other') NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `organizer_id` int DEFAULT NULL,
  `attendees_count` int DEFAULT NULL,
  `start_datetime` datetime NOT NULL,
  `end_datetime` datetime NOT NULL,
  `recurring_pattern` json DEFAULT NULL,
  `status` enum('pending','approved','confirmed','cancelled') DEFAULT 'pending',
  `equipment_requirements` text,
  `special_notes` text,
  `reference_id` int DEFAULT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `organizer_id` (`organizer_id`),
  KEY `created_by` (`created_by`),
  KEY `idx_room_booking_time` (`room_id`,`start_datetime`,`end_datetime`),
  KEY `idx_booking_status` (`status`),
  KEY `idx_booking_reference` (`reference_type`,`reference_id`),
  CONSTRAINT `room_bookings_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE CASCADE,
  CONSTRAINT `room_bookings_ibfk_2` FOREIGN KEY (`organizer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `room_bookings_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `room_bookings`
--

LOCK TABLES `room_bookings` WRITE;
/*!40000 ALTER TABLE `room_bookings` DISABLE KEYS */;
INSERT INTO `room_bookings` VALUES (1,1,1,'exam','JEE Main Mock Test 1','First mock test for JEE Main preparation',1,NULL,'2025-07-15 10:00:00','2025-07-15 13:00:00',NULL,'confirmed',NULL,NULL,1,'exam',1,'2025-07-10 07:47:10','2025-07-10 07:47:10'),(2,1,2,'exam','NEET Biology Test','Biology chapter test for NEET students',1,NULL,'2025-07-12 14:00:00','2025-07-12 15:30:00',NULL,'confirmed',NULL,NULL,2,'exam',1,'2025-07-10 07:47:10','2025-07-10 07:47:10'),(3,1,1,'exam','Mathematics Midterm','Midterm examination for mathematics',1,NULL,'2025-07-20 09:00:00','2025-07-20 12:00:00',NULL,'confirmed',NULL,NULL,3,'exam',1,'2025-07-10 10:18:28','2025-07-10 10:18:28'),(4,1,4,'workshop','new student orientation','',6,20,'2025-07-10 22:45:00','2025-07-10 23:46:00',NULL,'approved','','',NULL,NULL,1,'2025-07-10 16:46:01','2025-07-10 16:46:04');
/*!40000 ALTER TABLE `room_bookings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rooms`
--

DROP TABLE IF EXISTS `rooms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rooms` (
  `id` int NOT NULL AUTO_INCREMENT,
  `branch_id` int NOT NULL,
  `room_code` varchar(50) NOT NULL,
  `room_name` varchar(255) NOT NULL,
  `room_type` enum('classroom','lab','auditorium','library','office','other') DEFAULT 'classroom',
  `capacity` int NOT NULL DEFAULT '0',
  `equipment` text,
  `status` enum('active','inactive','maintenance') DEFAULT 'active',
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_room_code_per_branch` (`branch_id`,`room_code`),
  KEY `idx_branch_room` (`branch_id`,`status`),
  CONSTRAINT `rooms_ibfk_1` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rooms`
--

LOCK TABLES `rooms` WRITE;
/*!40000 ALTER TABLE `rooms` DISABLE KEYS */;
INSERT INTO `rooms` VALUES (1,1,'R101','Classroom 101','classroom',30,'Whiteboard, Projector, AC','active',NULL,'2025-07-09 17:31:02','2025-07-09 17:31:02'),(2,1,'R102','Classroom 102','classroom',25,'Whiteboard, AC','active',NULL,'2025-07-09 17:31:02','2025-07-09 17:31:02'),(3,1,'LAB1','Physics Lab','lab',20,'Lab Equipment, Safety Kit','active',NULL,'2025-07-09 17:31:02','2025-07-09 17:31:02'),(4,1,'AUD1','Main Auditorium','auditorium',100,'Sound System, Projector, AC','active',NULL,'2025-07-09 17:31:02','2025-07-09 17:31:02'),(5,3,'G101','Classroom G101','classroom',40,'Interactive Board, AC','active',NULL,'2025-07-09 17:31:02','2025-07-09 17:31:02'),(6,3,'G102','Classroom G102','classroom',35,'Whiteboard, Projector','active',NULL,'2025-07-09 17:31:02','2025-07-09 17:31:02'),(7,3,'GLIB','Library','library',50,'Books, Reading Tables, AC','active',NULL,'2025-07-09 17:31:02','2025-07-09 17:31:02'),(8,4,'U101','Classroom U101','classroom',30,'Whiteboard, AC','active',NULL,'2025-07-09 17:31:02','2025-07-09 17:31:02'),(9,4,'U102','Classroom U102','classroom',28,'Smart Board, Projector','active',NULL,'2025-07-09 17:31:02','2025-07-09 17:31:02'),(13,1,'R103','Classroom 103','classroom',35,'Smart Board, AC, Sound System','active','','2025-07-10 04:16:16','2025-07-10 04:16:16'),(14,1,'banani','Quas velit dolorem d','classroom',45,'Magnam consequatur a','active','Quod pariatur Labor','2025-07-10 05:03:40','2025-07-10 05:03:40'),(15,1,'Possimus id blandit','Neque laborum dolore','lab',43,'Quod et quis fugiat ','active','Adipisci earum non N','2025-07-10 06:03:47','2025-07-10 06:03:47');
/*!40000 ALTER TABLE `rooms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `salary_records`
--

DROP TABLE IF EXISTS `salary_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `salary_records` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `employee_id` int NOT NULL,
  `month` int NOT NULL,
  `year` int NOT NULL,
  `basic_salary` decimal(10,2) NOT NULL,
  `allowances` decimal(10,2) DEFAULT '0.00',
  `deductions` decimal(10,2) DEFAULT '0.00',
  `overtime_hours` decimal(5,2) DEFAULT '0.00',
  `overtime_rate` decimal(8,2) DEFAULT '0.00',
  `gross_salary` decimal(10,2) NOT NULL,
  `net_salary` decimal(10,2) NOT NULL,
  `status` enum('draft','processed','paid') DEFAULT 'draft',
  `payment_date` date DEFAULT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_employee_month_year` (`employee_id`,`month`,`year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `salary_records`
--

LOCK TABLES `salary_records` WRITE;
/*!40000 ALTER TABLE `salary_records` DISABLE KEYS */;
/*!40000 ALTER TABLE `salary_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sale_items`
--

DROP TABLE IF EXISTS `sale_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sale_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sale_id` int NOT NULL,
  `item_id` int NOT NULL,
  `quantity` int NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(12,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `item_id` (`item_id`),
  KEY `idx_sale_item` (`sale_id`,`item_id`),
  CONSTRAINT `sale_items_ibfk_1` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sale_items_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `inventory_items` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sale_items`
--

LOCK TABLES `sale_items` WRITE;
/*!40000 ALTER TABLE `sale_items` DISABLE KEYS */;
INSERT INTO `sale_items` VALUES (1,1,2,1,500.00,500.00,'2025-07-09 16:43:23'),(2,2,1,1,420.00,420.00,'2025-07-09 17:00:01'),(3,3,3,1,540.00,540.00,'2025-07-10 16:41:39');
/*!40000 ALTER TABLE `sale_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sales`
--

DROP TABLE IF EXISTS `sales`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sales` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `sale_number` varchar(50) NOT NULL,
  `student_id` int DEFAULT NULL,
  `customer_name` varchar(255) DEFAULT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `sale_date` datetime NOT NULL,
  `subtotal` decimal(12,2) NOT NULL DEFAULT '0.00',
  `tax_amount` decimal(10,2) DEFAULT '0.00',
  `discount_amount` decimal(10,2) DEFAULT '0.00',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00',
  `payment_method` enum('cash','card','mobile_banking','credit') NOT NULL,
  `payment_status` enum('pending','partial','paid') DEFAULT 'pending',
  `amount_paid` decimal(12,2) DEFAULT '0.00',
  `amount_due` decimal(12,2) DEFAULT '0.00',
  `due_date` date DEFAULT NULL,
  `notes` text,
  `cashier_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `sale_number` (`sale_number`),
  KEY `cashier_id` (`cashier_id`),
  KEY `idx_center_sale` (`center_id`,`sale_number`),
  KEY `idx_student_date` (`student_id`,`sale_date`),
  KEY `idx_payment_status` (`payment_status`,`due_date`),
  CONSTRAINT `sales_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sales_ibfk_2` FOREIGN KEY (`cashier_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sales`
--

LOCK TABLES `sales` WRITE;
/*!40000 ALTER TABLE `sales` DISABLE KEYS */;
INSERT INTO `sales` VALUES (1,1,'SAL2024001',3,NULL,NULL,'2024-01-22 10:30:00',500.00,0.00,0.00,500.00,'cash','paid',500.00,0.00,NULL,NULL,2,'2025-07-09 16:43:23','2025-07-09 16:43:23',1),(2,1,'SAL20250709401010',5,'Architecto et volupt','Magnam reprehenderit','2025-07-09 23:00:01',420.00,0.00,0.00,420.00,'credit','pending',0.00,420.00,NULL,'POS Sale - credit',1,'2025-07-09 17:00:01','2025-07-09 17:00:01',1),(3,1,'SAL20250710699152',3,'Mike Student','******-567-8903','2025-07-10 22:41:39',540.00,0.00,0.00,540.00,'cash','paid',540.00,0.00,NULL,'POS Sale - cash',1,'2025-07-10 16:41:39','2025-07-10 16:41:39',1);
/*!40000 ALTER TABLE `sales` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `schedules`
--

DROP TABLE IF EXISTS `schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `schedules` (
  `id` int NOT NULL AUTO_INCREMENT,
  `branch_id` int NOT NULL,
  `course_id` int NOT NULL,
  `batch_id` int DEFAULT NULL,
  `room_id` int NOT NULL,
  `teacher_id` int NOT NULL,
  `day_of_week` enum('monday','tuesday','wednesday','thursday','friday','saturday','sunday') NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `effective_from` date NOT NULL,
  `effective_to` date DEFAULT NULL,
  `status` enum('active','inactive','cancelled') DEFAULT 'active',
  `notes` text,
  `created_by` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `course_id` (`course_id`),
  KEY `batch_id` (`batch_id`),
  KEY `created_by` (`created_by`),
  KEY `idx_branch_schedule` (`branch_id`,`day_of_week`,`start_time`),
  KEY `idx_room_schedule` (`room_id`,`day_of_week`,`start_time`),
  KEY `idx_teacher_schedule` (`teacher_id`,`day_of_week`,`start_time`),
  CONSTRAINT `schedules_ibfk_1` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE,
  CONSTRAINT `schedules_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `schedules_ibfk_3` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE SET NULL,
  CONSTRAINT `schedules_ibfk_4` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `schedules_ibfk_5` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `schedules_ibfk_6` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `schedules`
--

LOCK TABLES `schedules` WRITE;
/*!40000 ALTER TABLE `schedules` DISABLE KEYS */;
INSERT INTO `schedules` VALUES (1,1,3,1,4,6,'saturday','11:29:00','12:30:00','2025-07-10','2025-08-25','active','',1,'2025-07-10 05:31:28','2025-07-10 05:31:28'),(2,1,3,1,4,6,'monday','11:30:00','12:30:00','2025-07-10','2025-08-25','active','',1,'2025-07-10 05:31:28','2025-07-10 05:31:28'),(3,1,2,1,4,2,'saturday','11:00:00','13:00:00','2025-07-10',NULL,'active','',1,'2025-07-10 16:43:14','2025-07-10 16:43:14');
/*!40000 ALTER TABLE `schedules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `staff_branches`
--

DROP TABLE IF EXISTS `staff_branches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `staff_branches` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `branch_id` int NOT NULL,
  `role` varchar(50) DEFAULT 'staff',
  `assigned_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_branch` (`user_id`,`branch_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_branch_id` (`branch_id`),
  CONSTRAINT `staff_branches_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `staff_branches_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `staff_branches`
--

LOCK TABLES `staff_branches` WRITE;
/*!40000 ALTER TABLE `staff_branches` DISABLE KEYS */;
/*!40000 ALTER TABLE `staff_branches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `student_account_balances`
--

DROP TABLE IF EXISTS `student_account_balances`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_account_balances` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `student_id` int NOT NULL,
  `balance` decimal(12,2) DEFAULT '0.00',
  `credit_limit` decimal(12,2) DEFAULT '0.00',
  `last_payment_date` date DEFAULT NULL,
  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_student_balance` (`center_id`,`student_id`),
  KEY `student_id` (`student_id`),
  KEY `idx_center_balance` (`center_id`,`balance`),
  CONSTRAINT `student_account_balances_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `student_account_balances`
--

LOCK TABLES `student_account_balances` WRITE;
/*!40000 ALTER TABLE `student_account_balances` DISABLE KEYS */;
INSERT INTO `student_account_balances` VALUES (1,1,5,420.00,0.00,NULL,'2025-07-09 17:00:01');
/*!40000 ALTER TABLE `student_account_balances` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `student_audit_logs`
--

DROP TABLE IF EXISTS `student_audit_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_audit_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `student_id` int NOT NULL,
  `action_type` enum('status_change','profile_update','course_assignment','fee_payment','created','deleted','transfer_requested','transfer_approved','transfer_completed','transfer_cancelled','certificate_issued','certificate_revoked','id_card_issued','id_card_revoked') NOT NULL,
  `old_value` text,
  `new_value` text,
  `field_name` varchar(100) DEFAULT NULL,
  `description` text,
  `performed_by` int NOT NULL,
  `performed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  PRIMARY KEY (`id`),
  KEY `idx_student_audit` (`student_id`,`performed_at` DESC),
  KEY `idx_center_audit` (`center_id`,`performed_at` DESC),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_performed_by` (`performed_by`),
  CONSTRAINT `student_audit_logs_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `student_audit_logs_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `student_audit_logs_ibfk_3` FOREIGN KEY (`performed_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `student_audit_logs`
--

LOCK TABLES `student_audit_logs` WRITE;
/*!40000 ALTER TABLE `student_audit_logs` DISABLE KEYS */;
INSERT INTO `student_audit_logs` VALUES (1,1,3,'created',NULL,'active','status','Student account created: Mike Student',1,'2025-01-10 04:00:00',NULL,NULL),(2,1,3,'profile_update',NULL,'A+','blood_group','Blood group added during profile update',1,'2025-01-10 08:30:00',NULL,NULL),(3,1,3,'id_card_issued',NULL,'MAIN-ID-2025-0003','id_card','ID card issued: Student ID Card (MAIN-ID-2025-0003)',1,'2025-07-10 13:35:16',NULL,NULL),(4,1,3,'id_card_issued',NULL,'MAIN-ID-2025-0004','id_card','ID card issued: Student Identification (MAIN-ID-2025-0004)',1,'2025-07-10 13:35:16',NULL,NULL);
/*!40000 ALTER TABLE `student_audit_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `student_audit_view`
--

DROP TABLE IF EXISTS `student_audit_view`;
/*!50001 DROP VIEW IF EXISTS `student_audit_view`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `student_audit_view` AS SELECT 
 1 AS `id`,
 1 AS `student_id`,
 1 AS `student_name`,
 1 AS `action_type`,
 1 AS `old_value`,
 1 AS `new_value`,
 1 AS `field_name`,
 1 AS `description`,
 1 AS `performed_by`,
 1 AS `performed_by_name`,
 1 AS `performed_at`,
 1 AS `ip_address`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `student_courses`
--

DROP TABLE IF EXISTS `student_courses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_courses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_id` int NOT NULL,
  `course_id` int NOT NULL,
  `batch_id` int DEFAULT NULL,
  `enrollment_type` enum('monthly','one_time') DEFAULT 'monthly',
  `monthly_fee` decimal(10,2) DEFAULT NULL,
  `one_time_fee` decimal(10,2) DEFAULT NULL,
  `discount_amount` decimal(10,2) DEFAULT '0.00',
  `discount_percentage` decimal(5,2) DEFAULT '0.00',
  `notes` text,
  `enrollment_date` date NOT NULL,
  `status` enum('active','inactive','completed','cancelled') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_student_course` (`student_id`,`course_id`),
  KEY `batch_id` (`batch_id`),
  KEY `idx_student_courses` (`student_id`,`status`),
  KEY `idx_course_students` (`course_id`,`status`),
  CONSTRAINT `student_courses_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `student_courses_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `student_courses_ibfk_3` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `student_courses`
--

LOCK TABLES `student_courses` WRITE;
/*!40000 ALTER TABLE `student_courses` DISABLE KEYS */;
INSERT INTO `student_courses` VALUES (1,3,2,NULL,'monthly',18000.00,0.00,0.00,0.00,'','2025-07-10','active','2025-07-10 10:14:57','2025-07-10 10:14:57');
/*!40000 ALTER TABLE `student_courses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `student_transfers`
--

DROP TABLE IF EXISTS `student_transfers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_transfers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL DEFAULT '1',
  `student_id` int NOT NULL,
  `from_branch_id` int NOT NULL,
  `to_branch_id` int NOT NULL,
  `transfer_reason` text,
  `transfer_date` date NOT NULL,
  `effective_date` date NOT NULL DEFAULT (curdate()),
  `reason` text NOT NULL,
  `status` enum('pending','approved','rejected','completed') DEFAULT 'pending',
  `requested_by` int NOT NULL,
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `notes` text,
  `data_migration_status` enum('pending','in_progress','completed','failed') DEFAULT 'pending',
  `migration_details` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_student_transfers` (`student_id`,`transfer_date`),
  KEY `idx_branch_transfers` (`from_branch_id`,`to_branch_id`,`status`),
  KEY `fk_transfers_center` (`center_id`),
  KEY `fk_transfers_to_branch` (`to_branch_id`),
  KEY `fk_transfers_requested_by` (`requested_by`),
  KEY `fk_transfers_approved_by` (`approved_by`),
  CONSTRAINT `fk_transfers_approved_by` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_transfers_center` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_transfers_from_branch` FOREIGN KEY (`from_branch_id`) REFERENCES `branches` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_transfers_requested_by` FOREIGN KEY (`requested_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_transfers_student` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_transfers_to_branch` FOREIGN KEY (`to_branch_id`) REFERENCES `branches` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `student_transfers_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `student_transfers_ibfk_2` FOREIGN KEY (`from_branch_id`) REFERENCES `branches` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `student_transfers_ibfk_3` FOREIGN KEY (`to_branch_id`) REFERENCES `branches` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `student_transfers_ibfk_4` FOREIGN KEY (`requested_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `student_transfers_ibfk_5` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `student_transfers`
--

LOCK TABLES `student_transfers` WRITE;
/*!40000 ALTER TABLE `student_transfers` DISABLE KEYS */;
INSERT INTO `student_transfers` VALUES (1,1,3,1,3,'Student requested transfer for convenience','2025-01-05','2025-01-10','','completed',1,1,'2025-01-06 04:00:00',NULL,'pending',NULL,'2025-07-10 12:51:52','2025-07-10 12:51:52'),(2,1,3,3,4,'Family relocated to Gulshan area','2025-01-15','2025-01-20','','pending',1,NULL,NULL,NULL,'pending',NULL,'2025-07-10 12:51:52','2025-07-10 12:51:52');
/*!40000 ALTER TABLE `student_transfers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `students`
--

DROP TABLE IF EXISTS `students`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `students` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `student_id` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other') DEFAULT NULL,
  `address` text,
  `guardian_name` varchar(255) DEFAULT NULL,
  `guardian_phone` varchar(20) DEFAULT NULL,
  `guardian_email` varchar(255) DEFAULT NULL,
  `guardian_relation` varchar(50) DEFAULT NULL,
  `admission_date` date DEFAULT NULL,
  `status` enum('active','inactive','graduated','dropped') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_student_id_tenant` (`student_id`,`tenant_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_tenant` (`tenant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_guardian_phone` (`guardian_phone`),
  CONSTRAINT `students_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `students_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `students`
--

LOCK TABLES `students` WRITE;
/*!40000 ALTER TABLE `students` DISABLE KEYS */;
/*!40000 ALTER TABLE `students` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `subjects`
--

DROP TABLE IF EXISTS `subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subjects` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(50) DEFAULT NULL,
  `description` text,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  CONSTRAINT `subjects_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subjects`
--

LOCK TABLES `subjects` WRITE;
/*!40000 ALTER TABLE `subjects` DISABLE KEYS */;
INSERT INTO `subjects` VALUES (1,1,'Mathematics','MATH101','Advanced Mathematics for competitive exams','active','2025-07-09 12:29:48','2025-07-09 12:29:48'),(2,1,'Physics','PHY101','Physics fundamentals and problem solving','active','2025-07-09 12:29:48','2025-07-09 12:29:48'),(3,1,'Chemistry','CHEM101','Organic and Inorganic Chemistry','active','2025-07-09 12:29:48','2025-07-09 12:29:48'),(4,1,'Mathematics','MATH101','Advanced Mathematics for competitive exams','active','2025-07-10 11:06:53','2025-07-10 11:06:53'),(5,1,'Physics','PHY101','Physics fundamentals and problem solving','active','2025-07-10 11:06:53','2025-07-10 11:06:53'),(6,1,'Chemistry','CHEM101','Organic and Inorganic Chemistry','active','2025-07-10 11:06:53','2025-07-10 11:06:53'),(7,1,'Biology','BIO101','Biology for NEET preparation','active','2025-07-10 11:06:53','2025-07-10 11:06:53'),(8,1,'English','ENG101','English language and literature','active','2025-07-10 11:06:53','2025-07-10 11:06:53'),(9,1,'Mathematics','MATH101','Advanced Mathematics for competitive exams','active','2025-07-10 11:10:54','2025-07-10 11:10:54'),(10,1,'Physics','PHY101','Physics fundamentals and problem solving','active','2025-07-10 11:10:54','2025-07-10 11:10:54'),(11,1,'Chemistry','CHEM101','Organic and Inorganic Chemistry','active','2025-07-10 11:10:54','2025-07-10 11:10:54'),(12,1,'Biology','BIO101','Biology for NEET preparation','active','2025-07-10 11:10:54','2025-07-10 11:10:54'),(13,1,'English','ENG101','English language and literature','active','2025-07-10 11:10:54','2025-07-10 11:10:54'),(14,1,'Mathematics','MATH101','Advanced Mathematics for competitive exams','active','2025-07-10 11:13:31','2025-07-10 11:13:31'),(15,1,'Physics','PHY101','Physics fundamentals and problem solving','active','2025-07-10 11:13:31','2025-07-10 11:13:31'),(16,1,'Chemistry','CHEM101','Organic and Inorganic Chemistry','active','2025-07-10 11:13:31','2025-07-10 11:13:31'),(17,1,'Biology','BIO101','Biology for NEET preparation','active','2025-07-10 11:13:31','2025-07-10 11:13:31'),(18,1,'English','ENG101','English language and literature','active','2025-07-10 11:13:31','2025-07-10 11:13:31');
/*!40000 ALTER TABLE `subjects` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `subscriptions`
--

DROP TABLE IF EXISTS `subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subscriptions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `plan_id` int NOT NULL,
  `status` enum('active','expired','cancelled','pending') DEFAULT 'pending',
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_status` enum('paid','pending','failed','refunded') DEFAULT 'pending',
  `auto_renewal` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `center_id` (`center_id`),
  KEY `plan_id` (`plan_id`),
  CONSTRAINT `subscriptions_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `subscriptions_ibfk_2` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subscriptions`
--

LOCK TABLES `subscriptions` WRITE;
/*!40000 ALTER TABLE `subscriptions` DISABLE KEYS */;
INSERT INTO `subscriptions` VALUES (1,1,2,'active','2024-01-01','2024-12-31',12000.00,'paid',1,'2025-07-09 12:29:48','2025-07-09 12:29:48');
/*!40000 ALTER TABLE `subscriptions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `teachers`
--

DROP TABLE IF EXISTS `teachers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `teachers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` int NOT NULL,
  `user_id` int NOT NULL,
  `employee_id` varchar(50) DEFAULT NULL,
  `subjects` json DEFAULT NULL,
  `qualifications` text,
  `experience_years` int DEFAULT '0',
  `salary` decimal(10,2) DEFAULT '0.00',
  `join_date` date DEFAULT NULL,
  `status` enum('active','inactive','on_leave') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_employee_id_tenant` (`employee_id`,`tenant_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_tenant` (`tenant_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `teachers_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `teachers_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `teachers`
--

LOCK TABLES `teachers` WRITE;
/*!40000 ALTER TABLE `teachers` DISABLE KEYS */;
/*!40000 ALTER TABLE `teachers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tenants`
--

DROP TABLE IF EXISTS `tenants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tenants` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text,
  `subscription_plan` enum('basic','premium','enterprise') DEFAULT 'basic',
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_slug` (`slug`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tenants`
--

LOCK TABLES `tenants` WRITE;
/*!40000 ALTER TABLE `tenants` DISABLE KEYS */;
INSERT INTO `tenants` VALUES (1,'ABC Coaching Center','abc-coaching','<EMAIL>','+1234567890','123 Main St, City, State','premium','active','2025-07-09 09:30:42','2025-07-09 09:30:42');
/*!40000 ALTER TABLE `tenants` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transactions`
--

DROP TABLE IF EXISTS `transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transactions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `transaction_id` varchar(255) NOT NULL,
  `type` enum('fee_collection','expense','refund','adjustment') NOT NULL,
  `student_id` int DEFAULT NULL,
  `fee_id` int DEFAULT NULL,
  `expense_id` int DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'INR',
  `payment_method` enum('cash','card','bank_transfer','upi','cheque','online','razorpay','payu','stripe','paypal','phonepe','gpay','paytm') NOT NULL,
  `payment_gateway_id` int DEFAULT NULL,
  `gateway_transaction_id` varchar(255) DEFAULT NULL,
  `gateway_payment_id` varchar(255) DEFAULT NULL,
  `gateway_order_id` varchar(255) DEFAULT NULL,
  `gateway_signature` varchar(500) DEFAULT NULL,
  `gateway_response` json DEFAULT NULL,
  `status` enum('pending','processing','completed','failed','cancelled','refunded') DEFAULT 'pending',
  `transaction_date` datetime NOT NULL,
  `description` text,
  `receipt_number` varchar(100) DEFAULT NULL,
  `receipt_url` varchar(500) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `notes` text,
  `processed_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `student_id` (`student_id`),
  KEY `fee_id` (`fee_id`),
  KEY `expense_id` (`expense_id`),
  KEY `payment_gateway_id` (`payment_gateway_id`),
  KEY `processed_by` (`processed_by`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_center_type` (`center_id`,`type`),
  KEY `idx_status_date` (`status`,`transaction_date`),
  KEY `idx_gateway_transaction` (`gateway_transaction_id`),
  CONSTRAINT `transactions_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `transactions_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `transactions_ibfk_3` FOREIGN KEY (`fee_id`) REFERENCES `fees` (`id`) ON DELETE SET NULL,
  CONSTRAINT `transactions_ibfk_4` FOREIGN KEY (`expense_id`) REFERENCES `expenses` (`id`) ON DELETE SET NULL,
  CONSTRAINT `transactions_ibfk_5` FOREIGN KEY (`payment_gateway_id`) REFERENCES `payment_gateways` (`id`) ON DELETE SET NULL,
  CONSTRAINT `transactions_ibfk_6` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transactions`
--

LOCK TABLES `transactions` WRITE;
/*!40000 ALTER TABLE `transactions` DISABLE KEYS */;
INSERT INTO `transactions` VALUES (1,1,'TXN001','fee_collection',3,1,NULL,15000.00,'INR','upi',3,NULL,NULL,NULL,NULL,NULL,'completed','2024-01-20 10:30:00','JEE Course Fee Payment','RCP001',NULL,NULL,NULL,NULL,1,'2025-07-09 12:51:11','2025-07-09 12:51:11',1),(2,1,'TXN002','fee_collection',3,1,NULL,5000.00,'INR','cash',1,NULL,NULL,NULL,NULL,NULL,'completed','2024-01-25 14:15:00','Partial Fee Payment','RCP002',NULL,NULL,NULL,NULL,1,'2025-07-09 12:51:11','2025-07-09 12:51:11',1),(3,1,'TXN003','expense',NULL,NULL,1,8500.00,'INR','bank_transfer',2,NULL,NULL,NULL,NULL,NULL,'completed','2024-01-15 09:00:00','Electricity Bill Payment','EXP001',NULL,NULL,NULL,NULL,1,'2025-07-09 12:51:11','2025-07-09 12:51:11',1),(4,1,'TXN004','expense',NULL,NULL,2,50000.00,'INR','bank_transfer',2,NULL,NULL,NULL,NULL,NULL,'completed','2024-01-31 16:00:00','Teacher Salary Payment','EXP002',NULL,NULL,NULL,NULL,1,'2025-07-09 12:51:11','2025-07-09 12:51:11',1),(5,1,'TXN005','expense',NULL,NULL,3,25000.00,'INR','cheque',4,NULL,NULL,NULL,NULL,NULL,'completed','2024-01-01 11:00:00','Monthly Rent Payment','EXP003',NULL,NULL,NULL,NULL,1,'2025-07-09 12:51:11','2025-07-09 12:51:11',1),(6,1,'EXP1752076551468','expense',NULL,NULL,5,5000.00,'INR','cash',1,NULL,NULL,NULL,NULL,NULL,'pending','2025-07-09 00:00:00','january','',NULL,NULL,NULL,NULL,1,'2025-07-09 15:55:51','2025-07-09 15:55:51',1);
/*!40000 ALTER TABLE `transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transfer_data_backup`
--

DROP TABLE IF EXISTS `transfer_data_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transfer_data_backup` (
  `id` int NOT NULL AUTO_INCREMENT,
  `transfer_id` int NOT NULL,
  `table_name` varchar(100) NOT NULL,
  `record_id` int NOT NULL,
  `original_data` json NOT NULL,
  `backup_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_transfer_backup` (`transfer_id`,`table_name`),
  CONSTRAINT `transfer_data_backup_ibfk_1` FOREIGN KEY (`transfer_id`) REFERENCES `student_transfers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transfer_data_backup`
--

LOCK TABLES `transfer_data_backup` WRITE;
/*!40000 ALTER TABLE `transfer_data_backup` DISABLE KEYS */;
/*!40000 ALTER TABLE `transfer_data_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_branch_assignments`
--

DROP TABLE IF EXISTS `user_branch_assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_branch_assignments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `branch_id` int NOT NULL,
  `role` enum('admin','manager','teacher','staff','viewer') NOT NULL,
  `is_primary` tinyint(1) DEFAULT '0',
  `assigned_by` int NOT NULL,
  `assigned_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('active','inactive') DEFAULT 'active',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_branch` (`user_id`,`branch_id`),
  KEY `assigned_by` (`assigned_by`),
  KEY `idx_user_branches` (`user_id`,`status`),
  KEY `idx_branch_users` (`branch_id`,`role`,`status`),
  CONSTRAINT `user_branch_assignments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_branch_assignments_ibfk_2` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_branch_assignments_ibfk_3` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_branch_assignments`
--

LOCK TABLES `user_branch_assignments` WRITE;
/*!40000 ALTER TABLE `user_branch_assignments` DISABLE KEYS */;
INSERT INTO `user_branch_assignments` VALUES (1,1,1,'admin',1,1,'2025-07-10 04:11:33','active');
/*!40000 ALTER TABLE `user_branch_assignments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other') DEFAULT NULL,
  `blood_group` enum('A+','A-','B+','B-','AB+','AB-','O+','O-') DEFAULT NULL,
  `role` enum('super_admin','center_admin','teacher','student') NOT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `profile_image` varchar(500) DEFAULT NULL,
  `emergency_contact` varchar(20) DEFAULT NULL,
  `parent_name` varchar(255) DEFAULT NULL,
  `parent_phone` varchar(20) DEFAULT NULL,
  `admission_date` date DEFAULT NULL,
  `employee_id` varchar(50) DEFAULT NULL,
  `qualification` text,
  `experience_years` int DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  `profile_image_webp` text,
  `class_name` varchar(100) DEFAULT NULL,
  `class_section` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_email_center` (`email`,`center_id`),
  KEY `center_id` (`center_id`),
  KEY `idx_users_blood_group` (`blood_group`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`center_id`) REFERENCES `centers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,1,'John Admin','<EMAIL>','$2b$12$86rRZ221DLVfNftmvyJTAe/0htt9MFhqr1ypIf3cJ3WvLCgtKmTE6','******-567-8901',NULL,NULL,NULL,NULL,'center_admin','active',NULL,NULL,NULL,NULL,'2024-01-01',NULL,NULL,NULL,NULL,'2025-07-09 12:29:48','2025-07-11 09:26:20',1,NULL,NULL,NULL),(2,1,'Sarah Teacher','<EMAIL>','$2b$10$1PSRXLfx9TArSNeO0QoPse5kjHv2QHC/xb5wH0q05dZv8TQQCTkvG','******-567-8902',NULL,NULL,NULL,NULL,'teacher','active',NULL,NULL,NULL,NULL,'2024-01-01',NULL,NULL,NULL,NULL,'2025-07-09 12:29:48','2025-07-09 12:29:48',1,NULL,NULL,NULL),(3,1,'Mike Student','<EMAIL>','$2b$10$1PSRXLfx9TArSNeO0QoPse5kjHv2QHC/xb5wH0q05dZv8TQQCTkvG','******-567-8903',NULL,NULL,NULL,'A+','student','active',NULL,NULL,NULL,NULL,'2024-01-15',NULL,NULL,NULL,NULL,'2025-07-09 12:29:48','2025-07-10 12:05:54',1,NULL,NULL,NULL),(4,NULL,'Super Admin','<EMAIL>','$2b$12$86rRZ221DLVfNftmvyJTAe/0htt9MFhqr1ypIf3cJ3WvLCgtKmTE6',NULL,NULL,NULL,NULL,NULL,'super_admin','active',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2025-07-09 13:44:34','2025-07-09 15:06:48',1,NULL,NULL,NULL),(5,1,'Architecto et volupt','<EMAIL>','$2b$10$I7t/0VAQC7oz4GO6M1L5MOwj6r2lFy6//GKzeg5lcgXlg6oXCGpjW','Magnam reprehenderit','Sapiente animi debi','2019-12-05','female',NULL,'student','active',NULL,'Minus est qui natus ','Quaerat dolores non ','Necessitatibus ipsum','2025-07-09',NULL,NULL,NULL,NULL,'2025-07-09 15:14:14','2025-07-09 15:14:14',1,NULL,NULL,NULL),(6,1,'Voluptate totam dolo','<EMAIL>','$2b$10$j82Rm2mIf1lZm0fpAxfKcuf/607IojDzSGQThoZ37xiQ1HYYczHpa','Quam dolore deserunt','Mollit omnis eum ut ',NULL,NULL,NULL,'teacher','active',NULL,NULL,NULL,NULL,NULL,'Voluptatem est quam','Voluptates quia expl',87,32.00,'2025-07-09 15:53:53','2025-07-09 15:53:53',1,NULL,NULL,NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `vendors`
--

DROP TABLE IF EXISTS `vendors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `vendors` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `vendor_code` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text,
  `payment_terms` varchar(100) DEFAULT NULL,
  `credit_limit` decimal(12,2) DEFAULT '0.00',
  `status` enum('active','inactive','blocked') DEFAULT 'active',
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `vendor_code` (`vendor_code`),
  KEY `idx_center_vendor` (`center_id`,`vendor_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `vendors`
--

LOCK TABLES `vendors` WRITE;
/*!40000 ALTER TABLE `vendors` DISABLE KEYS */;
INSERT INTO `vendors` VALUES (1,1,'VEN001','ABC Stationery Suppliers','Mr. Rahman','<EMAIL>','***********','Dhanmondi, Dhaka','Net 30',0.00,'active',NULL,'2025-07-09 16:43:23','2025-07-09 16:43:23',1),(2,1,'VEN002','Tech Equipment Ltd','Ms. Fatima','<EMAIL>','***********','Gulshan, Dhaka','Net 15',0.00,'active',NULL,'2025-07-09 16:43:23','2025-07-09 16:43:23',1),(3,1,'VEN003','Book Publishers BD','Mr. Karim','<EMAIL>','***********','Elephant Road, Dhaka','Net 45',0.00,'active',NULL,'2025-07-09 16:43:23','2025-07-09 16:43:23',1);
/*!40000 ALTER TABLE `vendors` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wastage_records`
--

DROP TABLE IF EXISTS `wastage_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wastage_records` (
  `id` int NOT NULL AUTO_INCREMENT,
  `center_id` int NOT NULL,
  `item_id` int NOT NULL,
  `quantity` int NOT NULL,
  `unit_cost` decimal(10,2) NOT NULL,
  `total_cost` decimal(12,2) NOT NULL,
  `reason` enum('expired','damaged','lost','theft','quality_issue','other') NOT NULL,
  `description` text,
  `wastage_date` date NOT NULL,
  `reported_by` int NOT NULL,
  `approved_by` int DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `branch_id` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `item_id` (`item_id`),
  KEY `reported_by` (`reported_by`),
  KEY `approved_by` (`approved_by`),
  KEY `idx_center_wastage` (`center_id`,`wastage_date`),
  CONSTRAINT `wastage_records_ibfk_1` FOREIGN KEY (`item_id`) REFERENCES `inventory_items` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `wastage_records_ibfk_2` FOREIGN KEY (`reported_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `wastage_records_ibfk_3` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wastage_records`
--

LOCK TABLES `wastage_records` WRITE;
/*!40000 ALTER TABLE `wastage_records` DISABLE KEYS */;
INSERT INTO `wastage_records` VALUES (1,1,2,2,450.00,900.00,'damaged','Books damaged due to water leak','2024-01-20',2,NULL,NULL,'approved',NULL,'2025-07-09 16:43:23','2025-07-09 16:43:23',1),(2,1,1,4,150.00,600.00,'quality_issue','','2025-07-10',1,1,'2025-07-10 16:41:08','approved','Wastage approved','2025-07-10 16:41:04','2025-07-10 16:41:08',1);
/*!40000 ALTER TABLE `wastage_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Final view structure for view `certificate_details`
--

/*!50001 DROP VIEW IF EXISTS `certificate_details`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `certificate_details` AS select `c`.`id` AS `id`,`c`.`certificate_number` AS `certificate_number`,`c`.`certificate_title` AS `certificate_title`,`c`.`course_name` AS `course_name`,`c`.`completion_date` AS `completion_date`,`c`.`grade` AS `grade`,`c`.`percentage` AS `percentage`,`c`.`language` AS `language`,`c`.`status` AS `status`,`c`.`issued_at` AS `issued_at`,`s`.`name` AS `student_name`,`s`.`email` AS `student_email`,`ct`.`name` AS `template_name`,`ct`.`template_type` AS `template_type`,`issuer`.`name` AS `issued_by_name`,`center`.`name` AS `center_name` from ((((`certificates` `c` join `users` `s` on((`c`.`student_id` = `s`.`id`))) join `certificate_templates` `ct` on((`c`.`template_id` = `ct`.`id`))) join `users` `issuer` on((`c`.`issued_by` = `issuer`.`id`))) join `centers` `center` on((`c`.`center_id` = `center`.`id`))) order by `c`.`issued_at` desc */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `id_card_details`
--

/*!50001 DROP VIEW IF EXISTS `id_card_details`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `id_card_details` AS select `ic`.`id` AS `id`,`ic`.`card_number` AS `card_number`,`ic`.`card_title` AS `card_title`,`ic`.`validity_date` AS `validity_date`,`ic`.`emergency_contact` AS `emergency_contact`,`ic`.`status` AS `status`,`ic`.`issued_at` AS `issued_at`,`s`.`name` AS `student_name`,`s`.`email` AS `student_email`,`s`.`id` AS `student_roll`,`s`.`class_name` AS `class_name`,`s`.`class_section` AS `class_section`,`s`.`blood_group` AS `blood_group`,`s`.`profile_image_webp` AS `profile_image_webp`,`ict`.`name` AS `template_name`,`ict`.`card_type` AS `card_type`,`ict`.`layout` AS `layout`,`issuer`.`name` AS `issued_by_name`,`center`.`name` AS `center_name` from ((((`id_cards` `ic` join `users` `s` on((`ic`.`student_id` = `s`.`id`))) join `id_card_templates` `ict` on((`ic`.`template_id` = `ict`.`id`))) join `users` `issuer` on((`ic`.`issued_by` = `issuer`.`id`))) join `centers` `center` on((`ic`.`center_id` = `center`.`id`))) order by `ic`.`issued_at` desc */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `student_audit_view`
--

/*!50001 DROP VIEW IF EXISTS `student_audit_view`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `student_audit_view` AS select `sal`.`id` AS `id`,`sal`.`student_id` AS `student_id`,`u`.`name` AS `student_name`,`sal`.`action_type` AS `action_type`,`sal`.`old_value` AS `old_value`,`sal`.`new_value` AS `new_value`,`sal`.`field_name` AS `field_name`,`sal`.`description` AS `description`,`sal`.`performed_by` AS `performed_by`,`performer`.`name` AS `performed_by_name`,`sal`.`performed_at` AS `performed_at`,`sal`.`ip_address` AS `ip_address` from ((`student_audit_logs` `sal` join `users` `u` on((`sal`.`student_id` = `u`.`id`))) left join `users` `performer` on((`sal`.`performed_by` = `performer`.`id`))) order by `sal`.`performed_at` desc */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-11 15:33:07
