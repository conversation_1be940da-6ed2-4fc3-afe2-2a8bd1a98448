import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { ThemeProvider } from './components/theme-provider'
import { AuthProvider } from './contexts/AuthContext'
import { LanguageProvider } from './contexts/LanguageContext'
import { BranchProvider } from './contexts/BranchContext'
import ProtectedRoute from './components/ProtectedRoute'
import './i18n'
import SmartHomePage from './components/SmartHomePage'
import LoginPage from './pages/LoginPage'
import SuperAdminDashboard from './pages/SuperAdminDashboard'
import CenterAdminDashboard from './pages/CenterAdminDashboard'
import CentersPage from './pages/admin/CentersPage'
import SubscriptionsPage from './pages/admin/SubscriptionsPage'
import PlansPage from './pages/admin/PlansPage'
import CoachingDashboard from './pages/coaching/DashboardPage'
import CoachingStudents from './pages/coaching/StudentsPage'
import CoachingTeachers from './pages/coaching/TeachersPage'
import CoachingCourses from './pages/coaching/CoursesPage'
import CoachingExpenses from './pages/coaching/ExpensesPage'
import CoachingTransactions from './pages/coaching/TransactionsPage'
import CoachingHRM from './pages/coaching/HRMPage'
import CoachingInventory from './pages/coaching/InventoryPage'
import CoachingPOS from './pages/coaching/POSPage'
import CoachingBranches from './pages/coaching/BranchManagementPage'
import CoachingSettings from './pages/coaching/SettingsPage'
import CertificateGenerator from './pages/coaching/CertificateGeneratorPage'
import IDCardGenerator from './pages/coaching/IDCardGeneratorPage'
import UserManagement from './pages/coaching/UserManagementPage'
import AssignmentsPage from './pages/coaching/AssignmentsPage'
import StudentCourses from './pages/coaching/StudentCoursesPage'
import InvoicePage from './pages/coaching/InvoicePage'
import ExamsPage from './pages/coaching/ExamsPage'
import ExamResultsPage from './pages/coaching/ExamResultsPage'
import ResultManagementPage from './pages/coaching/ResultManagementPage'
import RoomManagementPage from './pages/coaching/RoomManagementPage'
import SchedulePage from './pages/SchedulePage'
import AttendancePage from './pages/AttendancePage'
import FeesPage from './pages/FeesPage'
import ResultsPage from './pages/ResultsPage'
import HRMPage from './pages/HRMPage'
import NotificationsPage from './pages/NotificationsPage'
import ReportsPage from './pages/ReportsPage'
import CoachingFeesPage from './pages/coaching/FeesPage'
import CoachingReportsPage from './pages/coaching/ReportsPage'
import ScheduleManagementPage from './pages/ScheduleManagementPage'
import ExamManagementPage from './pages/ExamManagementPage'
import AssetExpenseManagementPage from './pages/AssetExpenseManagementPage'
import CustomPagesManagementPage from './pages/CustomPagesManagementPage'
import PublicCustomPage from './pages/PublicCustomPage'

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="coaching-ui-theme">
      <LanguageProvider>
        <AuthProvider>
          <BranchProvider>
            <Router>
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<SmartHomePage />} />
            <Route path="/login" element={<LoginPage />} />

            {/* Super Admin Routes */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute requiredRole="super_admin">
                  <SuperAdminDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/centers"
              element={
                <ProtectedRoute requiredRole="super_admin">
                  <CentersPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/subscriptions"
              element={
                <ProtectedRoute requiredRole="super_admin">
                  <SubscriptionsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/plans"
              element={
                <ProtectedRoute requiredRole="super_admin">
                  <PlansPage />
                </ProtectedRoute>
              }
            />

            {/* Coaching Center Routes */}
            <Route
              path="/coaching/dashboard"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/students"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingStudents />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/teachers"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingTeachers />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/courses"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingCourses />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/expenses"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingExpenses />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/transactions"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingTransactions />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/hrm"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingHRM />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/inventory"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingInventory />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/pos"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingPOS />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/branches"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingBranches />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/settings"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingSettings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/certificates"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CertificateGenerator />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/id-cards"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <IDCardGenerator />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/users"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <UserManagement />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/assignments"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <AssignmentsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/students/:studentId/courses"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <StudentCourses />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/invoices/:invoiceNumber"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <InvoicePage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/invoices/:invoiceNumber/print"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <InvoicePage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/exams"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <ExamsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/exams/:examId/results"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <ExamResultsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/results"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <ResultManagementPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/room-management"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <RoomManagementPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/fees"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingFeesPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/exam-results"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <ResultManagementPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/coaching/reports"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CoachingReportsPage />
                </ProtectedRoute>
              }
            />

            {/* New Enhanced Features */}
            <Route
              path="/schedule-management"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <ScheduleManagementPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/exam-management"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <ExamManagementPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/asset-expense-management"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <AssetExpenseManagementPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/custom-pages"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CustomPagesManagementPage />
                </ProtectedRoute>
              }
            />

            {/* Public Custom Pages Route */}
            <Route
              path="/:slug"
              element={<PublicCustomPage />}
            />

            {/* Center Admin Routes (Tenant-specific) */}
            <Route
              path="/center/dashboard"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <CenterAdminDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/schedule"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <SchedulePage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/attendance"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <AttendancePage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/fees"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <FeesPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/results"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <ResultsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/hrm"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <HRMPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/notifications"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <NotificationsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/reports"
              element={
                <ProtectedRoute requiredRole="center_admin" requireTenant={true}>
                  <ReportsPage />
                </ProtectedRoute>
              }
            />
          </Routes>
        </Router>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              style: {
                background: '#10b981',
              },
            },
            error: {
              duration: 5000,
              style: {
                background: '#ef4444',
              },
            },
          }}
        />
          </BranchProvider>
        </AuthProvider>
      </LanguageProvider>
    </ThemeProvider>
  )
}

export default App
