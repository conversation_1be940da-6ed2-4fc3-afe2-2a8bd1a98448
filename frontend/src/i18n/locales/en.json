{"common": {"loading": "Loading...", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "filter": "Filter", "filters": "Filters", "all": "All", "clearFilters": "Clear Filters", "actions": "Actions", "status": "Status", "active": "Active", "inactive": "Inactive", "total": "Total", "currency": "৳", "email": "Email", "phone": "Phone", "address": "Address"}, "navigation": {"dashboard": "Dashboard", "students": "Students", "teachers": "Teachers", "courses": "Courses & Batches", "attendance": "Attendance", "fees": "Fees & Payments", "expenses": "Expenses", "transactions": "Transactions", "assignments": "Assignments", "reports": "Reports", "settings": "Settings", "hrm": "Human Resources", "inventory": "Inventory", "pos": "Point of Sale", "adminPanel": "Admin Panel", "branchManagement": "Branch Management", "userManagement": "User Management"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back, {{name}}! Here's what's happening at your coaching center.", "totalStudents": "Total Students", "totalTeachers": "Total Teachers", "activeBatches": "Active Batches", "totalCourses": "Total Courses", "monthlyRevenue": "Monthly Revenue", "pendingFees": "Pending Fees", "currentMonthEarnings": "Current month earnings", "outstandingPayments": "Outstanding payments", "recentPayments": "Recent Payments", "quickActions": "Quick Actions", "performanceOverview": "Performance Overview", "addStudent": "Add Student", "registerNewStudent": "Register new student", "recordPayment": "Record Payment", "addFeePayment": "Add fee payment", "markAttendance": "Mark Attendance", "takeClassAttendance": "Take class attendance", "createAssignment": "Create Assignment", "addNewAssignment": "Add new assignment", "averageStudentsPerBatch": "Average Students per Batch", "revenuePerStudent": "Revenue per Student", "pendingFeePercentage": "Pending Fee Percentage"}, "students": {"title": "Students Management", "description": "Manage all students in your coaching center", "addNew": "Add New Student", "searchPlaceholder": "Search by name or email...", "filterByStatus": "Filter by Status", "allStudents": "All Students", "suspended": "Suspended", "totalStudents": "Total Students", "student": "Student", "contact": "Contact", "enrollment": "Enrollment", "fees": "Fees", "batches": "batches", "since": "Since", "pending": "Pending", "suspend": "Suspend", "addNewStudent": "Add New Student", "fullName": "Full Name", "email": "Email", "phone": "Phone", "dateOfBirth": "Date of Birth", "address": "Address", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "parentName": "Parent Name", "parentPhone": "Parent Phone", "emergencyContact": "Emergency Contact", "addStudent": "Add Student"}, "auth": {"signIn": "Sign In", "signOut": "Sign out", "email": "Email Address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "signingIn": "Signing in...", "welcomeBack": "Welcome back", "enterCredentials": "Enter your credentials to access your account", "needHelp": "Need help?", "contactSupport": "Contact Support", "authenticationFailed": "Authentication Failed", "loginFailed": "<PERSON><PERSON> failed. Please try again."}, "theme": {"toggleTheme": "Toggle theme", "light": "Light", "dark": "Dark", "system": "System"}, "language": {"switchLanguage": "Switch language", "english": "English", "bengali": "বাংলা"}, "inventory": {"title": "Inventory Management", "description": "Description", "totalItems": "Total Items", "categories": "Categories", "lowStock": "Low Stock", "totalValue": "Total Value", "items": "Items", "vendors": "Vend<PERSON>", "purchaseOrders": "Purchase Orders", "wastage": "Wastage", "sales": "Sales", "pos": "Point of Sale", "addItem": "Add Item", "addVendor": "Add <PERSON>", "addCategory": "Add Category", "updateStock": "Update Stock", "itemDetails": "<PERSON><PERSON>", "category": "Category", "stock": "Stock", "unitPrice": "Unit Price", "actions": "Actions", "itemCode": "Item Code", "itemName": "Item Name", "unit": "Unit", "currentStock": "Current Stock", "minimumStock": "Minimum Stock", "reorderLevel": "Reorder Level", "costPrice": "Cost Price", "sellingPrice": "Selling <PERSON>", "barcode": "Barcode", "isSaleable": "Is Saleable", "allCategories": "All Categories", "showLowStockOnly": "Show Low Stock Only", "transactionType": "Transaction Type", "stockIn": "Stock In", "stockOut": "Stock Out", "adjustment": "Adjustment", "quantity": "Quantity", "referenceType": "Reference Type", "purchase": "Purchase", "sale": "Sale", "damage": "Damage", "return": "Return", "referenceId": "Reference ID", "transactionDate": "Transaction Date", "notes": "Notes"}, "vendors": {"title": "Vendor Management", "description": "Manage your suppliers and vendors", "vendorCode": "Vendor Code", "vendorName": "Vendor Name", "contactPerson": "Contact Person", "email": "Email", "phone": "Phone", "address": "Address", "paymentTerms": "Payment Terms", "creditLimit": "Credit Limit", "status": "Status", "active": "Active", "inactive": "Inactive", "blocked": "Blocked", "addVendor": "Add <PERSON>", "editVendor": "<PERSON>", "vendorDetails": "<PERSON><PERSON><PERSON>", "totalPurchases": "Total Purchases", "lastPurchase": "Last Purchase", "outstandingAmount": "Outstanding Amount"}, "purchaseOrders": {"title": "Purchase Order Management", "description": "Create and manage purchase orders", "poNumber": "PO Number", "vendor": "<PERSON><PERSON><PERSON>", "orderDate": "Order Date", "expectedDelivery": "Expected Delivery", "actualDelivery": "Actual Delivery", "subtotal": "Subtotal", "taxAmount": "Tax Amount", "discountAmount": "Discount Amount", "totalAmount": "Total Amount", "status": "Status", "draft": "Draft", "pending": "Pending", "approved": "Approved", "ordered": "Ordered", "partiallyReceived": "Partially Received", "received": "Received", "cancelled": "Cancelled", "createPO": "Create Purchase Order", "editPO": "Edit Purchase Order", "approvePO": "Approve Purchase Order", "markReceived": "<PERSON> as Received", "cancelPO": "Cancel Purchase Order", "poItems": "Purchase Order Items", "quantityOrdered": "Quantity Ordered", "quantityReceived": "Quantity Received", "addItem": "Add Item", "removeItem": "Remove Item", "approvedBy": "Approved By", "approvedAt": "Approved At"}, "wastage": {"title": "Wastage Management", "description": "Track and manage inventory wastage", "wastageDate": "Wastage Date", "reason": "Reason", "expired": "Expired", "damaged": "Damaged", "lost": "Lost", "theft": "Theft", "qualityIssue": "Quality Issue", "other": "Other", "unitCost": "Unit Cost", "totalCost": "Total Cost", "reportedBy": "Reported By", "approvedBy": "Approved By", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "recordWastage": "Record Wastage", "approveWastage": "Approve Wastage", "rejectWastage": "Reject <PERSON>tage", "wastageReport": "Wastage Report", "totalWastage": "Total Wastage", "wastageValue": "Wastage Value"}, "sales": {"title": "Sales Management", "description": "Manage sales and invoicing", "saleNumber": "Sale Number", "customer": "Customer", "student": "Student", "customerName": "Customer Name", "customerPhone": "Customer Phone", "saleDate": "Sale Date", "paymentMethod": "Payment Method", "cash": "Cash", "card": "Card", "mobileBanking": "Mobile Banking", "credit": "Credit", "paymentStatus": "Payment Status", "paid": "Paid", "partial": "Partial", "amountPaid": "Amount <PERSON>", "amountDue": "Amount Due", "dueDate": "Due Date", "cashier": "Cashier", "createSale": "Create Sale", "editSale": "Edit Sale", "printInvoice": "Print Invoice", "recordPayment": "Record Payment", "saleItems": "Sale Items", "dailySales": "Daily Sales", "monthlySales": "Monthly Sales", "totalSales": "Total Sales", "salesReport": "Sales Report"}, "pos": {"title": "Point of Sale", "description": "Quick sales and checkout", "searchItems": "Search Items", "scanBarcode": "Scan Barcode", "shoppingCart": "Shopping Cart", "addToCart": "Add to Cart", "removeFromCart": "Remove from Cart", "clearCart": "Clear Cart", "checkout": "Checkout", "selectCustomer": "Select Customer", "newCustomer": "New Customer", "applyDiscount": "Apply Discount", "discountPercent": "Discount Percent", "discountAmount": "Discount Amount", "calculateTax": "Calculate Tax", "taxPercent": "Tax Percent", "finalTotal": "Final Total", "processPayment": "Process Payment", "printReceipt": "Print Receipt", "completeSale": "Complete Sale", "itemNotFound": "Item Not Found", "insufficientStock": "Insufficient Stock", "cartEmpty": "Cart is Empty", "paymentRequired": "Payment Required"}, "branches": {"title": "Branch Management", "description": "Manage your coaching center branches", "branchCode": "Branch Code", "branchName": "Branch Name", "address": "Address", "phone": "Phone", "email": "Email", "manager": "Manager", "establishmentDate": "Establishment Date", "status": "Status", "active": "Active", "inactive": "Inactive", "addBranch": "Add Branch", "editBranch": "Edit Branch", "branchDetails": "Branch Details", "selectBranch": "Select Branch", "currentBranch": "Current Branch", "switchBranch": "Switch Branch", "allBranches": "All Branches", "branchStats": "Branch Statistics", "totalStudents": "Total Students", "totalTeachers": "Total Teachers", "totalRooms": "Total Rooms", "branchDashboard": "Branch Dashboard"}, "rooms": {"title": "Room Management", "description": "Manage branch rooms and spaces", "roomCode": "Room Code", "roomName": "Room Name", "roomType": "Room Type", "capacity": "Capacity", "equipment": "Equipment", "classroom": "Classroom", "lab": "Laboratory", "auditorium": "Auditorium", "library": "Library", "office": "Office", "other": "Other", "maintenance": "Maintenance", "addRoom": "Add Room", "editRoom": "Edit Room", "roomDetails": "Room Details", "availability": "Availability", "available": "Available", "occupied": "Occupied", "roomBooking": "Room Booking"}, "schedules": {"title": "Schedule Management", "description": "Manage course and room schedules", "weeklySchedule": "Weekly Schedule", "basicInformation": "Basic Information", "weeklyTimeTable": "Weekly Time Table", "dayOfWeek": "Day of Week", "startTime": "Start Time", "endTime": "End Time", "effectiveFrom": "Effective From", "effectiveTo": "Effective To", "selectRoom": "Select Room", "selectCourse": "Select Course", "selectBatch": "Select Batch", "selectTeacher": "Select Teacher", "primaryTeacher": "Primary Teacher", "dayTeacher": "Day Teacher", "optional": "Optional", "saturday": "Saturday", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "addSchedule": "Add Schedule", "editSchedule": "Edit Schedule", "createWeeklySchedule": "Create Weekly Schedule", "editWeeklySchedule": "Edit Weekly Schedule", "conflictDetected": "Conflict Detected", "roomConflict": "Room Conflict", "teacherConflict": "Teacher Conflict", "timeSlot": "Time Slot", "scheduleConflict": "Schedule Conflict", "noTimeSelected": "No time selected", "selectDaysAndTimes": "Select days and times", "endTimeMustBeAfterStart": "End time must be after start time", "atLeastOneDayRequired": "At least one day is required"}, "settings": {"title": "Settings", "description": "Manage coaching center settings", "basicInformation": "Basic Information", "invoiceSettings": "Invoice Settings", "coachingCenterName": "Coaching Center Name", "whatsappNumber": "WhatsApp Number", "facebookLink": "Facebook Link", "invoiceLogo": "Invoice Logo", "invoiceWatermark": "Invoice Watermark", "imageNote": "Images will be automatically converted to WebP format and compressed", "watermarkNote": "Watermark will appear transparent on invoices"}, "exams": {"title": "Exam Management", "description": "Create, manage and publish exam results", "createExam": "Create New <PERSON>am", "examDetails": "<PERSON><PERSON>", "examName": "Exam Name", "examType": "Exam Type", "subject": "Subject", "examDate": "Exam Date", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "room": "Room", "totalMarks": "Total Marks", "passingMarks": "Passing Marks", "instructions": "Instructions", "batches": "Batches", "supervisors": "Supervisors", "status": "Status", "participants": "Participants", "schedule": "Schedule", "marks": "Marks", "actions": "Actions", "view": "View", "results": "Results", "schedule_exam": "Schedule Exam", "enter_results": "Enter Results", "publish_results": "Publish Results", "types": {"midterm": "Midterm", "final": "Final", "quiz": "Quiz", "mock_test": "<PERSON><PERSON>", "assignment": "Assignment", "practical": "Practical"}, "statuses": {"draft": "Draft", "scheduled": "Scheduled", "in_progress": "In Progress", "completed": "Completed", "results_published": "Results Published", "cancelled": "Cancelled"}}, "transfers": {"title": "Transfer Management", "description": "Manage student and inventory transfers", "studentTransfer": "Student Transfer", "inventoryTransfer": "Inventory Transfer", "fromBranch": "From Branch", "toBranch": "To Branch", "transferDate": "Transfer Date", "reason": "Reason", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "requestTransfer": "Request Transfer", "approveTransfer": "Approve Transfer", "rejectTransfer": "Reject Transfer", "transferHistory": "Transfer History", "transferNumber": "Transfer Number", "inTransit": "In Transit", "received": "Received", "cancelled": "Cancelled", "quantityRequested": "Quantity Requested", "quantitySent": "Quantity <PERSON>", "quantityReceived": "Quantity Received", "transferValue": "Transfer Value"}, "userManagement": {"title": "User Management", "description": "Manage users and their roles within your coaching center", "addUser": "Add User", "editUser": "Edit User", "createUser": "Create a new user account", "updateUser": "Update user information and permissions", "totalUsers": "Total Users", "activeUsers": "Active Users", "teachers": "Teachers", "students": "Students", "fullName": "Full Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "role": "Role", "selectRole": "Select role", "password": "Password", "newPassword": "New Password (leave blank to keep current)", "confirmPassword": "Confirm Password", "enterFullName": "Enter full name", "enterEmailAddress": "Enter email address", "enterPhoneNumber": "Enter phone number", "enterPassword": "Enter password", "confirmPasswordPlaceholder": "Confirm password", "status": "Status", "active": "Active", "inactive": "Inactive", "lastLogin": "Last Login", "createdAt": "Created At", "actions": "Actions", "edit": "Edit", "delete": "Delete", "resetPassword": "Reset Password", "toggleStatus": "Toggle Status", "deleteConfirm": "Are you sure you want to delete this user?", "userCreated": "User created successfully", "userUpdated": "User updated successfully", "userDeleted": "User deleted successfully", "passwordReset": "Password reset successfully", "statusUpdated": "User status updated successfully", "emailExists": "Email address already exists", "passwordsNotMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 6 characters long", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "fillRequiredFields": "Please fill in all required fields", "passwordRequired": "Password is required for new users", "failedToFetch": "Failed to fetch users", "failedToSave": "Failed to save user", "failedToDelete": "Failed to delete user", "failedToUpdateStatus": "Failed to update user status", "roles": {"center_admin": "Center Admin", "teacher": "Teacher", "staff": "Staff", "student": "Student"}, "permissions": {"all": "All Permissions", "view_students": "View Students", "manage_attendance": "Manage Attendance", "view_courses": "View Courses", "manage_assignments": "Manage Assignments", "manage_fees": "Manage Fees", "view_reports": "View Reports", "view_own_data": "View Own Data", "submit_assignments": "Submit Assignments"}}}