import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Plus, Package, DollarSign, TrendingDown, TrendingUp, Calendar, Building } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface InventoryItem {
  id: string
  name: string
  category: string
  quantity: number
  unitPrice: number
  totalValue: number
  branchName?: string
  vendorName?: string
  description?: string
  status: string
  createdAt: string
}

interface Expense {
  id: string
  title: string
  description?: string
  amount: number
  expenseDate: string
  expenseTypeName?: string
  branchName?: string
  paymentMethod: string
  vendorName?: string
  receiptNumber?: string
  status: string
  createdAt: string
}

interface ExpenseType {
  id: string
  name: string
  description?: string
}

interface Branch {
  id: string
  branchName: string
  branchCode: string
}

const PAYMENT_METHODS = [
  { value: 'CASH', label: 'Cash' },
  { value: 'CARD', label: 'Card' },
  { value: 'BANK_TRANSFER', label: 'Bank Transfer' },
  { value: 'UPI', label: 'UPI' },
  { value: 'CHEQUE', label: 'Cheque' },
  { value: 'ONLINE', label: 'Online' }
]

const STATUS_COLORS = {
  'ACTIVE': 'bg-green-100 text-green-800',
  'PENDING': 'bg-yellow-100 text-yellow-800',
  'APPROVED': 'bg-blue-100 text-blue-800',
  'REJECTED': 'bg-red-100 text-red-800'
}

export default function AssetExpenseManagementPage() {
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([])
  const [branches, setBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddInventoryForm, setShowAddInventoryForm] = useState(false)
  const [showAddExpenseForm, setShowAddExpenseForm] = useState(false)
  
  const [inventoryFormData, setInventoryFormData] = useState({
    name: "",
    category: "",
    quantity: "",
    unitPrice: "",
    branchId: "",
    vendorId: "",
    description: ""
  })

  const [expenseFormData, setExpenseFormData] = useState({
    title: "",
    description: "",
    amount: "",
    expenseDate: "",
    expenseTypeId: "",
    branchId: "",
    paymentMethod: "CASH",
    vendorName: "",
    receiptNumber: ""
  })

  useEffect(() => {
    fetchInventory()
    fetchExpenses()
    fetchExpenseTypes()
    fetchBranches()
  }, [])

  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token')
    const subdomain = window.location.hostname.split('.')[0]
    return {
      'Authorization': `Bearer ${token}`,
      'x-tenant-slug': subdomain === 'localhost' ? 'abc' : subdomain,
      'Content-Type': 'application/json'
    }
  }

  const fetchInventory = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/inventory', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setInventory(data.data.inventory || [])
      }
    } catch (error) {
      console.error('Error fetching inventory:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchExpenses = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/expenses', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setExpenses(data.data.expenses || [])
      }
    } catch (error) {
      console.error('Error fetching expenses:', error)
    }
  }

  const fetchExpenseTypes = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/expense-types', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setExpenseTypes(data.data.expenseTypes || [])
      }
    } catch (error) {
      console.error('Error fetching expense types:', error)
    }
  }

  const fetchBranches = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/branches', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setBranches(data.data.branches || [])
      }
    } catch (error) {
      console.error('Error fetching branches:', error)
    }
  }

  const handleInventorySubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('http://localhost:3004/api/coaching/inventory', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          name: inventoryFormData.name,
          category: inventoryFormData.category,
          quantity: parseInt(inventoryFormData.quantity),
          unitPrice: parseFloat(inventoryFormData.unitPrice),
          branchId: inventoryFormData.branchId || undefined,
          vendorId: inventoryFormData.vendorId || undefined,
          description: inventoryFormData.description
        })
      })

      const data = await response.json()

      if (response.ok) {
        await fetchInventory()
        setShowAddInventoryForm(false)
        setInventoryFormData({
          name: "",
          category: "",
          quantity: "",
          unitPrice: "",
          branchId: "",
          vendorId: "",
          description: ""
        })
        toast.success('Inventory item added successfully')
      } else {
        toast.error(data.message || 'Failed to add inventory item')
      }
    } catch (error) {
      console.error('Error adding inventory item:', error)
      toast.error('Failed to add inventory item')
    } finally {
      setLoading(false)
    }
  }

  const handleExpenseSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('http://localhost:3004/api/coaching/expenses', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          title: expenseFormData.title,
          description: expenseFormData.description,
          amount: parseFloat(expenseFormData.amount),
          expenseDate: expenseFormData.expenseDate,
          expenseTypeId: expenseFormData.expenseTypeId,
          branchId: expenseFormData.branchId || undefined,
          paymentMethod: expenseFormData.paymentMethod,
          vendorName: expenseFormData.vendorName,
          receiptNumber: expenseFormData.receiptNumber
        })
      })

      const data = await response.json()

      if (response.ok) {
        await fetchExpenses()
        setShowAddExpenseForm(false)
        setExpenseFormData({
          title: "",
          description: "",
          amount: "",
          expenseDate: "",
          expenseTypeId: "",
          branchId: "",
          paymentMethod: "CASH",
          vendorName: "",
          receiptNumber: ""
        })
        toast.success('Expense created successfully')
      } else {
        toast.error(data.message || 'Failed to create expense')
      }
    } catch (error) {
      console.error('Error creating expense:', error)
      toast.error('Failed to create expense')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-BD', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const totalInventoryValue = inventory.reduce((sum, item) => sum + item.totalValue, 0)
  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)

  if (loading && inventory.length === 0 && expenses.length === 0) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <TableSkeleton />
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Asset & Expense Management</h1>
          <p className="text-muted-foreground">
            Manage inventory assets and track expenses
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Inventory Value</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalInventoryValue)}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalExpenses)}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inventory Items</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{inventory.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Expense Records</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{expenses.length}</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="inventory" className="space-y-4">
          <TabsList>
            <TabsTrigger value="inventory">Inventory Management</TabsTrigger>
            <TabsTrigger value="expenses">Expense Management</TabsTrigger>
          </TabsList>

          <TabsContent value="inventory" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Inventory Items</h2>
              <Button onClick={() => setShowAddInventoryForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </div>

            {/* Add Inventory Form */}
            {showAddInventoryForm && (
              <Card>
                <CardHeader>
                  <CardTitle>Add Inventory Item</CardTitle>
                  <CardDescription>Add a new item to your inventory</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleInventorySubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Item Name</Label>
                        <Input
                          id="name"
                          value={inventoryFormData.name}
                          onChange={(e) => setInventoryFormData({...inventoryFormData, name: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="category">Category</Label>
                        <Input
                          id="category"
                          value={inventoryFormData.category}
                          onChange={(e) => setInventoryFormData({...inventoryFormData, category: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="quantity">Quantity</Label>
                        <Input
                          id="quantity"
                          type="number"
                          value={inventoryFormData.quantity}
                          onChange={(e) => setInventoryFormData({...inventoryFormData, quantity: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="unitPrice">Unit Price (৳)</Label>
                        <Input
                          id="unitPrice"
                          type="number"
                          step="0.01"
                          value={inventoryFormData.unitPrice}
                          onChange={(e) => setInventoryFormData({...inventoryFormData, unitPrice: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="branchId">Branch</Label>
                        <Select value={inventoryFormData.branchId} onValueChange={(value) => setInventoryFormData({...inventoryFormData, branchId: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select branch" />
                          </SelectTrigger>
                          <SelectContent>
                            {branches.map((branch) => (
                              <SelectItem key={branch.id} value={branch.id}>
                                {branch.branchName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={inventoryFormData.description}
                        onChange={(e) => setInventoryFormData({...inventoryFormData, description: e.target.value})}
                        rows={3}
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button type="submit" disabled={loading}>
                        {loading ? 'Adding...' : 'Add Item'}
                      </Button>
                      <Button type="button" variant="outline" onClick={() => setShowAddInventoryForm(false)}>
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            {/* Inventory List */}
            <div className="grid gap-4">
              {inventory.map((item) => (
                <Card key={item.id}>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{item.name}</h3>
                        <p className="text-sm text-muted-foreground">{item.category}</p>
                        {item.description && (
                          <p className="text-sm text-muted-foreground mt-1">{item.description}</p>
                        )}
                      </div>
                      <Badge className={STATUS_COLORS[item.status as keyof typeof STATUS_COLORS]}>
                        {item.status}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Quantity</p>
                        <p className="font-medium">{item.quantity}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Unit Price</p>
                        <p className="font-medium">{formatCurrency(item.unitPrice)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Total Value</p>
                        <p className="font-medium">{formatCurrency(item.totalValue)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Branch</p>
                        <p className="font-medium">{item.branchName || 'All Branches'}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="expenses" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Expense Records</h2>
              <Button onClick={() => setShowAddExpenseForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Expense
              </Button>
            </div>

            {/* Add Expense Form */}
            {showAddExpenseForm && (
              <Card>
                <CardHeader>
                  <CardTitle>Add Expense</CardTitle>
                  <CardDescription>Record a new expense</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleExpenseSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Expense Title</Label>
                        <Input
                          id="title"
                          value={expenseFormData.title}
                          onChange={(e) => setExpenseFormData({...expenseFormData, title: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="amount">Amount (৳)</Label>
                        <Input
                          id="amount"
                          type="number"
                          step="0.01"
                          value={expenseFormData.amount}
                          onChange={(e) => setExpenseFormData({...expenseFormData, amount: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="expenseDate">Expense Date</Label>
                        <Input
                          id="expenseDate"
                          type="date"
                          value={expenseFormData.expenseDate}
                          onChange={(e) => setExpenseFormData({...expenseFormData, expenseDate: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="expenseTypeId">Expense Type</Label>
                        <Select value={expenseFormData.expenseTypeId} onValueChange={(value) => setExpenseFormData({...expenseFormData, expenseTypeId: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select expense type" />
                          </SelectTrigger>
                          <SelectContent>
                            {expenseTypes.map((type) => (
                              <SelectItem key={type.id} value={type.id}>
                                {type.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="paymentMethod">Payment Method</Label>
                        <Select value={expenseFormData.paymentMethod} onValueChange={(value) => setExpenseFormData({...expenseFormData, paymentMethod: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select payment method" />
                          </SelectTrigger>
                          <SelectContent>
                            {PAYMENT_METHODS.map((method) => (
                              <SelectItem key={method.value} value={method.value}>
                                {method.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="branchId">Branch</Label>
                        <Select value={expenseFormData.branchId} onValueChange={(value) => setExpenseFormData({...expenseFormData, branchId: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select branch" />
                          </SelectTrigger>
                          <SelectContent>
                            {branches.map((branch) => (
                              <SelectItem key={branch.id} value={branch.id}>
                                {branch.branchName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={expenseFormData.description}
                        onChange={(e) => setExpenseFormData({...expenseFormData, description: e.target.value})}
                        rows={3}
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button type="submit" disabled={loading}>
                        {loading ? 'Adding...' : 'Add Expense'}
                      </Button>
                      <Button type="button" variant="outline" onClick={() => setShowAddExpenseForm(false)}>
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            {/* Expenses List */}
            <div className="grid gap-4">
              {expenses.map((expense) => (
                <Card key={expense.id}>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{expense.title}</h3>
                        <p className="text-sm text-muted-foreground">{expense.description}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold">{formatCurrency(expense.amount)}</p>
                        <Badge className={STATUS_COLORS[expense.status as keyof typeof STATUS_COLORS]}>
                          {expense.status}
                        </Badge>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{formatDate(expense.expenseDate)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{expense.paymentMethod}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{expense.branchName || 'All Branches'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <TrendingDown className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{expense.expenseTypeName}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
