import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Search, Users, DollarSign, Calendar, Download, Edit, Trash2, CheckCircle, XCircle } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton, ListItemSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface Employee {
  id: number
  name: string
  email: string
  phone: string
  position: string
  department: string
  salary: number
  join_date: string
  status: 'active' | 'inactive'
  created_at: string
}

interface LeaveRequest {
  id: number
  employee_id: number
  employee_name: string
  leave_type: string
  start_date: string
  end_date: string
  reason: string
  status: 'pending' | 'approved' | 'rejected'
  applied_date: string
}

interface PayrollRecord {
  id: number
  employee_id: number
  employee_name: string
  month: string
  year: number
  basic_salary: number
  allowances: number
  deductions: number
  net_salary: number
  status: 'pending' | 'paid'
  created_at: string
}

export default function HRMPage() {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([])
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'employees' | 'leaves' | 'payroll'>('employees')
  const [searchTerm, setSearchTerm] = useState("")
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    position: "",
    department: "",
    salary: "",
    join_date: ""
  })

  useEffect(() => {
    fetchEmployees()
    fetchLeaveRequests()
    fetchPayrollRecords()
  }, [])

  const fetchEmployees = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/employees', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setEmployees(data.employees || [])
      }
    } catch (error) {
      console.error('Error fetching employees:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchLeaveRequests = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/leave-requests', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setLeaveRequests(data.leaveRequests || [])
      }
    } catch (error) {
      console.error('Error fetching leave requests:', error)
    }
  }

  const fetchPayrollRecords = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/payroll', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setPayrollRecords(data.payrollRecords || [])
      }
    } catch (error) {
      console.error('Error fetching payroll records:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const url = editingEmployee
        ? `http://localhost:3000/api/employees/${editingEmployee.id}`
        : 'http://localhost:3000/api/employees'

      const method = editingEmployee ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          salary: parseFloat(formData.salary)
        }),
        credentials: 'include'
      })

      const data = await response.json()

      if (response.ok) {
        await fetchEmployees()
        setShowAddForm(false)
        setEditingEmployee(null)
        setFormData({ name: "", email: "", phone: "", position: "", department: "", salary: "", join_date: "" })
      } else {
        toast.error(data.error || 'Operation failed')
      }
    } catch (error) {
      console.error('Error saving employee:', error)
      toast.error('Failed to save employee')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (employee: Employee) => {
    setEditingEmployee(employee)
    setFormData({
      name: employee.name,
      email: employee.email,
      phone: employee.phone,
      position: employee.position,
      department: employee.department,
      salary: employee.salary.toString(),
      join_date: employee.join_date
    })
    setShowAddForm(true)
  }

  const handleDelete = async (employeeId: number) => {
    if (!confirm('Are you sure you want to delete this employee?')) {
      return
    }

    try {
      const response = await fetch(`http://localhost:3000/api/employees/${employeeId}`, {
        method: 'DELETE',
        credentials: 'include'
      })

      if (response.ok) {
        await fetchEmployees()
      } else {
        const data = await response.json()
        toast.error(data.error || 'Failed to delete employee')
      }
    } catch (error) {
      console.error('Error deleting employee:', error)
      toast.error('Failed to delete employee')
    }
  }

  const handleLeaveAction = async (leaveId: number, action: 'approve' | 'reject') => {
    try {
      const response = await fetch(`http://localhost:3000/api/leave-requests/${leaveId}/${action}`, {
        method: 'POST',
        credentials: 'include'
      })

      if (response.ok) {
        await fetchLeaveRequests()
      } else {
        const data = await response.json()
        toast.error(data.error || `Failed to ${action} leave request`)
      }
    } catch (error) {
      console.error(`Error ${action}ing leave request:`, error)
      toast.error(`Failed to ${action} leave request`)
    }
  }

  const generatePayroll = async (month: string, year: number) => {
    try {
      const response = await fetch('http://localhost:3000/api/payroll/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ month, year }),
        credentials: 'include'
      })

      if (response.ok) {
        await fetchPayrollRecords()
        toast.success('Payroll generated successfully')
      } else {
        const data = await response.json()
        toast.error(data.error || 'Failed to generate payroll')
      }
    } catch (error) {
      console.error('Error generating payroll:', error)
      toast.error('Failed to generate payroll')
    }
  }

  const downloadPayslip = async (payrollId: number) => {
    try {
      const response = await fetch(`http://localhost:3000/api/payroll/${payrollId}/payslip`, {
        credentials: 'include'
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `payslip-${payrollId}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        toast.error('Failed to download payslip')
      }
    } catch (error) {
      console.error('Error downloading payslip:', error)
      toast.error('Failed to download payslip')
    }
  }

  const filteredEmployees = employees.filter(employee =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.position.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getHRMStats = () => {
    const totalEmployees = employees.length
    const activeEmployees = employees.filter(emp => emp.status === 'active').length
    const pendingLeaves = leaveRequests.filter(req => req.status === 'pending').length
    const totalPayroll = payrollRecords.reduce((sum, record) => sum + record.net_salary, 0)

    return {
      totalEmployees,
      activeEmployees,
      pendingLeaves,
      totalPayroll
    }
  }

  const stats = getHRMStats()

  return (
    <DashboardLayout userRole="center_admin" title="HRM & Payroll">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">HRM & Payroll</h1>
            <p className="text-muted-foreground">Manage employees, leaves, and payroll</p>
          </div>

          <Button onClick={() => {
            setShowAddForm(true)
            setEditingEmployee(null)
            setFormData({ name: "", email: "", phone: "", position: "", department: "", salary: "", join_date: "" })
          }}>
            <Plus className="mr-2 h-4 w-4" />
            Add Employee
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Total Employees</div>
                  <div className="text-lg font-semibold">{stats.totalEmployees}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Active</div>
                  <div className="text-lg font-semibold">{stats.activeEmployees}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-yellow-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Pending Leaves</div>
                  <div className="text-lg font-semibold">{stats.pendingLeaves}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-purple-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Total Payroll</div>
                  <div className="text-lg font-semibold">${stats.totalPayroll.toFixed(2)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
          <button
            onClick={() => setActiveTab('employees')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'employees'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Employees
          </button>
          <button
            onClick={() => setActiveTab('leaves')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'leaves'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Leave Requests
          </button>
          <button
            onClick={() => setActiveTab('payroll')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'payroll'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Payroll
          </button>
        </div>

        {/* Search and Filters */}
        {activeTab === 'employees' && (
          <Card>
            <CardContent className="p-4">
              <div className="flex gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search employees..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Button variant="outline">
                  Export List
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Add/Edit Employee Form */}
        {showAddForm && activeTab === 'employees' && (
          <Card>
            <CardHeader>
              <CardTitle>{editingEmployee ? 'Edit Employee' : 'Add Employee'}</CardTitle>
              <CardDescription>
                {editingEmployee ? 'Update employee information' : 'Enter employee details'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="John Doe"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      name="phone"
                      placeholder="+1234567890"
                      value={formData.phone}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="position">Position *</Label>
                    <Input
                      id="position"
                      name="position"
                      placeholder="Teacher"
                      value={formData.position}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="department">Department</Label>
                    <Input
                      id="department"
                      name="department"
                      placeholder="Mathematics"
                      value={formData.department}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="salary">Salary *</Label>
                    <Input
                      id="salary"
                      name="salary"
                      type="number"
                      step="0.01"
                      placeholder="50000"
                      value={formData.salary}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="join_date">Join Date *</Label>
                    <Input
                      id="join_date"
                      name="join_date"
                      type="date"
                      value={formData.join_date}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Saving...' : (editingEmployee ? 'Update Employee' : 'Add Employee')}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowAddForm(false)
                      setEditingEmployee(null)
                      setFormData({ name: "", email: "", phone: "", position: "", department: "", salary: "", join_date: "" })
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Content based on active tab */}
        {activeTab === 'employees' && (
          <>
            { loading ? (
          <TableSkeleton rows={6} />
        ) : filteredEmployees.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No employees found</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm ? 'No employees match your search.' : 'Get started by adding your first employee.'}
                  </p>
                  {!searchTerm && (
                    <Button onClick={() => setShowAddForm(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Employee
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4">
                {filteredEmployees.map((employee) => (
                  <Card key={employee.id} data-testid="employee-record">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-4 mb-4">
                            <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                              <Users className="text-white h-6 w-6" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-lg">{employee.name}</h3>
                              <p className="text-sm text-muted-foreground">{employee.position}</p>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                            <div className="flex items-center text-muted-foreground">
                              <span className="font-medium">Email:</span>
                              <span className="ml-2">{employee.email}</span>
                            </div>
                            <div className="flex items-center text-muted-foreground">
                              <span className="font-medium">Department:</span>
                              <span className="ml-2">{employee.department}</span>
                            </div>
                            <div className="flex items-center text-muted-foreground">
                              <DollarSign className="h-4 w-4 mr-1" />
                              ${employee.salary}
                            </div>
                            <div className="flex items-center text-muted-foreground">
                              <Calendar className="h-4 w-4 mr-1" />
                              {new Date(employee.join_date).toLocaleDateString()}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <div className={`text-xs px-2 py-1 rounded-full ${
                            employee.status === 'active'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          }`}>
                            {employee.status}
                          </div>

                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(employee)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>

                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(employee.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  )
}