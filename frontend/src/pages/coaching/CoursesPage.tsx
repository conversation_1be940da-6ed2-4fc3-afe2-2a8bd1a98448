import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useAuth } from '../../contexts/AuthContext'
import { useApi } from '../../hooks/useApi'
import AdminLayout from '../../components/AdminLayout'

interface Course {
  id: number
  name: string
  code: string
  description: string
  duration_months: number
  fee: number
  status: 'active' | 'inactive'
  total_batches: number
  enrolled_students: number
  created_at: string
}

interface Batch {
  id: number
  name: string
  course_id: number
  course_name: string
  teacher_id: number
  teacher_name: string
  start_date: string
  end_date: string
  schedule_days: string[]
  start_time: string
  end_time: string
  max_students: number
  enrolled_students: number
  status: 'active' | 'completed' | 'cancelled'
}

interface Teacher {
  id: number
  name: string
  email: string
}

export default function CoursesPage() {
  const { user } = useAuth()
  const { adminGet, adminPost, adminPut } = useApi()
  const [courses, setCourses] = useState<Course[]>([])
  const [batches, setBatches] = useState<Batch[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'courses' | 'batches'>('courses')
  const [showCourseModal, setShowCourseModal] = useState(false)
  const [showBatchModal, setShowBatchModal] = useState(false)
  const [courseFormData, setCourseFormData] = useState({
    name: '',
    code: '',
    description: '',
    duration_months: '',
    fee: ''
  })
  const [batchFormData, setBatchFormData] = useState({
    course_id: '',
    name: '',
    teacher_id: '',
    start_date: '',
    end_date: '',
    schedule_days: [] as string[],
    start_time: '',
    end_time: '',
    max_students: '30'
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      const [coursesRes, batchesRes, teachersRes] = await Promise.all([
        adminGet('/api/coaching/courses'),
        adminGet('/api/coaching/batches'),
        adminGet('/api/coaching/teachers')
      ])
      
      if (coursesRes.courses) setCourses(coursesRes.courses)
      if (batchesRes.batches) setBatches(batchesRes.batches)
      if (teachersRes.teachers) setTeachers(teachersRes.teachers)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error(`Failed to fetch data: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleCourseSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const submitData = {
        ...courseFormData,
        duration_months: courseFormData.duration_months ? parseInt(courseFormData.duration_months) : null,
        fee: courseFormData.fee ? parseFloat(courseFormData.fee) : null
      }

      const response = await adminPost('/api/coaching/courses', submitData)
      if (response.success) {
        toast.success('Course added successfully!')
        fetchData()
        resetCourseForm()
      }
    } catch (error) {
      console.error('Error saving course:', error)
      toast.error(`Failed to save course: ${error.message}`)
    }
  }

  const handleBatchSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const submitData = {
        ...batchFormData,
        course_id: parseInt(batchFormData.course_id),
        teacher_id: batchFormData.teacher_id ? parseInt(batchFormData.teacher_id) : null,
        max_students: parseInt(batchFormData.max_students)
      }

      const response = await adminPost('/api/coaching/batches', submitData)
      if (response.success) {
        toast.success('Batch created successfully!')
        fetchData()
        resetBatchForm()
      }
    } catch (error) {
      console.error('Error creating batch:', error)
      toast.error(`Failed to create batch: ${error.message}`)
    }
  }

  const resetCourseForm = () => {
    setCourseFormData({
      name: '',
      code: '',
      description: '',
      duration_months: '',
      fee: ''
    })
    setShowCourseModal(false)
  }

  const resetBatchForm = () => {
    setBatchFormData({
      course_id: '',
      name: '',
      teacher_id: '',
      start_date: '',
      end_date: '',
      schedule_days: [],
      start_time: '',
      end_time: '',
      max_students: '30'
    })
    setShowBatchModal(false)
  }

  const handleScheduleDayChange = (day: string, checked: boolean) => {
    setBatchFormData(prev => ({
      ...prev,
      schedule_days: checked 
        ? [...prev.schedule_days, day]
        : prev.schedule_days.filter(d => d !== day)
    }))
  }

  const weekDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-lg">Loading courses and batches...</div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="px-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Courses & Batches</h1>
            <p className="text-gray-600 mt-2">Manage courses and batches in your coaching center</p>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowCourseModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Add Course
            </button>
            <button
              onClick={() => setShowBatchModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
            >
              Create Batch
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('courses')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'courses'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Courses ({courses.length})
            </button>
            <button
              onClick={() => setActiveTab('batches')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'batches'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Batches ({batches.length})
            </button>
          </nav>
        </div>

        {/* Courses Tab */}
        {activeTab === 'courses' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {courses.map((course) => (
              <div key={course.id} className="bg-white rounded-lg shadow-md p-6 border">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-xl font-semibold">{course.name}</h3>
                    <p className="text-gray-600">{course.code}</p>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    course.status === 'active' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {course.status}
                  </span>
                </div>

                <p className="text-gray-600 text-sm mb-4">{course.description}</p>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Duration:</span>
                    <span className="text-sm">{course.duration_months} months</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Fee:</span>
                    <span className="text-sm">৳{course.fee?.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Batches:</span>
                    <span className="text-sm">{course.total_batches}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Students:</span>
                    <span className="text-sm">{course.enrolled_students}</span>
                  </div>
                </div>

                <div className="flex gap-2">
                  <button className="flex-1 bg-blue-100 text-blue-800 px-3 py-2 rounded text-sm hover:bg-blue-200">
                    Edit
                  </button>
                  <button className="bg-green-100 text-green-800 px-3 py-2 rounded text-sm hover:bg-green-200">
                    Create Batch
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Batches Tab */}
        {activeTab === 'batches' && (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Batch
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Course
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Teacher
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Schedule
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Students
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {batches.map((batch) => (
                    <tr key={batch.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{batch.name}</div>
                        <div className="text-sm text-gray-500">ID: {batch.id}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{batch.course_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{batch.teacher_name || 'Not assigned'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {batch.start_time} - {batch.end_time}
                        </div>
                        <div className="text-sm text-gray-500">
                          {JSON.parse(batch.schedule_days || '[]').join(', ')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {batch.enrolled_students}/{batch.max_students}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          batch.status === 'active' 
                            ? 'bg-green-100 text-green-800'
                            : batch.status === 'completed'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {batch.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <button className="text-blue-600 hover:text-blue-900">
                            Edit
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            Students
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Add Course Modal */}
        {showCourseModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
              <h2 className="text-2xl font-bold mb-4">Add New Course</h2>
              <form onSubmit={handleCourseSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Course Name *</label>
                    <input
                      type="text"
                      value={courseFormData.name}
                      onChange={(e) => setCourseFormData(prev => ({ ...prev, name: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Course Code</label>
                    <input
                      type="text"
                      value={courseFormData.code}
                      onChange={(e) => setCourseFormData(prev => ({ ...prev, code: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Description</label>
                  <textarea
                    value={courseFormData.description}
                    onChange={(e) => setCourseFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Duration (Months)</label>
                    <input
                      type="number"
                      value={courseFormData.duration_months}
                      onChange={(e) => setCourseFormData(prev => ({ ...prev, duration_months: e.target.value }))}
                      min="1"
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Fee (৳)</label>
                    <input
                      type="number"
                      value={courseFormData.fee}
                      onChange={(e) => setCourseFormData(prev => ({ ...prev, fee: e.target.value }))}
                      min="0"
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                  >
                    Add Course
                  </button>
                  <button
                    type="button"
                    onClick={resetCourseForm}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Create Batch Modal */}
        {showBatchModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
              <h2 className="text-2xl font-bold mb-4">Create New Batch</h2>
              <form onSubmit={handleBatchSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Course *</label>
                    <select
                      value={batchFormData.course_id}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, course_id: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    >
                      <option value="">Select Course</option>
                      {courses.map(course => (
                        <option key={course.id} value={course.id}>{course.name}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Batch Name *</label>
                    <input
                      type="text"
                      value={batchFormData.name}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, name: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Assign Teacher</label>
                  <select
                    value={batchFormData.teacher_id}
                    onChange={(e) => setBatchFormData(prev => ({ ...prev, teacher_id: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="">Select Teacher</option>
                    {teachers.map(teacher => (
                      <option key={teacher.id} value={teacher.id}>{teacher.name}</option>
                    ))}
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Start Date</label>
                    <input
                      type="date"
                      value={batchFormData.start_date}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, start_date: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">End Date</label>
                    <input
                      type="date"
                      value={batchFormData.end_date}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, end_date: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Schedule Days</label>
                  <div className="grid grid-cols-4 gap-2">
                    {weekDays.map(day => (
                      <label key={day} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={batchFormData.schedule_days.includes(day)}
                          onChange={(e) => handleScheduleDayChange(day, e.target.checked)}
                          className="mr-2"
                        />
                        <span className="text-sm capitalize">{day}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Start Time</label>
                    <input
                      type="time"
                      value={batchFormData.start_time}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, start_time: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">End Time</label>
                    <input
                      type="time"
                      value={batchFormData.end_time}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, end_time: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Max Students</label>
                    <input
                      type="number"
                      value={batchFormData.max_students}
                      onChange={(e) => setBatchFormData(prev => ({ ...prev, max_students: e.target.value }))}
                      min="1"
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    type="submit"
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
                  >
                    Create Batch
                  </button>
                  <button
                    type="button"
                    onClick={resetBatchForm}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
