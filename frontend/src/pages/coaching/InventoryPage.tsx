import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import { useApi } from '../../hooks/useApi'
import AdminLayout from '../../components/AdminLayout'
import { TableSkeleton } from '../../components/skeletons/DashboardSkeleton'

interface InventoryItem {
  id: number
  category_id: number
  item_code: string
  name: string
  description: string
  unit: string
  current_stock: number
  minimum_stock: number
  unit_price: number
  status: 'active' | 'inactive'
  category_name: string
  created_at: string
}

interface InventoryCategory {
  id: number
  name: string
  description: string
  status: 'active' | 'inactive'
  item_count: number
}

interface Vendor {
  id: number
  vendor_code: string
  name: string
  contact_person: string
  email: string
  phone: string
  address: string
  payment_terms: string
  credit_limit: number
  status: 'active' | 'inactive' | 'blocked'
  total_orders: number
  total_purchases: number
  last_purchase_date: string
}

interface PurchaseOrder {
  id: number
  po_number: string
  vendor_id: number
  vendor_name: string
  order_date: string
  expected_delivery_date: string
  actual_delivery_date: string
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  status: 'draft' | 'pending' | 'approved' | 'ordered' | 'partially_received' | 'received' | 'cancelled'
  approved_by_name: string
  created_by_name: string
}

interface WastageRecord {
  id: number
  item_id: number
  item_name: string
  item_code: string
  quantity: number
  unit_cost: number
  total_cost: number
  reason: 'expired' | 'damaged' | 'lost' | 'theft' | 'quality_issue' | 'other'
  description: string
  wastage_date: string
  reported_by_name: string
  approved_by_name: string
  status: 'pending' | 'approved' | 'rejected'
}

export default function InventoryPage() {
  const { user } = useAuth()
  const { t } = useLanguage()
  const { adminGet, adminPost, adminPut } = useApi()
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'items' | 'categories' | 'vendors' | 'purchase-orders' | 'wastage' | 'sales' | 'transactions'>('items')
  
  // Items state
  const [items, setItems] = useState<InventoryItem[]>([])
  const [categories, setCategories] = useState<InventoryCategory[]>([])
  const [vendors, setVendors] = useState<Vendor[]>([])
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([])
  const [wastageRecords, setWastageRecords] = useState<WastageRecord[]>([])

  const [showItemForm, setShowItemForm] = useState(false)
  const [showStockForm, setShowStockForm] = useState(false)
  const [showVendorForm, setShowVendorForm] = useState(false)
  const [showPOForm, setShowPOForm] = useState(false)
  const [showWastageForm, setShowWastageForm] = useState(false)

  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null)
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null)
  const [selectedPO, setSelectedPO] = useState<PurchaseOrder | null>(null)
  
  const [itemFormData, setItemFormData] = useState({
    category_id: '',
    item_code: '',
    name: '',
    description: '',
    unit: '',
    current_stock: '',
    minimum_stock: '',
    unit_price: ''
  })

  const [stockFormData, setStockFormData] = useState({
    transaction_type: 'in',
    quantity: '',
    unit_price: '',
    reference_type: 'purchase',
    reference_id: '',
    notes: '',
    transaction_date: new Date().toISOString().split('T')[0]
  })

  const [vendorFormData, setVendorFormData] = useState({
    vendor_code: '',
    name: '',
    contact_person: '',
    email: '',
    phone: '',
    address: '',
    payment_terms: '',
    credit_limit: '',
    notes: ''
  })

  const [poFormData, setPOFormData] = useState({
    po_number: '',
    vendor_id: '',
    order_date: new Date().toISOString().split('T')[0],
    expected_delivery_date: '',
    tax_amount: '',
    discount_amount: '',
    notes: '',
    items: [] as any[]
  })

  const [wastageFormData, setWastageFormData] = useState({
    item_id: '',
    quantity: '',
    unit_cost: '',
    reason: 'damaged',
    description: '',
    wastage_date: new Date().toISOString().split('T')[0]
  })

  const [categoryFilter, setCategoryFilter] = useState('all')
  const [lowStockFilter, setLowStockFilter] = useState(false)

  useEffect(() => {
    fetchData()
  }, [activeTab])

  const fetchData = async () => {
    try {
      setLoading(true)
      if (activeTab === 'items') {
        const [itemsRes, categoriesRes] = await Promise.all([
          adminGet('/api/coaching/inventory/items'),
          adminGet('/api/coaching/inventory/categories')
        ])
        setItems(itemsRes.items || [])
        setCategories(categoriesRes.categories || [])
      } else if (activeTab === 'categories') {
        const response = await adminGet('/api/coaching/inventory/categories')
        setCategories(response.categories || [])
      } else if (activeTab === 'vendors') {
        const response = await adminGet('/api/coaching/vendors')
        setVendors(response.vendors || [])
      } else if (activeTab === 'purchase-orders') {
        const response = await adminGet('/api/coaching/purchase-orders')
        setPurchaseOrders(response.purchaseOrders || [])
      } else if (activeTab === 'wastage') {
        const response = await adminGet('/api/coaching/wastage')
        setWastageRecords(response.wastageRecords || [])
      }
    } catch (error: any) {
      console.error('Error fetching data:', error)
      toast.error('Failed to fetch data')
    } finally {
      setLoading(false)
    }
  }

  const handleItemSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await adminPost('/api/coaching/inventory/items', {
        ...itemFormData,
        current_stock: parseInt(itemFormData.current_stock) || 0,
        minimum_stock: parseInt(itemFormData.minimum_stock) || 0,
        unit_price: parseFloat(itemFormData.unit_price) || 0
      })
      
      if (response.success) {
        toast.success('Inventory item added successfully!')
        setShowItemForm(false)
        setItemFormData({
          category_id: '',
          item_code: '',
          name: '',
          description: '',
          unit: '',
          current_stock: '',
          minimum_stock: '',
          unit_price: ''
        })
        fetchData()
      }
    } catch (error: any) {
      console.error('Error adding item:', error)
      toast.error(`Failed to add item: ${error.message}`)
    }
  }

  const handleStockUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedItem) return

    try {
      const response = await adminPut(`/api/coaching/inventory/items/${selectedItem.id}/stock`, {
        ...stockFormData,
        quantity: parseInt(stockFormData.quantity),
        unit_price: parseFloat(stockFormData.unit_price) || 0
      })
      
      if (response.success) {
        toast.success('Stock updated successfully!')
        setShowStockForm(false)
        setSelectedItem(null)
        setStockFormData({
          transaction_type: 'in',
          quantity: '',
          unit_price: '',
          reference_type: 'purchase',
          reference_id: '',
          notes: '',
          transaction_date: new Date().toISOString().split('T')[0]
        })
        fetchData()
      }
    } catch (error: any) {
      console.error('Error updating stock:', error)
      toast.error(`Failed to update stock: ${error.message}`)
    }
  }

  const handleVendorSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await adminPost('/api/coaching/vendors', {
        ...vendorFormData,
        credit_limit: parseFloat(vendorFormData.credit_limit) || 0
      })

      if (response.success) {
        toast.success(t('vendors.addVendor') + ' successful!')
        setShowVendorForm(false)
        setVendorFormData({
          vendor_code: '',
          name: '',
          contact_person: '',
          email: '',
          phone: '',
          address: '',
          payment_terms: '',
          credit_limit: '',
          notes: ''
        })
        fetchData()
      }
    } catch (error: any) {
      console.error('Error adding vendor:', error)
      toast.error(`Failed to add vendor: ${error.message}`)
    }
  }

  const handlePOSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await adminPost('/api/coaching/purchase-orders', {
        ...poFormData,
        tax_amount: parseFloat(poFormData.tax_amount) || 0,
        discount_amount: parseFloat(poFormData.discount_amount) || 0
      })

      if (response.success) {
        toast.success(t('purchaseOrders.createPO') + ' successful!')
        setShowPOForm(false)
        setPOFormData({
          po_number: '',
          vendor_id: '',
          order_date: new Date().toISOString().split('T')[0],
          expected_delivery_date: '',
          tax_amount: '',
          discount_amount: '',
          notes: '',
          items: []
        })
        fetchData()
      }
    } catch (error: any) {
      console.error('Error creating purchase order:', error)
      toast.error(`Failed to create purchase order: ${error.message}`)
    }
  }

  const handleWastageSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await adminPost('/api/coaching/wastage', {
        ...wastageFormData,
        quantity: parseInt(wastageFormData.quantity),
        unit_cost: parseFloat(wastageFormData.unit_cost)
      })

      if (response.success) {
        toast.success(t('wastage.recordWastage') + ' successful!')
        setShowWastageForm(false)
        setWastageFormData({
          item_id: '',
          quantity: '',
          unit_cost: '',
          reason: 'damaged',
          description: '',
          wastage_date: new Date().toISOString().split('T')[0]
        })
        fetchData()
      }
    } catch (error: any) {
      console.error('Error recording wastage:', error)
      toast.error(`Failed to record wastage: ${error.message}`)
    }
  }

  const updatePOStatus = async (poId: number, status: string) => {
    try {
      const response = await adminPut(`/api/coaching/purchase-orders/${poId}/status`, {
        status,
        notes: `Status updated to ${status}`
      })

      if (response.success) {
        toast.success(`Purchase order ${status} successfully!`)
        fetchData()
      }
    } catch (error: any) {
      console.error('Error updating PO status:', error)
      toast.error(`Failed to update PO status: ${error.message}`)
    }
  }

  const updateWastageStatus = async (wastageId: number, status: string) => {
    try {
      const response = await adminPut(`/api/coaching/wastage/${wastageId}/status`, {
        status,
        notes: `Wastage ${status}`
      })

      if (response.success) {
        toast.success(`Wastage ${status} successfully!`)
        fetchData()
      }
    } catch (error: any) {
      console.error('Error updating wastage status:', error)
      toast.error(`Failed to update wastage status: ${error.message}`)
    }
  }

  const openStockForm = (item: InventoryItem) => {
    setSelectedItem(item)
    setStockFormData({
      ...stockFormData,
      unit_price: item.unit_price.toString()
    })
    setShowStockForm(true)
  }

  const filteredItems = items.filter(item => {
    if (categoryFilter !== 'all' && item.category_id.toString() !== categoryFilter) return false
    if (lowStockFilter && item.current_stock > item.minimum_stock) return false
    return true
  })

  const lowStockItems = items.filter(item => item.current_stock <= item.minimum_stock)

  return (
    <AdminLayout>
      <div className="px-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Inventory Management</h1>
          <p className="text-gray-600 mt-2">Manage your coaching center inventory and stock</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <span className="text-2xl">📦</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{items.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Categories</p>
                <p className="text-2xl font-bold text-gray-900">{categories.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
                <span className="text-2xl">⚠️</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Low Stock</p>
                <p className="text-2xl font-bold text-gray-900">{lowStockItems.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <span className="text-2xl">💰</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  ৳{items.reduce((sum, item) => sum + (item.current_stock * item.unit_price), 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <nav className="flex space-x-4 mb-6 overflow-x-auto">
          <button
            onClick={() => setActiveTab('items')}
            className={`py-2 px-3 border-b-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'items'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('inventory.items')} ({items.length})
          </button>
          <button
            onClick={() => setActiveTab('categories')}
            className={`py-2 px-3 border-b-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'categories'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('inventory.categories')} ({categories.length})
          </button>
          <button
            onClick={() => setActiveTab('vendors')}
            className={`py-2 px-3 border-b-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'vendors'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('inventory.vendors')}
          </button>
          <button
            onClick={() => setActiveTab('purchase-orders')}
            className={`py-2 px-3 border-b-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'purchase-orders'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('inventory.purchaseOrders')}
          </button>
          <button
            onClick={() => setActiveTab('wastage')}
            className={`py-2 px-3 border-b-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'wastage'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('inventory.wastage')}
          </button>
          <button
            onClick={() => setActiveTab('sales')}
            className={`py-2 px-3 border-b-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'sales'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('inventory.sales')}
          </button>
          <button
            onClick={() => setActiveTab('transactions')}
            className={`py-2 px-3 border-b-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'transactions'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('navigation.transactions')}
          </button>
        </nav>

        {/* Items Tab */}
        {activeTab === 'items' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">Inventory Items</h2>
                <p className="text-gray-600">Manage your inventory items and stock levels</p>
              </div>
              <button
                onClick={() => setShowItemForm(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                Add Item
              </button>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <div className="flex flex-wrap gap-4 items-center">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2"
                  >
                    <option value="all">All Categories</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="lowStock"
                    checked={lowStockFilter}
                    onChange={(e) => setLowStockFilter(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="lowStock" className="text-sm font-medium text-gray-700">
                    Show Low Stock Only
                  </label>
                </div>
              </div>
            </div>

            {loading ? (
              <TableSkeleton rows={6} />
            ) : filteredItems.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No items found</p>
              </div>
            ) : (
              <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Item Details
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Stock
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Value
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredItems.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{item.name}</div>
                            <div className="text-sm text-gray-500">Code: {item.item_code}</div>
                            <div className="text-sm text-gray-500">{item.description}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{item.category_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {item.current_stock} {item.unit}
                          </div>
                          <div className={`text-sm ${
                            item.current_stock <= item.minimum_stock 
                              ? 'text-red-600' 
                              : 'text-gray-500'
                          }`}>
                            Min: {item.minimum_stock} {item.unit}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">৳{item.unit_price.toLocaleString()}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            ৳{(item.current_stock * item.unit_price).toLocaleString()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => openStockForm(item)}
                              className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                            >
                              Update Stock
                            </button>
                            {item.current_stock <= item.minimum_stock && (
                              <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                Low Stock
                              </span>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Categories Tab */}
        {activeTab === 'categories' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">Inventory Categories</h2>
                <p className="text-gray-600">Manage your inventory categories</p>
              </div>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                Add Category
              </button>
            </div>

            {loading ? (
              <TableSkeleton rows={4} />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {categories.map((category) => (
                  <div key={category.id} className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">{category.name}</h3>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        category.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {category.status}
                      </span>
                    </div>
                    <p className="text-gray-600 text-sm mb-4">{category.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{category.item_count} items</span>
                      <button className="text-blue-600 hover:text-blue-800 text-sm">
                        Edit
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Vendors Tab */}
        {activeTab === 'vendors' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">{t('vendors.title')}</h2>
                <p className="text-gray-600">{t('vendors.description')}</p>
              </div>
              <button
                onClick={() => setShowVendorForm(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {t('vendors.addVendor')}
              </button>
            </div>

            {loading ? (
              <TableSkeleton rows={6} />
            ) : vendors.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No vendors found</p>
              </div>
            ) : (
              <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('vendors.vendorDetails')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('vendors.contactPerson')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('vendors.paymentTerms')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('vendors.totalPurchases')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('vendors.status')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('inventory.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {vendors.map((vendor) => (
                      <tr key={vendor.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{vendor.name}</div>
                            <div className="text-sm text-gray-500">Code: {vendor.vendor_code}</div>
                            <div className="text-sm text-gray-500">{vendor.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{vendor.contact_person}</div>
                          <div className="text-sm text-gray-500">{vendor.phone}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{vendor.payment_terms}</div>
                          <div className="text-sm text-gray-500">Credit: ৳{vendor.credit_limit?.toLocaleString()}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">৳{vendor.total_purchases?.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{vendor.total_orders} orders</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            vendor.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : vendor.status === 'inactive'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {t(`vendors.${vendor.status}`)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => {
                              setSelectedVendor(vendor)
                              setVendorFormData({
                                vendor_code: vendor.vendor_code,
                                name: vendor.name,
                                contact_person: vendor.contact_person,
                                email: vendor.email,
                                phone: vendor.phone,
                                address: vendor.address,
                                payment_terms: vendor.payment_terms,
                                credit_limit: vendor.credit_limit.toString(),
                                notes: ''
                              })
                              setShowVendorForm(true)
                            }}
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            {t('common.edit')}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Purchase Orders Tab */}
        {activeTab === 'purchase-orders' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">{t('purchaseOrders.title')}</h2>
                <p className="text-gray-600">{t('purchaseOrders.description')}</p>
              </div>
              <button
                onClick={() => setShowPOForm(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {t('purchaseOrders.createPO')}
              </button>
            </div>

            {loading ? (
              <TableSkeleton rows={6} />
            ) : purchaseOrders.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No purchase orders found</p>
              </div>
            ) : (
              <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('purchaseOrders.poNumber')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('purchaseOrders.vendor')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('purchaseOrders.orderDate')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('purchaseOrders.totalAmount')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('purchaseOrders.status')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('inventory.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {purchaseOrders.map((po) => (
                      <tr key={po.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{po.po_number}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{po.vendor_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{new Date(po.order_date).toLocaleDateString()}</div>
                          {po.expected_delivery_date && (
                            <div className="text-sm text-gray-500">
                              Expected: {new Date(po.expected_delivery_date).toLocaleDateString()}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">৳{po.total_amount?.toLocaleString()}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            po.status === 'received'
                              ? 'bg-green-100 text-green-800'
                              : po.status === 'approved'
                              ? 'bg-blue-100 text-blue-800'
                              : po.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {t(`purchaseOrders.${po.status}`)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex space-x-2">
                            {po.status === 'pending' && (
                              <button
                                onClick={() => updatePOStatus(po.id, 'approved')}
                                className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                              >
                                {t('purchaseOrders.approvePO')}
                              </button>
                            )}
                            {po.status === 'approved' && (
                              <button
                                onClick={() => updatePOStatus(po.id, 'received')}
                                className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                              >
                                {t('purchaseOrders.markReceived')}
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Wastage Tab */}
        {activeTab === 'wastage' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">{t('wastage.title')}</h2>
                <p className="text-gray-600">{t('wastage.description')}</p>
              </div>
              <button
                onClick={() => setShowWastageForm(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {t('wastage.recordWastage')}
              </button>
            </div>

            {loading ? (
              <TableSkeleton rows={6} />
            ) : wastageRecords.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No wastage records found</p>
              </div>
            ) : (
              <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('inventory.itemDetails')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('wastage.reason')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('inventory.quantity')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('wastage.totalCost')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('wastage.status')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('inventory.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {wastageRecords.map((wastage) => (
                      <tr key={wastage.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{wastage.item_name}</div>
                            <div className="text-sm text-gray-500">Code: {wastage.item_code}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 capitalize">{t(`wastage.${wastage.reason}`)}</div>
                          <div className="text-sm text-gray-500">{wastage.description}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{wastage.quantity}</div>
                          <div className="text-sm text-gray-500">@ ৳{wastage.unit_cost}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">৳{wastage.total_cost?.toLocaleString()}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            wastage.status === 'approved'
                              ? 'bg-green-100 text-green-800'
                              : wastage.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {t(`wastage.${wastage.status}`)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {wastage.status === 'pending' && (
                            <div className="flex space-x-2">
                              <button
                                onClick={() => updateWastageStatus(wastage.id, 'approved')}
                                className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                              >
                                {t('wastage.approveWastage')}
                              </button>
                              <button
                                onClick={() => updateWastageStatus(wastage.id, 'rejected')}
                                className="text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700"
                              >
                                {t('wastage.rejectWastage')}
                              </button>
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Sales Tab */}
        {activeTab === 'sales' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">{t('sales.title')}</h2>
                <p className="text-gray-600">{t('sales.description')}</p>
              </div>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                {t('sales.createSale')}
              </button>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6">
              <p className="text-gray-500 text-center">Sales management features coming soon...</p>
            </div>
          </div>
        )}

        {/* Transactions Tab */}
        {activeTab === 'transactions' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">Stock Transactions</h2>
                <p className="text-gray-600">View all inventory transactions</p>
              </div>
            </div>
            
            <div className="bg-white shadow-md rounded-lg p-6">
              <p className="text-gray-500 text-center">Transaction history coming soon...</p>
            </div>
          </div>
        )}
      </div>

      {/* Add Item Form Modal */}
      {showItemForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">Add New Item</h3>
            <form onSubmit={handleItemSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    value={itemFormData.category_id}
                    onChange={(e) => setItemFormData({...itemFormData, category_id: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    required
                  >
                    <option value="">Select Category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Item Code</label>
                  <input
                    type="text"
                    value={itemFormData.item_code}
                    onChange={(e) => setItemFormData({...itemFormData, item_code: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Item Name</label>
                <input
                  type="text"
                  value={itemFormData.name}
                  onChange={(e) => setItemFormData({...itemFormData, name: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={itemFormData.description}
                  onChange={(e) => setItemFormData({...itemFormData, description: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
                  <input
                    type="text"
                    value={itemFormData.unit}
                    onChange={(e) => setItemFormData({...itemFormData, unit: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    placeholder="e.g., Piece, Kg, Liter"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Current Stock</label>
                  <input
                    type="number"
                    value={itemFormData.current_stock}
                    onChange={(e) => setItemFormData({...itemFormData, current_stock: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Minimum Stock</label>
                  <input
                    type="number"
                    value={itemFormData.minimum_stock}
                    onChange={(e) => setItemFormData({...itemFormData, minimum_stock: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Unit Price (৳)</label>
                <input
                  type="number"
                  step="0.01"
                  value={itemFormData.unit_price}
                  onChange={(e) => setItemFormData({...itemFormData, unit_price: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>

              <div className="flex justify-end space-x-4 pt-4">
                <button
                  type="button"
                  onClick={() => setShowItemForm(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Add Item
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Stock Update Form Modal */}
      {showStockForm && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <h3 className="text-lg font-semibold mb-4">Update Stock - {selectedItem.name}</h3>
            <form onSubmit={handleStockUpdate} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Transaction Type</label>
                <select
                  value={stockFormData.transaction_type}
                  onChange={(e) => setStockFormData({...stockFormData, transaction_type: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  required
                >
                  <option value="in">Stock In</option>
                  <option value="out">Stock Out</option>
                  <option value="adjustment">Adjustment</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quantity ({selectedItem.unit})
                </label>
                <input
                  type="number"
                  value={stockFormData.quantity}
                  onChange={(e) => setStockFormData({...stockFormData, quantity: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  required
                />
                <p className="text-sm text-gray-500 mt-1">
                  Current stock: {selectedItem.current_stock} {selectedItem.unit}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Unit Price (৳)</label>
                <input
                  type="number"
                  step="0.01"
                  value={stockFormData.unit_price}
                  onChange={(e) => setStockFormData({...stockFormData, unit_price: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Reference Type</label>
                <select
                  value={stockFormData.reference_type}
                  onChange={(e) => setStockFormData({...stockFormData, reference_type: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  required
                >
                  <option value="purchase">Purchase</option>
                  <option value="sale">Sale</option>
                  <option value="adjustment">Adjustment</option>
                  <option value="damage">Damage</option>
                  <option value="return">Return</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Reference ID</label>
                <input
                  type="text"
                  value={stockFormData.reference_id}
                  onChange={(e) => setStockFormData({...stockFormData, reference_id: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  placeholder="Invoice/Order number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Transaction Date</label>
                <input
                  type="date"
                  value={stockFormData.transaction_date}
                  onChange={(e) => setStockFormData({...stockFormData, transaction_date: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                <textarea
                  value={stockFormData.notes}
                  onChange={(e) => setStockFormData({...stockFormData, notes: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  rows={2}
                />
              </div>

              <div className="flex justify-end space-x-4 pt-4">
                <button
                  type="button"
                  onClick={() => setShowStockForm(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Update Stock
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Vendor Form Modal */}
      {showVendorForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold dark:text-white">
                {selectedVendor ? t('vendors.editVendor') : t('vendors.addVendor')}
              </h3>
              <button
                onClick={() => {
                  setShowVendorForm(false)
                  setSelectedVendor(null)
                }}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleVendorSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vendors.vendorCode')} *
                </label>
                <input
                  type="text"
                  required
                  value={vendorFormData.vendor_code}
                  onChange={(e) => setVendorFormData({...vendorFormData, vendor_code: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vendors.vendorName')} *
                </label>
                <input
                  type="text"
                  required
                  value={vendorFormData.name}
                  onChange={(e) => setVendorFormData({...vendorFormData, name: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vendors.contactPerson')}
                </label>
                <input
                  type="text"
                  value={vendorFormData.contact_person}
                  onChange={(e) => setVendorFormData({...vendorFormData, contact_person: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vendors.email')}
                </label>
                <input
                  type="email"
                  value={vendorFormData.email}
                  onChange={(e) => setVendorFormData({...vendorFormData, email: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vendors.phone')}
                </label>
                <input
                  type="text"
                  value={vendorFormData.phone}
                  onChange={(e) => setVendorFormData({...vendorFormData, phone: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vendors.address')}
                </label>
                <textarea
                  value={vendorFormData.address}
                  onChange={(e) => setVendorFormData({...vendorFormData, address: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vendors.paymentTerms')}
                </label>
                <input
                  type="text"
                  value={vendorFormData.payment_terms}
                  onChange={(e) => setVendorFormData({...vendorFormData, payment_terms: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  placeholder="e.g., Net 30"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('vendors.creditLimit')}
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={vendorFormData.credit_limit}
                  onChange={(e) => setVendorFormData({...vendorFormData, credit_limit: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('inventory.notes')}
                </label>
                <textarea
                  value={vendorFormData.notes}
                  onChange={(e) => setVendorFormData({...vendorFormData, notes: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  rows={3}
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                >
                  {selectedVendor ? t('common.save') : t('common.add')}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowVendorForm(false)
                    setSelectedVendor(null)
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
                >
                  {t('common.cancel')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Purchase Order Form Modal */}
      {showPOForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold dark:text-white">{t('purchaseOrders.createPO')}</h3>
              <button
                onClick={() => setShowPOForm(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handlePOSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('purchaseOrders.poNumber')} *
                  </label>
                  <input
                    type="text"
                    required
                    value={poFormData.po_number}
                    onChange={(e) => setPOFormData({...poFormData, po_number: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                    placeholder="*********"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('purchaseOrders.vendor')} *
                  </label>
                  <select
                    required
                    value={poFormData.vendor_id}
                    onChange={(e) => setPOFormData({...poFormData, vendor_id: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select Vendor</option>
                    {vendors.map((vendor) => (
                      <option key={vendor.id} value={vendor.id}>
                        {vendor.name} ({vendor.vendor_code})
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('purchaseOrders.orderDate')} *
                  </label>
                  <input
                    type="date"
                    required
                    value={poFormData.order_date}
                    onChange={(e) => setPOFormData({...poFormData, order_date: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('purchaseOrders.expectedDelivery')}
                  </label>
                  <input
                    type="date"
                    value={poFormData.expected_delivery_date}
                    onChange={(e) => setPOFormData({...poFormData, expected_delivery_date: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('purchaseOrders.taxAmount')}
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={poFormData.tax_amount}
                    onChange={(e) => setPOFormData({...poFormData, tax_amount: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('purchaseOrders.discountAmount')}
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={poFormData.discount_amount}
                    onChange={(e) => setPOFormData({...poFormData, discount_amount: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('inventory.notes')}
                </label>
                <textarea
                  value={poFormData.notes}
                  onChange={(e) => setPOFormData({...poFormData, notes: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  rows={3}
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                >
                  {t('purchaseOrders.createPO')}
                </button>
                <button
                  type="button"
                  onClick={() => setShowPOForm(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
                >
                  {t('common.cancel')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Wastage Form Modal */}
      {showWastageForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold dark:text-white">{t('wastage.recordWastage')}</h3>
              <button
                onClick={() => setShowWastageForm(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleWastageSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('inventory.itemName')} *
                </label>
                <select
                  required
                  value={wastageFormData.item_id}
                  onChange={(e) => setWastageFormData({...wastageFormData, item_id: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select Item</option>
                  {items.map((item) => (
                    <option key={item.id} value={item.id}>
                      {item.name} ({item.item_code}) - Stock: {item.current_stock}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('inventory.quantity')} *
                </label>
                <input
                  type="number"
                  required
                  min="1"
                  value={wastageFormData.quantity}
                  onChange={(e) => setWastageFormData({...wastageFormData, quantity: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('wastage.unitCost')} *
                </label>
                <input
                  type="number"
                  required
                  min="0"
                  step="0.01"
                  value={wastageFormData.unit_cost}
                  onChange={(e) => setWastageFormData({...wastageFormData, unit_cost: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('wastage.reason')} *
                </label>
                <select
                  required
                  value={wastageFormData.reason}
                  onChange={(e) => setWastageFormData({...wastageFormData, reason: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                >
                  <option value="expired">{t('wastage.expired')}</option>
                  <option value="damaged">{t('wastage.damaged')}</option>
                  <option value="lost">{t('wastage.lost')}</option>
                  <option value="theft">{t('wastage.theft')}</option>
                  <option value="quality_issue">{t('wastage.qualityIssue')}</option>
                  <option value="other">{t('wastage.other')}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('inventory.description')}
                </label>
                <textarea
                  value={wastageFormData.description}
                  onChange={(e) => setWastageFormData({...wastageFormData, description: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  rows={3}
                  placeholder="Describe the wastage reason..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('wastage.wastageDate')} *
                </label>
                <input
                  type="date"
                  required
                  value={wastageFormData.wastage_date}
                  onChange={(e) => setWastageFormData({...wastageFormData, wastage_date: e.target.value})}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                >
                  {t('wastage.recordWastage')}
                </button>
                <button
                  type="button"
                  onClick={() => setShowWastageForm(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
                >
                  {t('common.cancel')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </AdminLayout>
  )
}
