import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import AdminLayout from '../../components/AdminLayout'
import { adminGet } from '../../utils/adminApi'

interface Invoice {
  id: number
  invoice_number: string
  student_name: string
  student_email: string
  student_phone: string
  student_address: string
  class_name: string
  class_section: string
  course_name: string
  course_code: string
  batch_name: string
  enrollment_type: 'monthly' | 'one_time'
  amount: number
  discount_amount: number
  tax_amount: number
  total_amount: number
  due_date: string
  payment_status: string
  payment_date: string
  payment_method: string
  notes: string
  created_at: string
}

interface CenterSettings {
  coaching_center_name: string
  address: string
  phone: string
  email: string
  invoice_logo: string
  invoice_watermark: string
}

export default function InvoicePage() {
  const { invoiceNumber } = useParams<{ invoiceNumber: string }>()
  const { t } = useTranslation()
  
  const [invoice, setInvoice] = useState<Invoice | null>(null)
  const [settings, setSettings] = useState<CenterSettings | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (invoiceNumber) {
      fetchInvoiceData()
    }
  }, [invoiceNumber])

  const fetchInvoiceData = async () => {
    try {
      setLoading(true)
      
      // Find invoice by invoice number
      const invoicesResponse = await adminGet('/api/coaching/invoices')
      const foundInvoice = invoicesResponse.invoices.find(
        (inv: any) => inv.invoice_number === invoiceNumber
      )
      
      if (!foundInvoice) {
        toast.error('Invoice not found')
        return
      }
      
      // Get detailed invoice data
      const invoiceResponse = await adminGet(`/api/coaching/invoices/${foundInvoice.id}`)
      setInvoice(invoiceResponse.invoice)
      
      // Get center settings
      const settingsResponse = await adminGet('/api/coaching/settings')
      setSettings(settingsResponse.settings)
      
    } catch (error: any) {
      console.error('Error fetching invoice:', error)
      toast.error('Failed to load invoice')
    } finally {
      setLoading(false)
    }
  }

  const handlePrint = () => {
    window.print()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB')
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Loading invoice...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (!invoice) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400">Invoice not found</p>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="max-w-4xl mx-auto">
        {/* Print Button */}
        <div className="mb-6 print:hidden">
          <button
            onClick={handlePrint}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            🖨️ Print Invoice
          </button>
        </div>

        {/* Invoice */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden print:shadow-none print:rounded-none">
          {/* Watermark */}
          {settings?.invoice_watermark && (
            <div 
              className="absolute inset-0 flex items-center justify-center pointer-events-none z-0 opacity-10"
              style={{
                backgroundImage: `url(${settings.invoice_watermark})`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
                backgroundSize: '300px'
              }}
            />
          )}
          
          <div className="relative z-10 p-8">
            {/* Header */}
            <div className="flex justify-between items-start mb-8">
              <div className="flex items-center">
                {settings?.invoice_logo && (
                  <img
                    src={settings.invoice_logo}
                    alt="Logo"
                    className="h-16 w-auto mr-4"
                  />
                )}
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {settings?.coaching_center_name || 'Coaching Center'}
                  </h1>
                  <p className="text-gray-600">{settings?.address}</p>
                  <p className="text-gray-600">
                    {settings?.phone} • {settings?.email}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <h2 className="text-xl font-bold text-gray-900">INVOICE</h2>
                <p className="text-gray-600">#{invoice.invoice_number}</p>
                <p className="text-gray-600">Date: {formatDate(invoice.created_at)}</p>
                <p className="text-gray-600">Due: {formatDate(invoice.due_date)}</p>
              </div>
            </div>

            {/* Student Information */}
            <div className="grid grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Bill To:</h3>
                <div className="text-gray-700">
                  <p className="font-medium">{invoice.student_name}</p>
                  <p>{invoice.student_email}</p>
                  <p>{invoice.student_phone}</p>
                  <p>{invoice.student_address}</p>
                  {(invoice.class_name || invoice.class_section) && (
                    <p className="text-blue-600 font-medium">
                      {invoice.class_name} {invoice.class_section && `- ${invoice.class_section}`}
                    </p>
                  )}
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Course Details:</h3>
                <div className="text-gray-700">
                  <p className="font-medium">{invoice.course_name}</p>
                  <p>Code: {invoice.course_code}</p>
                  {invoice.batch_name && <p>Batch: {invoice.batch_name}</p>}
                  <p>Type: {invoice.enrollment_type === 'monthly' ? 'Monthly Subscription' : 'One-time Payment'}</p>
                </div>
              </div>
            </div>

            {/* Invoice Items */}
            <div className="mb-8">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-4 py-2 text-left">Description</th>
                    <th className="border border-gray-300 px-4 py-2 text-right">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="border border-gray-300 px-4 py-2">
                      {invoice.course_name} - {invoice.enrollment_type === 'monthly' ? 'Monthly Fee' : 'One-time Fee'}
                      {invoice.notes && (
                        <div className="text-sm text-gray-500 mt-1">{invoice.notes}</div>
                      )}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-right">
                      ৳{invoice.amount.toFixed(2)}
                    </td>
                  </tr>
                  {invoice.discount_amount > 0 && (
                    <tr>
                      <td className="border border-gray-300 px-4 py-2">Discount</td>
                      <td className="border border-gray-300 px-4 py-2 text-right text-green-600">
                        -৳{invoice.discount_amount.toFixed(2)}
                      </td>
                    </tr>
                  )}
                  {invoice.tax_amount > 0 && (
                    <tr>
                      <td className="border border-gray-300 px-4 py-2">Tax</td>
                      <td className="border border-gray-300 px-4 py-2 text-right">
                        ৳{invoice.tax_amount.toFixed(2)}
                      </td>
                    </tr>
                  )}
                </tbody>
                <tfoot>
                  <tr className="bg-gray-50 font-bold">
                    <td className="border border-gray-300 px-4 py-2">Total</td>
                    <td className="border border-gray-300 px-4 py-2 text-right">
                      ৳{invoice.total_amount.toFixed(2)}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {/* Payment Status */}
            <div className="mb-8">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Payment Status</h3>
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                    invoice.payment_status === 'paid' 
                      ? 'bg-green-100 text-green-800' 
                      : invoice.payment_status === 'overdue'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {invoice.payment_status.toUpperCase()}
                  </span>
                </div>
                {invoice.payment_date && (
                  <div className="text-right">
                    <p className="text-gray-600">Paid on: {formatDate(invoice.payment_date)}</p>
                    {invoice.payment_method && (
                      <p className="text-gray-600">Method: {invoice.payment_method}</p>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="border-t border-gray-300 pt-6">
              <div className="text-center text-gray-600">
                <p className="mb-2">Thank you for choosing {settings?.coaching_center_name}!</p>
                <p className="text-sm">
                  For any queries, please contact us at {settings?.phone} or {settings?.email}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
