import { useState, useEffect } from "react"
import { But<PERSON> } from "../../components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card"
import { Input } from "../../components/ui/input"
import { Label } from "../../components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select"
import { Badge } from "../../components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "../../components/ui/dialog"
import { Plus, Search, DollarSign, Calendar, User, CheckCircle, XCircle, Clock, Eye, Edit, Trash2 } from "lucide-react"
import AdminLayout from "../../components/AdminLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton } from "../../components/skeletons/DashboardSkeleton"
import { adminGet, adminPost, adminPut, adminDelete } from "../../utils/adminApi"
import { useTranslation } from 'react-i18next'

interface FeeRecord {
  id: number
  student_id: number
  student_name: string
  course_name: string
  amount: number
  due_date: string
  paid_date?: string
  status: 'pending' | 'paid' | 'overdue'
  payment_method?: string
  notes?: string
  created_at: string
}

interface Student {
  id: number
  name: string
  email: string
  course_name: string
}

export default function FeesPage() {
  const { t } = useTranslation()
  const [feeRecords, setFeeRecords] = useState<FeeRecord[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("")
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingFee, setEditingFee] = useState<FeeRecord | null>(null)
  const [formData, setFormData] = useState({
    student_id: "",
    amount: "",
    due_date: "",
    notes: ""
  })

  useEffect(() => {
    fetchFeeRecords()
    fetchStudents()
  }, [])

  const fetchFeeRecords = async () => {
    try {
      setLoading(true)
      const response = await adminGet('/api/coaching/fees')
      setFeeRecords(response.data || [])
    } catch (error) {
      console.error('Error fetching fee records:', error)
      toast.error(t('Failed to fetch fee records'))
    } finally {
      setLoading(false)
    }
  }

  const fetchStudents = async () => {
    try {
      const response = await adminGet('/api/coaching/students')
      setStudents(response.data || [])
    } catch (error) {
      console.error('Error fetching students:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.student_id || !formData.amount || !formData.due_date) {
      toast.error(t('Please fill in all required fields'))
      return
    }

    try {
      const payload = {
        student_id: parseInt(formData.student_id),
        amount: parseFloat(formData.amount),
        due_date: formData.due_date,
        notes: formData.notes
      }

      if (editingFee) {
        await adminPut(`/api/coaching/fees/${editingFee.id}`, payload)
        toast.success(t('Fee record updated successfully'))
      } else {
        await adminPost('/api/coaching/fees', payload)
        toast.success(t('Fee record created successfully'))
      }

      setShowAddForm(false)
      setEditingFee(null)
      setFormData({ student_id: "", amount: "", due_date: "", notes: "" })
      fetchFeeRecords()
    } catch (error) {
      console.error('Error saving fee record:', error)
      toast.error(t('Failed to save fee record'))
    }
  }

  const handleEdit = (fee: FeeRecord) => {
    setEditingFee(fee)
    setFormData({
      student_id: fee.student_id.toString(),
      amount: fee.amount.toString(),
      due_date: fee.due_date,
      notes: fee.notes || ""
    })
    setShowAddForm(true)
  }

  const handleDelete = async (id: number) => {
    if (!confirm(t('Are you sure you want to delete this fee record?'))) return

    try {
      await adminDelete(`/api/coaching/fees/${id}`)
      toast.success(t('Fee record deleted successfully'))
      fetchFeeRecords()
    } catch (error) {
      console.error('Error deleting fee record:', error)
      toast.error(t('Failed to delete fee record'))
    }
  }

  const markAsPaid = async (id: number) => {
    try {
      await adminPut(`/api/coaching/fees/${id}/mark-paid`, {})
      toast.success(t('Fee marked as paid'))
      fetchFeeRecords()
    } catch (error) {
      console.error('Error marking fee as paid:', error)
      toast.error(t('Failed to mark fee as paid'))
    }
  }

  const filteredFees = feeRecords.filter(fee => {
    const matchesSearch = fee.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         fee.course_name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter || statusFilter === 'all' || fee.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />{t('Paid')}</Badge>
      case 'overdue':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />{t('Overdue')}</Badge>
      default:
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />{t('Pending')}</Badge>
    }
  }

  const totalAmount = filteredFees.reduce((sum, fee) => sum + fee.amount, 0)
  const paidAmount = filteredFees.filter(fee => fee.status === 'paid').reduce((sum, fee) => sum + fee.amount, 0)
  const pendingAmount = totalAmount - paidAmount

  if (loading) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <PageHeaderSkeleton />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                </CardHeader>
              </Card>
            ))}
          </div>
          <TableSkeleton />
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('Fee Management')}</h1>
            <p className="text-muted-foreground">{t('Manage student fees and payments')}</p>
          </div>
          <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
            <DialogTrigger asChild>
              <Button onClick={() => {
                setEditingFee(null)
                setFormData({ student_id: "", amount: "", due_date: "", notes: "" })
              }}>
                <Plus className="w-4 h-4 mr-2" />
                {t('Add Fee Record')}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editingFee ? t('Edit Fee Record') : t('Add Fee Record')}</DialogTitle>
                <DialogDescription>
                  {editingFee ? t('Update the fee record details') : t('Create a new fee record for a student')}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="student_id">{t('Student')}</Label>
                  <Select value={formData.student_id} onValueChange={(value) => setFormData({...formData, student_id: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('Select student')} />
                    </SelectTrigger>
                    <SelectContent>
                      {students && students.length > 0 ? students.map(student => (
                        <SelectItem key={student.id} value={student.id.toString()}>
                          {student.name} - {student.course_name || 'No Course'}
                        </SelectItem>
                      )) : (
                        <SelectItem value="no-students" disabled>No students available</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="amount">{t('Amount')} (৳)</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    value={formData.amount}
                    onChange={(e) => setFormData({...formData, amount: e.target.value})}
                    placeholder={t('Enter amount')}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="due_date">{t('Due Date')}</Label>
                  <Input
                    id="due_date"
                    type="date"
                    value={formData.due_date}
                    onChange={(e) => setFormData({...formData, due_date: e.target.value})}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="notes">{t('Notes')}</Label>
                  <Input
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    placeholder={t('Optional notes')}
                  />
                </div>
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                    {t('Cancel')}
                  </Button>
                  <Button type="submit">
                    {editingFee ? t('Update') : t('Create')}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('Total Amount')}</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">৳{totalAmount.toLocaleString()}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('Paid Amount')}</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">৳{paidAmount.toLocaleString()}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('Pending Amount')}</CardTitle>
              <Clock className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">৳{pendingAmount.toLocaleString()}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>{t('Fee Records')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t('Search by student or course name...')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder={t('Filter by status')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('All Status')}</SelectItem>
                  <SelectItem value="pending">{t('Pending')}</SelectItem>
                  <SelectItem value="paid">{t('Paid')}</SelectItem>
                  <SelectItem value="overdue">{t('Overdue')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Fee Records Table */}
            <div className="rounded-md border">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="h-12 px-4 text-left align-middle font-medium">{t('Student')}</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">{t('Course')}</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">{t('Amount')}</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">{t('Due Date')}</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">{t('Status')}</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">{t('Actions')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredFees.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="h-24 text-center">
                          {t('No fee records found')}
                        </td>
                      </tr>
                    ) : (
                      filteredFees.map((fee) => (
                        <tr key={fee.id} className="border-b">
                          <td className="p-4">
                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{fee.student_name}</span>
                            </div>
                          </td>
                          <td className="p-4">{fee.course_name}</td>
                          <td className="p-4 font-medium">৳{fee.amount.toLocaleString()}</td>
                          <td className="p-4">
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span>{new Date(fee.due_date).toLocaleDateString()}</span>
                            </div>
                          </td>
                          <td className="p-4">{getStatusBadge(fee.status)}</td>
                          <td className="p-4">
                            <div className="flex items-center space-x-2">
                              {fee.status === 'pending' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => markAsPaid(fee.id)}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <CheckCircle className="h-4 w-4" />
                                </Button>
                              )}
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEdit(fee)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDelete(fee.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
