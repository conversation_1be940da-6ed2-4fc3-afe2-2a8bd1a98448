import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import AdminLayout from '../../components/AdminLayout'
import { adminGet, adminPost } from '../../utils/adminApi'
import { toast } from 'react-hot-toast'
import { 
  Award, 
  Download, 
  Eye, 
  FileText, 
  Image, 
  Palette, 
  Plus, 
  Save, 
  Settings,
  User,
  Calendar,
  MapPin
} from 'lucide-react'

interface Student {
  id: number
  name: string
  email: string
  phone: string
  course_name?: string
  batch_name?: string
  admission_date: string
  completion_date?: string
  grade?: string
  percentage?: number
}

interface CertificateTemplate {
  id: number
  name: string
  template_type: 'completion' | 'achievement' | 'participation' | 'excellence'
  background_color: string
  border_style: string
  font_family: string
  language: 'bengali' | 'english' | 'both'
  layout: string
  is_active: boolean
  created_at: string
}

interface CertificateData {
  student_id: number
  template_id: number
  certificate_title: string
  course_name: string
  completion_date: string
  grade?: string
  percentage?: number
  custom_text?: string
  issued_by: string
  language: 'bengali' | 'english'
}

const CertificateGeneratorPage: React.FC = () => {
  const navigate = useNavigate()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  
  const [students, setStudents] = useState<Student[]>([])
  const [templates, setTemplates] = useState<CertificateTemplate[]>([])
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<CertificateTemplate | null>(null)
  const [language, setLanguage] = useState<'bengali' | 'english'>('bengali')
  const [showPreview, setShowPreview] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  
  const [certificateData, setCertificateData] = useState<CertificateData>({
    student_id: 0,
    template_id: 0,
    certificate_title: '',
    course_name: '',
    completion_date: new Date().toISOString().split('T')[0],
    custom_text: '',
    issued_by: 'Center Administrator',
    language: 'bengali'
  })

  const [centerInfo, setCenterInfo] = useState({
    name: 'ABC Coaching Center',
    address: 'Dhaka, Bangladesh',
    phone: '+880 1234567890',
    email: '<EMAIL>',
    logo: ''
  })

  useEffect(() => {
    fetchStudents()
    fetchTemplates()
    fetchCenterInfo()
  }, [])

  const fetchStudents = async () => {
    try {
      const response = await adminGet('/api/coaching/students')
      setStudents(response.students || [])
    } catch (error) {
      console.error('Error fetching students:', error)
      toast.error('শিক্ষার্থীদের তালিকা লোড করতে ব্যর্থ')
    }
  }

  const fetchTemplates = async () => {
    try {
      const response = await adminGet('/api/coaching/certificate-templates')
      setTemplates(response.templates || [])
    } catch (error) {
      console.error('Error fetching templates:', error)
      // Create default templates if none exist
      setTemplates([
        {
          id: 1,
          name: 'Course Completion Certificate',
          template_type: 'completion',
          background_color: '#ffffff',
          border_style: 'elegant',
          font_family: 'serif',
          language: 'both',
          layout: 'classic',
          is_active: true,
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          name: 'Achievement Certificate',
          template_type: 'achievement',
          background_color: '#f8f9fa',
          border_style: 'modern',
          font_family: 'sans-serif',
          language: 'both',
          layout: 'modern',
          is_active: true,
          created_at: new Date().toISOString()
        }
      ])
    }
  }

  const fetchCenterInfo = async () => {
    try {
      const response = await adminGet('/api/coaching/center-info')
      if (response.center) {
        setCenterInfo(response.center)
      }
    } catch (error) {
      console.error('Error fetching center info:', error)
    }
  }

  const handleStudentSelect = (student: Student) => {
    setSelectedStudent(student)
    setCertificateData(prev => ({
      ...prev,
      student_id: student.id,
      course_name: student.course_name || '',
      completion_date: student.completion_date || new Date().toISOString().split('T')[0],
      grade: student.grade || '',
      percentage: student.percentage || 0
    }))
  }

  const handleTemplateSelect = (template: CertificateTemplate) => {
    setSelectedTemplate(template)
    setCertificateData(prev => ({
      ...prev,
      template_id: template.id,
      language: template.language === 'both' ? language : template.language
    }))
  }

  const generatePreview = () => {
    if (!selectedStudent || !selectedTemplate) {
      toast.error('অনুগ্রহ করে শিক্ষার্থী এবং টেমপ্লেট নির্বাচন করুন')
      return
    }

    setShowPreview(true)
    drawCertificate()
  }

  const drawCertificate = () => {
    const canvas = canvasRef.current
    if (!canvas || !selectedStudent || !selectedTemplate) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size (A4 landscape)
    canvas.width = 1123
    canvas.height = 794

    // Clear canvas
    ctx.fillStyle = selectedTemplate.background_color
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Draw border
    ctx.strokeStyle = '#2563eb'
    ctx.lineWidth = 8
    ctx.strokeRect(20, 20, canvas.width - 40, canvas.height - 40)

    // Inner border
    ctx.strokeStyle = '#1d4ed8'
    ctx.lineWidth = 2
    ctx.strokeRect(40, 40, canvas.width - 80, canvas.height - 80)

    // Certificate title
    ctx.fillStyle = '#1e40af'
    ctx.font = 'bold 48px serif'
    ctx.textAlign = 'center'
    
    const title = language === 'bengali' ? 'সার্টিফিকেট অব কমপ্লিশন' : 'CERTIFICATE OF COMPLETION'
    ctx.fillText(title, canvas.width / 2, 150)

    // Center name
    ctx.fillStyle = '#374151'
    ctx.font = 'bold 24px serif'
    ctx.fillText(centerInfo.name, canvas.width / 2, 200)

    // "This is to certify that" text
    ctx.fillStyle = '#4b5563'
    ctx.font = '20px serif'
    const certifyText = language === 'bengali' 
      ? 'এই মর্মে প্রত্যয়ন করা যাচ্ছে যে' 
      : 'This is to certify that'
    ctx.fillText(certifyText, canvas.width / 2, 280)

    // Student name
    ctx.fillStyle = '#1e40af'
    ctx.font = 'bold 36px serif'
    ctx.fillText(selectedStudent.name, canvas.width / 2, 340)

    // Course completion text
    ctx.fillStyle = '#4b5563'
    ctx.font = '20px serif'
    const completionText = language === 'bengali'
      ? `সফলভাবে ${certificateData.course_name || 'কোর্স'} সম্পন্ন করেছেন`
      : `has successfully completed the course of ${certificateData.course_name || 'Study'}`
    ctx.fillText(completionText, canvas.width / 2, 400)

    // Grade/Percentage
    if (certificateData.grade || certificateData.percentage) {
      const gradeText = language === 'bengali'
        ? `গ্রেড: ${certificateData.grade || 'A'} (${certificateData.percentage || 85}%)`
        : `Grade: ${certificateData.grade || 'A'} (${certificateData.percentage || 85}%)`
      ctx.fillText(gradeText, canvas.width / 2, 450)
    }

    // Date
    ctx.fillStyle = '#6b7280'
    ctx.font = '18px serif'
    const dateText = language === 'bengali'
      ? `তারিখ: ${new Date(certificateData.completion_date).toLocaleDateString('bn-BD')}`
      : `Date: ${new Date(certificateData.completion_date).toLocaleDateString()}`
    ctx.fillText(dateText, canvas.width / 2, 550)

    // Signature line
    ctx.strokeStyle = '#374151'
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(canvas.width - 300, 650)
    ctx.lineTo(canvas.width - 100, 650)
    ctx.stroke()

    // Signature text
    ctx.fillStyle = '#374151'
    ctx.font = '16px serif'
    ctx.textAlign = 'center'
    const signatureText = language === 'bengali' ? 'কর্তৃপক্ষের স্বাক্ষর' : 'Authorized Signature'
    ctx.fillText(signatureText, canvas.width - 200, 680)

    // Center address
    ctx.fillStyle = '#6b7280'
    ctx.font = '14px serif'
    ctx.textAlign = 'left'
    ctx.fillText(centerInfo.address, 60, canvas.height - 60)
    ctx.fillText(centerInfo.phone, 60, canvas.height - 40)
  }

  const downloadPDF = async () => {
    if (!selectedStudent || !selectedTemplate) {
      toast.error('অনুগ্রহ করে শিক্ষার্থী এবং টেমপ্লেট নির্বাচন করুন')
      return
    }

    setIsGenerating(true)
    
    try {
      // Generate certificate data
      const canvas = canvasRef.current
      if (!canvas) return

      // Convert canvas to blob
      canvas.toBlob(async (blob) => {
        if (!blob) return

        // Create download link
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `certificate-${selectedStudent.name.replace(/\s+/g, '-')}-${Date.now()}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        toast.success('সার্টিফিকেট সফলভাবে ডাউনলোড হয়েছে!')
      }, 'image/png')

    } catch (error) {
      console.error('Error generating PDF:', error)
      toast.error('সার্টিফিকেট তৈরি করতে ব্যর্থ')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">সার্টিফিকেট জেনারেটর</h1>
            <p className="text-gray-600">শিক্ষার্থীদের জন্য কাস্টমাইজড সার্টিফিকেট তৈরি করুন</p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setLanguage(language === 'bengali' ? 'english' : 'bengali')}
              className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200"
            >
              {language === 'bengali' ? 'English' : 'বাংলা'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Panel - Configuration */}
          <div className="lg:col-span-1 space-y-6">
            {/* Student Selection */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <User className="h-5 w-5" />
                শিক্ষার্থী নির্বাচন
              </h3>
              <select
                value={selectedStudent?.id || ''}
                onChange={(e) => {
                  const student = students.find(s => s.id === parseInt(e.target.value))
                  if (student) handleStudentSelect(student)
                }}
                className="w-full border rounded-lg px-3 py-2"
              >
                <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                {students.map(student => (
                  <option key={student.id} value={student.id}>
                    {student.name} - {student.course_name || 'No Course'}
                  </option>
                ))}
              </select>
              
              {selectedStudent && (
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm"><strong>নাম:</strong> {selectedStudent.name}</p>
                  <p className="text-sm"><strong>কোর্স:</strong> {selectedStudent.course_name || 'N/A'}</p>
                  <p className="text-sm"><strong>ব্যাচ:</strong> {selectedStudent.batch_name || 'N/A'}</p>
                </div>
              )}
            </div>

            {/* Template Selection */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <FileText className="h-5 w-5" />
                টেমপ্লেট নির্বাচন
              </h3>
              <div className="space-y-2">
                {templates.map(template => (
                  <button
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className={`w-full text-left p-3 rounded-lg border transition-colors ${
                      selectedTemplate?.id === template.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium">{template.name}</div>
                    <div className="text-sm text-gray-500 capitalize">{template.template_type}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Right Panel - Preview and Controls */}
          <div className="lg:col-span-2 space-y-6">
            {/* Certificate Data */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Settings className="h-5 w-5" />
                সার্টিফিকেট তথ্য
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">সার্টিফিকেট শিরোনাম</label>
                  <input
                    type="text"
                    value={certificateData.certificate_title}
                    onChange={(e) => setCertificateData(prev => ({ ...prev, certificate_title: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="Certificate of Completion"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">কোর্সের নাম</label>
                  <input
                    type="text"
                    value={certificateData.course_name}
                    onChange={(e) => setCertificateData(prev => ({ ...prev, course_name: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="Course Name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">সমাপনীর তারিখ</label>
                  <input
                    type="date"
                    value={certificateData.completion_date}
                    onChange={(e) => setCertificateData(prev => ({ ...prev, completion_date: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">গ্রেড</label>
                  <input
                    type="text"
                    value={certificateData.grade || ''}
                    onChange={(e) => setCertificateData(prev => ({ ...prev, grade: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="A+"
                  />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex gap-4">
                <button
                  onClick={generatePreview}
                  disabled={!selectedStudent || !selectedTemplate}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Eye className="h-4 w-4" />
                  প্রিভিউ তৈরি করুন
                </button>
                <button
                  onClick={downloadPDF}
                  disabled={!showPreview || isGenerating}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Download className="h-4 w-4" />
                  {isGenerating ? 'তৈরি হচ্ছে...' : 'ডাউনলোড করুন'}
                </button>
              </div>
            </div>

            {/* Preview */}
            {showPreview && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  সার্টিফিকেট প্রিভিউ
                </h3>
                <div className="border rounded-lg p-4 bg-gray-50">
                  <canvas
                    ref={canvasRef}
                    className="w-full max-w-full h-auto border border-gray-300 rounded"
                    style={{ maxHeight: '500px' }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}

export default CertificateGeneratorPage
