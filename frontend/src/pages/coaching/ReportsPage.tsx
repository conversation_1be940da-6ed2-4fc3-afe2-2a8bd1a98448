import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../../components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card"
import { Input } from "../../components/ui/input"
import { Label } from "../../components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select"
import { BarChart3, Pie<PERSON><PERSON>, TrendingUp, Download, Calendar, Users, DollarSign, BookOpen, FileText, GraduationCap } from "lucide-react"
import AdminLayout from "../../components/AdminLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton } from "../../components/skeletons/DashboardSkeleton"
import { adminGet } from "../../utils/adminApi"
import { useTranslation } from 'react-i18next'

interface ReportData {
  students: {
    total: number
    active: number
    inactive: number
    byMonth: { month: string; count: number }[]
  }
  attendance: {
    averageRate: number
    totalSessions: number
    presentCount: number
    absentCount: number
    byMonth: { month: string; rate: number }[]
  }
  financial: {
    totalRevenue: number
    totalExpenses: number
    netProfit: number
    feeCollection: number
    byMonth: { month: string; revenue: number; expenses: number }[]
  }
  courses: {
    totalCourses: number
    activeCourses: number
    totalEnrollments: number
    popularCourses: { name: string; enrollments: number }[]
  }
  exams: {
    totalExams: number
    completedExams: number
    averageScore: number
    passRate: number
    byMonth: { month: string; exams: number; passRate: number }[]
  }
}

export default function ReportsPage() {
  const { t } = useTranslation()
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  })
  const [activeReport, setActiveReport] = useState<'overview' | 'students' | 'attendance' | 'financial' | 'courses' | 'exams'>('overview')

  useEffect(() => {
    fetchReportData()
  }, [dateRange])

  const fetchReportData = async () => {
    try {
      setLoading(true)
      const response = await adminGet(`/api/coaching/reports/dashboard?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`)
      setReportData(response.data || {
        students: { total: 0, active: 0, inactive: 0, byMonth: [] },
        attendance: { averageRate: 0, totalSessions: 0, presentCount: 0, absentCount: 0, byMonth: [] },
        financial: { totalRevenue: 0, totalExpenses: 0, netProfit: 0, feeCollection: 0, byMonth: [] },
        courses: { totalCourses: 0, activeCourses: 0, totalEnrollments: 0, popularCourses: [] },
        exams: { totalExams: 0, completedExams: 0, averageScore: 0, passRate: 0, byMonth: [] }
      })
    } catch (error) {
      console.error('Error fetching report data:', error)
      toast.error(t('Failed to fetch report data'))
      // Set default data on error
      setReportData({
        students: { total: 0, active: 0, inactive: 0, byMonth: [] },
        attendance: { averageRate: 0, totalSessions: 0, presentCount: 0, absentCount: 0, byMonth: [] },
        financial: { totalRevenue: 0, totalExpenses: 0, netProfit: 0, feeCollection: 0, byMonth: [] },
        courses: { totalCourses: 0, activeCourses: 0, totalEnrollments: 0, popularCourses: [] },
        exams: { totalExams: 0, completedExams: 0, averageScore: 0, passRate: 0, byMonth: [] }
      })
    } finally {
      setLoading(false)
    }
  }

  const exportReport = async (format: 'pdf' | 'excel') => {
    try {
      const response = await adminGet(`/api/coaching/reports/export?format=${format}&start_date=${dateRange.startDate}&end_date=${dateRange.endDate}`, {
        responseType: 'blob'
      })
      
      const blob = new Blob([response.data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `coaching-report-${dateRange.startDate}-to-${dateRange.endDate}.${format === 'pdf' ? 'pdf' : 'xlsx'}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      toast.success(t('Report exported successfully'))
    } catch (error) {
      console.error('Error exporting report:', error)
      toast.error(t('Failed to export report'))
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <PageHeaderSkeleton />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map(i => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                </CardHeader>
              </Card>
            ))}
          </div>
          <TableSkeleton />
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('Reports & Analytics')}</h1>
            <p className="text-muted-foreground">{t('Comprehensive reports and analytics for your coaching center')}</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => exportReport('excel')}>
              <Download className="w-4 h-4 mr-2" />
              {t('Export Excel')}
            </Button>
            <Button variant="outline" onClick={() => exportReport('pdf')}>
              <Download className="w-4 h-4 mr-2" />
              {t('Export PDF')}
            </Button>
          </div>
        </div>

        {/* Date Range Filter */}
        <Card>
          <CardHeader>
            <CardTitle>{t('Report Period')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div>
                <Label htmlFor="start_date">{t('Start Date')}</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={dateRange.startDate}
                  onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="end_date">{t('End Date')}</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={dateRange.endDate}
                  onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="report_type">{t('Report Type')}</Label>
                <Select value={activeReport} onValueChange={(value: any) => setActiveReport(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="overview">{t('Overview')}</SelectItem>
                    <SelectItem value="students">{t('Students')}</SelectItem>
                    <SelectItem value="attendance">{t('Attendance')}</SelectItem>
                    <SelectItem value="financial">{t('Financial')}</SelectItem>
                    <SelectItem value="courses">{t('Courses')}</SelectItem>
                    <SelectItem value="exams">{t('Exams')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Overview Stats */}
        {activeReport === 'overview' && reportData && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t('Total Students')}</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{reportData.students.total}</div>
                  <p className="text-xs text-muted-foreground">
                    {reportData.students.active} {t('active')}, {reportData.students.inactive} {t('inactive')}
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t('Total Revenue')}</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">৳{reportData.financial.totalRevenue.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    {t('Net Profit')}: ৳{reportData.financial.netProfit.toLocaleString()}
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t('Active Courses')}</CardTitle>
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{reportData.courses.activeCourses}</div>
                  <p className="text-xs text-muted-foreground">
                    {reportData.courses.totalEnrollments} {t('total enrollments')}
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t('Attendance Rate')}</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{reportData.attendance.averageRate.toFixed(1)}%</div>
                  <p className="text-xs text-muted-foreground">
                    {reportData.attendance.totalSessions} {t('total sessions')}
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Quick Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t('Popular Courses')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {reportData.courses.popularCourses.slice(0, 5).map((course, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{course.name}</span>
                        <span className="text-sm text-muted-foreground">{course.enrollments} {t('students')}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>{t('Exam Performance')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('Total Exams')}</span>
                      <span className="text-sm text-muted-foreground">{reportData.exams.totalExams}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('Completed Exams')}</span>
                      <span className="text-sm text-muted-foreground">{reportData.exams.completedExams}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('Average Score')}</span>
                      <span className="text-sm text-muted-foreground">{reportData.exams.averageScore.toFixed(1)}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{t('Pass Rate')}</span>
                      <span className="text-sm text-muted-foreground">{reportData.exams.passRate.toFixed(1)}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}

        {/* Students Report */}
        {activeReport === 'students' && reportData && (
          <Card>
            <CardHeader>
              <CardTitle>{t('Student Analytics')}</CardTitle>
              <CardDescription>{t('Detailed student enrollment and activity reports')}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">{reportData.students.total}</div>
                  <div className="text-sm text-muted-foreground">{t('Total Students')}</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">{reportData.students.active}</div>
                  <div className="text-sm text-muted-foreground">{t('Active Students')}</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-600">{reportData.students.inactive}</div>
                  <div className="text-sm text-muted-foreground">{t('Inactive Students')}</div>
                </div>
              </div>
              <div className="text-center text-muted-foreground">
                {t('Monthly enrollment trends and student activity patterns would be displayed here')}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Financial Report */}
        {activeReport === 'financial' && reportData && (
          <Card>
            <CardHeader>
              <CardTitle>{t('Financial Analytics')}</CardTitle>
              <CardDescription>{t('Revenue, expenses, and profit analysis')}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">৳{reportData.financial.totalRevenue.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">{t('Total Revenue')}</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-600">৳{reportData.financial.totalExpenses.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">{t('Total Expenses')}</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">৳{reportData.financial.netProfit.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">{t('Net Profit')}</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">৳{reportData.financial.feeCollection.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">{t('Fee Collection')}</div>
                </div>
              </div>
              <div className="text-center text-muted-foreground">
                {t('Monthly financial trends and cash flow analysis would be displayed here')}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Other report types would follow similar patterns */}
        {(activeReport === 'attendance' || activeReport === 'courses' || activeReport === 'exams') && (
          <Card>
            <CardHeader>
              <CardTitle>
                {activeReport === 'attendance' && t('Attendance Analytics')}
                {activeReport === 'courses' && t('Course Analytics')}
                {activeReport === 'exams' && t('Exam Analytics')}
              </CardTitle>
              <CardDescription>
                {activeReport === 'attendance' && t('Student attendance patterns and trends')}
                {activeReport === 'courses' && t('Course enrollment and performance metrics')}
                {activeReport === 'exams' && t('Exam results and performance analysis')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center text-muted-foreground py-12">
                {t('Detailed analytics for')} {activeReport} {t('would be displayed here')}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  )
}
