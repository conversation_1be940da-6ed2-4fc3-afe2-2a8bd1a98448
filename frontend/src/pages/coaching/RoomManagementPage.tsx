import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import AdminLayout from '../../components/AdminLayout'
import { adminGet, adminPost, adminPut } from '../../utils/adminApi'

interface Room {
  id: number
  room_name: string
  room_code: string
  capacity: number
  equipment: string
  status: 'active' | 'inactive' | 'maintenance'
}

interface RoomBooking {
  id: number
  room_id: number
  booking_type: 'class' | 'exam' | 'meeting' | 'conference' | 'training' | 'workshop' | 'other'
  title: string
  description: string
  organizer_name: string
  start_datetime: string
  end_datetime: string
  status: 'pending' | 'approved' | 'confirmed' | 'cancelled'
  room_name: string
  room_code: string
  attendees_count: number
}

interface ConflictCheck {
  hasConflicts: boolean
  bookingConflicts: any[]
  scheduleConflicts: any[]
  totalConflicts: number
}

export default function RoomManagementPage() {
  const { t } = useTranslation()
  const [rooms, setRooms] = useState<Room[]>([])
  const [bookings, setBookings] = useState<RoomBooking[]>([])
  const [teachers, setTeachers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [showBookingModal, setShowBookingModal] = useState(false)
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [selectedRoom, setSelectedRoom] = useState<string>('')
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar')

  const [bookingForm, setBookingForm] = useState({
    room_id: '',
    booking_type: 'meeting' as const,
    title: '',
    description: '',
    organizer_id: '',
    attendees_count: '',
    start_datetime: '',
    end_datetime: '',
    equipment_requirements: '',
    special_notes: ''
  })

  const [conflicts, setConflicts] = useState<ConflictCheck | null>(null)

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    if (selectedDate) {
      fetchBookings()
    }
  }, [selectedDate, selectedRoom])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [roomsRes, teachersRes] = await Promise.all([
        adminGet('/api/coaching/rooms'),
        adminGet('/api/coaching/teachers')
      ])

      setRooms(roomsRes.rooms || [])
      setTeachers(teachersRes.teachers || [])
      
    } catch (error: any) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const fetchBookings = async () => {
    try {
      const params = new URLSearchParams({
        start_date: selectedDate,
        end_date: selectedDate
      })
      
      if (selectedRoom) {
        params.append('room_id', selectedRoom)
      }

      const response = await adminGet(`/api/coaching/room-bookings?${params}`)
      setBookings(response.bookings || [])
      
    } catch (error: any) {
      console.error('Error fetching bookings:', error)
      toast.error('Failed to load bookings')
    }
  }

  const checkConflicts = async () => {
    if (!bookingForm.room_id || !bookingForm.start_datetime || !bookingForm.end_datetime) {
      return
    }

    try {
      const response = await adminPost('/api/coaching/room-bookings/check-conflicts', {
        room_id: parseInt(bookingForm.room_id),
        start_datetime: bookingForm.start_datetime,
        end_datetime: bookingForm.end_datetime
      })

      setConflicts(response)
      
      if (response.hasConflicts) {
        toast.error(`${response.totalConflicts} conflict(s) detected!`)
      } else {
        toast.success('No conflicts detected')
      }
    } catch (error: any) {
      console.error('Error checking conflicts:', error)
      toast.error('Failed to check conflicts')
    }
  }

  const handleCreateBooking = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!bookingForm.room_id || !bookingForm.title || !bookingForm.start_datetime || !bookingForm.end_datetime) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      const response = await adminPost('/api/coaching/room-bookings', {
        ...bookingForm,
        room_id: parseInt(bookingForm.room_id),
        organizer_id: bookingForm.organizer_id ? parseInt(bookingForm.organizer_id) : null,
        attendees_count: bookingForm.attendees_count ? parseInt(bookingForm.attendees_count) : null
      })

      if (response.success) {
        toast.success('Room booking created successfully!')
        setShowBookingModal(false)
        resetForm()
        fetchBookings()
      }
    } catch (error: any) {
      console.error('Error creating booking:', error)
      if (error.message.includes('conflict')) {
        toast.error('Room booking conflict detected. Please choose a different time or room.')
      } else {
        toast.error(`Failed to create booking: ${error.message}`)
      }
    }
  }

  const updateBookingStatus = async (bookingId: number, status: string) => {
    try {
      const response = await adminPut(`/api/coaching/room-bookings/${bookingId}/status`, { status })
      
      if (response.success) {
        toast.success(`Booking ${status} successfully`)
        fetchBookings()
      }
    } catch (error: any) {
      console.error('Error updating booking status:', error)
      toast.error(`Failed to update booking status: ${error.message}`)
    }
  }

  const resetForm = () => {
    setBookingForm({
      room_id: '',
      booking_type: 'meeting',
      title: '',
      description: '',
      organizer_id: '',
      attendees_count: '',
      start_datetime: '',
      end_datetime: '',
      equipment_requirements: '',
      special_notes: ''
    })
    setConflicts(null)
  }

  const getBookingTypeColor = (type: string) => {
    switch (type) {
      case 'class': return 'bg-blue-100 text-blue-800'
      case 'exam': return 'bg-red-100 text-red-800'
      case 'meeting': return 'bg-green-100 text-green-800'
      case 'conference': return 'bg-purple-100 text-purple-800'
      case 'training': return 'bg-orange-100 text-orange-800'
      case 'workshop': return 'bg-teal-100 text-teal-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'approved': return 'bg-blue-100 text-blue-800'
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime)
    return date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Loading room management...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Room Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage room bookings and view schedules
            </p>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setViewMode(viewMode === 'calendar' ? 'list' : 'calendar')}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
            >
              {viewMode === 'calendar' ? 'List View' : 'Calendar View'}
            </button>
            <button
              onClick={() => setShowBookingModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              New Booking
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Date
              </label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Room
              </label>
              <select
                value={selectedRoom}
                onChange={(e) => setSelectedRoom(e.target.value)}
                className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Rooms</option>
                {rooms.map((room) => (
                  <option key={room.id} value={room.id}>
                    {room.room_code} - {room.room_name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={fetchBookings}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Room Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Total Rooms</h3>
            <p className="text-3xl font-bold text-blue-600">{rooms.length}</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Today's Bookings</h3>
            <p className="text-3xl font-bold text-green-600">{bookings.length}</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Pending Approval</h3>
            <p className="text-3xl font-bold text-yellow-600">
              {bookings.filter(b => b.status === 'pending').length}
            </p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Confirmed</h3>
            <p className="text-3xl font-bold text-purple-600">
              {bookings.filter(b => b.status === 'confirmed').length}
            </p>
          </div>
        </div>

        {/* Bookings List */}
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Room Bookings - {new Date(selectedDate).toLocaleDateString('en-GB')}
            </h2>
          </div>
          
          {bookings.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No bookings found for selected date</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Room
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Event Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Organizer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {bookings.map((booking) => (
                    <tr key={booking.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {booking.room_code}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {booking.room_name}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {booking.title}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {booking.description}
                          </div>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getBookingTypeColor(booking.booking_type)}`}>
                            {booking.booking_type}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {formatDateTime(booking.start_datetime)}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            to {formatDateTime(booking.end_datetime)}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {booking.organizer_name || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {booking.attendees_count} attendees
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(booking.status)}`}>
                          {booking.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          {booking.status === 'pending' && (
                            <>
                              <button
                                onClick={() => updateBookingStatus(booking.id, 'approved')}
                                className="text-green-600 hover:text-green-900"
                              >
                                Approve
                              </button>
                              <button
                                onClick={() => updateBookingStatus(booking.id, 'cancelled')}
                                className="text-red-600 hover:text-red-900"
                              >
                                Cancel
                              </button>
                            </>
                          )}
                          {booking.status === 'approved' && (
                            <button
                              onClick={() => updateBookingStatus(booking.id, 'confirmed')}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Confirm
                            </button>
                          )}
                          <button
                            onClick={() => window.location.href = `/coaching/room-bookings/${booking.id}`}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            View
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create Booking Modal */}
      {showBookingModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Create Room Booking
              </h3>
              <button
                onClick={() => setShowBookingModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleCreateBooking} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Room *</label>
                  <select
                    required
                    value={bookingForm.room_id}
                    onChange={(e) => setBookingForm(prev => ({ ...prev, room_id: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="">Select Room</option>
                    {rooms.map((room) => (
                      <option key={room.id} value={room.id}>
                        {room.room_code} - {room.room_name} (Capacity: {room.capacity})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Booking Type</label>
                  <select
                    value={bookingForm.booking_type}
                    onChange={(e) => setBookingForm(prev => ({ ...prev, booking_type: e.target.value as any }))}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="meeting">Meeting</option>
                    <option value="conference">Conference</option>
                    <option value="training">Training</option>
                    <option value="workshop">Workshop</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-1">Title *</label>
                  <input
                    type="text"
                    required
                    value={bookingForm.title}
                    onChange={(e) => setBookingForm(prev => ({ ...prev, title: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="Enter booking title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Organizer</label>
                  <select
                    value={bookingForm.organizer_id}
                    onChange={(e) => setBookingForm(prev => ({ ...prev, organizer_id: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="">Select Organizer</option>
                    {teachers.map((teacher) => (
                      <option key={teacher.id} value={teacher.id}>
                        {teacher.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Attendees Count</label>
                  <input
                    type="number"
                    min="1"
                    value={bookingForm.attendees_count}
                    onChange={(e) => setBookingForm(prev => ({ ...prev, attendees_count: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="Number of attendees"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Start Date & Time *</label>
                  <input
                    type="datetime-local"
                    required
                    value={bookingForm.start_datetime}
                    onChange={(e) => setBookingForm(prev => ({ ...prev, start_datetime: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">End Date & Time *</label>
                  <input
                    type="datetime-local"
                    required
                    value={bookingForm.end_datetime}
                    onChange={(e) => setBookingForm(prev => ({ ...prev, end_datetime: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-1">Description</label>
                  <textarea
                    value={bookingForm.description}
                    onChange={(e) => setBookingForm(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    rows={3}
                    placeholder="Booking description..."
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-1">Equipment Requirements</label>
                  <textarea
                    value={bookingForm.equipment_requirements}
                    onChange={(e) => setBookingForm(prev => ({ ...prev, equipment_requirements: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    rows={2}
                    placeholder="Projector, microphone, etc..."
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-1">Special Notes</label>
                  <textarea
                    value={bookingForm.special_notes}
                    onChange={(e) => setBookingForm(prev => ({ ...prev, special_notes: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    rows={2}
                    placeholder="Any special requirements or notes..."
                  />
                </div>
              </div>

              {/* Conflict Check */}
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={checkConflicts}
                  className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700"
                >
                  Check Conflicts
                </button>
                {conflicts && (
                  <div className={`px-3 py-2 rounded-lg text-sm ${
                    conflicts.hasConflicts 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {conflicts.hasConflicts 
                      ? `${conflicts.totalConflicts} conflict(s) detected!`
                      : 'No conflicts detected'
                    }
                  </div>
                )}
              </div>

              <div className="flex gap-2 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700"
                >
                  Create Booking
                </button>
                <button
                  type="button"
                  onClick={() => setShowBookingModal(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </AdminLayout>
  )
}
