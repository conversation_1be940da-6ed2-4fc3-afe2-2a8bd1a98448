import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import AdminLayout from '../../components/AdminLayout'
import { adminGet, adminPost, adminPut, adminDelete } from '../../utils/adminApi'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Plus, 
  Search, 
  Award, 
  Calendar, 
  User, 
  FileText, 
  Download, 
  Edit, 
  Trash2,
  Eye,
  Filter,
  BarChart3,
  TrendingUp,
  Users,
  BookOpen
} from "lucide-react"

// Simple Badge component
const Badge = ({ children, className, variant }: {
  children: React.ReactNode;
  className?: string;
  variant?: 'destructive' | 'default'
}) => (
  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
    variant === 'destructive'
      ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      : className || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
  }`}>
    {children}
  </span>
)

interface ExamResult {
  id: number
  exam_id: number
  exam_name: string
  exam_date: string
  student_id: number
  student_name: string
  student_email: string
  course_name: string
  batch_name?: string
  total_marks: number
  obtained_marks: number
  percentage: number
  grade: string
  position_in_batch?: number
  position_overall?: number
  is_absent: boolean
  notes?: string
  status: 'draft' | 'published'
  created_at: string
  updated_at: string
}

interface Exam {
  id: number
  exam_name: string
  exam_date: string
  course_name: string
  total_marks: number
  passing_marks: number
  status: string
  results_count: number
}

interface ResultStats {
  totalResults: number
  publishedResults: number
  draftResults: number
  averagePercentage: number
  passRate: number
  totalStudents: number
  examsWithResults: number
}

export default function ResultManagementPage() {
  const { t } = useTranslation()
  const navigate = useNavigate()
  
  const [results, setResults] = useState<ExamResult[]>([])
  const [exams, setExams] = useState<Exam[]>([])
  const [stats, setStats] = useState<ResultStats>({
    totalResults: 0,
    publishedResults: 0,
    draftResults: 0,
    averagePercentage: 0,
    passRate: 0,
    totalStudents: 0,
    examsWithResults: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [examFilter, setExamFilter] = useState<string>('')
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    setLoading(true)
    try {
      await Promise.all([
        fetchResults(),
        fetchExams(),
        fetchStats()
      ])
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const fetchResults = async () => {
    try {
      const response = await adminGet('/api/coaching/exam-results')
      setResults(response.results || [])
    } catch (error) {
      console.error('Error fetching results:', error)
    }
  }

  const fetchExams = async () => {
    try {
      const response = await adminGet('/api/coaching/exams')
      setExams(response.exams || [])
    } catch (error) {
      console.error('Error fetching exams:', error)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await adminGet('/api/coaching/exam-results/stats')
      setStats(response.stats || stats)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handlePublishResults = async (examId: number) => {
    try {
      await adminPost(`/api/coaching/exams/${examId}/publish-results`)
      toast.success('Results published successfully!')
      fetchData()
    } catch (error) {
      toast.error('Failed to publish results')
    }
  }

  const handleDeleteResult = async (resultId: number) => {
    if (!confirm('Are you sure you want to delete this result?')) {
      return
    }

    try {
      await adminDelete(`/api/coaching/exam-results/${resultId}`)
      toast.success('Result deleted successfully!')
      fetchData()
    } catch (error) {
      toast.error('Failed to delete result')
    }
  }

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A+':
      case 'A':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'B+':
      case 'B':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'C+':
      case 'C':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      default:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const filteredResults = results.filter(result => {
    const matchesSearch = result.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         result.exam_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         result.course_name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === '' || result.status === statusFilter
    const matchesExam = examFilter === '' || result.exam_id.toString() === examFilter
    return matchesSearch && matchesStatus && matchesExam
  })

  if (loading) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">পরীক্ষার ফলাফল ব্যবস্থাপনা</h1>
            <p className="text-muted-foreground">
              পরীক্ষার ফলাফল দেখুন, পরিচালনা করুন এবং রিপোর্ট তৈরি করুন
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="mr-2 h-4 w-4" />
              ফিল্টার
            </Button>
            <Button onClick={() => navigate('/coaching/exams')}>
              <Plus className="mr-2 h-4 w-4" />
              নতুন পরীক্ষা
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                <div>
                  <div className="text-sm text-muted-foreground">মোট ফলাফল</div>
                  <div className="text-2xl font-bold">{stats.totalResults}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Award className="h-5 w-5 text-green-500" />
                <div>
                  <div className="text-sm text-muted-foreground">প্রকাশিত ফলাফল</div>
                  <div className="text-2xl font-bold">{stats.publishedResults}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-purple-500" />
                <div>
                  <div className="text-sm text-muted-foreground">গড় শতাংশ</div>
                  <div className="text-2xl font-bold">{(stats.averagePercentage || 0).toFixed(1)}%</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-orange-500" />
                <div>
                  <div className="text-sm text-muted-foreground">পাস রেট</div>
                  <div className="text-2xl font-bold">{(stats.passRate || 0).toFixed(1)}%</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        {showFilters && (
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="search">অনুসন্ধান</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      id="search"
                      placeholder="শিক্ষার্থী বা পরীক্ষার নাম..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">স্ট্যাটাস</Label>
                  <select
                    id="status"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-input bg-background rounded-md"
                  >
                    <option value="">সব স্ট্যাটাস</option>
                    <option value="published">প্রকাশিত</option>
                    <option value="draft">খসড়া</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="exam">পরীক্ষা</Label>
                  <select
                    id="exam"
                    value={examFilter}
                    onChange={(e) => setExamFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-input bg-background rounded-md"
                  >
                    <option value="">সব পরীক্ষা</option>
                    {exams.map(exam => (
                      <option key={exam.id} value={exam.id}>
                        {exam.exam_name} - {exam.course_name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label>দ্রুত অ্যাকশন</Label>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Download className="mr-2 h-4 w-4" />
                      এক্সপোর্ট
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Results List */}
        {filteredResults.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Award className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold mb-2">কোনো ফলাফল পাওয়া যায়নি</h3>
              <p className="text-muted-foreground mb-6">
                {searchTerm || statusFilter || examFilter
                  ? 'আপনার ফিল্টার অনুযায়ী কোনো ফলাফল পাওয়া যায়নি।'
                  : 'এখনো কোনো পরীক্ষার ফলাফল প্রবেশ করানো হয়নি।'
                }
              </p>
              <Button onClick={() => navigate('/coaching/exams')}>
                <Plus className="mr-2 h-4 w-4" />
                নতুন পরীক্ষা তৈরি করুন
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {filteredResults.map((result) => (
              <Card key={result.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                          <Award className="text-white h-6 w-6" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{result.student_name}</h3>
                          <p className="text-sm text-muted-foreground">{result.student_email}</p>
                        </div>
                        <div className="ml-auto">
                          <Badge className={getStatusColor(result.status)}>
                            {result.status === 'published' ? 'প্রকাশিত' : 'খসড়া'}
                          </Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                        <div className="flex items-center text-muted-foreground">
                          <FileText className="h-4 w-4 mr-2" />
                          <div>
                            <div className="font-medium">{result.exam_name}</div>
                            <div className="text-xs">{result.course_name}</div>
                          </div>
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          <Calendar className="h-4 w-4 mr-2" />
                          {new Date(result.exam_date).toLocaleDateString('bn-BD')}
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          <Award className="h-4 w-4 mr-2" />
                          <div>
                            <div className="font-medium">
                              {result.obtained_marks}/{result.total_marks}
                            </div>
                            <div className="text-xs">({(result.percentage || 0).toFixed(1)}%)</div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <Badge className={getGradeColor(result.grade)}>
                            গ্রেড: {result.grade}
                          </Badge>
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          {result.is_absent ? (
                            <Badge variant="destructive">অনুপস্থিত</Badge>
                          ) : result.position_in_batch ? (
                            <div className="text-xs">
                              <div>ব্যাচে অবস্থান: {result.position_in_batch}</div>
                              {result.position_overall && (
                                <div>সামগ্রিক: {result.position_overall}</div>
                              )}
                            </div>
                          ) : null}
                        </div>
                      </div>

                      {result.notes && (
                        <div className="mt-3 p-3 bg-muted rounded-md">
                          <div className="text-sm text-muted-foreground">
                            <strong>মন্তব্য:</strong> {result.notes}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigate(`/coaching/exams/${result.exam_id}/results`)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        দেখুন
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigate(`/coaching/students/${result.student_id}/report`)}
                      >
                        <Download className="h-4 w-4 mr-1" />
                        রিপোর্ট
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteResult(result.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Exams with Results Summary */}
        {exams.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                পরীক্ষার সারসংক্ষেপ
              </CardTitle>
              <CardDescription>
                ফলাফল সহ পরীক্ষাগুলির তালিকা
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {exams.filter(exam => exam.results_count > 0).map((exam) => (
                  <div key={exam.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{exam.exam_name}</div>
                      <div className="text-sm text-muted-foreground">
                        {exam.course_name} • {new Date(exam.exam_date).toLocaleDateString('bn-BD')}
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-sm text-muted-foreground">
                        {exam.results_count} টি ফলাফল
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => navigate(`/coaching/exams/${exam.id}/results`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          দেখুন
                        </Button>
                        {exam.status === 'completed' && (
                          <Button
                            size="sm"
                            onClick={() => handlePublishResults(exam.id)}
                          >
                            প্রকাশ করুন
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  )
}
