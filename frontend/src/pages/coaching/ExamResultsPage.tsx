import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import AdminLayout from '../../components/AdminLayout'
import { adminGet, adminPost } from '../../utils/adminApi'

interface Exam {
  exam_name: string
  total_marks: number
  passing_marks: number
}

interface Student {
  id: number
  name: string
  email: string
  profile_image_webp?: string
  class_name?: string
  class_section?: string
  roll_number?: string
  obtained_marks?: number
  percentage?: number
  grade?: string
  position_in_batch?: number
  position_overall?: number
  is_absent?: boolean
  notes?: string
  entered_at?: string
}

interface Batch {
  id: number
  name: string
}

export default function ExamResultsPage() {
  const { examId } = useParams<{ examId: string }>()
  const { t } = useTranslation()
  
  const [exam, setExam] = useState<Exam | null>(null)
  const [students, setStudents] = useState<Student[]>([])
  const [batches, setBatches] = useState<Batch[]>([])
  const [selectedBatch, setSelectedBatch] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [results, setResults] = useState<{ [key: number]: { marks: string; absent: boolean; notes: string } }>({})

  useEffect(() => {
    if (examId) {
      fetchExamDetails()
    }
  }, [examId])

  useEffect(() => {
    if (selectedBatch && examId) {
      fetchStudents()
    }
  }, [selectedBatch, examId])

  const fetchExamDetails = async () => {
    try {
      const response = await adminGet(`/api/coaching/exams/${examId}`)
      setExam(response.exam)
      setBatches(response.batches || [])
      
      if (response.batches && response.batches.length > 0) {
        setSelectedBatch(response.batches[0].id.toString())
      }
    } catch (error: any) {
      console.error('Error fetching exam details:', error)
      toast.error('Failed to load exam details')
    }
  }

  const fetchStudents = async () => {
    try {
      setLoading(true)
      const response = await adminGet(`/api/coaching/exams/${examId}/results/${selectedBatch}`)
      setStudents(response.students || [])
      
      // Initialize results state
      const initialResults: { [key: number]: { marks: string; absent: boolean; notes: string } } = {}
      response.students.forEach((student: Student) => {
        initialResults[student.id] = {
          marks: student.obtained_marks?.toString() || '',
          absent: student.is_absent || false,
          notes: student.notes || ''
        }
      })
      setResults(initialResults)
      
    } catch (error: any) {
      console.error('Error fetching students:', error)
      toast.error('Failed to load students')
    } finally {
      setLoading(false)
    }
  }

  const handleResultChange = (studentId: number, field: 'marks' | 'absent' | 'notes', value: string | boolean) => {
    setResults(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        [field]: value
      }
    }))
  }

  const handleSaveResults = async () => {
    try {
      setSaving(true)
      
      // Prepare results data
      const resultsData = students.map(student => ({
        student_id: student.id,
        batch_id: parseInt(selectedBatch),
        obtained_marks: results[student.id]?.absent ? 0 : parseFloat(results[student.id]?.marks || '0'),
        is_absent: results[student.id]?.absent || false,
        notes: results[student.id]?.notes || ''
      }))

      // Validate marks
      for (const result of resultsData) {
        if (!result.is_absent && (result.obtained_marks < 0 || result.obtained_marks > (exam?.total_marks || 0))) {
          toast.error(`Invalid marks for student. Marks should be between 0 and ${exam?.total_marks}`)
          return
        }
      }

      const response = await adminPost(`/api/coaching/exams/${examId}/results`, {
        results: resultsData
      })

      if (response.success) {
        toast.success('Results saved successfully!')
        fetchStudents() // Refresh to show calculated grades and positions
      }
    } catch (error: any) {
      console.error('Error saving results:', error)
      toast.error(`Failed to save results: ${error.message}`)
    } finally {
      setSaving(false)
    }
  }

  const markAllAbsent = () => {
    const updatedResults = { ...results }
    students.forEach(student => {
      updatedResults[student.id] = {
        ...updatedResults[student.id],
        absent: true,
        marks: '0'
      }
    })
    setResults(updatedResults)
    toast.success('All students marked as absent')
  }

  const markAllPresent = () => {
    const updatedResults = { ...results }
    students.forEach(student => {
      updatedResults[student.id] = {
        ...updatedResults[student.id],
        absent: false
      }
    })
    setResults(updatedResults)
    toast.success('All students marked as present')
  }

  if (loading && !exam) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Loading exam details...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Result Entry - {exam?.exam_name}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Total Marks: {exam?.total_marks} • Passing Marks: {exam?.passing_marks}
              </p>
            </div>
            <div className="flex gap-2">
              <button
                onClick={markAllAbsent}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
              >
                Mark All Absent
              </button>
              <button
                onClick={markAllPresent}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                Mark All Present
              </button>
              <button
                onClick={handleSaveResults}
                disabled={saving}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'Saving...' : 'Save Results'}
              </button>
            </div>
          </div>
        </div>

        {/* Batch Selection */}
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Select Batch:
            </label>
            <select
              value={selectedBatch}
              onChange={(e) => setSelectedBatch(e.target.value)}
              className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
            >
              {batches.map((batch) => (
                <option key={batch.id} value={batch.id}>
                  {batch.name}
                </option>
              ))}
            </select>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {students.length} students
            </span>
          </div>
        </div>

        {/* Results Entry Table */}
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Student Results
            </h2>
          </div>
          
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600 dark:text-gray-400">Loading students...</p>
            </div>
          ) : students.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No students found in this batch</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Student
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Roll No.
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Obtained Marks
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Grade
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Notes
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {students.map((student) => (
                    <tr key={student.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {student.profile_image_webp ? (
                            <img
                              src={student.profile_image_webp}
                              alt={student.name}
                              className="h-10 w-10 rounded-full object-cover mr-3"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                              <span className="text-gray-600 font-medium">
                                {student.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {student.name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {student.email}
                            </div>
                            {(student.class_name || student.class_section) && (
                              <div className="text-xs text-blue-600">
                                {student.class_name} {student.class_section && `- ${student.class_section}`}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {student.roll_number || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          min="0"
                          max={exam?.total_marks}
                          value={results[student.id]?.marks || ''}
                          onChange={(e) => handleResultChange(student.id, 'marks', e.target.value)}
                          disabled={results[student.id]?.absent}
                          className="w-20 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-sm dark:bg-gray-700 dark:text-white disabled:bg-gray-100 disabled:text-gray-500"
                          placeholder="0"
                        />
                        <span className="text-xs text-gray-500 ml-1">/ {exam?.total_marks}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={results[student.id]?.absent || false}
                            onChange={(e) => handleResultChange(student.id, 'absent', e.target.checked)}
                            className="mr-2"
                          />
                          <span className="text-sm text-gray-700 dark:text-gray-300">Absent</span>
                        </label>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {student.grade || '-'}
                        </div>
                        {student.percentage !== undefined && (
                          <div className="text-xs text-gray-500">
                            {student.percentage}%
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {student.position_in_batch ? `#${student.position_in_batch}` : '-'}
                        </div>
                        {student.position_overall && (
                          <div className="text-xs text-gray-500">
                            Overall: #{student.position_overall}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <input
                          type="text"
                          value={results[student.id]?.notes || ''}
                          onChange={(e) => handleResultChange(student.id, 'notes', e.target.value)}
                          className="w-32 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-sm dark:bg-gray-700 dark:text-white"
                          placeholder="Notes..."
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Save Button */}
        {students.length > 0 && (
          <div className="flex justify-end">
            <button
              onClick={handleSaveResults}
              disabled={saving}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium"
            >
              {saving ? 'Saving Results...' : 'Save All Results'}
            </button>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
