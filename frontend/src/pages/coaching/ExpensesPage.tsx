import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useAuth } from '../../contexts/AuthContext'
import { useApi } from '../../hooks/useApi'
import AdminLayout from '../../components/AdminLayout'

interface ExpenseType {
  id: number
  name: string
  description: string
  category: string
  is_active: boolean
}

interface PaymentGateway {
  id: number
  name: string
  type: string
  is_active: boolean
  is_default: boolean
}

interface Expense {
  id: number
  title: string
  description: string
  amount: number
  expense_date: string
  payment_method: string
  vendor_name: string
  vendor_contact: string
  receipt_number: string
  status: 'pending' | 'paid' | 'cancelled'
  expense_type_name: string
  category: string
  payment_gateway_name: string
  created_by_name: string
}

export default function ExpensesPage() {
  const { user } = useAuth()
  const { adminGet, adminPost, adminPut } = useApi()
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([])
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'expenses' | 'types' | 'gateways'>('expenses')
  const [showExpenseModal, setShowExpenseModal] = useState(false)
  const [showTypeModal, setShowTypeModal] = useState(false)
  const [showGatewayModal, setShowGatewayModal] = useState(false)
  const [statusFilter, setStatusFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')

  const [expenseFormData, setExpenseFormData] = useState({
    expense_type_id: '',
    title: '',
    description: '',
    amount: '',
    expense_date: new Date().toISOString().split('T')[0],
    payment_method: 'cash',
    payment_gateway_id: '',
    vendor_name: '',
    vendor_contact: '',
    receipt_number: '',
    notes: ''
  })

  const [typeFormData, setTypeFormData] = useState({
    name: '',
    description: '',
    category: 'other'
  })

  const [gatewayFormData, setGatewayFormData] = useState({
    name: '',
    type: 'cash',
    api_key: '',
    api_secret: '',
    webhook_url: '',
    is_active: true,
    is_default: false
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      const [expensesRes, typesRes, gatewaysRes] = await Promise.all([
        adminGet('/api/coaching/expenses'),
        adminGet('/api/coaching/expense-types'),
        adminGet('/api/coaching/payment-gateways')
      ])

      if (expensesRes.expenses) setExpenses(expensesRes.expenses)
      if (typesRes.expenseTypes) setExpenseTypes(typesRes.expenseTypes)
      if (gatewaysRes.gateways) setPaymentGateways(gatewaysRes.gateways)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error(`Failed to fetch data: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleExpenseSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const submitData = {
        ...expenseFormData,
        amount: parseFloat(expenseFormData.amount),
        expense_type_id: parseInt(expenseFormData.expense_type_id),
        payment_gateway_id: expenseFormData.payment_gateway_id ? parseInt(expenseFormData.payment_gateway_id) : null
      }

      const response = await adminPost('/api/coaching/expenses', submitData)
      if (response.success) {
        toast.success('Expense added successfully!')
        fetchData()
        resetExpenseForm()
      }
    } catch (error) {
      console.error('Error adding expense:', error)
      toast.error(`Failed to add expense: ${error.message}`)
    }
  }

  const handleTypeSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await adminPost('/api/coaching/expense-types', typeFormData)
      if (response.success) {
        toast.success('Expense type added successfully!')
        fetchData()
        resetTypeForm()
      }
    } catch (error) {
      console.error('Error adding expense type:', error)
      toast.error(`Failed to add expense type: ${error.message}`)
    }
  }

  const handleGatewaySubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await adminPost('/api/coaching/payment-gateways', gatewayFormData)
      if (response.success) {
        toast.success('Payment gateway added successfully!')
        fetchData()
        resetGatewayForm()
      }
    } catch (error) {
      console.error('Error adding payment gateway:', error)
      toast.error(`Failed to add payment gateway: ${error.message}`)
    }
  }

  const resetExpenseForm = () => {
    setExpenseFormData({
      expense_type_id: '',
      title: '',
      description: '',
      amount: '',
      expense_date: new Date().toISOString().split('T')[0],
      payment_method: 'cash',
      payment_gateway_id: '',
      vendor_name: '',
      vendor_contact: '',
      receipt_number: '',
      notes: ''
    })
    setShowExpenseModal(false)
  }

  const resetTypeForm = () => {
    setTypeFormData({
      name: '',
      description: '',
      category: 'other'
    })
    setShowTypeModal(false)
  }

  const resetGatewayForm = () => {
    setGatewayFormData({
      name: '',
      type: 'cash',
      api_key: '',
      api_secret: '',
      webhook_url: '',
      is_active: true,
      is_default: false
    })
    setShowGatewayModal(false)
  }

  const updateExpenseStatus = async (expenseId: number, status: string) => {
    try {
      const response = await adminPut(`/api/coaching/expenses/${expenseId}/status`, {
        status,
        payment_date: status === 'paid' ? new Date().toISOString().split('T')[0] : null,
        notes: status === 'paid' ? 'Payment processed' : null
      })

      if (response.success) {
        toast.success(`Expense marked as ${status}!`)
        fetchData() // Refresh the data
      } else {
        toast.error('Failed to update expense status')
      }
    } catch (error: any) {
      console.error('Error updating expense status:', error)
      toast.error(`Failed to update expense status: ${error.message}`)
    }
  }

  const filteredExpenses = expenses.filter(expense => {
    const matchesStatus = statusFilter === 'all' || expense.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || expense.category === categoryFilter
    return matchesStatus && matchesCategory
  })

  const categories = [
    { value: 'infrastructure', label: 'Infrastructure' },
    { value: 'utilities', label: 'Utilities' },
    { value: 'salaries', label: 'Salaries' },
    { value: 'marketing', label: 'Marketing' },
    { value: 'supplies', label: 'Supplies' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'transport', label: 'Transport' },
    { value: 'other', label: 'Other' }
  ]

  const paymentMethods = [
    { value: 'cash', label: 'Cash' },
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'upi', label: 'UPI' },
    { value: 'cheque', label: 'Cheque' },
    { value: 'card', label: 'Card' },
    { value: 'online', label: 'Online' }
  ]

  const gatewayTypes = [
    { value: 'cash', label: 'Cash Payment' },
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'upi', label: 'UPI Payment' },
    { value: 'cheque', label: 'Cheque Payment' },
    { value: 'razorpay', label: 'Razorpay' },
    { value: 'payu', label: 'PayU' },
    { value: 'stripe', label: 'Stripe' },
    { value: 'paypal', label: 'PayPal' },
    { value: 'phonepe', label: 'PhonePe' },
    { value: 'gpay', label: 'Google Pay' },
    { value: 'paytm', label: 'Paytm' }
  ]

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-lg">Loading expenses...</div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="px-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Expense Management</h1>
            <p className="text-gray-600 mt-2">Track and manage all coaching center expenses</p>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowExpenseModal(true)}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
            >
              Add Expense
            </button>
            <button
              onClick={() => setShowTypeModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Add Type
            </button>
            <button
              onClick={() => setShowGatewayModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
            >
              Add Gateway
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('expenses')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'expenses'
                  ? 'border-red-500 text-red-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Expenses ({expenses.length})
            </button>
            <button
              onClick={() => setActiveTab('types')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'types'
                  ? 'border-red-500 text-red-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Expense Types ({expenseTypes.length})
            </button>
            <button
              onClick={() => setActiveTab('gateways')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'gateways'
                  ? 'border-red-500 text-red-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Payment Gateways ({paymentGateways.length})
            </button>
          </nav>
        </div>

        {/* Expenses Tab */}
        {activeTab === 'expenses' && (
          <div>
            {/* Filters */}
            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Filter by Status</label>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="paid">Paid</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Filter by Category</label>
                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="all">All Categories</option>
                    {categories.map(cat => (
                      <option key={cat.value} value={cat.value}>{cat.label}</option>
                    ))}
                  </select>
                </div>
                <div className="flex items-end">
                  <div className="text-sm text-gray-600">
                    Total: ৳{filteredExpenses.reduce((sum, exp) => sum + parseFloat(exp.amount || 0), 0).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            {/* Expenses Table */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Expense Details
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount & Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Payment
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Vendor
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredExpenses.map((expense) => (
                      <tr key={expense.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{expense.title}</div>
                            <div className="text-sm text-gray-500">{expense.expense_type_name}</div>
                            <div className="text-xs text-gray-400">{expense.category}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">৳{expense.amount.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{new Date(expense.expense_date).toLocaleDateString()}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{expense.payment_method}</div>
                          <div className="text-sm text-gray-500">{expense.payment_gateway_name}</div>
                          <div className="text-xs text-gray-400">{expense.receipt_number}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{expense.vendor_name || 'N/A'}</div>
                          <div className="text-sm text-gray-500">{expense.vendor_contact || 'N/A'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              expense.status === 'paid'
                                ? 'bg-green-100 text-green-800'
                                : expense.status === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {expense.status}
                            </span>
                            {expense.status === 'pending' && (
                              <button
                                onClick={() => updateExpenseStatus(expense.id, 'paid')}
                                className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                                title="Mark as Paid"
                              >
                                Pay
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Expense Types Tab */}
        {activeTab === 'types' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {expenseTypes.map((type) => (
              <div key={type.id} className="bg-white rounded-lg shadow-md p-6 border">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold">{type.name}</h3>
                    <p className="text-gray-600 capitalize">{type.category}</p>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    type.is_active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {type.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <p className="text-gray-600 text-sm">{type.description}</p>
              </div>
            ))}
          </div>
        )}

        {/* Payment Gateways Tab */}
        {activeTab === 'gateways' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {paymentGateways.map((gateway) => (
              <div key={gateway.id} className="bg-white rounded-lg shadow-md p-6 border">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold">{gateway.name}</h3>
                    <p className="text-gray-600 capitalize">{gateway.type.replace('_', ' ')}</p>
                  </div>
                  <div className="flex gap-2">
                    {gateway.is_default && (
                      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                        Default
                      </span>
                    )}
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      gateway.is_active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {gateway.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Add Expense Modal */}
        {showExpenseModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
              <h2 className="text-2xl font-bold mb-4">Add New Expense</h2>
              <form onSubmit={handleExpenseSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Expense Type *</label>
                    <select
                      value={expenseFormData.expense_type_id}
                      onChange={(e) => setExpenseFormData(prev => ({ ...prev, expense_type_id: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    >
                      <option value="">Select Expense Type</option>
                      {expenseTypes.map(type => (
                        <option key={type.id} value={type.id}>{type.name}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Title *</label>
                    <input
                      type="text"
                      value={expenseFormData.title}
                      onChange={(e) => setExpenseFormData(prev => ({ ...prev, title: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Amount (৳) *</label>
                    <input
                      type="number"
                      value={expenseFormData.amount}
                      onChange={(e) => setExpenseFormData(prev => ({ ...prev, amount: e.target.value }))}
                      required
                      min="0"
                      step="0.01"
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Payment Method *</label>
                    <select
                      value={expenseFormData.payment_method}
                      onChange={(e) => setExpenseFormData(prev => ({ ...prev, payment_method: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    >
                      {paymentMethods.map(method => (
                        <option key={method.value} value={method.value}>{method.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Payment Gateway</label>
                    <select
                      value={expenseFormData.payment_gateway_id}
                      onChange={(e) => setExpenseFormData(prev => ({ ...prev, payment_gateway_id: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    >
                      <option value="">Select Gateway</option>
                      {paymentGateways.filter(g => g.is_active).map(gateway => (
                        <option key={gateway.id} value={gateway.id}>{gateway.name}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    type="submit"
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
                  >
                    Add Expense
                  </button>
                  <button
                    type="button"
                    onClick={resetExpenseForm}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Add Expense Type Modal */}
        {showTypeModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h2 className="text-2xl font-bold mb-4">Add Expense Type</h2>
              <form onSubmit={handleTypeSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Name *</label>
                  <input
                    type="text"
                    value={typeFormData.name}
                    onChange={(e) => setTypeFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Category</label>
                  <select
                    value={typeFormData.category}
                    onChange={(e) => setTypeFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    {categories.map(cat => (
                      <option key={cat.value} value={cat.value}>{cat.label}</option>
                    ))}
                  </select>
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                  >
                    Add Type
                  </button>
                  <button
                    type="button"
                    onClick={resetTypeForm}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Add Payment Gateway Modal */}
        {showGatewayModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
              <h2 className="text-2xl font-bold mb-4">Add Payment Gateway</h2>
              <form onSubmit={handleGatewaySubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Gateway Name *</label>
                    <input
                      type="text"
                      value={gatewayFormData.name}
                      onChange={(e) => setGatewayFormData(prev => ({ ...prev, name: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Gateway Type *</label>
                    <select
                      value={gatewayFormData.type}
                      onChange={(e) => setGatewayFormData(prev => ({ ...prev, type: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    >
                      {gatewayTypes.map(type => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    type="submit"
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
                  >
                    Add Gateway
                  </button>
                  <button
                    type="button"
                    onClick={resetGatewayForm}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}