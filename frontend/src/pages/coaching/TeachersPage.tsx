import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useAuth } from '../../contexts/AuthContext'
import { useApi } from '../../hooks/useApi'
import AdminLayout from '../../components/AdminLayout'

interface Teacher {
  id: number
  name: string
  email: string
  phone: string
  address: string
  qualification: string
  experience_years: number
  salary: number
  employee_id: string
  status: 'active' | 'inactive' | 'suspended'
  assigned_batches: number
  created_at: string
}

export default function TeachersPage() {
  const { user } = useAuth()
  const { adminGet, adminPost, adminPut } = useApi()
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    qualification: '',
    experience_years: '',
    salary: '',
    employee_id: ''
  })

  useEffect(() => {
    fetchTeachers()
  }, [])

  const fetchTeachers = async () => {
    try {
      setLoading(true)
      const response = await adminGet('/api/coaching/teachers')
      if (response.teachers) {
        setTeachers(response.teachers)
      }
    } catch (error) {
      console.error('Error fetching teachers:', error)
      toast.error(`Failed to fetch teachers: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const submitData = {
        ...formData,
        experience_years: formData.experience_years ? parseInt(formData.experience_years) : 0,
        salary: formData.salary ? parseFloat(formData.salary) : 0
      }

      if (editingTeacher) {
        const response = await adminPut(`/api/coaching/teachers/${editingTeacher.id}`, submitData)
        if (response.success) {
          toast.success('Teacher updated successfully!')
          fetchTeachers()
          resetForm()
        }
      } else {
        const response = await adminPost('/api/coaching/teachers', submitData)
        if (response.success) {
          toast.success(`Teacher added successfully! Default password: ${response.defaultPassword}`)
          fetchTeachers()
          resetForm()
        }
      }
    } catch (error) {
      console.error('Error saving teacher:', error)
      toast.error(`Failed to save teacher: ${error.message}`)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      address: '',
      qualification: '',
      experience_years: '',
      salary: '',
      employee_id: ''
    })
    setShowAddModal(false)
    setEditingTeacher(null)
  }

  const editTeacher = (teacher: Teacher) => {
    setEditingTeacher(teacher)
    setFormData({
      name: teacher.name,
      email: teacher.email,
      phone: teacher.phone || '',
      address: teacher.address || '',
      qualification: teacher.qualification || '',
      experience_years: teacher.experience_years?.toString() || '',
      salary: teacher.salary?.toString() || '',
      employee_id: teacher.employee_id || ''
    })
    setShowAddModal(true)
  }

  const filteredTeachers = teachers.filter(teacher =>
    teacher.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    teacher.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    teacher.employee_id?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-lg">Loading teachers...</div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="px-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Teachers Management</h1>
            <p className="text-gray-600 mt-2">Manage all teachers in your coaching center</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Add New Teacher
          </button>
        </div>

        {/* Search */}
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Search Teachers</label>
              <input
                type="text"
                placeholder="Search by name, email, or employee ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full border rounded-lg px-3 py-2"
              />
            </div>
            <div className="flex items-end">
              <div className="text-sm text-gray-600">
                Total Teachers: {filteredTeachers.length}
              </div>
            </div>
          </div>
        </div>

        {/* Teachers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTeachers.map((teacher) => (
            <div key={teacher.id} className="bg-white rounded-lg shadow-md p-6 border">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="text-xl font-semibold">{teacher.name}</h3>
                  <p className="text-gray-600">{teacher.email}</p>
                  <p className="text-sm text-gray-500">ID: {teacher.employee_id}</p>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  teacher.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {teacher.status}
                </span>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Phone:</span>
                  <span className="text-sm">{teacher.phone || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Experience:</span>
                  <span className="text-sm">{teacher.experience_years || 0} years</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Assigned Batches:</span>
                  <span className="text-sm">{teacher.assigned_batches}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Salary:</span>
                  <span className="text-sm">৳{teacher.salary?.toLocaleString() || 'N/A'}</span>
                </div>
              </div>

              {teacher.qualification && (
                <div className="mb-4">
                  <p className="text-sm text-gray-600">Qualification:</p>
                  <p className="text-sm">{teacher.qualification}</p>
                </div>
              )}

              <div className="flex gap-2">
                <button
                  onClick={() => editTeacher(teacher)}
                  className="flex-1 bg-blue-100 text-blue-800 px-3 py-2 rounded text-sm hover:bg-blue-200"
                >
                  Edit
                </button>
                <button
                  className="bg-gray-100 text-gray-800 px-3 py-2 rounded text-sm hover:bg-gray-200"
                >
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredTeachers.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold mb-2">No teachers found</h3>
            <p className="text-gray-600 mb-4">Get started by adding your first teacher.</p>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Add Teacher
            </button>
          </div>
        )}

        {/* Add/Edit Teacher Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <h2 className="text-2xl font-bold mb-4">
                {editingTeacher ? 'Edit Teacher' : 'Add New Teacher'}
              </h2>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Full Name *</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Email *</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Phone</label>
                    <input
                      type="text"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Employee ID</label>
                    <input
                      type="text"
                      value={formData.employee_id}
                      onChange={(e) => setFormData(prev => ({ ...prev, employee_id: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Address</label>
                  <textarea
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Qualification</label>
                  <input
                    type="text"
                    value={formData.qualification}
                    onChange={(e) => setFormData(prev => ({ ...prev, qualification: e.target.value }))}
                    placeholder="e.g., M.Sc. Mathematics, B.Tech Computer Science"
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Experience (Years)</label>
                    <input
                      type="number"
                      value={formData.experience_years}
                      onChange={(e) => setFormData(prev => ({ ...prev, experience_years: e.target.value }))}
                      min="0"
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Salary (৳)</label>
                    <input
                      type="number"
                      value={formData.salary}
                      onChange={(e) => setFormData(prev => ({ ...prev, salary: e.target.value }))}
                      min="0"
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                  >
                    {editingTeacher ? 'Update Teacher' : 'Add Teacher'}
                  </button>
                  <button
                    type="button"
                    onClick={resetForm}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
