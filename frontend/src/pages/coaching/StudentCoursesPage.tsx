import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import AdminLayout from '../../components/AdminLayout'
import { adminGet, adminPost } from '../../utils/adminApi'

interface Course {
  id: number
  name: string
  code: string
  fee: number
}

interface Batch {
  id: number
  name: string
  course_id: number
}

interface StudentCourse {
  id: number
  course_name: string
  course_code: string
  batch_name: string
  enrollment_type: 'monthly' | 'one_time'
  monthly_fee: number
  one_time_fee: number
  discount_amount: number
  enrollment_date: string
  status: string
  invoice_number: string
  payment_status: string
  total_amount: number
}

interface Student {
  id: number
  name: string
  email: string
  class_name: string
  class_section: string
}

export default function StudentCoursesPage() {
  const { studentId } = useParams<{ studentId: string }>()
  const { t } = useTranslation()
  
  const [student, setStudent] = useState<Student | null>(null)
  const [studentCourses, setStudentCourses] = useState<StudentCourse[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [batches, setBatches] = useState<Batch[]>([])
  const [loading, setLoading] = useState(true)
  const [showAssignModal, setShowAssignModal] = useState(false)
  
  const [assignmentForm, setAssignmentForm] = useState({
    course_id: '',
    batch_id: '',
    enrollment_type: 'monthly' as 'monthly' | 'one_time',
    monthly_fee: '',
    one_time_fee: '',
    discount_amount: '0',
    discount_percentage: '0',
    notes: ''
  })

  useEffect(() => {
    if (studentId) {
      fetchData()
    }
  }, [studentId])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch student details
      const studentResponse = await adminGet(`/api/coaching/students/${studentId}`)
      setStudent(studentResponse.student)
      
      // Fetch student courses
      const coursesResponse = await adminGet(`/api/coaching/students/${studentId}/courses`)
      setStudentCourses(coursesResponse.courses)
      
      // Fetch available courses
      const allCoursesResponse = await adminGet('/api/coaching/courses')
      setCourses(allCoursesResponse.courses)
      
      // Fetch batches
      const batchesResponse = await adminGet('/api/coaching/batches')
      setBatches(batchesResponse.batches)
      
    } catch (error: any) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const handleAssignCourse = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!assignmentForm.course_id || !assignmentForm.enrollment_type) {
      toast.error('Please select course and enrollment type')
      return
    }

    try {
      const response = await adminPost(`/api/coaching/students/${studentId}/courses`, {
        ...assignmentForm,
        monthly_fee: parseFloat(assignmentForm.monthly_fee) || 0,
        one_time_fee: parseFloat(assignmentForm.one_time_fee) || 0,
        discount_amount: parseFloat(assignmentForm.discount_amount) || 0,
        discount_percentage: parseFloat(assignmentForm.discount_percentage) || 0
      })

      if (response.success) {
        toast.success('Course assigned successfully!')
        setShowAssignModal(false)
        resetAssignmentForm()
        fetchData()
      }
    } catch (error: any) {
      console.error('Error assigning course:', error)
      toast.error(`Failed to assign course: ${error.message}`)
    }
  }

  const resetAssignmentForm = () => {
    setAssignmentForm({
      course_id: '',
      batch_id: '',
      enrollment_type: 'monthly',
      monthly_fee: '',
      one_time_fee: '',
      discount_amount: '0',
      discount_percentage: '0',
      notes: ''
    })
  }

  const getFilteredBatches = () => {
    return batches.filter(batch => batch.course_id.toString() === assignmentForm.course_id)
  }

  const viewInvoice = (invoiceNumber: string) => {
    window.open(`/coaching/invoices/${invoiceNumber}`, '_blank')
  }

  const printInvoice = (invoiceNumber: string) => {
    window.open(`/coaching/invoices/${invoiceNumber}/print`, '_blank')
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Student Info Header */}
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Course Assignments - {student?.name}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {student?.email} • {student?.class_name} {student?.class_section}
              </p>
            </div>
            <button
              onClick={() => setShowAssignModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Assign New Course
            </button>
          </div>
        </div>

        {/* Assigned Courses */}
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Assigned Courses
            </h2>
          </div>
          
          {studentCourses.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No courses assigned yet</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Course
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Type & Fee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Invoice
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {studentCourses.map((course) => (
                    <tr key={course.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {course.course_name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {course.course_code} • {course.batch_name}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {course.enrollment_type === 'monthly' ? 'Monthly' : 'One-time'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            ৳{course.enrollment_type === 'monthly' ? course.monthly_fee : course.one_time_fee}
                            {course.discount_amount > 0 && (
                              <span className="text-green-600"> (-৳{course.discount_amount})</span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {course.invoice_number}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            ৳{course.total_amount}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          course.payment_status === 'paid' 
                            ? 'bg-green-100 text-green-800' 
                            : course.payment_status === 'overdue'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {course.payment_status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <button
                            onClick={() => viewInvoice(course.invoice_number)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            View Invoice
                          </button>
                          <button
                            onClick={() => printInvoice(course.invoice_number)}
                            className="text-green-600 hover:text-green-900"
                          >
                            Print
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Assignment Modal */}
      {showAssignModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Assign Course
              </h3>
              <button
                onClick={() => setShowAssignModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleAssignCourse} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Course *</label>
                <select
                  required
                  value={assignmentForm.course_id}
                  onChange={(e) => setAssignmentForm(prev => ({ ...prev, course_id: e.target.value, batch_id: '' }))}
                  className="w-full border rounded-lg px-3 py-2"
                >
                  <option value="">Select Course</option>
                  {courses.map((course) => (
                    <option key={course.id} value={course.id}>
                      {course.name} - ৳{course.fee}
                    </option>
                  ))}
                </select>
              </div>

              {assignmentForm.course_id && (
                <div>
                  <label className="block text-sm font-medium mb-1">Batch</label>
                  <select
                    value={assignmentForm.batch_id}
                    onChange={(e) => setAssignmentForm(prev => ({ ...prev, batch_id: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="">Select Batch (Optional)</option>
                    {getFilteredBatches().map((batch) => (
                      <option key={batch.id} value={batch.id}>
                        {batch.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-1">Enrollment Type *</label>
                <div className="flex gap-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="monthly"
                      checked={assignmentForm.enrollment_type === 'monthly'}
                      onChange={(e) => setAssignmentForm(prev => ({ ...prev, enrollment_type: e.target.value as 'monthly' }))}
                      className="mr-2"
                    />
                    Monthly
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="one_time"
                      checked={assignmentForm.enrollment_type === 'one_time'}
                      onChange={(e) => setAssignmentForm(prev => ({ ...prev, enrollment_type: e.target.value as 'one_time' }))}
                      className="mr-2"
                    />
                    One-time
                  </label>
                </div>
              </div>

              {assignmentForm.enrollment_type === 'monthly' ? (
                <div>
                  <label className="block text-sm font-medium mb-1">Monthly Fee *</label>
                  <input
                    type="number"
                    required
                    value={assignmentForm.monthly_fee}
                    onChange={(e) => setAssignmentForm(prev => ({ ...prev, monthly_fee: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="Enter monthly fee"
                  />
                </div>
              ) : (
                <div>
                  <label className="block text-sm font-medium mb-1">One-time Fee *</label>
                  <input
                    type="number"
                    required
                    value={assignmentForm.one_time_fee}
                    onChange={(e) => setAssignmentForm(prev => ({ ...prev, one_time_fee: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="Enter one-time fee"
                  />
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Discount Amount</label>
                  <input
                    type="number"
                    value={assignmentForm.discount_amount}
                    onChange={(e) => setAssignmentForm(prev => ({ ...prev, discount_amount: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Discount %</label>
                  <input
                    type="number"
                    value={assignmentForm.discount_percentage}
                    onChange={(e) => setAssignmentForm(prev => ({ ...prev, discount_percentage: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="0"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Notes</label>
                <textarea
                  value={assignmentForm.notes}
                  onChange={(e) => setAssignmentForm(prev => ({ ...prev, notes: e.target.value }))}
                  className="w-full border rounded-lg px-3 py-2"
                  rows={3}
                  placeholder="Additional notes..."
                />
              </div>

              <div className="flex gap-2 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700"
                >
                  Assign Course
                </button>
                <button
                  type="button"
                  onClick={() => setShowAssignModal(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </AdminLayout>
  )
}
