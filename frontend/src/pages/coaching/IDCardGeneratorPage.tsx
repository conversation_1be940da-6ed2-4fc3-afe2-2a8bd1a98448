import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import AdminLayout from '../../components/AdminLayout'
import { adminGet, adminPost } from '../../utils/adminApi'
import { toast } from 'react-hot-toast'
import { 
  CreditCard, 
  Download, 
  Eye, 
  Image, 
  Palette, 
  Plus, 
  Save, 
  Settings,
  User,
  Calendar,
  MapPin,
  QrCode,
  Printer
} from 'lucide-react'

interface Student {
  id: number
  name: string
  email: string
  phone: string
  student_id?: string
  course_name?: string
  batch_name?: string
  admission_date: string
  blood_group?: string
  profile_image_webp?: string
  class_name?: string
  class_section?: string
}

interface IDCardTemplate {
  id: number
  name: string
  card_type: 'student' | 'staff' | 'visitor'
  orientation: 'portrait' | 'landscape'
  background_color: string
  text_color: string
  layout: 'classic' | 'modern' | 'minimal'
  include_photo: boolean
  include_qr: boolean
  include_barcode: boolean
  is_active: boolean
}

interface IDCardData {
  student_id: number
  template_id: number
  card_title: string
  validity_date: string
  emergency_contact?: string
  custom_fields: Record<string, string>
  card_side: 'front' | 'back'
}

const IDCardGeneratorPage: React.FC = () => {
  const navigate = useNavigate()
  const frontCanvasRef = useRef<HTMLCanvasElement>(null)
  const backCanvasRef = useRef<HTMLCanvasElement>(null)
  
  const [students, setStudents] = useState<Student[]>([])
  const [templates, setTemplates] = useState<IDCardTemplate[]>([])
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<IDCardTemplate | null>(null)
  const [currentSide, setCurrentSide] = useState<'front' | 'back'>('front')
  const [showPreview, setShowPreview] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  
  const [cardData, setCardData] = useState<IDCardData>({
    student_id: 0,
    template_id: 0,
    card_title: 'Student ID Card',
    validity_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year from now
    emergency_contact: '',
    custom_fields: {},
    card_side: 'front'
  })

  const [centerInfo, setCenterInfo] = useState({
    name: 'ABC Coaching Center',
    address: 'Dhaka, Bangladesh',
    phone: '+880 1234567890',
    email: '<EMAIL>',
    logo: ''
  })

  useEffect(() => {
    fetchStudents()
    fetchTemplates()
    fetchCenterInfo()
  }, [])

  const fetchStudents = async () => {
    try {
      const response = await adminGet('/api/coaching/students')
      setStudents(response.students || [])
    } catch (error) {
      console.error('Error fetching students:', error)
      toast.error('শিক্ষার্থীদের তালিকা লোড করতে ব্যর্থ')
    }
  }

  const fetchTemplates = async () => {
    try {
      const response = await adminGet('/api/coaching/id-card-templates')
      setTemplates(response.templates || [])
    } catch (error) {
      console.error('Error fetching templates:', error)
      // Create default templates if none exist
      setTemplates([
        {
          id: 1,
          name: 'Classic Student ID',
          card_type: 'student',
          orientation: 'portrait',
          background_color: '#ffffff',
          text_color: '#000000',
          layout: 'classic',
          include_photo: true,
          include_qr: true,
          include_barcode: false,
          is_active: true
        },
        {
          id: 2,
          name: 'Modern Student ID',
          card_type: 'student',
          orientation: 'portrait',
          background_color: '#f8f9fa',
          text_color: '#1e40af',
          layout: 'modern',
          include_photo: true,
          include_qr: true,
          include_barcode: true,
          is_active: true
        }
      ])
    }
  }

  const fetchCenterInfo = async () => {
    try {
      const response = await adminGet('/api/coaching/center-info')
      if (response.center) {
        setCenterInfo(response.center)
      }
    } catch (error) {
      console.error('Error fetching center info:', error)
    }
  }

  const handleStudentSelect = (student: Student) => {
    setSelectedStudent(student)
    setCardData(prev => ({
      ...prev,
      student_id: student.id,
      emergency_contact: student.phone || ''
    }))
  }

  const handleTemplateSelect = (template: IDCardTemplate) => {
    setSelectedTemplate(template)
    setCardData(prev => ({
      ...prev,
      template_id: template.id
    }))
  }

  const generatePreview = () => {
    if (!selectedStudent || !selectedTemplate) {
      toast.error('অনুগ্রহ করে শিক্ষার্থী এবং টেমপ্লেট নির্বাচন করুন')
      return
    }

    setShowPreview(true)
    drawIDCard('front')
    drawIDCard('back')
  }

  const drawIDCard = (side: 'front' | 'back') => {
    const canvas = side === 'front' ? frontCanvasRef.current : backCanvasRef.current
    if (!canvas || !selectedStudent || !selectedTemplate) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size (ID card size: 85.6mm x 53.98mm at 300 DPI)
    canvas.width = 320
    canvas.height = 200

    // Clear canvas
    ctx.fillStyle = selectedTemplate.background_color
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Draw border
    ctx.strokeStyle = selectedTemplate.text_color
    ctx.lineWidth = 2
    ctx.strokeRect(5, 5, canvas.width - 10, canvas.height - 10)

    if (side === 'front') {
      drawFrontSide(ctx)
    } else {
      drawBackSide(ctx)
    }
  }

  const drawFrontSide = (ctx: CanvasRenderingContext2D) => {
    if (!selectedStudent || !selectedTemplate) return

    // Center logo/name
    ctx.fillStyle = selectedTemplate.text_color
    ctx.font = 'bold 14px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(centerInfo.name, canvas.width / 2, 25)

    // Card title
    ctx.font = '10px Arial'
    ctx.fillText(cardData.card_title, canvas.width / 2, 40)

    // Student photo placeholder
    if (selectedTemplate.include_photo) {
      ctx.strokeStyle = selectedTemplate.text_color
      ctx.lineWidth = 1
      ctx.strokeRect(20, 50, 60, 80)
      
      // Photo placeholder text
      ctx.font = '8px Arial'
      ctx.textAlign = 'center'
      ctx.fillText('PHOTO', 50, 95)
      
      if (selectedStudent.profile_image_webp) {
        // Load and draw actual photo
        const img = new Image()
        img.onload = () => {
          ctx.drawImage(img, 20, 50, 60, 80)
        }
        img.src = selectedStudent.profile_image_webp
      }
    }

    // Student information
    ctx.font = '10px Arial'
    ctx.textAlign = 'left'
    ctx.fillText(`Name: ${selectedStudent.name}`, 90, 65)
    ctx.fillText(`ID: ${selectedStudent.student_id || selectedStudent.id}`, 90, 80)
    ctx.fillText(`Class: ${selectedStudent.class_name || 'N/A'}`, 90, 95)
    ctx.fillText(`Section: ${selectedStudent.class_section || 'N/A'}`, 90, 110)
    
    if (selectedStudent.blood_group) {
      ctx.fillText(`Blood: ${selectedStudent.blood_group}`, 90, 125)
    }

    // QR Code placeholder
    if (selectedTemplate.include_qr) {
      ctx.strokeRect(canvas.width - 50, canvas.height - 50, 40, 40)
      ctx.font = '6px Arial'
      ctx.textAlign = 'center'
      ctx.fillText('QR', canvas.width - 30, canvas.height - 25)
    }

    // Validity date
    ctx.font = '8px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(`Valid Until: ${new Date(cardData.validity_date).toLocaleDateString()}`, canvas.width / 2, canvas.height - 10)
  }

  const drawBackSide = (ctx: CanvasRenderingContext2D) => {
    if (!selectedStudent || !selectedTemplate) return

    // Instructions/Rules
    ctx.fillStyle = selectedTemplate.text_color
    ctx.font = 'bold 12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('INSTRUCTIONS', canvas.width / 2, 30)

    ctx.font = '8px Arial'
    ctx.textAlign = 'left'
    const instructions = [
      '1. This card is non-transferable',
      '2. Report if lost immediately',
      '3. Valid for current academic year',
      '4. Must be carried at all times',
      '5. Contact center for renewal'
    ]

    instructions.forEach((instruction, index) => {
      ctx.fillText(instruction, 20, 50 + (index * 15))
    })

    // Emergency contact
    ctx.font = 'bold 10px Arial'
    ctx.fillText('Emergency Contact:', 20, 140)
    ctx.font = '9px Arial'
    ctx.fillText(cardData.emergency_contact || centerInfo.phone, 20, 155)

    // Center contact
    ctx.font = '8px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(centerInfo.address, canvas.width / 2, canvas.height - 25)
    ctx.fillText(centerInfo.phone, canvas.width / 2, canvas.height - 10)

    // Barcode placeholder
    if (selectedTemplate.include_barcode) {
      ctx.strokeRect(canvas.width - 80, canvas.height - 40, 70, 20)
      ctx.font = '6px Arial'
      ctx.fillText('BARCODE', canvas.width - 45, canvas.height - 25)
    }
  }

  const downloadIDCard = async () => {
    if (!selectedStudent || !selectedTemplate) {
      toast.error('অনুগ্রহ করে শিক্ষার্থী এবং টেমপ্লেট নির্বাচন করুন')
      return
    }

    setIsGenerating(true)
    
    try {
      // Create a combined canvas for both sides
      const combinedCanvas = document.createElement('canvas')
      const combinedCtx = combinedCanvas.getContext('2d')
      if (!combinedCtx) return

      // Set size for both cards side by side
      combinedCanvas.width = 640 // Two cards side by side
      combinedCanvas.height = 200

      // Draw front side
      const frontCanvas = frontCanvasRef.current
      if (frontCanvas) {
        combinedCtx.drawImage(frontCanvas, 0, 0)
      }

      // Draw back side
      const backCanvas = backCanvasRef.current
      if (backCanvas) {
        combinedCtx.drawImage(backCanvas, 320, 0)
      }

      // Add labels
      combinedCtx.fillStyle = '#000000'
      combinedCtx.font = '12px Arial'
      combinedCtx.textAlign = 'center'
      combinedCtx.fillText('FRONT', 160, 15)
      combinedCtx.fillText('BACK', 480, 15)

      // Convert to blob and download
      combinedCanvas.toBlob(async (blob) => {
        if (!blob) return

        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `id-card-${selectedStudent.name.replace(/\s+/g, '-')}-${Date.now()}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        toast.success('আইডি কার্ড সফলভাবে ডাউনলোড হয়েছে!')
      }, 'image/png')

    } catch (error) {
      console.error('Error generating ID card:', error)
      toast.error('আইডি কার্ড তৈরি করতে ব্যর্থ')
    } finally {
      setIsGenerating(false)
    }
  }

  const printIDCard = () => {
    if (!showPreview) {
      toast.error('প্রথমে প্রিভিউ তৈরি করুন')
      return
    }

    // Create print window
    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    const frontCanvas = frontCanvasRef.current
    const backCanvas = backCanvasRef.current

    if (frontCanvas && backCanvas) {
      const frontDataUrl = frontCanvas.toDataURL()
      const backDataUrl = backCanvas.toDataURL()

      printWindow.document.write(`
        <html>
          <head>
            <title>ID Card - ${selectedStudent?.name}</title>
            <style>
              body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
              .card-container { display: flex; gap: 20px; justify-content: center; }
              .card { text-align: center; }
              .card img { border: 1px solid #ccc; }
              .card h3 { margin: 10px 0 5px 0; }
              @media print {
                body { margin: 0; padding: 10px; }
                .card-container { gap: 10px; }
              }
            </style>
          </head>
          <body>
            <div class="card-container">
              <div class="card">
                <h3>Front</h3>
                <img src="${frontDataUrl}" alt="ID Card Front" />
              </div>
              <div class="card">
                <h3>Back</h3>
                <img src="${backDataUrl}" alt="ID Card Back" />
              </div>
            </div>
            <script>
              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                }
              }
            </script>
          </body>
        </html>
      `)
      printWindow.document.close()
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">আইডি কার্ড জেনারেটর</h1>
            <p className="text-gray-600">শিক্ষার্থীদের জন্য কাস্টমাইজড আইডি কার্ড তৈরি করুন</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Panel - Configuration */}
          <div className="lg:col-span-1 space-y-6">
            {/* Student Selection */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <User className="h-5 w-5" />
                শিক্ষার্থী নির্বাচন
              </h3>
              <select
                value={selectedStudent?.id || ''}
                onChange={(e) => {
                  const student = students.find(s => s.id === parseInt(e.target.value))
                  if (student) handleStudentSelect(student)
                }}
                className="w-full border rounded-lg px-3 py-2"
              >
                <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                {students.map(student => (
                  <option key={student.id} value={student.id}>
                    {student.name} - {student.class_name || 'No Class'}
                  </option>
                ))}
              </select>
              
              {selectedStudent && (
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm"><strong>নাম:</strong> {selectedStudent.name}</p>
                  <p className="text-sm"><strong>ক্লাস:</strong> {selectedStudent.class_name || 'N/A'}</p>
                  <p className="text-sm"><strong>সেকশন:</strong> {selectedStudent.class_section || 'N/A'}</p>
                  <p className="text-sm"><strong>রক্তের গ্রুপ:</strong> {selectedStudent.blood_group || 'N/A'}</p>
                </div>
              )}
            </div>

            {/* Template Selection */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                টেমপ্লেট নির্বাচন
              </h3>
              <div className="space-y-2">
                {templates.map(template => (
                  <button
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className={`w-full text-left p-3 rounded-lg border transition-colors ${
                      selectedTemplate?.id === template.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium">{template.name}</div>
                    <div className="text-sm text-gray-500 capitalize">{template.layout} layout</div>
                    <div className="text-xs text-gray-400 flex gap-2 mt-1">
                      {template.include_photo && <span>📷 Photo</span>}
                      {template.include_qr && <span>📱 QR</span>}
                      {template.include_barcode && <span>📊 Barcode</span>}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Card Settings */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Settings className="h-5 w-5" />
                কার্ড সেটিংস
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">কার্ড শিরোনাম</label>
                  <input
                    type="text"
                    value={cardData.card_title}
                    onChange={(e) => setCardData(prev => ({ ...prev, card_title: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="Student ID Card"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">মেয়াদ উত্তীর্ণের তারিখ</label>
                  <input
                    type="date"
                    value={cardData.validity_date}
                    onChange={(e) => setCardData(prev => ({ ...prev, validity_date: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">জরুরি যোগাযোগ</label>
                  <input
                    type="text"
                    value={cardData.emergency_contact}
                    onChange={(e) => setCardData(prev => ({ ...prev, emergency_contact: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="Emergency contact number"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - Preview and Controls */}
          <div className="lg:col-span-2 space-y-6">
            {/* Action Buttons */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex gap-4">
                <button
                  onClick={generatePreview}
                  disabled={!selectedStudent || !selectedTemplate}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Eye className="h-4 w-4" />
                  প্রিভিউ তৈরি করুন
                </button>
                <button
                  onClick={downloadIDCard}
                  disabled={!showPreview || isGenerating}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Download className="h-4 w-4" />
                  {isGenerating ? 'তৈরি হচ্ছে...' : 'ডাউনলোড করুন'}
                </button>
                <button
                  onClick={printIDCard}
                  disabled={!showPreview}
                  className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Printer className="h-4 w-4" />
                  প্রিন্ট করুন
                </button>
              </div>
            </div>

            {/* Preview */}
            {showPreview && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  আইডি কার্ড প্রিভিউ
                </h3>
                
                {/* Side Toggle */}
                <div className="flex gap-2 mb-4">
                  <button
                    onClick={() => setCurrentSide('front')}
                    className={`px-4 py-2 rounded-lg ${
                      currentSide === 'front'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    সামনে
                  </button>
                  <button
                    onClick={() => setCurrentSide('back')}
                    className={`px-4 py-2 rounded-lg ${
                      currentSide === 'back'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    পিছনে
                  </button>
                </div>

                <div className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex justify-center gap-8">
                    <div className={`${currentSide === 'front' ? 'block' : 'hidden'}`}>
                      <h4 className="text-center mb-2 font-medium">সামনে</h4>
                      <canvas
                        ref={frontCanvasRef}
                        className="border border-gray-300 rounded shadow-lg"
                        style={{ maxWidth: '100%', height: 'auto' }}
                      />
                    </div>
                    <div className={`${currentSide === 'back' ? 'block' : 'hidden'}`}>
                      <h4 className="text-center mb-2 font-medium">পিছনে</h4>
                      <canvas
                        ref={backCanvasRef}
                        className="border border-gray-300 rounded shadow-lg"
                        style={{ maxWidth: '100%', height: 'auto' }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}

export default IDCardGeneratorPage
