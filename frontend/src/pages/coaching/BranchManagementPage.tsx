import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import { useBranch } from '../../contexts/BranchContext'
import { useApi } from '../../hooks/useApi'
import AdminLayout from '../../components/AdminLayout'

interface Branch {
  id: number
  branch_code: string
  branch_name: string
  address: string
  phone: string
  email: string
  manager_id: number
  manager_name: string
  establishment_date: string
  status: 'active' | 'inactive'
  total_users: number
  total_rooms: number
  created_at: string
}

interface Room {
  id: number
  branch_id: number
  room_code: string
  room_name: string
  room_type: 'classroom' | 'lab' | 'auditorium' | 'library' | 'office' | 'other'
  capacity: number
  equipment: string
  status: 'active' | 'inactive' | 'maintenance'
  notes: string
  scheduled_classes: number
  today_classes: number
}

interface Schedule {
  id: number
  branch_id: number
  course_id: number
  course_name: string
  batch_id: number
  batch_name: string
  room_id: number
  room_name: string
  room_code: string
  teacher_id: number
  teacher_name: string
  day_of_week: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday'
  start_time: string
  end_time: string
  effective_from: string
  effective_to: string
  status: 'active' | 'inactive' | 'cancelled'
  notes: string
}

// Utility function to format time in 12-hour format with AM/PM
const formatTime12Hour = (time24: string) => {
  if (!time24) return ''
  const [hours, minutes] = time24.split(':')
  const hour = parseInt(hours)
  const ampm = hour >= 12 ? 'PM' : 'AM'
  const hour12 = hour % 12 || 12
  return `${hour12}:${minutes} ${ampm}`
}

export default function BranchManagementPage() {
  const { user } = useAuth()
  const { t } = useLanguage()
  const { currentBranch } = useBranch()
  const { adminGet, adminPost, adminPut } = useApi()
  
  const [activeTab, setActiveTab] = useState<'branches' | 'rooms' | 'schedules'>('branches')
  const [branches, setBranches] = useState<Branch[]>([])
  const [rooms, setRooms] = useState<Room[]>([])
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [filteredSchedules, setFilteredSchedules] = useState<Schedule[]>([])
  const [loading, setLoading] = useState(false)
  const [showBranchForm, setShowBranchForm] = useState(false)
  const [showRoomForm, setShowRoomForm] = useState(false)
  const [showScheduleForm, setShowScheduleForm] = useState(false)
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null)
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null)
  const [selectedSchedule, setSelectedSchedule] = useState<Schedule | null>(null)
  const [scheduleConflicts, setScheduleConflicts] = useState<any[]>([])

  // Filter states
  const [filters, setFilters] = useState({
    room: '',
    course: '',
    teacher: '',
    day: ''
  })

  const [branchFormData, setBranchFormData] = useState({
    branch_code: '',
    branch_name: '',
    address: '',
    phone: '',
    email: '',
    manager_id: '',
    establishment_date: new Date().toISOString().split('T')[0]
  })

  const [roomFormData, setRoomFormData] = useState({
    room_code: '',
    room_name: '',
    room_type: 'classroom',
    capacity: '',
    equipment: '',
    notes: ''
  })

  const [scheduleFormData, setScheduleFormData] = useState({
    course_id: '',
    batch_id: '',
    room_id: '',
    teacher_id: '', // Primary teacher
    effective_from: new Date().toISOString().split('T')[0],
    effective_to: '',
    notes: '',
    weekly_schedule: {
      saturday: { start_time: '', end_time: '', teacher_id: '' },
      sunday: { start_time: '', end_time: '', teacher_id: '' },
      monday: { start_time: '', end_time: '', teacher_id: '' },
      tuesday: { start_time: '', end_time: '', teacher_id: '' },
      wednesday: { start_time: '', end_time: '', teacher_id: '' },
      thursday: { start_time: '', end_time: '', teacher_id: '' },
      friday: { start_time: '', end_time: '', teacher_id: '' }
    }
  })

  // Available options for dropdowns
  const [courses, setCourses] = useState([])
  const [batches, setBatches] = useState([])
  const [teachers, setTeachers] = useState([])

  useEffect(() => {
    fetchData()
  }, [activeTab, currentBranch])

  const fetchData = async () => {
    try {
      setLoading(true)
      if (activeTab === 'branches') {
        const response = await adminGet('/api/coaching/branches')
        setBranches(response.branches || [])
      } else if (activeTab === 'rooms') {
        const response = await adminGet(`/api/coaching/rooms?branch_id=${currentBranch?.id}`)
        setRooms(response.rooms || [])
      } else if (activeTab === 'schedules') {
        const [schedulesRes, coursesRes, batchesRes, teachersRes] = await Promise.all([
          adminGet(`/api/coaching/schedules?branch_id=${currentBranch?.id}`),
          adminGet(`/api/coaching/courses?branch_id=${currentBranch?.id}`),
          adminGet(`/api/coaching/batches?branch_id=${currentBranch?.id}`),
          adminGet(`/api/coaching/teachers?branch_id=${currentBranch?.id}`)
        ])
        setSchedules(schedulesRes.schedules || [])
        setCourses(coursesRes.courses || [])
        setBatches(batchesRes.batches || [])
        setTeachers(teachersRes.teachers || [])
      }
    } catch (error: any) {
      console.error('Error fetching data:', error)
      toast.error('Failed to fetch data')
    } finally {
      setLoading(false)
    }
  }

  const handleBranchSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = selectedBranch
        ? await adminPut(`/api/coaching/branches/${selectedBranch.id}`, branchFormData)
        : await adminPost('/api/coaching/branches', branchFormData)
      
      if (response.success) {
        toast.success(selectedBranch ? 'Branch updated successfully!' : 'Branch created successfully!')
        setShowBranchForm(false)
        setSelectedBranch(null)
        setBranchFormData({
          branch_code: '',
          branch_name: '',
          address: '',
          phone: '',
          email: '',
          manager_id: '',
          establishment_date: new Date().toISOString().split('T')[0]
        })
        fetchData()
      }
    } catch (error: any) {
      console.error('Error saving branch:', error)
      toast.error(`Failed to save branch: ${error.message}`)
    }
  }

  const handleRoomSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = selectedRoom
        ? await adminPut(`/api/coaching/rooms/${selectedRoom.id}`, roomFormData)
        : await adminPost(`/api/coaching/rooms?branch_id=${currentBranch?.id}`, roomFormData)

      if (response.success) {
        toast.success(selectedRoom ? 'Room updated successfully!' : 'Room created successfully!')
        setShowRoomForm(false)
        setSelectedRoom(null)
        setRoomFormData({
          room_code: '',
          room_name: '',
          room_type: 'classroom',
          capacity: '',
          equipment: '',
          notes: ''
        })
        fetchData()
      }
    } catch (error: any) {
      console.error('Error saving room:', error)
      toast.error(`Failed to save room: ${error.message}`)
    }
  }

  const handleScheduleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate that at least one day has times selected
    const selectedDays = Object.entries(scheduleFormData.weekly_schedule).filter(
      ([_, dayData]) => dayData.start_time && dayData.end_time
    )

    if (selectedDays.length === 0) {
      toast.error(t('schedules.atLeastOneDayRequired'))
      return
    }

    // Validate that end time is after start time for each selected day
    for (const [day, dayData] of selectedDays) {
      if (dayData.start_time >= dayData.end_time) {
        toast.error(`${t('schedules.endTimeMustBeAfterStart')} (${t(`schedules.${day}`)})`)
        return
      }
    }

    try {
      // Prepare schedule data for each selected day
      const scheduleEntries = selectedDays.map(([day, dayData]) => ({
        course_id: scheduleFormData.course_id,
        batch_id: scheduleFormData.batch_id || null,
        room_id: scheduleFormData.room_id,
        teacher_id: dayData.teacher_id || scheduleFormData.teacher_id,
        day_of_week: day,
        start_time: dayData.start_time,
        end_time: dayData.end_time,
        effective_from: scheduleFormData.effective_from,
        effective_to: scheduleFormData.effective_to || null,
        notes: scheduleFormData.notes
      }))

      const response = await adminPost(`/api/coaching/schedules/weekly?branch_id=${currentBranch?.id}`, {
        schedules: scheduleEntries
      })

      if (response.success) {
        toast.success('Weekly schedule created successfully!')
        setShowScheduleForm(false)
        setSelectedSchedule(null)
        resetScheduleForm()
        fetchData()
      }
    } catch (error: any) {
      console.error('Error saving schedule:', error)
      if (error.conflicts) {
        setScheduleConflicts(error.conflicts)
        const conflictMessages = error.conflicts.map((c: any) =>
          `${c.conflict_type === 'room' ? 'Room' : 'Teacher'} "${c.resource_name}" on ${t(`schedules.${c.day}`)} at ${formatTime12Hour(c.start_time)}-${formatTime12Hour(c.end_time)}`
        )
        toast.error(`Schedule conflicts detected:\n${conflictMessages.join('\n')}`)
      } else {
        toast.error(`Failed to save schedule: ${error.message}`)
      }
    }
  }

  const resetScheduleForm = () => {
    setScheduleFormData({
      course_id: '',
      batch_id: '',
      room_id: '',
      teacher_id: '',
      effective_from: new Date().toISOString().split('T')[0],
      effective_to: '',
      notes: '',
      weekly_schedule: {
        saturday: { start_time: '', end_time: '', teacher_id: '' },
        sunday: { start_time: '', end_time: '', teacher_id: '' },
        monday: { start_time: '', end_time: '', teacher_id: '' },
        tuesday: { start_time: '', end_time: '', teacher_id: '' },
        wednesday: { start_time: '', end_time: '', teacher_id: '' },
        thursday: { start_time: '', end_time: '', teacher_id: '' },
        friday: { start_time: '', end_time: '', teacher_id: '' }
      }
    })
  }

  // Filter schedules based on selected filters
  const applyFilters = () => {
    let filtered = [...schedules]

    if (filters.room) {
      filtered = filtered.filter(schedule => schedule.room_id.toString() === filters.room)
    }

    if (filters.course) {
      filtered = filtered.filter(schedule => schedule.course_id.toString() === filters.course)
    }

    if (filters.teacher) {
      filtered = filtered.filter(schedule => schedule.teacher_id.toString() === filters.teacher)
    }

    if (filters.day) {
      filtered = filtered.filter(schedule => schedule.day_of_week === filters.day)
    }

    setFilteredSchedules(filtered)
  }

  // Apply filters whenever schedules or filters change
  useEffect(() => {
    applyFilters()
  }, [schedules, filters])

  const openBranchForm = (branch?: Branch) => {
    if (branch) {
      setSelectedBranch(branch)
      setBranchFormData({
        branch_code: branch.branch_code,
        branch_name: branch.branch_name,
        address: branch.address,
        phone: branch.phone,
        email: branch.email,
        manager_id: branch.manager_id?.toString() || '',
        establishment_date: branch.establishment_date
      })
    }
    setShowBranchForm(true)
  }

  const openRoomForm = (room?: Room) => {
    if (room) {
      setSelectedRoom(room)
      setRoomFormData({
        room_code: room.room_code,
        room_name: room.room_name,
        room_type: room.room_type,
        capacity: room.capacity.toString(),
        equipment: room.equipment,
        notes: room.notes
      })
    }
    setShowRoomForm(true)
  }

  const openScheduleForm = (schedule?: Schedule) => {
    if (schedule) {
      setSelectedSchedule(schedule)
      // For editing, we'll populate the form with the single schedule data
      // In a real implementation, you might want to load all related schedules for the week
      const weeklySchedule = {
        saturday: { start_time: '', end_time: '', teacher_id: '' },
        sunday: { start_time: '', end_time: '', teacher_id: '' },
        monday: { start_time: '', end_time: '', teacher_id: '' },
        tuesday: { start_time: '', end_time: '', teacher_id: '' },
        wednesday: { start_time: '', end_time: '', teacher_id: '' },
        thursday: { start_time: '', end_time: '', teacher_id: '' },
        friday: { start_time: '', end_time: '', teacher_id: '' }
      }

      // Populate the specific day
      weeklySchedule[schedule.day_of_week] = {
        start_time: schedule.start_time,
        end_time: schedule.end_time,
        teacher_id: schedule.teacher_id.toString()
      }

      setScheduleFormData({
        course_id: schedule.course_id.toString(),
        batch_id: schedule.batch_id?.toString() || '',
        room_id: schedule.room_id.toString(),
        teacher_id: schedule.teacher_id.toString(),
        effective_from: schedule.effective_from,
        effective_to: schedule.effective_to || '',
        notes: schedule.notes,
        weekly_schedule: weeklySchedule
      })
    } else {
      resetScheduleForm()
    }
    setShowScheduleForm(true)
  }

  // Both super admin and center admin can access branch management
  if (!['super_admin', 'center_admin'].includes(user?.role || '') && activeTab === 'branches') {
    return (
      <AdminLayout>
        <div className="px-6">
          <div className="text-center py-8">
            <p className="text-gray-500">Access denied. Only admin users can manage branches.</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="px-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold dark:text-white">{t('branches.title')}</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">{t('branches.description')}</p>
        </div>

        {/* Tab Navigation */}
        <nav className="flex space-x-4 mb-6 overflow-x-auto">
          <button
            onClick={() => setActiveTab('branches')}
            className={`py-2 px-3 border-b-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'branches'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'
            }`}
          >
            {t('branches.title')}
          </button>
          <button
            onClick={() => setActiveTab('rooms')}
            className={`py-2 px-3 border-b-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'rooms'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'
            }`}
          >
            {t('rooms.title')}
          </button>
          <button
            onClick={() => setActiveTab('schedules')}
            className={`py-2 px-3 border-b-2 font-medium text-sm whitespace-nowrap ${
              activeTab === 'schedules'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-200'
            }`}
          >
            {t('schedules.title')}
          </button>
        </nav>

        {/* Branches Tab */}
        {activeTab === 'branches' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold dark:text-white">{t('branches.title')}</h2>
                <p className="text-gray-600 dark:text-gray-400">{t('branches.description')}</p>
              </div>
              <button
                onClick={() => openBranchForm()}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {t('branches.addBranch')}
              </button>
            </div>

            {loading ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">Loading...</p>
              </div>
            ) : branches.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">No branches found</p>
              </div>
            ) : (
              <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('branches.branchDetails')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('branches.manager')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('branches.branchStats')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('branches.status')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('inventory.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {branches.map((branch) => (
                      <tr key={branch.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{branch.branch_name}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">Code: {branch.branch_code}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">{branch.address}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{branch.manager_name || 'Not assigned'}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">{branch.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{branch.total_users} users</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">{branch.total_rooms} rooms</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            branch.status === 'active'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          }`}>
                            {t(`branches.${branch.status}`)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => openBranchForm(branch)}
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm"
                          >
                            {t('common.edit')}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Rooms Tab */}
        {activeTab === 'rooms' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold dark:text-white">{t('rooms.title')}</h2>
                <p className="text-gray-600 dark:text-gray-400">{t('rooms.description')}</p>
                {currentBranch && (
                  <p className="text-sm text-blue-600 dark:text-blue-400">
                    {t('branches.currentBranch')}: {currentBranch.branch_name}
                  </p>
                )}
              </div>
              <button
                onClick={() => openRoomForm()}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {t('rooms.addRoom')}
              </button>
            </div>

            {loading ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">Loading...</p>
              </div>
            ) : rooms.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">No rooms found</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {rooms.map((room) => (
                  <div key={room.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-lg font-semibold dark:text-white">{room.room_name}</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Code: {room.room_code}</p>
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        room.status === 'active'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : room.status === 'maintenance'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }`}>
                        {t(`rooms.${room.status}`)}
                      </span>
                    </div>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">{t('rooms.roomType')}:</span>
                        <span className="text-sm font-medium dark:text-white">{t(`rooms.${room.room_type}`)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">{t('rooms.capacity')}:</span>
                        <span className="text-sm font-medium dark:text-white">{room.capacity}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Classes Today:</span>
                        <span className="text-sm font-medium dark:text-white">{room.today_classes}</span>
                      </div>
                    </div>

                    {room.equipment && (
                      <div className="mb-4">
                        <p className="text-sm text-gray-600 dark:text-gray-400">{t('rooms.equipment')}:</p>
                        <p className="text-sm dark:text-white">{room.equipment}</p>
                      </div>
                    )}

                    <button
                      onClick={() => openRoomForm(room)}
                      className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 text-sm"
                    >
                      {t('common.edit')}
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Schedules Tab */}
        {activeTab === 'schedules' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold dark:text-white">{t('schedules.title')}</h2>
                <p className="text-gray-600 dark:text-gray-400">{t('schedules.description')}</p>
                {currentBranch && (
                  <p className="text-sm text-blue-600 dark:text-blue-400">
                    {t('branches.currentBranch')}: {currentBranch.branch_name}
                  </p>
                )}
              </div>
              <button
                onClick={() => openScheduleForm()}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {t('schedules.addSchedule')}
              </button>
            </div>

            {/* Filter Controls */}
            <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-4 mb-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {t('common.filters')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('rooms.title')}
                  </label>
                  <select
                    value={filters.room}
                    onChange={(e) => setFilters({...filters, room: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">{t('common.all')}</option>
                    {rooms.map((room) => (
                      <option key={room.id} value={room.id}>
                        {room.room_code} - {room.room_name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('courses.course')}
                  </label>
                  <select
                    value={filters.course}
                    onChange={(e) => setFilters({...filters, course: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">{t('common.all')}</option>
                    {courses.map((course) => (
                      <option key={course.id} value={course.id}>
                        {course.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('teachers.teacher')}
                  </label>
                  <select
                    value={filters.teacher}
                    onChange={(e) => setFilters({...filters, teacher: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">{t('common.all')}</option>
                    {teachers.map((teacher) => (
                      <option key={teacher.id} value={teacher.id}>
                        {teacher.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('schedules.dayOfWeek')}
                  </label>
                  <select
                    value={filters.day}
                    onChange={(e) => setFilters({...filters, day: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">{t('common.all')}</option>
                    <option value="saturday">{t('schedules.saturday')}</option>
                    <option value="sunday">{t('schedules.sunday')}</option>
                    <option value="monday">{t('schedules.monday')}</option>
                    <option value="tuesday">{t('schedules.tuesday')}</option>
                    <option value="wednesday">{t('schedules.wednesday')}</option>
                    <option value="thursday">{t('schedules.thursday')}</option>
                    <option value="friday">{t('schedules.friday')}</option>
                  </select>
                </div>
              </div>

              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => setFilters({ room: '', course: '', teacher: '', day: '' })}
                  className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                >
                  {t('common.clearFilters')}
                </button>
              </div>
            </div>

            {loading ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">Loading...</p>
              </div>
            ) : filteredSchedules.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  {schedules.length === 0 ? 'No schedules found' : 'No schedules match the current filters'}
                </p>
              </div>
            ) : (
              <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('schedules.dayOfWeek')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('schedules.timeSlot')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('courses.course')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('rooms.title')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('teachers.teacher')}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {t('inventory.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredSchedules.map((schedule) => (
                      <tr key={schedule.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {t(`schedules.${schedule.day_of_week}`)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">
                            {formatTime12Hour(schedule.start_time)} - {formatTime12Hour(schedule.end_time)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{schedule.course_name}</div>
                          {schedule.batch_name && (
                            <div className="text-sm text-gray-500 dark:text-gray-400">{schedule.batch_name}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{schedule.room_name}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">{schedule.room_code}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{schedule.teacher_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => openScheduleForm(schedule)}
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 text-sm"
                          >
                            {t('common.edit')}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Branch Form Modal */}
        {showBranchForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold dark:text-white">
                  {selectedBranch ? t('branches.editBranch') : t('branches.addBranch')}
                </h3>
                <button
                  onClick={() => {
                    setShowBranchForm(false)
                    setSelectedBranch(null)
                  }}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  ✕
                </button>
              </div>

              <form onSubmit={handleBranchSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('branches.branchCode')} *
                  </label>
                  <input
                    type="text"
                    required
                    value={branchFormData.branch_code}
                    onChange={(e) => setBranchFormData({...branchFormData, branch_code: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('branches.branchName')} *
                  </label>
                  <input
                    type="text"
                    required
                    value={branchFormData.branch_name}
                    onChange={(e) => setBranchFormData({...branchFormData, branch_name: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('branches.address')}
                  </label>
                  <textarea
                    value={branchFormData.address}
                    onChange={(e) => setBranchFormData({...branchFormData, address: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('branches.phone')}
                  </label>
                  <input
                    type="text"
                    value={branchFormData.phone}
                    onChange={(e) => setBranchFormData({...branchFormData, phone: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('branches.email')}
                  </label>
                  <input
                    type="email"
                    value={branchFormData.email}
                    onChange={(e) => setBranchFormData({...branchFormData, email: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('branches.establishmentDate')}
                  </label>
                  <input
                    type="date"
                    value={branchFormData.establishment_date}
                    onChange={(e) => setBranchFormData({...branchFormData, establishment_date: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                  >
                    {selectedBranch ? t('common.save') : t('common.add')}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowBranchForm(false)
                      setSelectedBranch(null)
                    }}
                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
                  >
                    {t('common.cancel')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Room Form Modal */}
        {showRoomForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold dark:text-white">
                  {selectedRoom ? t('rooms.editRoom') : t('rooms.addRoom')}
                </h3>
                <button
                  onClick={() => {
                    setShowRoomForm(false)
                    setSelectedRoom(null)
                  }}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  ✕
                </button>
              </div>

              <form onSubmit={handleRoomSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('rooms.roomCode')} *
                  </label>
                  <input
                    type="text"
                    required
                    value={roomFormData.room_code}
                    onChange={(e) => setRoomFormData({...roomFormData, room_code: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('rooms.roomName')} *
                  </label>
                  <input
                    type="text"
                    required
                    value={roomFormData.room_name}
                    onChange={(e) => setRoomFormData({...roomFormData, room_name: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('rooms.roomType')} *
                  </label>
                  <select
                    required
                    value={roomFormData.room_type}
                    onChange={(e) => setRoomFormData({...roomFormData, room_type: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="classroom">{t('rooms.classroom')}</option>
                    <option value="lab">{t('rooms.lab')}</option>
                    <option value="auditorium">{t('rooms.auditorium')}</option>
                    <option value="library">{t('rooms.library')}</option>
                    <option value="office">{t('rooms.office')}</option>
                    <option value="other">{t('rooms.other')}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('rooms.capacity')} *
                  </label>
                  <input
                    type="number"
                    required
                    min="1"
                    value={roomFormData.capacity}
                    onChange={(e) => setRoomFormData({...roomFormData, capacity: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('rooms.equipment')}
                  </label>
                  <textarea
                    value={roomFormData.equipment}
                    onChange={(e) => setRoomFormData({...roomFormData, equipment: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                    rows={3}
                    placeholder="e.g., Projector, Whiteboard, AC"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('inventory.notes')}
                  </label>
                  <textarea
                    value={roomFormData.notes}
                    onChange={(e) => setRoomFormData({...roomFormData, notes: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                    rows={3}
                  />
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                  >
                    {selectedRoom ? t('common.save') : t('common.add')}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowRoomForm(false)
                      setSelectedRoom(null)
                    }}
                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
                  >
                    {t('common.cancel')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Schedule Form Modal */}
        {showScheduleForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <div className="sticky top-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-xl font-semibold dark:text-white">
                    {selectedSchedule ? t('schedules.editWeeklySchedule') : t('schedules.createWeeklySchedule')}
                  </h3>
                  <button
                    onClick={() => {
                      setShowScheduleForm(false)
                      setSelectedSchedule(null)
                    }}
                    className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xl"
                  >
                    ✕
                  </button>
                </div>
              </div>

              <form onSubmit={handleScheduleSubmit} className="p-6 space-y-6">
                {/* Basic Information Section */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    {t('schedules.basicInformation')}
                  </h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {t('schedules.selectRoom')} *
                      </label>
                      <select
                        required
                        value={scheduleFormData.room_id}
                        onChange={(e) => setScheduleFormData({...scheduleFormData, room_id: e.target.value})}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">{t('schedules.selectRoom')}</option>
                        {rooms.map((room) => (
                          <option key={room.id} value={room.id}>
                            {room.room_code} - {room.room_name} (Capacity: {room.capacity})
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {t('schedules.selectCourse')} *
                      </label>
                      <select
                        required
                        value={scheduleFormData.course_id}
                        onChange={(e) => setScheduleFormData({...scheduleFormData, course_id: e.target.value})}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">{t('schedules.selectCourse')}</option>
                        {courses.map((course) => (
                          <option key={course.id} value={course.id}>
                            {course.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {t('schedules.selectBatch')} ({t('schedules.optional')})
                      </label>
                      <select
                        value={scheduleFormData.batch_id}
                        onChange={(e) => setScheduleFormData({...scheduleFormData, batch_id: e.target.value})}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">{t('schedules.selectBatch')}</option>
                        {batches.map((batch) => (
                          <option key={batch.id} value={batch.id}>
                            {batch.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {t('schedules.primaryTeacher')} *
                      </label>
                      <select
                        required
                        value={scheduleFormData.teacher_id}
                        onChange={(e) => setScheduleFormData({...scheduleFormData, teacher_id: e.target.value})}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">{t('schedules.selectTeacher')}</option>
                        {teachers.map((teacher) => (
                          <option key={teacher.id} value={teacher.id}>
                            {teacher.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {t('schedules.effectiveFrom')} *
                      </label>
                      <input
                        type="date"
                        required
                        value={scheduleFormData.effective_from}
                        onChange={(e) => setScheduleFormData({...scheduleFormData, effective_from: e.target.value})}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {t('schedules.effectiveTo')} ({t('schedules.optional')})
                      </label>
                      <input
                        type="date"
                        value={scheduleFormData.effective_to}
                        onChange={(e) => setScheduleFormData({...scheduleFormData, effective_to: e.target.value})}
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                  </div>
                </div>

                {/* Weekly Schedule Grid */}
                <div>
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    {t('schedules.weeklyTimeTable')}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    {t('schedules.selectDaysAndTimes')}
                  </p>

                  <div className="space-y-4">
                    {['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'].map((day) => (
                      <div key={day} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              {t(`schedules.${day}`)}
                            </label>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {scheduleFormData.weekly_schedule[day].start_time && scheduleFormData.weekly_schedule[day].end_time
                                ? `${scheduleFormData.weekly_schedule[day].start_time} - ${scheduleFormData.weekly_schedule[day].end_time}`
                                : t('schedules.noTimeSelected')
                              }
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              {t('schedules.startTime')}
                            </label>
                            <input
                              type="time"
                              value={scheduleFormData.weekly_schedule[day].start_time}
                              onChange={(e) => setScheduleFormData({
                                ...scheduleFormData,
                                weekly_schedule: {
                                  ...scheduleFormData.weekly_schedule,
                                  [day]: {
                                    ...scheduleFormData.weekly_schedule[day],
                                    start_time: e.target.value
                                  }
                                }
                              })}
                              className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              {t('schedules.endTime')}
                            </label>
                            <input
                              type="time"
                              value={scheduleFormData.weekly_schedule[day].end_time}
                              onChange={(e) => setScheduleFormData({
                                ...scheduleFormData,
                                weekly_schedule: {
                                  ...scheduleFormData.weekly_schedule,
                                  [day]: {
                                    ...scheduleFormData.weekly_schedule[day],
                                    end_time: e.target.value
                                  }
                                }
                              })}
                              className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              {t('schedules.dayTeacher')} ({t('schedules.optional')})
                            </label>
                            <select
                              value={scheduleFormData.weekly_schedule[day].teacher_id}
                              onChange={(e) => setScheduleFormData({
                                ...scheduleFormData,
                                weekly_schedule: {
                                  ...scheduleFormData.weekly_schedule,
                                  [day]: {
                                    ...scheduleFormData.weekly_schedule[day],
                                    teacher_id: e.target.value
                                  }
                                }
                              })}
                              className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                            >
                              <option value="">{t('schedules.selectTeacher')}</option>
                              {teachers.map((teacher) => (
                                <option key={teacher.id} value={teacher.id}>
                                  {teacher.name}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {t('inventory.notes')} ({t('schedules.optional')})
                  </label>
                  <textarea
                    value={scheduleFormData.notes}
                    onChange={(e) => setScheduleFormData({...scheduleFormData, notes: e.target.value})}
                    className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 dark:bg-gray-700 dark:text-white"
                    rows={3}
                    placeholder="Additional notes about this schedule..."
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <button
                    type="submit"
                    className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 font-medium"
                  >
                    {selectedSchedule ? t('common.save') : t('common.add')}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowScheduleForm(false)
                      setSelectedSchedule(null)
                    }}
                    className="flex-1 bg-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-400 font-medium"
                  >
                    {t('common.cancel')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
