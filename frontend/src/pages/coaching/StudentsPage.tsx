import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useAuth } from '../../contexts/AuthContext'
import { useApi } from '../../hooks/useApi'
import AdminLayout from '../../components/AdminLayout'

interface Student {
  id: number
  name: string
  email: string
  phone: string
  address: string
  date_of_birth: string
  gender: 'male' | 'female' | 'other'
  blood_group?: 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-'
  parent_name: string
  parent_phone: string
  emergency_contact: string
  admission_date: string
  status: 'active' | 'inactive' | 'suspended'
  enrolled_batches: number
  total_fees: number
  pending_fees: number
  profile_image_webp?: string
  class_name?: string
  class_section?: string
}

export default function StudentsPage() {
  const { user } = useAuth()
  const { adminGet, adminPost, adminPut } = useApi()
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingStudent, setEditingStudent] = useState<Student | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showTransferModal, setShowTransferModal] = useState(false)
  const [transferringStudent, setTransferringStudent] = useState<Student | null>(null)
  const [branches, setBranches] = useState<any[]>([])
  const [transferData, setTransferData] = useState({
    to_branch_id: '',
    transfer_reason: '',
    effective_date: ''
  })
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    date_of_birth: '',
    gender: 'male',
    blood_group: '',
    parent_name: '',
    parent_phone: '',
    emergency_contact: '',
    profile_image_webp: '',
    class_name: '',
    class_section: ''
  })

  useEffect(() => {
    fetchStudents()
  }, [])

  const fetchStudents = async () => {
    try {
      setLoading(true)
      const response = await adminGet('/api/coaching/students')
      if (response.students) {
        setStudents(response.students)
      }
    } catch (error) {
      console.error('Error fetching students:', error)
      toast.error(`Failed to fetch students: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (editingStudent) {
        const response = await adminPut(`/api/coaching/students/${editingStudent.id}`, formData)
        if (response.success) {
          toast.success('Student updated successfully!')
          fetchStudents()
          resetForm()
        }
      } else {
        const response = await adminPost('/api/coaching/students', formData)
        if (response.success) {
          toast.success(`Student added successfully! Default password: ${response.defaultPassword}`)
          fetchStudents()
          resetForm()
        }
      }
    } catch (error) {
      console.error('Error saving student:', error)
      toast.error(`Failed to save student: ${error.message}`)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      address: '',
      date_of_birth: '',
      gender: 'male',
      blood_group: '',
      parent_name: '',
      parent_phone: '',
      emergency_contact: '',
      profile_image_webp: '',
      class_name: '',
      class_section: ''
    })
    setShowAddModal(false)
    setEditingStudent(null)
  }

  // Image upload and compression function
  const handleImageUpload = async (file: File) => {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      return new Promise<string>((resolve) => {
        img.onload = () => {
          // Calculate new dimensions (max 400px width for profile images)
          const maxWidth = 400
          const ratio = Math.min(maxWidth / img.width, maxWidth / img.height)
          canvas.width = img.width * ratio
          canvas.height = img.height * ratio

          // Draw and compress to WebP
          ctx?.drawImage(img, 0, 0, canvas.width, canvas.height)
          const webpDataUrl = canvas.toDataURL('image/webp', 0.8)

          setFormData(prev => ({ ...prev, profile_image_webp: webpDataUrl }))
          toast.success('Image uploaded and compressed successfully!')
          resolve(webpDataUrl)
        }

        img.src = URL.createObjectURL(file)
      })
    } catch (error) {
      console.error('Error processing image:', error)
      toast.error('Failed to process image')
      return ''
    }
  }

  const editStudent = (student: Student) => {
    setEditingStudent(student)
    setFormData({
      name: student.name,
      email: student.email,
      phone: student.phone || '',
      address: student.address || '',
      date_of_birth: student.date_of_birth || '',
      gender: student.gender || 'male',
      blood_group: student.blood_group || '',
      parent_name: student.parent_name || '',
      parent_phone: student.parent_phone || '',
      emergency_contact: student.emergency_contact || '',
      profile_image_webp: student.profile_image_webp || '',
      class_name: student.class_name || '',
      class_section: student.class_section || ''
    })
    setShowAddModal(true)
  }

  const updateStudentStatus = async (studentId: number, newStatus: string) => {
    try {
      const response = await adminPut(`/api/coaching/students/${studentId}`, { status: newStatus })
      if (response.success) {
        toast.success('শিক্ষার্থীর স্ট্যাটাস সফলভাবে আপডেট হয়েছে!')
        fetchStudents()
      }
    } catch (error) {
      console.error('Error updating student status:', error)
      toast.error(`শিক্ষার্থীর স্ট্যাটাস আপডেট করতে ব্যর্থ: ${error.message}`)
    }
  }

  const handleStatusToggle = async (student: Student) => {
    const newStatus = student.status === 'active' ? 'inactive' : 'active'
    const actionText = newStatus === 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'

    const confirmed = window.confirm(
      `আপনি কি নিশ্চিত যে আপনি ${student.name} কে ${actionText} করতে চান?`
    )

    if (confirmed) {
      await updateStudentStatus(student.id, newStatus)
    }
  }

  const fetchBranches = async () => {
    try {
      const response = await adminGet('/api/coaching/branches')
      setBranches(response.branches || [])
    } catch (error) {
      console.error('Error fetching branches:', error)
      toast.error('শাখার তালিকা লোড করতে ব্যর্থ')
    }
  }

  const openTransferModal = (student: Student) => {
    setTransferringStudent(student)
    setTransferData({
      to_branch_id: '',
      transfer_reason: '',
      effective_date: new Date().toISOString().split('T')[0]
    })
    setShowTransferModal(true)
    fetchBranches()
  }

  const closeTransferModal = () => {
    setShowTransferModal(false)
    setTransferringStudent(null)
    setTransferData({
      to_branch_id: '',
      transfer_reason: '',
      effective_date: ''
    })
  }

  const handleTransferSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!transferringStudent || !transferData.to_branch_id) {
      toast.error('অনুগ্রহ করে গন্তব্য শাখা নির্বাচন করুন')
      return
    }

    if (!transferData.transfer_reason.trim()) {
      toast.error('অনুগ্রহ করে স্থানান্তরের কারণ লিখুন')
      return
    }

    try {
      const response = await adminPost(`/api/coaching/students/${transferringStudent.id}/transfer`, {
        to_branch_id: parseInt(transferData.to_branch_id),
        transfer_reason: transferData.transfer_reason,
        effective_date: transferData.effective_date
      })

      if (response.success) {
        toast.success('শিক্ষার্থী সফলভাবে স্থানান্তরিত হয়েছে!')
        closeTransferModal()
        fetchStudents()
      }
    } catch (error) {
      console.error('Error transferring student:', error)
      toast.error(`স্থানান্তর করতে ব্যর্থ: ${error.message}`)
    }
  }

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || student.status === statusFilter
    return matchesSearch && matchesStatus
  })

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-lg">Loading students...</div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="px-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Students Management</h1>
            <p className="text-gray-600 mt-2">Manage all students in your coaching center</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Add New Student
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">শিক্ষার্থী অনুসন্ধান</label>
              <input
                type="text"
                placeholder="নাম বা ইমেইল দিয়ে অনুসন্ধান করুন..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full border rounded-lg px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">স্ট্যাটাস অনুযায়ী ফিল্টার</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full border rounded-lg px-3 py-2"
              >
                <option value="all">সব শিক্ষার্থী</option>
                <option value="active">সক্রিয়</option>
                <option value="inactive">নিষ্ক্রিয়</option>
                <option value="suspended">স্থগিত</option>
              </select>
            </div>
            <div className="flex items-end">
              <div className="text-sm text-gray-600">
                মোট শিক্ষার্থী: {filteredStudents.length}
              </div>
            </div>
          </div>
        </div>

        {/* Students Table */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    রক্তের গ্রুপ
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Enrollment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Fees
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredStudents.map((student) => (
                  <tr key={student.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {student.profile_image_webp ? (
                          <img
                            src={student.profile_image_webp}
                            alt={student.name}
                            className="h-10 w-10 rounded-full object-cover mr-3"
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                            <span className="text-gray-600 font-medium">
                              {student.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900">{student.name}</div>
                          <div className="text-sm text-gray-500">{student.email}</div>
                          <div className="text-xs text-gray-400">ID: {student.id}</div>
                          {(student.class_name || student.class_section) && (
                            <div className="text-xs text-blue-600">
                              {student.class_name} {student.class_section && `- ${student.class_section}`}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{student.phone}</div>
                      <div className="text-sm text-gray-500">Parent: {student.parent_phone}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {student.blood_group ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            {student.blood_group}
                          </span>
                        ) : (
                          <span className="text-gray-400">Not specified</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{student.enrolled_batches} batches</div>
                      <div className="text-sm text-gray-500">Since: {new Date(student.admission_date).toLocaleDateString()}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">৳{student.total_fees.toLocaleString()}</div>
                      <div className="text-sm text-red-600">Pending: ৳{student.pending_fees.toLocaleString()}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        student.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : student.status === 'suspended'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {student.status === 'active' ? 'সক্রিয়' : student.status === 'suspended' ? 'স্থগিত' : 'নিষ্ক্রিয়'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <button
                          onClick={() => editStudent(student)}
                          className="text-blue-600 hover:text-blue-900"
                          disabled={student.status === 'inactive'}
                        >
                          সম্পাদনা
                        </button>
                        <button
                          onClick={() => window.location.href = `/coaching/students/${student.id}/courses`}
                          className="text-green-600 hover:text-green-900"
                          disabled={student.status === 'inactive'}
                        >
                          কোর্স নিয়োগ
                        </button>
                        <button
                          onClick={() => handleStatusToggle(student)}
                          className={`${
                            student.status === 'active'
                              ? 'text-red-600 hover:text-red-900'
                              : 'text-green-600 hover:text-green-900'
                          }`}
                        >
                          {student.status === 'active' ? 'নিষ্ক্রিয়' : 'সক্রিয়'}
                        </button>
                        <button
                          onClick={() => openTransferModal(student)}
                          className="text-purple-600 hover:text-purple-900"
                          disabled={student.status === 'inactive'}
                        >
                          স্থানান্তর
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {filteredStudents.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold mb-2">No students found</h3>
            <p className="text-gray-600">No students match the current filters.</p>
          </div>
        )}

        {/* Add/Edit Student Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <h2 className="text-2xl font-bold mb-4">
                {editingStudent ? 'Edit Student' : 'Add New Student'}
              </h2>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Full Name *</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Email *</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      required
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Phone</label>
                    <input
                      type="text"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Date of Birth</label>
                    <input
                      type="date"
                      value={formData.date_of_birth}
                      onChange={(e) => setFormData(prev => ({ ...prev, date_of_birth: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Address</label>
                  <textarea
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Gender</label>
                    <select
                      value={formData.gender}
                      onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value as any }))}
                      className="w-full border rounded-lg px-3 py-2"
                    >
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">রক্তের গ্রুপ / Blood Group</label>
                    <select
                      name="blood_group"
                      value={formData.blood_group}
                      onChange={(e) => setFormData(prev => ({ ...prev, blood_group: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    >
                      <option value="">Select Blood Group</option>
                      <option value="A+">A+</option>
                      <option value="A-">A-</option>
                      <option value="B+">B+</option>
                      <option value="B-">B-</option>
                      <option value="AB+">AB+</option>
                      <option value="AB-">AB-</option>
                      <option value="O+">O+</option>
                      <option value="O-">O-</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Parent Name</label>
                    <input
                      type="text"
                      value={formData.parent_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, parent_name: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Parent Phone</label>
                    <input
                      type="text"
                      value={formData.parent_phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, parent_phone: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Emergency Contact</label>
                  <input
                    type="text"
                    value={formData.emergency_contact}
                    onChange={(e) => setFormData(prev => ({ ...prev, emergency_contact: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>

                <div className="flex gap-4 pt-4">

                {/* Profile Image Upload */}
                <div>
                  <label className="block text-sm font-medium mb-1">Profile Image</label>
                  <div className="space-y-2">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) {
                          handleImageUpload(file)
                        }
                      }}
                      className="w-full border rounded-lg px-3 py-2"
                    />
                    {formData.profile_image_webp && (
                      <div className="mt-2">
                        <img
                          src={formData.profile_image_webp}
                          alt="Profile Preview"
                          className="h-20 w-20 rounded-full object-cover border border-gray-300"
                        />
                      </div>
                    )}
                    <p className="text-xs text-gray-500">
                      Image will be automatically converted to WebP format and compressed
                    </p>
                  </div>
                </div>

                {/* Class Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Class</label>
                    <input
                      type="text"
                      value={formData.class_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, class_name: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                      placeholder="e.g., Class 10, HSC"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Section</label>
                    <input
                      type="text"
                      value={formData.class_section}
                      onChange={(e) => setFormData(prev => ({ ...prev, class_section: e.target.value }))}
                      className="w-full border rounded-lg px-3 py-2"
                      placeholder="e.g., A, B, Science"
                    />
                  </div>
                </div>
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                  >
                    {editingStudent ? 'Update Student' : 'Add Student'}
                  </button>
                  <button
                    type="button"
                    onClick={resetForm}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Transfer Modal */}
        {showTransferModal && transferringStudent && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h2 className="text-xl font-bold mb-4">শিক্ষার্থী স্থানান্তর</h2>
              <p className="text-gray-600 mb-4">
                <strong>{transferringStudent.name}</strong> কে অন্য শাখায় স্থানান্তর করুন
              </p>

              <form onSubmit={handleTransferSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">গন্তব্য শাখা</label>
                  <select
                    name="branch_id"
                    value={transferData.to_branch_id}
                    onChange={(e) => setTransferData(prev => ({ ...prev, to_branch_id: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    required
                  >
                    <option value="">শাখা নির্বাচন করুন</option>
                    {branches
                      .filter(branch => branch.id !== transferringStudent.branch_id)
                      .map(branch => (
                        <option key={branch.id} value={branch.id}>
                          {branch.branch_name}
                        </option>
                      ))
                    }
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">স্থানান্তরের কারণ</label>
                  <textarea
                    value={transferData.transfer_reason}
                    onChange={(e) => setTransferData(prev => ({ ...prev, transfer_reason: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    rows={3}
                    placeholder="স্থানান্তরের কারণ লিখুন..."
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">কার্যকর তারিখ</label>
                  <input
                    type="date"
                    value={transferData.effective_date}
                    onChange={(e) => setTransferData(prev => ({ ...prev, effective_date: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    required
                  />
                </div>

                <div className="flex gap-2 pt-4">
                  <button
                    type="submit"
                    className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex-1"
                  >
                    স্থানান্তর নিশ্চিত করুন
                  </button>
                  <button
                    type="button"
                    onClick={closeTransferModal}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 flex-1"
                  >
                    বাতিল
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
