import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import { useApi } from '../../hooks/useApi'
import AdminLayout from '../../components/AdminLayout'
import { TableSkeleton } from '../../components/skeletons/DashboardSkeleton'

interface Employee {
  id: number
  employee_id: string
  name: string
  email: string
  phone: string
  position: string
  department: string
  hire_date: string
  salary: number
  status: 'active' | 'inactive' | 'terminated'
  address: string
  emergency_contact: string
  total_leave_requests: number
  pending_leaves: number
}

interface LeaveRequest {
  id: number
  employee_id: number
  employee_name: string
  employee_id_code: string
  position: string
  leave_type: 'sick' | 'casual' | 'annual' | 'maternity' | 'emergency'
  start_date: string
  end_date: string
  days_requested: number
  reason: string
  status: 'pending' | 'approved' | 'rejected'
  approved_by_name: string
  approved_at: string
  notes: string
  created_at: string
}

export default function HRMPage() {
  const { user } = useAuth()
  const { t } = useLanguage()
  const { adminGet, adminPost, adminPut } = useApi()
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'employees' | 'leaves' | 'salary'>('employees')
  
  // Employee state
  const [employees, setEmployees] = useState<Employee[]>([])
  const [showEmployeeForm, setShowEmployeeForm] = useState(false)
  const [employeeFormData, setEmployeeFormData] = useState({
    employee_id: '',
    name: '',
    email: '',
    phone: '',
    position: '',
    department: '',
    hire_date: '',
    salary: '',
    address: '',
    emergency_contact: ''
  })

  // Leave request state
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([])
  const [showLeaveForm, setShowLeaveForm] = useState(false)
  const [leaveFormData, setLeaveFormData] = useState({
    employee_id: '',
    leave_type: 'casual',
    start_date: '',
    end_date: '',
    days_requested: '',
    reason: ''
  })

  const [statusFilter, setStatusFilter] = useState('all')

  useEffect(() => {
    fetchData()
  }, [activeTab])

  const fetchData = async () => {
    try {
      setLoading(true)
      if (activeTab === 'employees') {
        const response = await adminGet('/api/coaching/employees')
        setEmployees(response.employees || [])
      } else if (activeTab === 'leaves') {
        const response = await adminGet('/api/coaching/leave-requests')
        setLeaveRequests(response.leaveRequests || [])
      }
    } catch (error: any) {
      console.error('Error fetching data:', error)
      toast.error('Failed to fetch data')
    } finally {
      setLoading(false)
    }
  }

  const handleEmployeeSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await adminPost('/api/coaching/employees', {
        ...employeeFormData,
        salary: parseFloat(employeeFormData.salary)
      })
      
      if (response.success) {
        toast.success('Employee added successfully!')
        setShowEmployeeForm(false)
        setEmployeeFormData({
          employee_id: '',
          name: '',
          email: '',
          phone: '',
          position: '',
          department: '',
          hire_date: '',
          salary: '',
          address: '',
          emergency_contact: ''
        })
        fetchData()
      }
    } catch (error: any) {
      console.error('Error adding employee:', error)
      toast.error(`Failed to add employee: ${error.message}`)
    }
  }

  const handleLeaveSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await adminPost('/api/coaching/leave-requests', {
        ...leaveFormData,
        days_requested: parseInt(leaveFormData.days_requested)
      })
      
      if (response.success) {
        toast.success('Leave request submitted successfully!')
        setShowLeaveForm(false)
        setLeaveFormData({
          employee_id: '',
          leave_type: 'casual',
          start_date: '',
          end_date: '',
          days_requested: '',
          reason: ''
        })
        fetchData()
      }
    } catch (error: any) {
      console.error('Error submitting leave request:', error)
      toast.error(`Failed to submit leave request: ${error.message}`)
    }
  }

  const handleLeaveAction = async (leaveId: number, status: 'approved' | 'rejected', notes?: string) => {
    try {
      const response = await adminPut(`/api/coaching/leave-requests/${leaveId}/status`, {
        status,
        notes: notes || `Leave request ${status}`
      })
      
      if (response.success) {
        toast.success(`Leave request ${status} successfully!`)
        fetchData()
      }
    } catch (error: any) {
      console.error('Error updating leave request:', error)
      toast.error(`Failed to update leave request: ${error.message}`)
    }
  }

  const filteredLeaveRequests = leaveRequests.filter(request => {
    if (statusFilter === 'all') return true
    return request.status === statusFilter
  })

  return (
    <AdminLayout>
      <div className="px-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Human Resource Management</h1>
          <p className="text-gray-600 mt-2">Manage employees, leave requests, and salary records</p>
        </div>

        {/* Tab Navigation */}
        <nav className="flex space-x-8 mb-6">
          <button
            onClick={() => setActiveTab('employees')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'employees'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Employees ({employees.length})
          </button>
          <button
            onClick={() => setActiveTab('leaves')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'leaves'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Leave Requests ({leaveRequests.length})
          </button>
          <button
            onClick={() => setActiveTab('salary')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'salary'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Salary Management
          </button>
        </nav>

        {/* Employees Tab */}
        {activeTab === 'employees' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">Employee Management</h2>
                <p className="text-gray-600">Manage your coaching center staff</p>
              </div>
              <button
                onClick={() => setShowEmployeeForm(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                Add Employee
              </button>
            </div>

            {loading ? (
              <TableSkeleton rows={6} />
            ) : employees.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No employees found</p>
              </div>
            ) : (
              <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employee
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Position
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Contact
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Salary
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Leave Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {employees.map((employee) => (
                      <tr key={employee.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{employee.name}</div>
                            <div className="text-sm text-gray-500">ID: {employee.employee_id}</div>
                            <div className="text-sm text-gray-500">{employee.department}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{employee.position}</div>
                          <div className="text-sm text-gray-500">Since: {new Date(employee.hire_date).toLocaleDateString()}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{employee.email}</div>
                          <div className="text-sm text-gray-500">{employee.phone}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">৳{employee.salary?.toLocaleString()}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">Total: {employee.total_leave_requests}</div>
                          <div className="text-sm text-yellow-600">Pending: {employee.pending_leaves}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            employee.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : employee.status === 'inactive'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {employee.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Leave Requests Tab */}
        {activeTab === 'leaves' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">Leave Management</h2>
                <p className="text-gray-600">Manage employee leave requests</p>
              </div>
              <div className="flex space-x-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                </select>
                <button
                  onClick={() => setShowLeaveForm(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                >
                  New Leave Request
                </button>
              </div>
            </div>

            {loading ? (
              <TableSkeleton rows={6} />
            ) : filteredLeaveRequests.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No leave requests found</p>
              </div>
            ) : (
              <div className="bg-white shadow-md rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employee
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Leave Details
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredLeaveRequests.map((request) => (
                      <tr key={request.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{request.employee_name}</div>
                            <div className="text-sm text-gray-500">ID: {request.employee_id_code}</div>
                            <div className="text-sm text-gray-500">{request.position}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-900 capitalize">{request.leave_type} Leave</div>
                            <div className="text-sm text-gray-500">{request.reason}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {new Date(request.start_date).toLocaleDateString()} - {new Date(request.end_date).toLocaleDateString()}
                          </div>
                          <div className="text-sm text-gray-500">{request.days_requested} days</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            request.status === 'approved'
                              ? 'bg-green-100 text-green-800'
                              : request.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {request.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {request.status === 'pending' && (
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleLeaveAction(request.id, 'approved')}
                                className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                              >
                                Approve
                              </button>
                              <button
                                onClick={() => handleLeaveAction(request.id, 'rejected')}
                                className="text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700"
                              >
                                Reject
                              </button>
                            </div>
                          )}
                          {request.status !== 'pending' && request.approved_by_name && (
                            <div className="text-xs text-gray-500">
                              By: {request.approved_by_name}
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Salary Management Tab */}
        {activeTab === 'salary' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">Salary Management</h2>
                <p className="text-gray-600">Manage employee salary records and payments</p>
              </div>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                Process Salaries
              </button>
            </div>
            
            <div className="bg-white shadow-md rounded-lg p-6">
              <p className="text-gray-500 text-center">Salary management features coming soon...</p>
            </div>
          </div>
        )}
      </div>

      {/* Employee Form Modal */}
      {showEmployeeForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">Add New Employee</h3>
            <form onSubmit={handleEmployeeSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Employee ID</label>
                  <input
                    type="text"
                    value={employeeFormData.employee_id}
                    onChange={(e) => setEmployeeFormData({...employeeFormData, employee_id: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                  <input
                    type="text"
                    value={employeeFormData.name}
                    onChange={(e) => setEmployeeFormData({...employeeFormData, name: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    required
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={employeeFormData.email}
                    onChange={(e) => setEmployeeFormData({...employeeFormData, email: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                  <input
                    type="text"
                    value={employeeFormData.phone}
                    onChange={(e) => setEmployeeFormData({...employeeFormData, phone: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Position</label>
                  <input
                    type="text"
                    value={employeeFormData.position}
                    onChange={(e) => setEmployeeFormData({...employeeFormData, position: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                  <select
                    value={employeeFormData.department}
                    onChange={(e) => setEmployeeFormData({...employeeFormData, department: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  >
                    <option value="">Select Department</option>
                    <option value="Academic">Academic</option>
                    <option value="Administration">Administration</option>
                    <option value="Finance">Finance</option>
                    <option value="IT">IT</option>
                    <option value="Marketing">Marketing</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Hire Date</label>
                  <input
                    type="date"
                    value={employeeFormData.hire_date}
                    onChange={(e) => setEmployeeFormData({...employeeFormData, hire_date: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Salary (৳)</label>
                  <input
                    type="number"
                    value={employeeFormData.salary}
                    onChange={(e) => setEmployeeFormData({...employeeFormData, salary: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                <textarea
                  value={employeeFormData.address}
                  onChange={(e) => setEmployeeFormData({...employeeFormData, address: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  rows={2}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Emergency Contact</label>
                <input
                  type="text"
                  value={employeeFormData.emergency_contact}
                  onChange={(e) => setEmployeeFormData({...employeeFormData, emergency_contact: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>

              <div className="flex justify-end space-x-4 pt-4">
                <button
                  type="button"
                  onClick={() => setShowEmployeeForm(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Add Employee
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Leave Request Form Modal */}
      {showLeaveForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <h3 className="text-lg font-semibold mb-4">New Leave Request</h3>
            <form onSubmit={handleLeaveSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Employee</label>
                <select
                  value={leaveFormData.employee_id}
                  onChange={(e) => setLeaveFormData({...leaveFormData, employee_id: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  required
                >
                  <option value="">Select Employee</option>
                  {employees.map((employee) => (
                    <option key={employee.id} value={employee.id}>
                      {employee.name} ({employee.employee_id})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Leave Type</label>
                <select
                  value={leaveFormData.leave_type}
                  onChange={(e) => setLeaveFormData({...leaveFormData, leave_type: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  required
                >
                  <option value="casual">Casual Leave</option>
                  <option value="sick">Sick Leave</option>
                  <option value="annual">Annual Leave</option>
                  <option value="maternity">Maternity Leave</option>
                  <option value="emergency">Emergency Leave</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                  <input
                    type="date"
                    value={leaveFormData.start_date}
                    onChange={(e) => setLeaveFormData({...leaveFormData, start_date: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                  <input
                    type="date"
                    value={leaveFormData.end_date}
                    onChange={(e) => setLeaveFormData({...leaveFormData, end_date: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Days Requested</label>
                <input
                  type="number"
                  value={leaveFormData.days_requested}
                  onChange={(e) => setLeaveFormData({...leaveFormData, days_requested: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Reason</label>
                <textarea
                  value={leaveFormData.reason}
                  onChange={(e) => setLeaveFormData({...leaveFormData, reason: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  rows={3}
                  required
                />
              </div>

              <div className="flex justify-end space-x-4 pt-4">
                <button
                  type="button"
                  onClick={() => setShowLeaveForm(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Submit Request
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </AdminLayout>
  )
}
