import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import AdminLayout from '../../components/AdminLayout'
import { adminGet, adminPost, adminPut, adminDelete } from '../../utils/adminApi'
import { toast } from 'react-hot-toast'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Calendar, 
  BookOpen, 
  Users, 
  FileText,
  Search,
  Filter,
  Download
} from 'lucide-react'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Textarea } from '../../components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'
import { Badge } from '../../components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON><PERSON>rigger } from '../../components/ui/dialog'
import { useTranslation } from 'react-i18next'

interface Assignment {
  id: number
  title: string
  description: string
  course_id: number
  course_name: string
  batch_id: number
  batch_name: string
  due_date: string
  total_marks: number
  status: 'active' | 'completed' | 'cancelled'
  created_by: number
  created_at: string
  updated_at: string
}

interface Course {
  id: number
  name: string
  batches: Batch[]
}

interface Batch {
  id: number
  name: string
}

export default function AssignmentsPage() {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [assignments, setAssignments] = useState<Assignment[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterCourse, setFilterCourse] = useState<string>('all')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingAssignment, setEditingAssignment] = useState<Assignment | null>(null)

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    batch_id: '',
    due_date: '',
    total_marks: '',
    status: 'active' as const
  })

  useEffect(() => {
    fetchAssignments()
    fetchCourses()
  }, [])

  const fetchAssignments = async () => {
    try {
      setLoading(true)
      const response = await adminGet('/api/coaching/assignments')
      if (response.assignments) {
        setAssignments(response.assignments)
      }
    } catch (error) {
      console.error('Error fetching assignments:', error)
      toast.error('Failed to fetch assignments')
    } finally {
      setLoading(false)
    }
  }

  const fetchCourses = async () => {
    try {
      const response = await adminGet('/api/coaching/batches')
      if (response.batches) {
        // Group batches by course
        const coursesMap = new Map()
        response.batches.forEach((batch: any) => {
          if (!coursesMap.has(batch.course_id)) {
            coursesMap.set(batch.course_id, {
              id: batch.course_id,
              name: batch.course_name,
              batches: []
            })
          }
          coursesMap.get(batch.course_id).batches.push({
            id: batch.id,
            name: batch.name
          })
        })
        setCourses(Array.from(coursesMap.values()))
      }
    } catch (error) {
      console.error('Error fetching courses:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.title || !formData.batch_id || !formData.due_date) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      const assignmentData = {
        ...formData,
        batch_id: parseInt(formData.batch_id),
        total_marks: parseInt(formData.total_marks) || 100
      }

      if (editingAssignment) {
        await adminPut(`/api/coaching/assignments/${editingAssignment.id}`, assignmentData)
        toast.success('Assignment updated successfully')
      } else {
        await adminPost('/api/coaching/assignments', assignmentData)
        toast.success('Assignment created successfully')
      }

      setIsCreateModalOpen(false)
      setEditingAssignment(null)
      resetForm()
      fetchAssignments()
    } catch (error) {
      console.error('Error saving assignment:', error)
      toast.error('Failed to save assignment')
    }
  }

  const handleEdit = (assignment: Assignment) => {
    setEditingAssignment(assignment)
    setFormData({
      title: assignment.title,
      description: assignment.description,
      batch_id: assignment.batch_id.toString(),
      due_date: assignment.due_date.split('T')[0],
      total_marks: assignment.total_marks.toString(),
      status: assignment.status
    })
    setIsCreateModalOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this assignment?')) return

    try {
      await adminDelete(`/api/coaching/assignments/${id}`)
      toast.success('Assignment deleted successfully')
      fetchAssignments()
    } catch (error) {
      console.error('Error deleting assignment:', error)
      toast.error('Failed to delete assignment')
    }
  }

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      batch_id: '',
      due_date: '',
      total_marks: '',
      status: 'active'
    })
  }

  const filteredAssignments = assignments.filter(assignment => {
    const matchesSearch = assignment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.course_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.batch_name?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === 'all' || assignment.status === filterStatus
    const matchesCourse = filterCourse === 'all' || assignment.course_id?.toString() === filterCourse

    return matchesSearch && matchesStatus && matchesCourse
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const selectedBatch = courses.flatMap(course => course.batches).find(batch => batch.id.toString() === formData.batch_id)
  const selectedCourse = courses.find(course => course.batches.some(batch => batch.id.toString() === formData.batch_id))

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">অ্যাসাইনমেন্ট ব্যবস্থাপনা</h1>
            <p className="text-muted-foreground">কোর্স এবং ব্যাচের জন্য অ্যাসাইনমেন্ট তৈরি ও পরিচালনা করুন</p>
          </div>
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => { resetForm(); setEditingAssignment(null) }}>
                <Plus className="h-4 w-4 mr-2" />
                নতুন অ্যাসাইনমেন্ট
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {editingAssignment ? 'অ্যাসাইনমেন্ট সম্পাদনা' : 'নতুন অ্যাসাইনমেন্ট তৈরি'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="title">শিরোনাম *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="অ্যাসাইনমেন্টের শিরোনাম"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">বিবরণ</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="অ্যাসাইনমেন্টের বিস্তারিত বিবরণ"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="batch_id">ব্যাচ *</Label>
                  <Select value={formData.batch_id} onValueChange={(value) => setFormData({ ...formData, batch_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="ব্যাচ নির্বাচন করুন" />
                    </SelectTrigger>
                    <SelectContent>
                      {courses.map((course) => (
                        <div key={course.id}>
                          <div className="px-2 py-1 text-sm font-medium text-gray-500">{course.name}</div>
                          {course.batches.map((batch) => (
                            <SelectItem key={batch.id} value={batch.id.toString()}>
                              {batch.name}
                            </SelectItem>
                          ))}
                        </div>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="due_date">জমা দেওয়ার তারিখ *</Label>
                    <Input
                      id="due_date"
                      type="date"
                      value={formData.due_date}
                      onChange={(e) => setFormData({ ...formData, due_date: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="total_marks">মোট নম্বর</Label>
                    <Input
                      id="total_marks"
                      type="number"
                      value={formData.total_marks}
                      onChange={(e) => setFormData({ ...formData, total_marks: e.target.value })}
                      placeholder="100"
                    />
                  </div>
                  <div>
                    <Label htmlFor="status">অবস্থা</Label>
                    <Select value={formData.status} onValueChange={(value: any) => setFormData({ ...formData, status: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">সক্রিয়</SelectItem>
                        <SelectItem value="completed">সম্পন্ন</SelectItem>
                        <SelectItem value="cancelled">বাতিল</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                    বাতিল
                  </Button>
                  <Button type="submit">
                    {editingAssignment ? 'আপডেট করুন' : 'তৈরি করুন'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex gap-4 items-center">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="অ্যাসাইনমেন্ট বা কোর্স খুঁজুন..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="অবস্থা" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">সব অবস্থা</SelectItem>
                  <SelectItem value="active">সক্রিয়</SelectItem>
                  <SelectItem value="completed">সম্পন্ন</SelectItem>
                  <SelectItem value="cancelled">বাতিল</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterCourse} onValueChange={setFilterCourse}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="কোর্স" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">সব কোর্স</SelectItem>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id.toString()}>
                      {course.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Assignments List */}
        <div className="grid gap-4">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">লোড হচ্ছে...</p>
            </div>
          ) : filteredAssignments.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">কোনো অ্যাসাইনমেন্ট পাওয়া যায়নি</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || filterStatus !== 'all' || filterCourse !== 'all' 
                    ? 'আপনার ফিল্টার অনুযায়ী কোনো অ্যাসাইনমেন্ট পাওয়া যায়নি।'
                    : 'এখনো কোনো অ্যাসাইনমেন্ট তৈরি করা হয়নি।'
                  }
                </p>
                <Button onClick={() => setIsCreateModalOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  প্রথম অ্যাসাইনমেন্ট তৈরি করুন
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredAssignments.map((assignment) => (
              <Card key={assignment.id}>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-semibold">{assignment.title}</h3>
                        <Badge className={getStatusColor(assignment.status)}>
                          {assignment.status === 'active' ? 'সক্রিয়' :
                           assignment.status === 'completed' ? 'সম্পন্ন' : 'বাতিল'}
                        </Badge>
                      </div>
                      
                      {assignment.description && (
                        <p className="text-muted-foreground mb-3">{assignment.description}</p>
                      )}
                      
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        {assignment.course_name && (
                          <div className="flex items-center gap-1">
                            <BookOpen className="h-4 w-4" />
                            <span>{assignment.course_name}</span>
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          <span>{assignment.batch_name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>জমা: {new Date(assignment.due_date).toLocaleDateString('bn-BD')}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <FileText className="h-4 w-4" />
                          <span>নম্বর: {assignment.total_marks}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(assignment)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(assignment.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </AdminLayout>
  )
}
