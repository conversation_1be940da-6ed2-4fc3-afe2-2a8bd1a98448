import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import AdminLayout from '../../components/AdminLayout'
import { adminGet, adminPost, adminPut } from '../../utils/adminApi'

interface Exam {
  id: number
  exam_name: string
  exam_description: string
  exam_type: 'midterm' | 'final' | 'quiz' | 'mock_test' | 'assignment' | 'practical'
  subject_name: string
  exam_date: string
  start_time: string
  end_time: string
  duration_minutes: number
  room_name: string
  room_code: string
  total_marks: number
  passing_marks: number
  status: 'draft' | 'scheduled' | 'in_progress' | 'completed' | 'results_published' | 'cancelled'
  batch_count: number
  supervisor_count: number
  student_count: number
}

interface Course {
  id: number
  name: string
  code: string
}

interface Room {
  id: number
  room_name: string
  room_code: string
  capacity: number
}

interface Batch {
  id: number
  name: string
  course_id: number
}

interface Teacher {
  id: number
  name: string
  email: string
}

export default function ExamsPage() {
  const { t } = useTranslation()
  const [exams, setExams] = useState<Exam[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [rooms, setRooms] = useState<Room[]>([])
  const [batches, setBatches] = useState<Batch[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null)

  const [examForm, setExamForm] = useState({
    exam_name: '',
    exam_description: '',
    exam_type: 'midterm' as const,
    subject_id: '',
    exam_date: '',
    start_time: '',
    end_time: '',
    room_id: '',
    total_marks: '',
    passing_marks: '',
    exam_instructions: '',
    batch_ids: [] as number[],
    supervisor_ids: [] as { id: number; role: string }[]
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [examsRes, coursesRes, roomsRes, batchesRes, teachersRes] = await Promise.all([
        adminGet('/api/coaching/exams'),
        adminGet('/api/coaching/courses'),
        adminGet('/api/coaching/rooms'),
        adminGet('/api/coaching/batches'),
        adminGet('/api/coaching/teachers')
      ])

      setExams(examsRes.exams || [])
      setCourses(coursesRes.courses || [])
      setRooms(roomsRes.rooms || [])
      setBatches(batchesRes.batches || [])
      setTeachers(teachersRes.teachers || [])
      
    } catch (error: any) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateExam = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!examForm.exam_name || !examForm.exam_date || !examForm.start_time || !examForm.end_time) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      const response = await adminPost('/api/coaching/exams', {
        ...examForm,
        total_marks: parseFloat(examForm.total_marks),
        passing_marks: parseFloat(examForm.passing_marks),
        subject_id: examForm.subject_id ? parseInt(examForm.subject_id) : null,
        room_id: examForm.room_id ? parseInt(examForm.room_id) : null
      })

      if (response.success) {
        toast.success('Exam created successfully!')
        setShowCreateModal(false)
        resetForm()
        fetchData()
      }
    } catch (error: any) {
      console.error('Error creating exam:', error)
      toast.error(`Failed to create exam: ${error.message}`)
    }
  }

  const updateExamStatus = async (examId: number, status: string) => {
    try {
      const response = await adminPut(`/api/coaching/exams/${examId}/status`, { status })
      
      if (response.success) {
        toast.success(`Exam status updated to ${status}`)
        fetchData()
      }
    } catch (error: any) {
      console.error('Error updating exam status:', error)
      toast.error(`Failed to update exam status: ${error.message}`)
    }
  }

  const resetForm = () => {
    setExamForm({
      exam_name: '',
      exam_description: '',
      exam_type: 'midterm',
      subject_id: '',
      exam_date: '',
      start_time: '',
      end_time: '',
      room_id: '',
      total_marks: '',
      passing_marks: '',
      exam_instructions: '',
      batch_ids: [],
      supervisor_ids: []
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'results_published': return 'bg-purple-100 text-purple-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getExamTypeLabel = (type: string) => {
    const types = {
      midterm: 'Midterm',
      final: 'Final',
      quiz: 'Quiz',
      mock_test: 'Mock Test',
      assignment: 'Assignment',
      practical: 'Practical'
    }
    return types[type] || type
  }

  const formatTime = (time: string) => {
    if (!time) return ''
    const [hours, minutes] = time.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'PM' : 'AM'
    const hour12 = hour % 12 || 12
    return `${hour12}:${minutes} ${ampm}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB')
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Loading exams...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('exams.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t('exams.description')}
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            {t('exams.createExam')}
          </button>
        </div>

        {/* Exam Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Total Exams</h3>
            <p className="text-3xl font-bold text-blue-600">{exams.length}</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Scheduled</h3>
            <p className="text-3xl font-bold text-yellow-600">
              {exams.filter(e => e.status === 'scheduled').length}
            </p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Completed</h3>
            <p className="text-3xl font-bold text-green-600">
              {exams.filter(e => e.status === 'completed').length}
            </p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Results Published</h3>
            <p className="text-3xl font-bold text-purple-600">
              {exams.filter(e => e.status === 'results_published').length}
            </p>
          </div>
        </div>

        {/* Exams Table */}
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              All Exams
            </h2>
          </div>
          
          {exams.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No exams found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Exam Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Schedule
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Marks
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Participants
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {exams.map((exam) => (
                    <tr key={exam.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {exam.exam_name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {getExamTypeLabel(exam.exam_type)} • {exam.subject_name}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {formatDate(exam.exam_date)}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {formatTime(exam.start_time)} - {formatTime(exam.end_time)}
                          </div>
                          <div className="text-xs text-gray-400">
                            {exam.room_code} • {exam.duration_minutes} min
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            Total: {exam.total_marks}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            Pass: {exam.passing_marks}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {exam.student_count} students
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {exam.batch_count} batches • {exam.supervisor_count} supervisors
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(exam.status)}`}>
                          {exam.status.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <button
                            onClick={() => window.location.href = `/coaching/exams/${exam.id}`}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            View
                          </button>
                          {exam.status === 'completed' && (
                            <button
                              onClick={() => window.location.href = `/coaching/exams/${exam.id}/results`}
                              className="text-green-600 hover:text-green-900"
                            >
                              Results
                            </button>
                          )}
                          {exam.status === 'draft' && (
                            <button
                              onClick={() => updateExamStatus(exam.id, 'scheduled')}
                              className="text-purple-600 hover:text-purple-900"
                            >
                              Schedule
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create Exam Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Create New Exam
              </h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleCreateExam} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-1">Exam Name *</label>
                  <input
                    type="text"
                    required
                    value={examForm.exam_name}
                    onChange={(e) => setExamForm(prev => ({ ...prev, exam_name: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="Enter exam name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Exam Type</label>
                  <select
                    value={examForm.exam_type}
                    onChange={(e) => setExamForm(prev => ({ ...prev, exam_type: e.target.value as any }))}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="midterm">Midterm</option>
                    <option value="final">Final</option>
                    <option value="quiz">Quiz</option>
                    <option value="mock_test">Mock Test</option>
                    <option value="assignment">Assignment</option>
                    <option value="practical">Practical</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Subject</label>
                  <select
                    value={examForm.subject_id}
                    onChange={(e) => setExamForm(prev => ({ ...prev, subject_id: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="">Select Subject</option>
                    {courses.map((course) => (
                      <option key={course.id} value={course.id}>
                        {course.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Exam Date *</label>
                  <input
                    type="date"
                    required
                    value={examForm.exam_date}
                    onChange={(e) => setExamForm(prev => ({ ...prev, exam_date: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Room</label>
                  <select
                    value={examForm.room_id}
                    onChange={(e) => setExamForm(prev => ({ ...prev, room_id: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="">Select Room</option>
                    {rooms.map((room) => (
                      <option key={room.id} value={room.id}>
                        {room.room_code} - {room.room_name} (Capacity: {room.capacity})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Start Time *</label>
                  <input
                    type="time"
                    required
                    value={examForm.start_time}
                    onChange={(e) => setExamForm(prev => ({ ...prev, start_time: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">End Time *</label>
                  <input
                    type="time"
                    required
                    value={examForm.end_time}
                    onChange={(e) => setExamForm(prev => ({ ...prev, end_time: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Total Marks *</label>
                  <input
                    type="number"
                    required
                    min="1"
                    value={examForm.total_marks}
                    onChange={(e) => setExamForm(prev => ({ ...prev, total_marks: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="100"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Passing Marks *</label>
                  <input
                    type="number"
                    required
                    min="1"
                    value={examForm.passing_marks}
                    onChange={(e) => setExamForm(prev => ({ ...prev, passing_marks: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    placeholder="40"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-1">Description</label>
                  <textarea
                    value={examForm.exam_description}
                    onChange={(e) => setExamForm(prev => ({ ...prev, exam_description: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    rows={3}
                    placeholder="Exam description..."
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-1">Exam Instructions</label>
                  <textarea
                    value={examForm.exam_instructions}
                    onChange={(e) => setExamForm(prev => ({ ...prev, exam_instructions: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    rows={3}
                    placeholder="Instructions for students..."
                  />
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700"
                >
                  Create Exam
                </button>
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </AdminLayout>
  )
}
