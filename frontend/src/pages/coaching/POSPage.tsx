import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import { useApi } from '../../hooks/useApi'
import AdminLayout from '../../components/AdminLayout'

interface POSItem {
  id: number
  item_code: string
  name: string
  description: string
  unit: string
  current_stock: number
  selling_price: number
  barcode: string
  category_name: string
}

interface CartItem extends POSItem {
  quantity: number
  total: number
}

interface Student {
  id: number
  name: string
  email: string
  phone: string
}

export default function POSPage() {
  const { user } = useAuth()
  const { t } = useLanguage()
  const { adminGet, adminPost } = useApi()
  
  const [items, setItems] = useState<POSItem[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [cart, setCart] = useState<CartItem[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCustomer, setSelectedCustomer] = useState<Student | null>(null)
  const [customerName, setCustomerName] = useState('')
  const [customerPhone, setCustomerPhone] = useState('')
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'mobile_banking' | 'credit'>('cash')
  const [discountPercent, setDiscountPercent] = useState(0)
  const [taxPercent, setTaxPercent] = useState(0)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchItems()
    fetchStudents()
  }, [])

  const fetchItems = async () => {
    try {
      const response = await adminGet(`/api/coaching/pos/items?search=${searchTerm}`)
      setItems(response.items || [])
    } catch (error: any) {
      console.error('Error fetching items:', error)
      toast.error('Failed to fetch items')
    }
  }

  const fetchStudents = async () => {
    try {
      const response = await adminGet('/api/coaching/pos/students')
      setStudents(response.students || [])
    } catch (error: any) {
      console.error('Error fetching students:', error)
    }
  }

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      fetchItems()
    }, 300)

    return () => clearTimeout(delayedSearch)
  }, [searchTerm])

  const addToCart = (item: POSItem) => {
    const existingItem = cart.find(cartItem => cartItem.id === item.id)
    
    if (existingItem) {
      if (existingItem.quantity >= item.current_stock) {
        toast.error(t('pos.insufficientStock'))
        return
      }
      
      setCart(cart.map(cartItem =>
        cartItem.id === item.id
          ? { ...cartItem, quantity: cartItem.quantity + 1, total: (cartItem.quantity + 1) * cartItem.selling_price }
          : cartItem
      ))
    } else {
      setCart([...cart, { ...item, quantity: 1, total: item.selling_price }])
    }
    
    toast.success(`${item.name} ${t('pos.addToCart')}`)
  }

  const updateCartQuantity = (itemId: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId)
      return
    }

    const item = items.find(i => i.id === itemId)
    if (item && quantity > item.current_stock) {
      toast.error(t('pos.insufficientStock'))
      return
    }

    setCart(cart.map(cartItem =>
      cartItem.id === itemId
        ? { ...cartItem, quantity, total: quantity * cartItem.selling_price }
        : cartItem
    ))
  }

  const removeFromCart = (itemId: number) => {
    setCart(cart.filter(cartItem => cartItem.id !== itemId))
  }

  const clearCart = () => {
    setCart([])
    setSelectedCustomer(null)
    setCustomerName('')
    setCustomerPhone('')
    setDiscountPercent(0)
    setTaxPercent(0)
  }

  const calculateTotals = () => {
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0)
    const discountAmount = (subtotal * discountPercent) / 100
    const taxableAmount = subtotal - discountAmount
    const taxAmount = (taxableAmount * taxPercent) / 100
    const finalTotal = taxableAmount + taxAmount

    return {
      subtotal,
      discountAmount,
      taxAmount,
      finalTotal
    }
  }

  const generateSaleNumber = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const time = String(now.getTime()).slice(-6)
    return `SAL${year}${month}${day}${time}`
  }

  const processSale = async () => {
    if (cart.length === 0) {
      toast.error(t('pos.cartEmpty'))
      return
    }

    const totals = calculateTotals()
    
    if (paymentMethod === 'credit' && !selectedCustomer) {
      toast.error('Please select a student for credit sales')
      return
    }

    setLoading(true)

    try {
      const saleData = {
        sale_number: generateSaleNumber(),
        student_id: selectedCustomer?.id || null,
        customer_name: selectedCustomer ? selectedCustomer.name : customerName,
        customer_phone: selectedCustomer ? selectedCustomer.phone : customerPhone,
        items: cart.map(item => ({
          item_id: item.id,
          quantity: item.quantity,
          unit_price: item.selling_price
        })),
        payment_method: paymentMethod,
        payment_status: paymentMethod === 'credit' ? 'pending' : 'paid',
        amount_paid: paymentMethod === 'credit' ? 0 : totals.finalTotal,
        tax_amount: totals.taxAmount,
        discount_amount: totals.discountAmount,
        notes: `POS Sale - ${paymentMethod}`
      }

      const response = await adminPost('/api/coaching/sales', saleData)
      
      if (response.success) {
        toast.success(t('pos.completeSale'))
        clearCart()
        // Here you could print receipt or show sale details
      }
    } catch (error: any) {
      console.error('Error processing sale:', error)
      toast.error(`Failed to process sale: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const totals = calculateTotals()

  return (
    <AdminLayout>
      <div className="px-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">{t('pos.title')}</h1>
          <p className="text-gray-600 mt-2">{t('pos.description')}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Items Section */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="mb-4">
                <div className="flex space-x-4">
                  <div className="flex-1">
                    <input
                      type="text"
                      placeholder={t('pos.searchItems')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-4 py-2"
                    />
                  </div>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    {t('pos.scanBarcode')}
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                {items.map((item) => (
                  <div
                    key={item.id}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md cursor-pointer"
                    onClick={() => addToCart(item)}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium text-sm">{item.name}</h3>
                      <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                        {item.current_stock} {item.unit}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mb-2">{item.category_name}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-green-600">
                        ৳{item.selling_price.toLocaleString()}
                      </span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          addToCart(item)
                        }}
                        className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                      >
                        {t('pos.addToCart')}
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {items.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">{t('pos.itemNotFound')}</p>
                </div>
              )}
            </div>
          </div>

          {/* Cart Section */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">{t('pos.shoppingCart')}</h2>
                <button
                  onClick={clearCart}
                  className="text-sm text-red-600 hover:text-red-800"
                >
                  {t('pos.clearCart')}
                </button>
              </div>

              {/* Customer Selection */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('pos.selectCustomer')}
                </label>
                <select
                  value={selectedCustomer?.id || ''}
                  onChange={(e) => {
                    const student = students.find(s => s.id === parseInt(e.target.value))
                    setSelectedCustomer(student || null)
                  }}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
                >
                  <option value="">{t('pos.newCustomer')}</option>
                  {students.map((student) => (
                    <option key={student.id} value={student.id}>
                      {student.name} - {student.phone}
                    </option>
                  ))}
                </select>
              </div>

              {!selectedCustomer && (
                <div className="mb-4 space-y-2">
                  <input
                    type="text"
                    placeholder={t('sales.customerName')}
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
                  />
                  <input
                    type="text"
                    placeholder={t('sales.customerPhone')}
                    value={customerPhone}
                    onChange={(e) => setCustomerPhone(e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
                  />
                </div>
              )}

              {/* Cart Items */}
              <div className="mb-4 max-h-48 overflow-y-auto">
                {cart.map((item) => (
                  <div key={item.id} className="flex items-center justify-between py-2 border-b">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{item.name}</p>
                      <p className="text-xs text-gray-500">৳{item.selling_price} each</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateCartQuantity(item.id, item.quantity - 1)}
                        className="w-6 h-6 bg-gray-200 rounded text-xs"
                      >
                        -
                      </button>
                      <span className="text-sm w-8 text-center">{item.quantity}</span>
                      <button
                        onClick={() => updateCartQuantity(item.id, item.quantity + 1)}
                        className="w-6 h-6 bg-gray-200 rounded text-xs"
                      >
                        +
                      </button>
                      <button
                        onClick={() => removeFromCart(item.id)}
                        className="text-red-600 text-xs ml-2"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {cart.length === 0 && (
                <div className="text-center py-4">
                  <p className="text-gray-500 text-sm">{t('pos.cartEmpty')}</p>
                </div>
              )}

              {/* Discount and Tax */}
              <div className="mb-4 space-y-2">
                <div className="flex items-center space-x-2">
                  <label className="text-sm">{t('pos.discountPercent')}:</label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={discountPercent}
                    onChange={(e) => setDiscountPercent(parseFloat(e.target.value) || 0)}
                    className="w-16 border border-gray-300 rounded px-2 py-1 text-sm"
                  />
                  <span className="text-sm">%</span>
                </div>
                <div className="flex items-center space-x-2">
                  <label className="text-sm">{t('pos.taxPercent')}:</label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={taxPercent}
                    onChange={(e) => setTaxPercent(parseFloat(e.target.value) || 0)}
                    className="w-16 border border-gray-300 rounded px-2 py-1 text-sm"
                  />
                  <span className="text-sm">%</span>
                </div>
              </div>

              {/* Totals */}
              {cart.length > 0 && (
                <div className="mb-4 space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>{t('purchaseOrders.subtotal')}:</span>
                    <span>৳{totals.subtotal.toLocaleString()}</span>
                  </div>
                  {totals.discountAmount > 0 && (
                    <div className="flex justify-between text-red-600">
                      <span>{t('pos.discountAmount')}:</span>
                      <span>-৳{totals.discountAmount.toLocaleString()}</span>
                    </div>
                  )}
                  {totals.taxAmount > 0 && (
                    <div className="flex justify-between">
                      <span>{t('purchaseOrders.taxAmount')}:</span>
                      <span>৳{totals.taxAmount.toLocaleString()}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-bold text-lg border-t pt-1">
                    <span>{t('pos.finalTotal')}:</span>
                    <span>৳{totals.finalTotal.toLocaleString()}</span>
                  </div>
                </div>
              )}

              {/* Payment Method */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('sales.paymentMethod')}
                </label>
                <select
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value as any)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
                >
                  <option value="cash">{t('sales.cash')}</option>
                  <option value="card">{t('sales.card')}</option>
                  <option value="mobile_banking">{t('sales.mobileBanking')}</option>
                  <option value="credit">{t('sales.credit')}</option>
                </select>
              </div>

              {/* Checkout Button */}
              <button
                onClick={processSale}
                disabled={cart.length === 0 || loading}
                className="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 disabled:bg-gray-400"
              >
                {loading ? t('common.loading') : t('pos.checkout')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
