import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import AdminLayout from '../../components/AdminLayout'
import { adminGet, adminPost, adminPut, adminDelete } from '../../utils/adminApi'
import { toast } from 'react-hot-toast'
import { 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  Key, 
  Shield, 
  Search,
  Filter,
  MoreVertical,
  UserCheck,
  UserX,
  Activity
} from 'lucide-react'

interface User {
  id: number
  name: string
  email: string
  phone: string
  role: 'center_admin' | 'teacher' | 'accountant' | 'receptionist'
  status: 'active' | 'inactive' | 'suspended'
  branch_id?: number
  branch_name?: string
  last_login?: string
  created_at: string
  permissions?: string[]
}

interface UserFormData {
  name: string
  email: string
  phone: string
  role: string
  branch_id: string
  password: string
  confirmPassword: string
  permissions: string[]
}

const UserManagementPage: React.FC = () => {
  const navigate = useNavigate()
  
  const [users, setUsers] = useState<User[]>([])
  const [branches, setBranches] = useState<any[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showModal, setShowModal] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  
  const [formData, setFormData] = useState<UserFormData>({
    name: '',
    email: '',
    phone: '',
    role: 'teacher',
    branch_id: '',
    password: '',
    confirmPassword: '',
    permissions: []
  })

  const [passwordData, setPasswordData] = useState({
    newPassword: '',
    confirmPassword: ''
  })

  const roleOptions = [
    { value: 'teacher', label: 'শিক্ষক', permissions: ['view_students', 'manage_attendance', 'view_courses'] },
    { value: 'accountant', label: 'হিসাবরক্ষক', permissions: ['manage_fees', 'view_reports', 'manage_expenses'] },
    { value: 'receptionist', label: 'রিসেপশনিস্ট', permissions: ['manage_admissions', 'view_students', 'basic_reports'] },
    { value: 'center_admin', label: 'সেন্টার অ্যাডমিন', permissions: ['full_access'] }
  ]

  useEffect(() => {
    fetchUsers()
    fetchBranches()
  }, [])

  useEffect(() => {
    filterUsers()
  }, [users, searchTerm, roleFilter, statusFilter])

  const fetchUsers = async () => {
    try {
      const response = await adminGet('/api/coaching/users')
      setUsers(response.users || [])
    } catch (error) {
      console.error('Error fetching users:', error)
      toast.error('ব্যবহারকারীদের তালিকা লোড করতে ব্যর্থ')
    }
  }

  const fetchBranches = async () => {
    try {
      const response = await adminGet('/api/coaching/branches')
      setBranches(response.branches || [])
    } catch (error) {
      console.error('Error fetching branches:', error)
    }
  }

  const filterUsers = () => {
    let filtered = users

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter)
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => user.status === statusFilter)
    }

    setFilteredUsers(filtered)
  }

  const openModal = (user?: User) => {
    if (user) {
      setEditingUser(user)
      setFormData({
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        branch_id: user.branch_id?.toString() || '',
        password: '',
        confirmPassword: '',
        permissions: user.permissions || []
      })
    } else {
      setEditingUser(null)
      setFormData({
        name: '',
        email: '',
        phone: '',
        role: 'teacher',
        branch_id: '',
        password: '',
        confirmPassword: '',
        permissions: []
      })
    }
    setShowModal(true)
  }

  const closeModal = () => {
    setShowModal(false)
    setEditingUser(null)
    setFormData({
      name: '',
      email: '',
      phone: '',
      role: 'teacher',
      branch_id: '',
      password: '',
      confirmPassword: '',
      permissions: []
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name || !formData.email || !formData.role) {
      toast.error('অনুগ্রহ করে সব প্রয়োজনীয় ক্ষেত্র পূরণ করুন')
      return
    }

    if (!editingUser && (!formData.password || formData.password !== formData.confirmPassword)) {
      toast.error('পাসওয়ার্ড মিলছে না')
      return
    }

    setIsLoading(true)

    try {
      const userData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        role: formData.role,
        branch_id: formData.branch_id ? parseInt(formData.branch_id) : null,
        permissions: formData.permissions
      }

      if (editingUser) {
        const response = await adminPut(`/api/coaching/users/${editingUser.id}`, userData)
        if (response.success) {
          toast.success('ব্যবহারকারী সফলভাবে আপডেট হয়েছে!')
          fetchUsers()
          closeModal()
        }
      } else {
        const response = await adminPost('/api/coaching/users', {
          ...userData,
          password: formData.password
        })
        if (response.success) {
          toast.success('নতুন ব্যবহারকারী সফলভাবে তৈরি হয়েছে!')
          fetchUsers()
          closeModal()
        }
      }
    } catch (error) {
      console.error('Error saving user:', error)
      toast.error(`ব্যবহারকারী সংরক্ষণ করতে ব্যর্থ: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const updateUserStatus = async (userId: number, newStatus: string) => {
    try {
      const response = await adminPut(`/api/coaching/users/${userId}`, { status: newStatus })
      if (response.success) {
        toast.success('ব্যবহারকারীর স্ট্যাটাস সফলভাবে আপডেট হয়েছে!')
        fetchUsers()
      }
    } catch (error) {
      console.error('Error updating user status:', error)
      toast.error(`স্ট্যাটাস আপডেট করতে ব্যর্থ: ${error.message}`)
    }
  }

  const handleStatusToggle = async (user: User) => {
    const newStatus = user.status === 'active' ? 'inactive' : 'active'
    const actionText = newStatus === 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'
    
    const confirmed = window.confirm(
      `আপনি কি নিশ্চিত যে আপনি ${user.name} কে ${actionText} করতে চান?`
    )
    
    if (confirmed) {
      await updateUserStatus(user.id, newStatus)
    }
  }

  const openPasswordModal = (user: User) => {
    setSelectedUser(user)
    setPasswordData({ newPassword: '', confirmPassword: '' })
    setShowPasswordModal(true)
  }

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!passwordData.newPassword || passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('পাসওয়ার্ড মিলছে না')
      return
    }

    if (!selectedUser) return

    setIsLoading(true)

    try {
      const response = await adminPut(`/api/coaching/users/${selectedUser.id}/password`, {
        password: passwordData.newPassword
      })
      
      if (response.success) {
        toast.success('পাসওয়ার্ড সফলভাবে রিসেট হয়েছে!')
        setShowPasswordModal(false)
        setSelectedUser(null)
        setPasswordData({ newPassword: '', confirmPassword: '' })
      }
    } catch (error) {
      console.error('Error resetting password:', error)
      toast.error(`পাসওয়ার্ড রিসেট করতে ব্যর্থ: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const deleteUser = async (userId: number) => {
    const confirmed = window.confirm('আপনি কি নিশ্চিত যে আপনি এই ব্যবহারকারীকে মুছে ফেলতে চান?')
    
    if (confirmed) {
      try {
        const response = await adminDelete(`/api/coaching/users/${userId}`)
        if (response.success) {
          toast.success('ব্যবহারকারী সফলভাবে মুছে ফেলা হয়েছে!')
          fetchUsers()
        }
      } catch (error) {
        console.error('Error deleting user:', error)
        toast.error(`ব্যবহারকারী মুছে ফেলতে ব্যর্থ: ${error.message}`)
      }
    }
  }

  const getRoleLabel = (role: string) => {
    const roleOption = roleOptions.find(option => option.value === role)
    return roleOption ? roleOption.label : role
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: 'সক্রিয়', class: 'bg-green-100 text-green-800' },
      inactive: { label: 'নিষ্ক্রিয়', class: 'bg-gray-100 text-gray-800' },
      suspended: { label: 'স্থগিত', class: 'bg-red-100 text-red-800' }
    }
    
    const config = statusConfig[status] || statusConfig.inactive
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${config.class}`}>
        {config.label}
      </span>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">ব্যবহারকারী ব্যবস্থাপনা</h1>
            <p className="text-gray-600">কেন্দ্রের ব্যবহারকারী এবং তাদের অনুমতি পরিচালনা করুন</p>
          </div>
          <button
            onClick={() => openModal()}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="h-4 w-4" />
            নতুন ব্যবহারকারী
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">ব্যবহারকারী অনুসন্ধান</label>
              <input
                type="text"
                placeholder="নাম বা ইমেইল দিয়ে অনুসন্ধান করুন..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full border rounded-lg px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">ভূমিকা অনুযায়ী ফিল্টার</label>
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="w-full border rounded-lg px-3 py-2"
              >
                <option value="all">সব ভূমিকা</option>
                {roleOptions.map(role => (
                  <option key={role.value} value={role.value}>{role.label}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">স্ট্যাটাস অনুযায়ী ফিল্টার</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full border rounded-lg px-3 py-2"
              >
                <option value="all">সব স্ট্যাটাস</option>
                <option value="active">সক্রিয়</option>
                <option value="inactive">নিষ্ক্রিয়</option>
                <option value="suspended">স্থগিত</option>
              </select>
            </div>
            <div className="flex items-end">
              <div className="text-sm text-gray-600">
                মোট ব্যবহারকারী: {filteredUsers.length}
              </div>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ব্যবহারকারী
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ভূমিকা
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    শাখা
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    স্ট্যাটাস
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    শেষ লগইন
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    কার্যক্রম
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                        <div className="text-sm text-gray-500">{user.phone}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        {getRoleLabel(user.role)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.branch_name || 'সব শাখা'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(user.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.last_login ? new Date(user.last_login).toLocaleDateString('bn-BD') : 'কখনো নয়'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => openModal(user)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        সম্পাদনা
                      </button>
                      <button
                        onClick={() => openPasswordModal(user)}
                        className="text-yellow-600 hover:text-yellow-900"
                      >
                        পাসওয়ার্ড
                      </button>
                      <button
                        onClick={() => handleStatusToggle(user)}
                        className={`${
                          user.status === 'active'
                            ? 'text-red-600 hover:text-red-900'
                            : 'text-green-600 hover:text-green-900'
                        }`}
                      >
                        {user.status === 'active' ? 'নিষ্ক্রিয়' : 'সক্রিয়'}
                      </button>
                      {user.role !== 'center_admin' && (
                        <button
                          onClick={() => deleteUser(user.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          মুছুন
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* User Form Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
              <h2 className="text-xl font-bold mb-4">
                {editingUser ? 'ব্যবহারকারী সম্পাদনা' : 'নতুন ব্যবহারকারী'}
              </h2>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">নাম *</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">ইমেইল *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">ফোন</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">ভূমিকা *</label>
                  <select
                    name="role"
                    value={formData.role}
                    onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    required
                  >
                    {roleOptions.map(role => (
                      <option key={role.value} value={role.value}>{role.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">শাখা</label>
                  <select
                    name="branch_id"
                    value={formData.branch_id}
                    onChange={(e) => setFormData(prev => ({ ...prev, branch_id: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  >
                    <option value="">সব শাখা</option>
                    {branches.map(branch => (
                      <option key={branch.id} value={branch.id}>{branch.branch_name}</option>
                    ))}
                  </select>
                </div>

                {!editingUser && (
                  <>
                    <div>
                      <label className="block text-sm font-medium mb-1">পাসওয়ার্ড *</label>
                      <input
                        type="password"
                        name="password"
                        value={formData.password}
                        onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                        className="w-full border rounded-lg px-3 py-2"
                        required={!editingUser}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">পাসওয়ার্ড নিশ্চিত করুন *</label>
                      <input
                        type="password"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        className="w-full border rounded-lg px-3 py-2"
                        required={!editingUser}
                      />
                    </div>
                  </>
                )}

                <div className="flex gap-2 pt-4">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex-1 disabled:opacity-50"
                  >
                    {isLoading ? 'সংরক্ষণ হচ্ছে...' : 'সংরক্ষণ করুন'}
                  </button>
                  <button
                    type="button"
                    onClick={closeModal}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 flex-1"
                  >
                    বাতিল
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Password Reset Modal */}
        {showPasswordModal && selectedUser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h2 className="text-xl font-bold mb-4">পাসওয়ার্ড রিসেট</h2>
              <p className="text-gray-600 mb-4">
                <strong>{selectedUser.name}</strong> এর পাসওয়ার্ড রিসেট করুন
              </p>

              <form onSubmit={handlePasswordReset} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">নতুন পাসওয়ার্ড</label>
                  <input
                    type="password"
                    value={passwordData.newPassword}
                    onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">পাসওয়ার্ড নিশ্চিত করুন</label>
                  <input
                    type="password"
                    value={passwordData.confirmPassword}
                    onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                    required
                  />
                </div>

                <div className="flex gap-2 pt-4">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex-1 disabled:opacity-50"
                  >
                    {isLoading ? 'রিসেট হচ্ছে...' : 'পাসওয়ার্ড রিসেট করুন'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowPasswordModal(false)}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 flex-1"
                  >
                    বাতিল
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}

export default UserManagementPage
