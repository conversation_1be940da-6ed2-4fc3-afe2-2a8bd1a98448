import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, Building2, CreditCard, TrendingUp } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"

export default function AdminDashboard() {
  const stats = [
    {
      title: "Total Centers",
      value: "24",
      icon: <Building2 className="h-4 w-4" />,
      description: "Active coaching centers"
    },
    {
      title: "Active Subscriptions", 
      value: "18",
      icon: <CreditCard className="h-4 w-4" />,
      description: "Paying customers"
    },
    {
      title: "Total Users",
      value: "1,247",
      icon: <Users className="h-4 w-4" />,
      description: "Registered users"
    },
    {
      title: "Monthly Revenue",
      value: "$12,450",
      icon: <TrendingUp className="h-4 w-4" />,
      description: "This month's earnings"
    }
  ]

  return (
    <DashboardLayout userRole="super_admin" title="Admin Dashboard">
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="rounded-2xl bg-gradient-to-r from-blue-600 to-purple-600 p-8 text-white mb-8">
          <h1 className="text-3xl font-bold mb-3">Good morning, Admin!</h1>
          <p className="text-blue-100 text-lg">Welcome to your admin dashboard</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                {stat.icon}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">{stat.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Manage your platform</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full justify-start" variant="outline">
                <Building2 className="mr-2 h-4 w-4" />
                Manage Centers
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Users className="mr-2 h-4 w-4" />
                View All Users
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <CreditCard className="mr-2 h-4 w-4" />
                Subscription Management
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <TrendingUp className="mr-2 h-4 w-4" />
                Analytics & Reports
              </Button>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest platform updates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-4 p-4 bg-muted/50 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <p className="font-medium">New center registered</p>
                    <p className="text-sm text-muted-foreground">ABC Coaching Center joined the platform</p>
                    <p className="text-xs text-muted-foreground mt-1">2 hours ago</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4 p-4 bg-muted/50 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <p className="font-medium">Payment received</p>
                    <p className="text-sm text-muted-foreground">Monthly subscription payment processed</p>
                    <p className="text-xs text-muted-foreground mt-1">4 hours ago</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4 p-4 bg-muted/50 rounded-lg">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <p className="font-medium">System update</p>
                    <p className="text-sm text-muted-foreground">Platform updated to version 2.1.0</p>
                    <p className="text-xs text-muted-foreground mt-1">1 day ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
