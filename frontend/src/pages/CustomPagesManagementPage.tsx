import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  FileText, 
  Globe, 
  Calendar,
  User,
  ExternalLink,
  AlertCircle
} from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton } from "@/components/skeletons/DashboardSkeleton"
import { Editor } from '@tinymce/tinymce-react'
import DOMPurify from 'dompurify'

interface CustomPage {
  id: string
  title: string
  slug: string
  content: string
  metaDescription?: string
  isPublished: boolean
  createdBy: string
  creatorName?: string
  createdAt: string
  updatedAt: string
}

export default function CustomPagesManagementPage() {
  const [pages, setPages] = useState<CustomPage[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingPage, setEditingPage] = useState<CustomPage | null>(null)
  const [previewPage, setPreviewPage] = useState<CustomPage | null>(null)
  const [formData, setFormData] = useState({
    title: "",
    slug: "",
    content: "",
    metaDescription: "",
    isPublished: false
  })

  useEffect(() => {
    fetchPages()
  }, [])

  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token')
    const subdomain = window.location.hostname.split('.')[0]
    return {
      'Authorization': `Bearer ${token}`,
      'x-tenant-slug': subdomain === 'localhost' ? 'abc' : subdomain,
      'Content-Type': 'application/json'
    }
  }

  const fetchPages = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/pages', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setPages(data.data.pages || [])
      } else {
        toast.error(data.message || 'Failed to fetch pages')
      }
    } catch (error) {
      console.error('Error fetching pages:', error)
      toast.error('Failed to fetch pages')
    } finally {
      setLoading(false)
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Sanitize content
      const sanitizedContent = DOMPurify.sanitize(formData.content)
      
      const url = editingPage 
        ? `http://localhost:3004/api/coaching/pages/${editingPage.id}`
        : 'http://localhost:3004/api/coaching/pages'
      
      const method = editingPage ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: getAuthHeaders(),
        body: JSON.stringify({
          ...formData,
          content: sanitizedContent
        })
      })

      const data = await response.json()

      if (response.ok) {
        await fetchPages()
        setShowAddForm(false)
        setEditingPage(null)
        setFormData({
          title: "",
          slug: "",
          content: "",
          metaDescription: "",
          isPublished: false
        })
        toast.success(editingPage ? 'Page updated successfully' : 'Page created successfully')
      } else {
        toast.error(data.message || 'Operation failed')
      }
    } catch (error) {
      console.error('Error saving page:', error)
      toast.error('Failed to save page')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (page: CustomPage) => {
    setEditingPage(page)
    setFormData({
      title: page.title,
      slug: page.slug,
      content: page.content,
      metaDescription: page.metaDescription || "",
      isPublished: page.isPublished
    })
    setShowAddForm(true)
  }

  const handleDelete = async (pageId: string) => {
    if (!confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`http://localhost:3004/api/coaching/pages/${pageId}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      })

      const data = await response.json()

      if (response.ok) {
        await fetchPages()
        toast.success('Page deleted successfully')
      } else {
        toast.error(data.message || 'Failed to delete page')
      }
    } catch (error) {
      console.error('Error deleting page:', error)
      toast.error('Failed to delete page')
    }
  }

  const handlePreview = (page: CustomPage) => {
    setPreviewPage(page)
  }

  const getPublicUrl = (slug: string) => {
    const subdomain = window.location.hostname.split('.')[0]
    const tenantSlug = subdomain === 'localhost' ? 'abc' : subdomain
    return `http://${tenantSlug}.localhost:5175/${slug}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading && pages.length === 0) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <TableSkeleton />
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Custom Pages Management</h1>
            <p className="text-muted-foreground">
              Create and manage custom pages for your coaching center website
            </p>
          </div>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Page
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Pages</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pages.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Published</CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {pages.filter(p => p.isPublished).length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Drafts</CardTitle>
              <Edit className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {pages.filter(p => !p.isPublished).length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Last Updated</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-sm font-medium">
                {pages.length > 0 ? formatDate(pages[0].updatedAt) : 'No pages'}
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="list" className="space-y-4">
          <TabsList>
            <TabsTrigger value="list">Pages List</TabsTrigger>
            {showAddForm && (
              <TabsTrigger value="editor">
                {editingPage ? 'Edit Page' : 'Create Page'}
              </TabsTrigger>
            )}
            {previewPage && (
              <TabsTrigger value="preview">Preview</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="list" className="space-y-4">
            {/* Pages List */}
            <div className="grid gap-4">
              {pages.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8">
                    <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No pages found</h3>
                    <p className="text-muted-foreground mb-4">Get started by creating your first custom page</p>
                    <Button onClick={() => setShowAddForm(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Create Page
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                pages.map((page) => (
                  <Card key={page.id}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div className="space-y-1">
                          <CardTitle className="flex items-center gap-2">
                            {page.title}
                            <Badge variant={page.isPublished ? "default" : "secondary"}>
                              {page.isPublished ? 'Published' : 'Draft'}
                            </Badge>
                          </CardTitle>
                          <CardDescription>
                            <span className="font-mono text-sm">/{page.slug}</span>
                          </CardDescription>
                          {page.metaDescription && (
                            <p className="text-sm text-muted-foreground">{page.metaDescription}</p>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" onClick={() => handlePreview(page)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleEdit(page)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          {page.isPublished && (
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => window.open(getPublicUrl(page.slug), '_blank')}
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          )}
                          <Button 
                            size="sm" 
                            variant="outline" 
                            onClick={() => handleDelete(page.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          <span>{page.creatorName || 'Unknown'}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>Updated {formatDate(page.updatedAt)}</span>
                        </div>
                        {page.isPublished && (
                          <div className="flex items-center gap-1">
                            <Globe className="h-4 w-4" />
                            <span>Public</span>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          {/* Editor Tab */}
          {showAddForm && (
            <TabsContent value="editor" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{editingPage ? 'Edit Page' : 'Create New Page'}</CardTitle>
                  <CardDescription>
                    {editingPage ? 'Update your custom page content' : 'Create a new custom page for your coaching center'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Page Title</Label>
                        <Input
                          id="title"
                          value={formData.title}
                          onChange={(e) => handleTitleChange(e.target.value)}
                          placeholder="e.g., About Us"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="slug">URL Slug</Label>
                        <Input
                          id="slug"
                          value={formData.slug}
                          onChange={(e) => setFormData({...formData, slug: e.target.value})}
                          placeholder="e.g., about-us"
                          pattern="^[a-z0-9-]+$"
                          title="Only lowercase letters, numbers, and hyphens allowed"
                          required
                        />
                        <p className="text-xs text-muted-foreground">
                          Page will be accessible at: /{formData.slug}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="metaDescription">Meta Description (SEO)</Label>
                      <Textarea
                        id="metaDescription"
                        value={formData.metaDescription}
                        onChange={(e) => setFormData({...formData, metaDescription: e.target.value})}
                        placeholder="Brief description for search engines (150-160 characters recommended)"
                        rows={2}
                        maxLength={160}
                      />
                      <p className="text-xs text-muted-foreground">
                        {formData.metaDescription.length}/160 characters
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="content">Page Content</Label>
                      <div className="border rounded-md">
                        <Editor
                          apiKey="no-api-key"
                          value={formData.content}
                          onEditorChange={(content) => setFormData({...formData, content})}
                          init={{
                            height: 400,
                            menubar: false,
                            plugins: [
                              'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                              'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                              'insertdatetime', 'media', 'table', 'help', 'wordcount'
                            ],
                            toolbar: 'undo redo | blocks | ' +
                              'bold italic forecolor | alignleft aligncenter ' +
                              'alignright alignjustify | bullist numlist outdent indent | ' +
                              'removeformat | help',
                            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                            branding: false,
                            promotion: false
                          }}
                        />
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isPublished"
                        checked={formData.isPublished}
                        onCheckedChange={(checked) => setFormData({...formData, isPublished: checked})}
                      />
                      <Label htmlFor="isPublished">Publish page immediately</Label>
                    </div>

                    <div className="flex gap-2">
                      <Button type="submit" disabled={loading}>
                        {loading ? 'Saving...' : (editingPage ? 'Update Page' : 'Create Page')}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setShowAddForm(false)
                          setEditingPage(null)
                          setFormData({
                            title: "",
                            slug: "",
                            content: "",
                            metaDescription: "",
                            isPublished: false
                          })
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>
          )}

          {/* Preview Tab */}
          {previewPage && (
            <TabsContent value="preview" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>Preview: {previewPage.title}</CardTitle>
                      <CardDescription>
                        This is how your page will appear to visitors
                      </CardDescription>
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => setPreviewPage(null)}
                    >
                      Close Preview
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg p-6 bg-white">
                    <h1 className="text-3xl font-bold mb-4">{previewPage.title}</h1>
                    <div
                      className="prose max-w-none"
                      dangerouslySetInnerHTML={{
                        __html: DOMPurify.sanitize(previewPage.content)
                      }}
                    />
                  </div>

                  {previewPage.isPublished && (
                    <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800">
                          This page is live and accessible at:
                        </span>
                      </div>
                      <a
                        href={getPublicUrl(previewPage.slug)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-green-600 hover:text-green-800 underline"
                      >
                        {getPublicUrl(previewPage.slug)}
                      </a>
                    </div>
                  )}

                  {!previewPage.isPublished && (
                    <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm font-medium text-yellow-800">
                          This page is in draft mode and not publicly accessible
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
