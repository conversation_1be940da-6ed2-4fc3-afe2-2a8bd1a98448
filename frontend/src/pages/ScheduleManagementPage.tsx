import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Calendar, Clock, MapPin, Users, AlertTriangle } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface Schedule {
  id: string
  batchName: string
  courseName: string
  roomName?: string
  roomCode?: string
  teacherName?: string
  dayOfWeek: string
  startTime: string
  endTime: string
  isActive: boolean
}

interface Batch {
  id: string
  name: string
  courseName: string
  teacherName?: string
}

interface Room {
  id: string
  roomCode: string
  roomName: string
  capacity: number
}

const DAYS_OF_WEEK = [
  'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'
]

export default function ScheduleManagementPage() {
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [batches, setBatches] = useState<Batch[]>([])
  const [rooms, setRooms] = useState<Room[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    batchId: "",
    roomId: "",
    dayOfWeek: "",
    startTime: "",
    endTime: ""
  })

  useEffect(() => {
    fetchSchedules()
    fetchBatches()
    fetchRooms()
  }, [])

  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token')
    const subdomain = window.location.hostname.split('.')[0]
    return {
      'Authorization': `Bearer ${token}`,
      'x-tenant-slug': subdomain === 'localhost' ? 'abc' : subdomain,
      'Content-Type': 'application/json'
    }
  }

  const fetchSchedules = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/schedules', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setSchedules(data.data.schedules || [])
      } else {
        toast.error(data.message || 'Failed to fetch schedules')
      }
    } catch (error) {
      console.error('Error fetching schedules:', error)
      toast.error('Failed to fetch schedules')
    } finally {
      setLoading(false)
    }
  }

  const fetchBatches = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/batches', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setBatches(data.data.batches || [])
      }
    } catch (error) {
      console.error('Error fetching batches:', error)
    }
  }

  const fetchRooms = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/rooms', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setRooms(data.data.rooms || [])
      }
    } catch (error) {
      console.error('Error fetching rooms:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('http://localhost:3004/api/coaching/schedules', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (response.ok) {
        await fetchSchedules()
        setShowAddForm(false)
        setFormData({
          batchId: "",
          roomId: "",
          dayOfWeek: "",
          startTime: "",
          endTime: ""
        })
        toast.success('Schedule created successfully')
      } else {
        toast.error(data.message || 'Failed to create schedule')
      }
    } catch (error) {
      console.error('Error creating schedule:', error)
      toast.error('Failed to create schedule')
    } finally {
      setLoading(false)
    }
  }

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'PM' : 'AM'
    const displayHour = hour % 12 || 12
    return `${displayHour}:${minutes} ${ampm}`
  }

  const getDayColor = (day: string) => {
    const colors: { [key: string]: string } = {
      'MONDAY': 'bg-blue-100 text-blue-800',
      'TUESDAY': 'bg-green-100 text-green-800',
      'WEDNESDAY': 'bg-yellow-100 text-yellow-800',
      'THURSDAY': 'bg-purple-100 text-purple-800',
      'FRIDAY': 'bg-red-100 text-red-800',
      'SATURDAY': 'bg-indigo-100 text-indigo-800',
      'SUNDAY': 'bg-gray-100 text-gray-800'
    }
    return colors[day] || 'bg-gray-100 text-gray-800'
  }

  if (loading && schedules.length === 0) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <TableSkeleton />
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Schedule Management</h1>
            <p className="text-muted-foreground">
              Manage class schedules and room bookings
            </p>
          </div>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Schedule
          </Button>
        </div>

        {/* Add Schedule Form */}
        {showAddForm && (
          <Card>
            <CardHeader>
              <CardTitle>Create New Schedule</CardTitle>
              <CardDescription>
                Add a new class schedule with room and time details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="batchId">Batch</Label>
                    <Select value={formData.batchId} onValueChange={(value) => setFormData({...formData, batchId: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select batch" />
                      </SelectTrigger>
                      <SelectContent>
                        {batches.map((batch) => (
                          <SelectItem key={batch.id} value={batch.id}>
                            {batch.name} - {batch.courseName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="roomId">Room</Label>
                    <Select value={formData.roomId} onValueChange={(value) => setFormData({...formData, roomId: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select room" />
                      </SelectTrigger>
                      <SelectContent>
                        {rooms.map((room) => (
                          <SelectItem key={room.id} value={room.id}>
                            {room.roomCode} - {room.roomName} (Capacity: {room.capacity})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dayOfWeek">Day of Week</Label>
                    <Select value={formData.dayOfWeek} onValueChange={(value) => setFormData({...formData, dayOfWeek: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select day" />
                      </SelectTrigger>
                      <SelectContent>
                        {DAYS_OF_WEEK.map((day) => (
                          <SelectItem key={day} value={day}>
                            {day.charAt(0) + day.slice(1).toLowerCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="startTime">Start Time</Label>
                    <Input
                      id="startTime"
                      type="time"
                      value={formData.startTime}
                      onChange={(e) => setFormData({...formData, startTime: e.target.value})}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="endTime">End Time</Label>
                    <Input
                      id="endTime"
                      type="time"
                      value={formData.endTime}
                      onChange={(e) => setFormData({...formData, endTime: e.target.value})}
                      required
                    />
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Creating...' : 'Create Schedule'}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Schedules Grid */}
        <div className="grid gap-4">
          {DAYS_OF_WEEK.map((day) => {
            const daySchedules = schedules.filter(s => s.dayOfWeek === day)
            return (
              <Card key={day}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    <span className={`px-2 py-1 rounded-full text-sm font-medium ${getDayColor(day)}`}>
                      {day.charAt(0) + day.slice(1).toLowerCase()}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      ({daySchedules.length} classes)
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {daySchedules.length === 0 ? (
                    <p className="text-muted-foreground text-center py-4">No classes scheduled</p>
                  ) : (
                    <div className="space-y-2">
                      {daySchedules
                        .sort((a, b) => a.startTime.localeCompare(b.startTime))
                        .map((schedule) => (
                        <div key={schedule.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">
                                {formatTime(schedule.startTime)} - {formatTime(schedule.endTime)}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span>{schedule.batchName}</span>
                              <span className="text-muted-foreground">({schedule.courseName})</span>
                            </div>
                            {schedule.roomName && (
                              <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm">{schedule.roomCode} - {schedule.roomName}</span>
                              </div>
                            )}
                          </div>
                          {schedule.teacherName && (
                            <div className="text-sm text-muted-foreground">
                              Teacher: {schedule.teacherName}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </DashboardLayout>
  )
}
