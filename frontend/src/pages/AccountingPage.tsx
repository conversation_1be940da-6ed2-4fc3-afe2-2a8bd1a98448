import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Search, TrendingUp, TrendingDown, DollarSign, Calendar, Download, Edit, Trash2 } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton, ListItemSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface Transaction {
  id: number
  type: 'income' | 'expense'
  category: string
  amount: number
  description: string
  date: string
  reference_id?: number
  reference_type?: string
  created_by: string
  created_at: string
}

export default function AccountingPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState<string>("")
  const [dateFilter, setDateFilter] = useState("")
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingTransaction, setEditingTransaction] = useState<Transaction | null>(null)
  const [formData, setFormData] = useState({
    type: "income",
    category: "",
    amount: "",
    description: "",
    date: ""
  })

  useEffect(() => {
    fetchTransactions()
  }, [])

  const fetchTransactions = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/accounting/transactions', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setTransactions(data.transactions || [])
      }
    } catch (error) {
      console.error('Error fetching transactions:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const url = editingTransaction 
        ? `http://localhost:3000/api/accounting/transactions/${editingTransaction.id}`
        : 'http://localhost:3000/api/accounting/transactions'
      
      const method = editingTransaction ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          amount: parseFloat(formData.amount)
        }),
        credentials: 'include'
      })

      const data = await response.json()

      if (response.ok) {
        await fetchTransactions()
        setShowAddForm(false)
        setEditingTransaction(null)
        setFormData({ type: "income", category: "", amount: "", description: "", date: "" })
      } else {
        toast.error(data.error || 'Operation failed')
      }
    } catch (error) {
      console.error('Error saving transaction:', error)
      toast.error('Failed to save transaction')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (transaction: Transaction) => {
    setEditingTransaction(transaction)
    setFormData({
      type: transaction.type,
      category: transaction.category,
      amount: transaction.amount.toString(),
      description: transaction.description,
      date: transaction.date
    })
    setShowAddForm(true)
  }

  const handleDelete = async (transactionId: number) => {
    if (!confirm('Are you sure you want to delete this transaction?')) {
      return
    }

    try {
      const response = await fetch(`http://localhost:3000/api/accounting/transactions/${transactionId}`, {
        method: 'DELETE',
        credentials: 'include'
      })

      if (response.ok) {
        await fetchTransactions()
      } else {
        const data = await response.json()
        toast.error(data.error || 'Failed to delete transaction')
      }
    } catch (error) {
      console.error('Error deleting transaction:', error)
      toast.error('Failed to delete transaction')
    }
  }

  const exportReport = async (format: 'excel' | 'pdf') => {
    try {
      const response = await fetch(`http://localhost:3000/api/accounting/export?format=${format}`, {
        credentials: 'include'
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `accounting-report.${format === 'excel' ? 'xlsx' : 'pdf'}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        toast.error('Failed to export report')
      }
    } catch (error) {
      console.error('Error exporting report:', error)
      toast.error('Failed to export report')
    }
  }

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.category.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === '' || transaction.type === typeFilter
    const matchesDate = dateFilter === '' || transaction.date === dateFilter
    return matchesSearch && matchesType && matchesDate
  })

  const getAccountingStats = () => {
    const totalIncome = transactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0)
    const totalExpense = transactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0)
    const netProfit = totalIncome - totalExpense
    const transactionCount = transactions.length

    return {
      totalIncome,
      totalExpense,
      netProfit,
      transactionCount
    }
  }

  const stats = getAccountingStats()

  return (
    <DashboardLayout userRole="center_admin" title="Accounting">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Accounting</h1>
            <p className="text-muted-foreground">Manage income, expenses, and financial reports</p>
          </div>
          
          <Button onClick={() => {
            setShowAddForm(true)
            setEditingTransaction(null)
            setFormData({ type: "income", category: "", amount: "", description: "", date: "" })
          }}>
            <Plus className="mr-2 h-4 w-4" />
            Add Transaction
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Total Income</div>
                  <div className="text-lg font-semibold">${stats.totalIncome.toFixed(2)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingDown className="h-4 w-4 text-red-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Total Expense</div>
                  <div className="text-lg font-semibold">${stats.totalExpense.toFixed(2)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className={`h-4 w-4 ${stats.netProfit >= 0 ? 'text-green-500' : 'text-red-500'}`} />
                <div>
                  <div className="text-sm text-muted-foreground">Net Profit</div>
                  <div className={`text-lg font-semibold ${stats.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    ${stats.netProfit.toFixed(2)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Transactions</div>
                  <div className="text-lg font-semibold">{stats.transactionCount}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    id="search"
                    placeholder="Search transactions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="type">Type Filter</Label>
                <select
                  id="type"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-input bg-background rounded-md"
                >
                  <option value="">All Types</option>
                  <option value="income">Income</option>
                  <option value="expense">Expense</option>
                </select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="date">Date Filter</Label>
                <Input
                  id="date"
                  type="date"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label>Export Reports</Label>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" onClick={() => exportReport('excel')}>
                    <Download className="mr-2 h-4 w-4" />
                    Excel
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => exportReport('pdf')}>
                    <Download className="mr-2 h-4 w-4" />
                    PDF
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
