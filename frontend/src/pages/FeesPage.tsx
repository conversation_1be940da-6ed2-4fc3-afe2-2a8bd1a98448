import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Search, DollarSign, Calendar, User, CheckCircle, XCircle, Clock } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton, ListItemSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface FeeRecord {
  id: number
  student_id: number
  student_name: string
  course_name: string
  amount: number
  due_date: string
  paid_date?: string
  status: 'pending' | 'paid' | 'overdue'
  payment_method?: string
  notes?: string
  created_at: string
}

interface Student {
  id: number
  name: string
  email: string
  course_name: string
}

export default function FeesPage() {
  const [feeRecords, setFeeRecords] = useState<FeeRecord[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("")
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    student_id: "",
    amount: "",
    due_date: "",
    notes: ""
  })

  useEffect(() => {
    fetchFeeRecords()
    fetchStudents()
  }, [])

  const fetchFeeRecords = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/fees', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setFeeRecords(data.fees || [])
      }
    } catch (error) {
      console.error('Error fetching fee records:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStudents = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/students', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setStudents(data.students || [])
      }
    } catch (error) {
      console.error('Error fetching students:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('http://localhost:3000/api/fees', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          amount: parseFloat(formData.amount)
        }),
        credentials: 'include'
      })

      const data = await response.json()

      if (response.ok) {
        await fetchFeeRecords()
        setShowAddForm(false)
        setFormData({ student_id: "", amount: "", due_date: "", notes: "" })
      } else {
        toast.error(data.error || 'Failed to create fee record')
      }
    } catch (error) {
      console.error('Error creating fee record:', error)
      toast.error('Failed to create fee record')
    } finally {
      setLoading(false)
    }
  }

  const markAsPaid = async (feeId: number, paymentMethod: string = 'cash') => {
    try {
      const response = await fetch(`http://localhost:3000/api/fees/${feeId}/pay`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          payment_method: paymentMethod,
          paid_date: new Date().toISOString().split('T')[0]
        }),
        credentials: 'include'
      })

      if (response.ok) {
        await fetchFeeRecords()
      } else {
        const data = await response.json()
        toast.error(data.error || 'Failed to mark as paid')
      }
    } catch (error) {
      console.error('Error marking fee as paid:', error)
      toast.error('Failed to mark as paid')
    }
  }

  const generateInvoice = async (feeId: number) => {
    try {
      const response = await fetch(`http://localhost:3000/api/fees/${feeId}/invoice`, {
        credentials: 'include'
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `invoice-${feeId}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        toast.error('Failed to generate invoice')
      }
    } catch (error) {
      console.error('Error generating invoice:', error)
      toast.error('Failed to generate invoice')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />
      case 'overdue':
        return <XCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const filteredFeeRecords = feeRecords.filter(fee => {
    const matchesSearch = fee.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         fee.course_name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === '' || fee.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getTotalStats = () => {
    const total = feeRecords.reduce((sum, fee) => sum + fee.amount, 0)
    const paid = feeRecords.filter(fee => fee.status === 'paid').reduce((sum, fee) => sum + fee.amount, 0)
    const pending = feeRecords.filter(fee => fee.status === 'pending').reduce((sum, fee) => sum + fee.amount, 0)
    const overdue = feeRecords.filter(fee => fee.status === 'overdue').reduce((sum, fee) => sum + fee.amount, 0)
    
    return { total, paid, pending, overdue }
  }

  const stats = getTotalStats()

  return (
    <DashboardLayout userRole="center_admin" title="Fee Management">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Fee Management</h1>
            <p className="text-muted-foreground">Track student fees and payments</p>
          </div>
          
          <Button onClick={() => {
            setShowAddForm(true)
            setFormData({ student_id: "", amount: "", due_date: "", notes: "" })
          }}>
            <Plus className="mr-2 h-4 w-4" />
            Add Fee Record
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Total Fees</div>
                  <div className="text-lg font-semibold">${stats.total.toFixed(2)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Paid</div>
                  <div className="text-lg font-semibold">${stats.paid.toFixed(2)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-yellow-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Pending</div>
                  <div className="text-lg font-semibold">${stats.pending.toFixed(2)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Overdue</div>
                  <div className="text-lg font-semibold">${stats.overdue.toFixed(2)}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    id="search"
                    placeholder="Search by student or course..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">Status Filter</Label>
                <select
                  id="status"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-input bg-background rounded-md"
                >
                  <option value="">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="paid">Paid</option>
                  <option value="overdue">Overdue</option>
                </select>
              </div>
              
              <div className="space-y-2">
                <Label>Quick Actions</Label>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline">
                    Export Report
                  </Button>
                  <Button size="sm" variant="outline">
                    Send Reminders
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Add Fee Form */}
        {showAddForm && (
          <Card>
            <CardHeader>
              <CardTitle>Add Fee Record</CardTitle>
              <CardDescription>Create a new fee record for a student</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="student_id">Student *</Label>
                    <select
                      id="student_id"
                      name="student_id"
                      value={formData.student_id}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-input bg-background rounded-md"
                    >
                      <option value="">Select student</option>
                      {students.map(student => (
                        <option key={student.id} value={student.id}>
                          {student.name} - {student.course_name}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount *</Label>
                    <Input
                      id="amount"
                      name="amount"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      value={formData.amount}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="due_date">Due Date *</Label>
                    <Input
                      id="due_date"
                      name="due_date"
                      type="date"
                      value={formData.due_date}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes</Label>
                    <Input
                      id="notes"
                      name="notes"
                      placeholder="Additional notes..."
                      value={formData.notes}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Creating...' : 'Create Fee Record'}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setShowAddForm(false)
                      setFormData({ student_id: "", amount: "", due_date: "", notes: "" })
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Fee Records List */}
        { loading ? (
          <TableSkeleton rows={6} />
        ) : filteredFeeRecords.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <DollarSign className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No fee records found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter ? 'No records match your filters.' : 'Get started by adding your first fee record.'}
              </p>
              {!searchTerm && !statusFilter && (
                <Button onClick={() => setShowAddForm(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Fee Record
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {filteredFeeRecords.map((fee) => (
              <Card key={fee.id} data-testid="fee-record">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                          <DollarSign className="text-white h-6 w-6" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{fee.student_name}</h3>
                          <p className="text-sm text-muted-foreground">{fee.course_name}</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center text-muted-foreground">
                          <DollarSign className="h-4 w-4 mr-2" />
                          ${fee.amount}
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          <Calendar className="h-4 w-4 mr-2" />
                          Due: {new Date(fee.due_date).toLocaleDateString()}
                        </div>
                        {fee.paid_date && (
                          <div className="flex items-center text-muted-foreground">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Paid: {new Date(fee.paid_date).toLocaleDateString()}
                          </div>
                        )}
                        {fee.payment_method && (
                          <div className="flex items-center text-muted-foreground">
                            <User className="h-4 w-4 mr-2" />
                            {fee.payment_method}
                          </div>
                        )}
                      </div>
                      
                      {fee.notes && (
                        <div className="mt-2 text-sm text-muted-foreground">
                          Note: {fee.notes}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <div className="text-right mr-4">
                        <div className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 ${getStatusColor(fee.status)}`}>
                          {getStatusIcon(fee.status)}
                          {fee.status}
                        </div>
                      </div>
                      
                      {fee.status !== 'paid' && (
                        <Button
                          size="sm"
                          onClick={() => markAsPaid(fee.id)}
                        >
                          Mark Paid
                        </Button>
                      )}
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => generateInvoice(fee.id)}
                      >
                        Invoice
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
