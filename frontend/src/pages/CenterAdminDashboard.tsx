import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, BookOpen, Calendar, DollarSign, TrendingUp, Award, Bell, BarChart3 } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import { useAuth } from "@/contexts/AuthContext"

interface DashboardStats {
  students: {
    total: number
    active: number
    newThisMonth: number
  }
  courses: {
    total: number
    active: number
    enrollments: number
  }
  attendance: {
    todayRate: number
    weeklyAverage: number
    totalSessions: number
  }
  financial: {
    monthlyRevenue: number
    pendingFees: number
    collectionRate: number
  }
  recentActivities: Array<{
    id: number
    type: string
    message: string
    timestamp: string
  }>
}

export default function CenterAdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const { user, getTenantSlug } = useAuth()
  const tenantSlug = getTenantSlug()

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      const url = tenantSlug 
        ? `http://localhost:3000/api/dashboard/stats?tenant_slug=${tenantSlug}`
        : 'http://localhost:3000/api/dashboard/stats'
        
      const response = await fetch(url, {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <DashboardLayout userRole="center_admin" title="Dashboard">
        <div className="text-center py-8">
          <div className="text-muted-foreground">Loading dashboard...</div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout userRole="center_admin" title="Dashboard">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Welcome back, {user?.name}!</h1>
            <p className="text-muted-foreground">
              Here's what's happening at {user?.tenant_name || 'your coaching center'} today.
            </p>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            {new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </div>
        </div>

        {/* Quick Stats */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-blue-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Total Students</div>
                    <div className="text-2xl font-bold">{stats.students.total}</div>
                    <div className="text-xs text-green-600">
                      +{stats.students.newThisMonth} this month
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-green-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Active Courses</div>
                    <div className="text-2xl font-bold">{stats.courses.active}</div>
                    <div className="text-xs text-muted-foreground">
                      {stats.courses.enrollments} enrollments
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-purple-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Today's Attendance</div>
                    <div className="text-2xl font-bold">{stats.attendance.todayRate.toFixed(1)}%</div>
                    <div className="text-xs text-muted-foreground">
                      Weekly avg: {stats.attendance.weeklyAverage.toFixed(1)}%
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-yellow-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Monthly Revenue</div>
                    <div className="text-2xl font-bold">${stats.financial.monthlyRevenue.toFixed(0)}</div>
                    <div className="text-xs text-muted-foreground">
                      {stats.financial.collectionRate.toFixed(1)}% collection rate
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Quick Actions & Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Quick Actions
              </CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <a
                  href="/students"
                  className="p-4 border rounded-lg hover:bg-accent transition-colors"
                >
                  <Users className="h-6 w-6 text-blue-500 mb-2" />
                  <div className="font-medium">Manage Students</div>
                  <div className="text-sm text-muted-foreground">Add or edit student records</div>
                </a>
                
                <a
                  href="/attendance"
                  className="p-4 border rounded-lg hover:bg-accent transition-colors"
                >
                  <Calendar className="h-6 w-6 text-green-500 mb-2" />
                  <div className="font-medium">Mark Attendance</div>
                  <div className="text-sm text-muted-foreground">Record today's attendance</div>
                </a>
                
                <a
                  href="/fees"
                  className="p-4 border rounded-lg hover:bg-accent transition-colors"
                >
                  <DollarSign className="h-6 w-6 text-yellow-500 mb-2" />
                  <div className="font-medium">Fee Management</div>
                  <div className="text-sm text-muted-foreground">Track payments and dues</div>
                </a>
                
                <a
                  href="/reports"
                  className="p-4 border rounded-lg hover:bg-accent transition-colors"
                >
                  <BarChart3 className="h-6 w-6 text-purple-500 mb-2" />
                  <div className="font-medium">View Reports</div>
                  <div className="text-sm text-muted-foreground">Analytics and insights</div>
                </a>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Recent Activities
              </CardTitle>
              <CardDescription>Latest updates and notifications</CardDescription>
            </CardHeader>
            <CardContent>
              {stats?.recentActivities && stats.recentActivities.length > 0 ? (
                <div className="space-y-4">
                  {stats.recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3">
                      <div className="w-2 h-2 rounded-full bg-blue-500 mt-2"></div>
                      <div className="flex-1">
                        <p className="text-sm">{activity.message}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Bell className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">No recent activities</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Performance Overview */}
        {stats && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Student Growth
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">Active Students</span>
                    <span className="font-medium">{stats.students.active}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">New This Month</span>
                    <span className="font-medium text-green-600">+{stats.students.newThisMonth}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Growth Rate</span>
                    <span className="font-medium">
                      {((stats.students.newThisMonth / stats.students.total) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Course Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Courses</span>
                    <span className="font-medium">{stats.courses.total}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Active Courses</span>
                    <span className="font-medium">{stats.courses.active}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Avg. Enrollment</span>
                    <span className="font-medium">
                      {(stats.courses.enrollments / stats.courses.total).toFixed(1)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Financial Health
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">Monthly Revenue</span>
                    <span className="font-medium">${stats.financial.monthlyRevenue.toFixed(0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Pending Fees</span>
                    <span className="font-medium text-orange-600">${stats.financial.pendingFees.toFixed(0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Collection Rate</span>
                    <span className="font-medium">{stats.financial.collectionRate.toFixed(1)}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
