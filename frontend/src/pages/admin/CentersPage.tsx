import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useAuth } from '../../contexts/AuthContext'
import { useApi } from '../../hooks/useApi'
import AdminLayout from '../../components/AdminLayout'

interface Center {
  id: number
  name: string
  slug: string
  email: string
  phone: string
  address: string
  status: 'active' | 'suspended'
  subscription_plan: string
  created_at: string
  admin_name: string
  total_students: number
  total_teachers: number
}

export default function CentersPage() {
  const { user } = useAuth()
  const { adminGet, adminPost, adminPut } = useApi()
  const [centers, setCenters] = useState<Center[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    adminName: '',
    adminEmail: '',
    adminPassword: '',
    subscription_plan: 'basic'
  })

  useEffect(() => {
    fetchCenters()
  }, [])

  const fetchCenters = async () => {
    try {
      setLoading(true)
      const response = await adminGet('/api/admin/centers')
      if (response.centers) {
        setCenters(response.centers)
      }
    } catch (error) {
      console.error('Error fetching centers:', error)
      toast.error(`Failed to fetch centers: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await adminPost('/api/admin/centers', formData)
      if (response.success) {
        toast.success('Center created successfully!')
        setShowAddModal(false)
        setFormData({
          name: '',
          email: '',
          phone: '',
          address: '',
          adminName: '',
          adminEmail: '',
          adminPassword: '',
          subscription_plan: 'basic'
        })
        fetchCenters()
      }
    } catch (error) {
      console.error('Error creating center:', error)
      toast.error(`Failed to create center: ${error.message}`)
    }
  }

  const toggleStatus = async (centerId: number, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'suspended' : 'active'
      const response = await adminPut(`/api/admin/centers/${centerId}/status`, { status: newStatus })
      if (response.success) {
        toast.success('Status updated successfully!')
        fetchCenters()
      }
    } catch (error) {
      console.error('Error updating status:', error)
      toast.error(`Failed to update status: ${error.message}`)
    }
  }

  const generateSlug = (name: string) => {
    return name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
  }

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value
    setFormData(prev => ({
      ...prev,
      name,
      slug: generateSlug(name)
    }))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading centers...</div>
      </div>
    )
  }

  return (
    <AdminLayout>
      <div className="px-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Coaching Centers</h1>
          <p className="text-gray-600 mt-2">Manage all coaching centers in the system</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          Add New Center
        </button>
      </div>

      {centers.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold mb-2">No coaching centers found</h3>
          <p className="text-gray-600 mb-4">Get started by adding your first coaching center.</p>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Add Center
          </button>
        </div>
      ) : (
        <div className="grid gap-6">
          {centers.map((center) => (
            <div key={center.id} className="bg-white rounded-lg shadow-md p-6 border">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="text-xl font-semibold">{center.name}</h3>
                  <p className="text-gray-600">Slug: {center.slug}</p>
                  <p className="text-gray-600">Email: {center.email}</p>
                  <p className="text-gray-600">Phone: {center.phone}</p>
                  <p className="text-gray-600">Address: {center.address}</p>
                  <p className="text-gray-600">Admin: {center.admin_name}</p>
                  <div className="mt-2">
                    <span className="text-sm bg-gray-100 px-2 py-1 rounded mr-2">
                      {center.total_students} students
                    </span>
                    <span className="text-sm bg-gray-100 px-2 py-1 rounded mr-2">
                      {center.total_teachers} teachers
                    </span>
                    <span className={`text-sm px-2 py-1 rounded ${
                      center.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {center.status}
                    </span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => toggleStatus(center.id, center.status)}
                    className={`px-3 py-1 rounded text-sm ${
                      center.status === 'active'
                        ? 'bg-red-100 text-red-800 hover:bg-red-200'
                        : 'bg-green-100 text-green-800 hover:bg-green-200'
                    }`}
                  >
                    {center.status === 'active' ? 'Suspend' : 'Activate'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Center Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-2xl font-bold mb-4">Add New Coaching Center</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Center Name *</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleNameChange}
                  required
                  className="w-full border rounded-lg px-3 py-2"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Email *</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  required
                  className="w-full border rounded-lg px-3 py-2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Phone</label>
                <input
                  type="text"
                  name="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full border rounded-lg px-3 py-2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Address</label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  className="w-full border rounded-lg px-3 py-2"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Admin Name *</label>
                <input
                  type="text"
                  name="adminName"
                  value={formData.adminName}
                  onChange={(e) => setFormData(prev => ({ ...prev, adminName: e.target.value }))}
                  required
                  className="w-full border rounded-lg px-3 py-2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Admin Email *</label>
                <input
                  type="email"
                  name="adminEmail"
                  value={formData.adminEmail}
                  onChange={(e) => setFormData(prev => ({ ...prev, adminEmail: e.target.value }))}
                  required
                  className="w-full border rounded-lg px-3 py-2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Admin Password *</label>
                <input
                  type="password"
                  name="adminPassword"
                  value={formData.adminPassword}
                  onChange={(e) => setFormData(prev => ({ ...prev, adminPassword: e.target.value }))}
                  required
                  className="w-full border rounded-lg px-3 py-2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Subscription Plan</label>
                <select
                  name="subscription_plan"
                  value={formData.subscription_plan}
                  onChange={(e) => setFormData(prev => ({ ...prev, subscription_plan: e.target.value }))}
                  className="w-full border rounded-lg px-3 py-2"
                >
                  <option value="basic">Basic Plan</option>
                  <option value="premium">Premium Plan</option>
                  <option value="enterprise">Enterprise Plan</option>
                </select>
              </div>

              <div className="flex gap-4 pt-4">
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                >
                  Create Center
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      </div>
    </AdminLayout>
  )
}
