import React, { useState, useEffect } from 'react'
import toast from 'react-hot-toast'
import { useAuth } from '../../contexts/AuthContext'
import { useApi } from '../../hooks/useApi'
import AdminLayout from '../../components/AdminLayout'

interface Plan {
  id: number
  name: string
  description: string
  price: number
  duration_months: number
  max_students: number
  max_teachers: number
  features: string[]
  is_active: boolean
  created_at: string
}

export default function PlansPage() {
  const { user } = useAuth()
  const { adminGet, adminPost, adminPut, adminDel } = useApi()
  const [plans, setPlans] = useState<Plan[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingPlan, setEditingPlan] = useState<Plan | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    duration_months: '12',
    max_students: '',
    max_teachers: '',
    features: '',
    is_active: true
  })

  useEffect(() => {
    fetchPlans()
  }, [])

  const fetchPlans = async () => {
    try {
      setLoading(true)
      const response = await adminGet('/api/admin/plans')
      if (response.plans) {
        setPlans(response.plans)
      }
    } catch (error) {
      console.error('Error fetching plans:', error)
      toast.error(`Failed to fetch plans: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const planData = {
        ...formData,
        price: parseInt(formData.price),
        duration_months: parseInt(formData.duration_months),
        max_students: formData.max_students === '' ? -1 : parseInt(formData.max_students),
        max_teachers: formData.max_teachers === '' ? -1 : parseInt(formData.max_teachers),
        features: formData.features.split('\n').filter(f => f.trim())
      }

      if (editingPlan) {
        // Update existing plan
        const response = await adminPut(`/api/admin/plans/${editingPlan.id}`, planData)
        if (response.success) {
          toast.success('Plan updated successfully!')
          fetchPlans()
        }
      } else {
        // Create new plan
        const response = await adminPost('/api/admin/plans', planData)
        if (response.success) {
          toast.success('Plan created successfully!')
          fetchPlans()
        }
      }

      resetForm()
    } catch (error) {
      console.error('Error saving plan:', error)
      toast.error(`Failed to save plan: ${error.message}`)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      duration_months: '12',
      max_students: '',
      max_teachers: '',
      features: '',
      is_active: true
    })
    setShowAddModal(false)
    setEditingPlan(null)
  }

  const editPlan = (plan: Plan) => {
    setEditingPlan(plan)
    setFormData({
      name: plan.name,
      description: plan.description,
      price: plan.price.toString(),
      duration_months: plan.duration_months.toString(),
      max_students: plan.max_students === -1 ? '' : plan.max_students.toString(),
      max_teachers: plan.max_teachers === -1 ? '' : plan.max_teachers.toString(),
      features: plan.features.join('\n'),
      is_active: plan.is_active
    })
    setShowAddModal(true)
  }

  const togglePlanStatus = async (planId: number) => {
    try {
      setPlans(prev => prev.map(p => 
        p.id === planId 
          ? { ...p, is_active: !p.is_active }
          : p
      ))
      toast.success('Plan status updated successfully!')
    } catch (error) {
      console.error('Error updating plan status:', error)
      toast.error('Failed to update plan status')
    }
  }

  const deletePlan = async (planId: number) => {
    if (confirm('Are you sure you want to delete this plan?')) {
      try {
        setPlans(prev => prev.filter(p => p.id !== planId))
        toast.success('Plan deleted successfully!')
      } catch (error) {
        console.error('Error deleting plan:', error)
        toast.error('Failed to delete plan')
      }
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading plans...</div>
      </div>
    )
  }

  return (
    <AdminLayout>
      <div className="px-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Subscription Plans</h1>
          <p className="text-gray-600 mt-2">Manage subscription plans for coaching centers</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          Add New Plan
        </button>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {plans.map((plan) => (
          <div key={plan.id} className={`bg-white rounded-lg shadow-lg border-2 ${
            plan.is_active ? 'border-blue-200' : 'border-gray-200 opacity-75'
          }`}>
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-bold">{plan.name}</h3>
                  <p className="text-gray-600 text-sm">{plan.description}</p>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  plan.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {plan.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>

              <div className="mb-4">
                <div className="text-3xl font-bold text-blue-600">
                  ₹{plan.price.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">
                  per {plan.duration_months} months
                </div>
              </div>

              <div className="mb-4">
                <div className="text-sm text-gray-600 mb-2">Limits:</div>
                <div className="space-y-1">
                  <div className="text-sm">
                    Students: {plan.max_students === -1 ? 'Unlimited' : plan.max_students}
                  </div>
                  <div className="text-sm">
                    Teachers: {plan.max_teachers === -1 ? 'Unlimited' : plan.max_teachers}
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <div className="text-sm text-gray-600 mb-2">Features:</div>
                <ul className="space-y-1">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="text-sm flex items-center">
                      <span className="text-green-500 mr-2">✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={() => editPlan(plan)}
                  className="flex-1 bg-blue-100 text-blue-800 px-3 py-2 rounded text-sm hover:bg-blue-200"
                >
                  Edit
                </button>
                <button
                  onClick={() => togglePlanStatus(plan.id)}
                  className={`flex-1 px-3 py-2 rounded text-sm ${
                    plan.is_active
                      ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                      : 'bg-green-100 text-green-800 hover:bg-green-200'
                  }`}
                >
                  {plan.is_active ? 'Deactivate' : 'Activate'}
                </button>
                <button
                  onClick={() => deletePlan(plan.id)}
                  className="bg-red-100 text-red-800 px-3 py-2 rounded text-sm hover:bg-red-200"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {plans.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold mb-2">No plans found</h3>
          <p className="text-gray-600 mb-4">Get started by creating your first subscription plan.</p>
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Add Plan
          </button>
        </div>
      )}

      {/* Add/Edit Plan Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-2xl font-bold mb-4">
              {editingPlan ? 'Edit Plan' : 'Add New Plan'}
            </h2>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Plan Name *</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  required
                  className="w-full border rounded-lg px-3 py-2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Description *</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  required
                  className="w-full border rounded-lg px-3 py-2"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Price (₹) *</label>
                  <input
                    type="number"
                    value={formData.price}
                    onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                    required
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Duration (months) *</label>
                  <input
                    type="number"
                    value={formData.duration_months}
                    onChange={(e) => setFormData(prev => ({ ...prev, duration_months: e.target.value }))}
                    required
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Max Students (empty for unlimited)</label>
                  <input
                    type="number"
                    value={formData.max_students}
                    onChange={(e) => setFormData(prev => ({ ...prev, max_students: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Max Teachers (empty for unlimited)</label>
                  <input
                    type="number"
                    value={formData.max_teachers}
                    onChange={(e) => setFormData(prev => ({ ...prev, max_teachers: e.target.value }))}
                    className="w-full border rounded-lg px-3 py-2"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Features (one per line) *</label>
                <textarea
                  value={formData.features}
                  onChange={(e) => setFormData(prev => ({ ...prev, features: e.target.value }))}
                  required
                  placeholder="Student Management&#10;Basic Reports&#10;Email Support"
                  className="w-full border rounded-lg px-3 py-2"
                  rows={5}
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                  className="mr-2"
                />
                <label htmlFor="is_active" className="text-sm font-medium">
                  Plan is active
                </label>
              </div>

              <div className="flex gap-4 pt-4">
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                >
                  {editingPlan ? 'Update Plan' : 'Create Plan'}
                </button>
                <button
                  type="button"
                  onClick={resetForm}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      </div>
    </AdminLayout>
  )
}
