import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Calendar, Clock, Users, MapPin, Edit, Trash2 } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton, ListItemSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface Schedule {
  id: number
  course_name: string
  teacher_name: string
  room: string
  day_of_week: string
  start_time: string
  end_time: string
  batch: string
  status: string
  created_at: string
}

export default function SchedulePage() {
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingSchedule, setEditingSchedule] = useState<Schedule | null>(null)
  const [viewMode, setViewMode] = useState<'week' | 'month'>('week')
  const [formData, setFormData] = useState({
    course_id: "",
    teacher_id: "",
    room: "",
    day_of_week: "",
    start_time: "",
    end_time: "",
    batch: ""
  })

  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
  const timeSlots = [
    '08:00', '09:00', '10:00', '11:00', '12:00', 
    '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'
  ]

  useEffect(() => {
    fetchSchedules()
  }, [])

  const fetchSchedules = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/schedules', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setSchedules(data.schedules || [])
      } else {
        console.error('Failed to fetch schedules:', data.error)
      }
    } catch (error) {
      console.error('Error fetching schedules:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const url = editingSchedule 
        ? `http://localhost:3000/api/schedules/${editingSchedule.id}`
        : 'http://localhost:3000/api/schedules'
      
      const method = editingSchedule ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
        credentials: 'include'
      })

      const data = await response.json()

      if (response.ok) {
        await fetchSchedules()
        setShowAddForm(false)
        setEditingSchedule(null)
        setFormData({ course_id: "", teacher_id: "", room: "", day_of_week: "", start_time: "", end_time: "", batch: "" })
      } else {
        toast.error(data.error || 'Operation failed')
      }
    } catch (error) {
      console.error('Error saving schedule:', error)
      toast.error('Failed to save schedule')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (schedule: Schedule) => {
    setEditingSchedule(schedule)
    setFormData({
      course_id: schedule.course_name, // This would be course_id in real implementation
      teacher_id: schedule.teacher_name, // This would be teacher_id in real implementation
      room: schedule.room,
      day_of_week: schedule.day_of_week,
      start_time: schedule.start_time,
      end_time: schedule.end_time,
      batch: schedule.batch
    })
    setShowAddForm(true)
  }

  const handleDelete = async (scheduleId: number) => {
    if (!confirm('Are you sure you want to delete this schedule?')) {
      return
    }

    try {
      const response = await fetch(`http://localhost:3000/api/schedules/${scheduleId}`, {
        method: 'DELETE',
        credentials: 'include'
      })

      if (response.ok) {
        await fetchSchedules()
      } else {
        const data = await response.json()
        toast.error(data.error || 'Failed to delete schedule')
      }
    } catch (error) {
      console.error('Error deleting schedule:', error)
      toast.error('Failed to delete schedule')
    }
  }

  const checkConflict = (newSchedule: any) => {
    return schedules.some(schedule => 
      schedule.day_of_week === newSchedule.day_of_week &&
      schedule.teacher_name === newSchedule.teacher_name &&
      ((newSchedule.start_time >= schedule.start_time && newSchedule.start_time < schedule.end_time) ||
       (newSchedule.end_time > schedule.start_time && newSchedule.end_time <= schedule.end_time))
    )
  }

  const getSchedulesByDay = (day: string) => {
    return schedules.filter(schedule => schedule.day_of_week === day)
      .sort((a, b) => a.start_time.localeCompare(b.start_time))
  }

  return (
    <DashboardLayout userRole="center_admin" title="Class Schedule">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Class Schedule</h1>
            <p className="text-muted-foreground">Manage class timetables and prevent conflicts</p>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'week' ? 'default' : 'outline'}
              onClick={() => setViewMode('week')}
            >
              Week View
            </Button>
            <Button
              variant={viewMode === 'month' ? 'default' : 'outline'}
              onClick={() => setViewMode('month')}
            >
              Month View
            </Button>
            <Button onClick={() => {
              setShowAddForm(true)
              setEditingSchedule(null)
              setFormData({ course_id: "", teacher_id: "", room: "", day_of_week: "", start_time: "", end_time: "", batch: "" })
            }}>
              <Plus className="mr-2 h-4 w-4" />
              Add Schedule
            </Button>
          </div>
        </div>

        {/* Add/Edit Form */}
        {showAddForm && (
          <Card>
            <CardHeader>
              <CardTitle>{editingSchedule ? 'Edit Schedule' : 'Create New Schedule'}</CardTitle>
              <CardDescription>
                {editingSchedule ? 'Update class schedule information' : 'Add a new class to the timetable'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="course_id">Course *</Label>
                    <Input
                      id="course_id"
                      name="course_id"
                      placeholder="Select course"
                      value={formData.course_id}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="teacher_id">Teacher *</Label>
                    <Input
                      id="teacher_id"
                      name="teacher_id"
                      placeholder="Select teacher"
                      value={formData.teacher_id}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="room">Room *</Label>
                    <Input
                      id="room"
                      name="room"
                      placeholder="e.g., Room 101"
                      value={formData.room}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="batch">Batch</Label>
                    <Input
                      id="batch"
                      name="batch"
                      placeholder="e.g., Batch A"
                      value={formData.batch}
                      onChange={handleInputChange}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="day_of_week">Day of Week *</Label>
                    <select
                      id="day_of_week"
                      name="day_of_week"
                      value={formData.day_of_week}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-input bg-background rounded-md"
                    >
                      <option value="">Select day</option>
                      {daysOfWeek.map(day => (
                        <option key={day} value={day}>{day}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="start_time">Start Time *</Label>
                    <select
                      id="start_time"
                      name="start_time"
                      value={formData.start_time}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-input bg-background rounded-md"
                    >
                      <option value="">Select time</option>
                      {timeSlots.map(time => (
                        <option key={time} value={time}>{time}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="end_time">End Time *</Label>
                    <select
                      id="end_time"
                      name="end_time"
                      value={formData.end_time}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-input bg-background rounded-md"
                    >
                      <option value="">Select time</option>
                      {timeSlots.map(time => (
                        <option key={time} value={time}>{time}</option>
                      ))}
                    </select>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Saving...' : (editingSchedule ? 'Update Schedule' : 'Create Schedule')}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setShowAddForm(false)
                      setEditingSchedule(null)
                      setFormData({ course_id: "", teacher_id: "", room: "", day_of_week: "", start_time: "", end_time: "", batch: "" })
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Schedule View */}
        { loading ? (
          <TableSkeleton rows={6} />
        ) : viewMode === 'week' ? (
          <div className="grid gap-4">
            <h2 className="text-xl font-semibold">Weekly Schedule</h2>
            <div className="grid grid-cols-1 lg:grid-cols-7 gap-4">
              {daysOfWeek.map(day => (
                <Card key={day} className="min-h-[300px]">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">{day}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {getSchedulesByDay(day).map(schedule => (
                      <div
                        key={schedule.id}
                        className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded border-l-4 border-blue-500"
                        data-testid="schedule-item"
                      >
                        <div className="text-xs font-medium">{schedule.start_time} - {schedule.end_time}</div>
                        <div className="text-sm font-semibold">{schedule.course_name}</div>
                        <div className="text-xs text-muted-foreground">{schedule.teacher_name}</div>
                        <div className="text-xs text-muted-foreground">{schedule.room}</div>
                        {schedule.batch && (
                          <div className="text-xs text-muted-foreground">Batch: {schedule.batch}</div>
                        )}
                        <div className="flex gap-1 mt-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(schedule)}
                            className="h-6 px-2 text-xs"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(schedule.id)}
                            className="h-6 px-2 text-xs"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    {getSchedulesByDay(day).length === 0 && (
                      <div className="text-xs text-muted-foreground text-center py-4">
                        No classes scheduled
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Monthly Calendar View</CardTitle>
              <CardDescription>Full month schedule overview</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Monthly calendar view coming soon...
              </div>
            </CardContent>
          </Card>
        )}


      </div>
    </DashboardLayout>
  )
}
