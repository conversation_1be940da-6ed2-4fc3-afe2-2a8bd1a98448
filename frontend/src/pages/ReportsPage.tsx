import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON>, TrendingUp, Download, Calendar, Users, DollarSign, BookOpen, FileText } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton, ListItemSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface ReportData {
  students: {
    total: number
    active: number
    inactive: number
    byMonth: { month: string; count: number }[]
  }
  attendance: {
    averageRate: number
    totalSessions: number
    presentCount: number
    absentCount: number
    byMonth: { month: string; rate: number }[]
  }
  financial: {
    totalRevenue: number
    totalExpenses: number
    netProfit: number
    feeCollection: number
    byMonth: { month: string; revenue: number; expenses: number }[]
  }
  courses: {
    totalCourses: number
    activeCourses: number
    totalEnrollments: number
    popularCourses: { name: string; enrollments: number }[]
  }
}

export default function ReportsPage() {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  })
  const [activeReport, setActiveReport] = useState<'overview' | 'students' | 'attendance' | 'financial' | 'courses'>('overview')

  useEffect(() => {
    fetchReportData()
  }, [dateRange])

  const fetchReportData = async () => {
    try {
      const response = await fetch(`http://localhost:3000/api/reports/dashboard?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setReportData(data.reportData)
      }
    } catch (error) {
      console.error('Error fetching report data:', error)
    } finally {
      setLoading(false)
    }
  }

  const exportReport = async (type: string, format: 'pdf' | 'excel') => {
    try {
      const response = await fetch(`http://localhost:3000/api/reports/export?type=${type}&format=${format}&startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`, {
        credentials: 'include'
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${type}-report.${format === 'excel' ? 'xlsx' : 'pdf'}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        toast.error('Failed to export report')
      }
    } catch (error) {
      console.error('Error exporting report:', error)
      toast.error('Failed to export report')
    }
  }

  const handleDateRangeChange = (field: 'startDate' | 'endDate', value: string) => {
    setDateRange(prev => ({ ...prev, [field]: value }))
  }

  if (loading || !reportData) {
    return (
      <DashboardLayout userRole="center_admin" title="Reports & Analytics">
        <div className="text-center py-8">
          <div className="text-muted-foreground">Loading reports...</div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout userRole="center_admin" title="Reports & Analytics">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Reports & Analytics</h1>
            <p className="text-muted-foreground">Comprehensive insights and data analysis</p>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => exportReport(activeReport, 'pdf')}>
              <Download className="mr-2 h-4 w-4" />
              Export PDF
            </Button>
            <Button variant="outline" onClick={() => exportReport(activeReport, 'excel')}>
              <Download className="mr-2 h-4 w-4" />
              Export Excel
            </Button>
          </div>
        </div>

        {/* Date Range Filter */}
        <Card>
          <CardContent className="p-4">
            <div className="flex gap-4 items-end">
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={dateRange.startDate}
                  onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={dateRange.endDate}
                  onChange={(e) => handleDateRangeChange('endDate', e.target.value)}
                />
              </div>
              <Button onClick={fetchReportData}>
                Update Reports
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Report Navigation */}
        <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
          {[
            { key: 'overview', label: 'Overview', icon: BarChart3 },
            { key: 'students', label: 'Students', icon: Users },
            { key: 'attendance', label: 'Attendance', icon: Calendar },
            { key: 'financial', label: 'Financial', icon: DollarSign },
            { key: 'courses', label: 'Courses', icon: BookOpen }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveReport(key as any)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2 ${
                activeReport === key
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              <Icon className="h-4 w-4" />
              {label}
            </button>
          ))}
        </div>

        {/* Overview Dashboard */}
        {activeReport === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-blue-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Total Students</div>
                    <div className="text-2xl font-bold">{reportData.students.total}</div>
                    <div className="text-xs text-green-600">
                      {reportData.students.active} active
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-green-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Attendance Rate</div>
                    <div className="text-2xl font-bold">{reportData.attendance.averageRate.toFixed(1)}%</div>
                    <div className="text-xs text-muted-foreground">
                      {reportData.attendance.totalSessions} sessions
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-purple-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Revenue</div>
                    <div className="text-2xl font-bold">${reportData.financial.totalRevenue.toFixed(0)}</div>
                    <div className="text-xs text-green-600">
                      ${reportData.financial.netProfit.toFixed(0)} profit
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-orange-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Active Courses</div>
                    <div className="text-2xl font-bold">{reportData.courses.activeCourses}</div>
                    <div className="text-xs text-muted-foreground">
                      {reportData.courses.totalEnrollments} enrollments
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Student Reports */}
        {activeReport === 'students' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Student Statistics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Total Students</span>
                    <span className="font-semibold">{reportData.students.total}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Active Students</span>
                    <span className="font-semibold text-green-600">{reportData.students.active}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Inactive Students</span>
                    <span className="font-semibold text-red-600">{reportData.students.inactive}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Activity Rate</span>
                    <span className="font-semibold">
                      {((reportData.students.active / reportData.students.total) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Monthly Enrollment Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {reportData.students.byMonth.map((month, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-sm">{month.month}</span>
                      <span className="font-semibold">{month.count} students</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Attendance Reports */}
        {activeReport === 'attendance' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Attendance Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Average Attendance Rate</span>
                    <span className="font-semibold">{reportData.attendance.averageRate.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Total Sessions</span>
                    <span className="font-semibold">{reportData.attendance.totalSessions}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Present Count</span>
                    <span className="font-semibold text-green-600">{reportData.attendance.presentCount}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Absent Count</span>
                    <span className="font-semibold text-red-600">{reportData.attendance.absentCount}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Monthly Attendance Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {reportData.attendance.byMonth.map((month, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-sm">{month.month}</span>
                      <span className="font-semibold">{month.rate.toFixed(1)}%</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Financial Reports */}
        {activeReport === 'financial' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Financial Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Total Revenue</span>
                    <span className="font-semibold text-green-600">${reportData.financial.totalRevenue.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Total Expenses</span>
                    <span className="font-semibold text-red-600">${reportData.financial.totalExpenses.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Net Profit</span>
                    <span className={`font-semibold ${reportData.financial.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      ${reportData.financial.netProfit.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Fee Collection</span>
                    <span className="font-semibold">${reportData.financial.feeCollection.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Monthly Financial Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {reportData.financial.byMonth.map((month, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{month.month}</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-green-600">Revenue: ${month.revenue.toFixed(0)}</span>
                        <span className="text-red-600">Expenses: ${month.expenses.toFixed(0)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Course Reports */}
        {activeReport === 'courses' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Course Statistics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Total Courses</span>
                    <span className="font-semibold">{reportData.courses.totalCourses}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Active Courses</span>
                    <span className="font-semibold text-green-600">{reportData.courses.activeCourses}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Total Enrollments</span>
                    <span className="font-semibold">{reportData.courses.totalEnrollments}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Avg. Enrollments per Course</span>
                    <span className="font-semibold">
                      {(reportData.courses.totalEnrollments / reportData.courses.totalCourses).toFixed(1)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Popular Courses
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {reportData.courses.popularCourses.map((course, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-sm">{course.name}</span>
                      <span className="font-semibold">{course.enrollments} students</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
