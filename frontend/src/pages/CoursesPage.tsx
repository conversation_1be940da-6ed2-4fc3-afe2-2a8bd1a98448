import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Search, Edit, Trash2, BookOpen, Calendar, DollarSign, Clock, Users } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton, ListItemSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface Course {
  id: string
  name: string
  code?: string
  description?: string
  durationMonths?: number
  fee?: number
  status: string
  subjectName?: string
  subjectCode?: string
  branchName?: string
  batchCount?: number
  createdAt: string
}

interface Subject {
  id: string
  name: string
  code?: string
}

interface Branch {
  id: string
  branchName: string
  branchCode: string
}

export default function CoursesPage() {
  const [courses, setCourses] = useState<Course[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [branches, setBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingCourse, setEditingCourse] = useState<Course | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    description: "",
    subjectId: "",
    branchId: "",
    durationMonths: "",
    fee: ""
  })

  useEffect(() => {
    fetchCourses()
    fetchSubjects()
    fetchBranches()
  }, [])

  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token')
    const subdomain = window.location.hostname.split('.')[0]
    return {
      'Authorization': `Bearer ${token}`,
      'x-tenant-slug': subdomain === 'localhost' ? 'abc' : subdomain,
      'Content-Type': 'application/json'
    }
  }

  const fetchCourses = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/courses', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setCourses(data.data.courses || [])
      } else {
        console.error('Failed to fetch courses:', data.error)
      }
    } catch (error) {
      console.error('Error fetching courses:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchSubjects = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/subjects', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setSubjects(data.data.subjects || [])
      }
    } catch (error) {
      console.error('Error fetching subjects:', error)
    }
  }

  const fetchBranches = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/branches', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setBranches(data.data.branches || [])
      }
    } catch (error) {
      console.error('Error fetching branches:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const url = editingCourse
        ? `http://localhost:3004/api/coaching/courses/${editingCourse.id}`
        : 'http://localhost:3004/api/coaching/courses'

      const method = editingCourse ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: getAuthHeaders(),
        body: JSON.stringify({
          name: formData.name,
          code: formData.code,
          description: formData.description,
          subjectId: formData.subjectId,
          branchId: formData.branchId || undefined,
          durationMonths: formData.durationMonths ? parseInt(formData.durationMonths) : undefined,
          fee: formData.fee ? parseFloat(formData.fee) : undefined
        })
      })

      const data = await response.json()

      if (response.ok) {
        await fetchCourses()
        setShowAddForm(false)
        setEditingCourse(null)
        setFormData({
          name: "",
          code: "",
          description: "",
          subjectId: "",
          branchId: "",
          durationMonths: "",
          fee: ""
        })
        toast.success(editingCourse ? 'Course updated successfully' : 'Course created successfully')
      } else {
        toast.error(data.message || 'Operation failed')
      }
    } catch (error) {
      console.error('Error saving course:', error)
      toast.error('Failed to save course')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (course: Course) => {
    setEditingCourse(course)
    setFormData({
      name: course.name,
      code: course.code || "",
      description: course.description || "",
      subjectId: "", // Will need to be populated from course data
      branchId: "", // Will need to be populated from course data
      durationMonths: course.durationMonths?.toString() || "",
      fee: course.fee?.toString() || ""
    })
    setShowAddForm(true)
  }

  const handleDelete = async (courseId: number) => {
    if (!confirm('Are you sure you want to delete this course?')) {
      return
    }

    try {
      const response = await fetch(`http://localhost:3000/api/courses/${courseId}`, {
        method: 'DELETE',
        credentials: 'include'
      })

      if (response.ok) {
        await fetchCourses()
      } else {
        const data = await response.json()
        toast.error(data.error || 'Failed to delete course')
      }
    } catch (error) {
      console.error('Error deleting course:', error)
      toast.error('Failed to delete course')
    }
  }

  const filteredCourses = courses.filter(course =>
    course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (course.description && course.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  return (
    <DashboardLayout userRole="center_admin" title="Course Management">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold">Courses</h1>
            <p className="text-muted-foreground">Manage your course catalog</p>
          </div>
          
          <Button onClick={() => {
            setShowAddForm(true)
            setEditingCourse(null)
            setFormData({ name: "", description: "", duration: "", price: "", start_date: "", end_date: "" })
          }}>
            <Plus className="mr-2 h-4 w-4" />
            Add Course
          </Button>
        </div>

        {/* Search Bar */}
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search courses by name or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Add/Edit Form */}
        {showAddForm && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>{editingCourse ? 'Edit Course' : 'Add New Course'}</CardTitle>
              <CardDescription>
                {editingCourse ? 'Update course information' : 'Enter course details to add to your catalog'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Course Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="duration">Duration *</Label>
                    <Input
                      id="duration"
                      placeholder="e.g., 3 months, 6 weeks"
                      value={formData.duration}
                      onChange={(e) => setFormData(prev => ({ ...prev, duration: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="price">Price *</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      value={formData.price}
                      onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="start_date">Start Date</Label>
                    <Input
                      id="start_date"
                      type="date"
                      value={formData.start_date}
                      onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="end_date">End Date</Label>
                    <Input
                      id="end_date"
                      type="date"
                      value={formData.end_date}
                      onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
                    />
                  </div>
                  
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="description">Description</Label>
                    <Input
                      id="description"
                      placeholder="Course description..."
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Saving...' : (editingCourse ? 'Update Course' : 'Add Course')}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setShowAddForm(false)
                      setEditingCourse(null)
                      setFormData({ name: "", description: "", duration: "", price: "", start_date: "", end_date: "" })
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Courses List */}
        { loading ? (
          <TableSkeleton rows={6} /> : filteredCourses.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <BookOpen className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No courses found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ? 'No courses match your search criteria.' : 'Get started by adding your first course.'}
              </p>
              {!searchTerm && (
                <Button onClick={() => setShowAddForm(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Course
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {filteredCourses.map((course) => (
              <Card key={course.id} data-testid="course-card">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                          <BookOpen className="text-white h-6 w-6" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{course.name}</h3>
                          {course.description && (
                            <p className="text-sm text-muted-foreground mt-1">{course.description}</p>
                          )}
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center text-muted-foreground">
                          <Clock className="h-4 w-4 mr-2" />
                          {course.duration}
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          <DollarSign className="h-4 w-4 mr-2" />
                          ${course.price}
                        </div>
                        {course.start_date && (
                          <div className="flex items-center text-muted-foreground">
                            <Calendar className="h-4 w-4 mr-2" />
                            {new Date(course.start_date).toLocaleDateString()}
                          </div>
                        )}
                        {course.end_date && (
                          <div className="flex items-center text-muted-foreground">
                            <Calendar className="h-4 w-4 mr-2" />
                            {new Date(course.end_date).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <div className="text-right mr-4">
                        <div className="text-sm font-medium">
                          Created: {new Date(course.created_at).toLocaleDateString()}
                        </div>
                        <div className={`text-xs px-2 py-1 rounded-full ${
                          course.status === 'active' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                        }`}>
                          {course.status}
                        </div>
                      </div>
                      
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleEdit(course)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleDelete(course.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
