import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, BookOpen, GraduationCap, DollarSign, Building2 } from "lucide-react"
import { <PERSON> } from "react-router-dom"
import DashboardLayout from "@/components/DashboardLayout"

export default function TenantDashboard() {
  const stats = [
    {
      title: "Total Students",
      value: "156",
      icon: <GraduationCap className="h-4 w-4" />,
      description: "Enrolled students"
    },
    {
      title: "Active Courses",
      value: "12",
      icon: <BookOpen className="h-4 w-4" />,
      description: "Running courses"
    },
    {
      title: "Total Teachers",
      value: "8",
      icon: <Users className="h-4 w-4" />,
      description: "Teaching staff"
    },
    {
      title: "Monthly Revenue",
      value: "$4,250",
      icon: <DollarSign className="h-4 w-4" />,
      description: "This month's earnings"
    }
  ]

  return (
    <DashboardLayout userRole="center_admin" title="Dashboard">
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="rounded-2xl bg-gradient-to-r from-blue-600 to-purple-600 p-8 text-white mb-8">
          <h1 className="text-3xl font-bold mb-3">Good morning, John!</h1>
          <p className="text-blue-100 text-lg">Welcome back to ABC Coaching Center</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                {stat.icon}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">{stat.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Manage your coaching center</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full justify-start" variant="outline" asChild>
                <Link to="/students">
                  <GraduationCap className="mr-2 h-4 w-4" />
                  Manage Students
                </Link>
              </Button>
              <Button className="w-full justify-start" variant="outline" asChild>
                <Link to="/courses">
                  <BookOpen className="mr-2 h-4 w-4" />
                  Manage Courses
                </Link>
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Users className="mr-2 h-4 w-4" />
                Manage Teachers
              </Button>
              <Button className="w-full justify-start" variant="outline" asChild>
                <Link to="/branches">
                  <Building2 className="mr-2 h-4 w-4" />
                  Manage Branches
                </Link>
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <DollarSign className="mr-2 h-4 w-4" />
                View Reports
              </Button>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest updates in your center</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-4 p-4 bg-muted/50 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <p className="font-medium">New student enrolled</p>
                    <p className="text-sm text-muted-foreground">Sarah Johnson enrolled in Mathematics course</p>
                    <p className="text-xs text-muted-foreground mt-1">2 hours ago</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4 p-4 bg-muted/50 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <p className="font-medium">Payment received</p>
                    <p className="text-sm text-muted-foreground">Monthly fee payment from 5 students</p>
                    <p className="text-xs text-muted-foreground mt-1">4 hours ago</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4 p-4 bg-muted/50 rounded-lg">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <p className="font-medium">Class schedule updated</p>
                    <p className="text-sm text-muted-foreground">Physics class rescheduled for tomorrow</p>
                    <p className="text-xs text-muted-foreground mt-1">1 day ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
