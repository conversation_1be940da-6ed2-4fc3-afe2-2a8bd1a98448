import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Calendar, Users, CheckCircle, XCircle, Clock, Search, Filter } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton, ListItemSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface AttendanceRecord {
  id: number
  student_id: number
  student_name: string
  course_name: string
  date: string
  status: 'present' | 'absent' | 'late'
  marked_by: string
  notes?: string
  created_at: string
}

interface Student {
  id: number
  name: string
  email: string
  course_name: string
}

export default function AttendancePage() {
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [selectedCourse, setSelectedCourse] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'mark' | 'view'>('mark')

  useEffect(() => {
    fetchStudents()
    fetchAttendance()
  }, [selectedDate, selectedCourse])

  const fetchStudents = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/students', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setStudents(data.students || [])
      }
    } catch (error) {
      console.error('Error fetching students:', error)
    }
  }

  const fetchAttendance = async () => {
    try {
      const params = new URLSearchParams()
      if (selectedDate) params.append('date', selectedDate)
      if (selectedCourse) params.append('course', selectedCourse)

      const response = await fetch(`http://localhost:3000/api/attendance?${params}`, {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setAttendanceRecords(data.attendance || [])
      }
    } catch (error) {
      console.error('Error fetching attendance:', error)
    } finally {
      setLoading(false)
    }
  }

  const markAttendance = async (studentId: number, status: 'present' | 'absent' | 'late', notes?: string) => {
    try {
      const response = await fetch('http://localhost:3000/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          student_id: studentId,
          date: selectedDate,
          status,
          notes
        }),
        credentials: 'include'
      })

      if (response.ok) {
        await fetchAttendance()
      } else {
        const data = await response.json()
        toast.error(data.error || 'Failed to mark attendance')
      }
    } catch (error) {
      console.error('Error marking attendance:', error)
      toast.error('Failed to mark attendance')
    }
  }

  const getAttendanceStatus = (studentId: number) => {
    const record = attendanceRecords.find(r => r.student_id === studentId && r.date === selectedDate)
    return record?.status || null
  }

  const getAttendanceStats = () => {
    const total = attendanceRecords.length
    const present = attendanceRecords.filter(r => r.status === 'present').length
    const absent = attendanceRecords.filter(r => r.status === 'absent').length
    const late = attendanceRecords.filter(r => r.status === 'late').length
    
    return {
      total,
      present,
      absent,
      late,
      percentage: total > 0 ? Math.round((present / total) * 100) : 0
    }
  }

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (selectedCourse === '' || student.course_name === selectedCourse)
  )

  const courses = [...new Set(students.map(s => s.course_name))].filter(Boolean)
  const stats = getAttendanceStats()

  return (
    <DashboardLayout userRole="center_admin" title="Attendance Management">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Attendance</h1>
            <p className="text-muted-foreground">Track and manage student attendance</p>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'mark' ? 'default' : 'outline'}
              onClick={() => setViewMode('mark')}
            >
              Mark Attendance
            </Button>
            <Button
              variant={viewMode === 'view' ? 'default' : 'outline'}
              onClick={() => setViewMode('view')}
            >
              View Records
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="course">Course</Label>
                <select
                  id="course"
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                  className="w-full px-3 py-2 border border-input bg-background rounded-md"
                >
                  <option value="">All Courses</option>
                  {courses.map(course => (
                    <option key={course} value={course}>{course}</option>
                  ))}
                </select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="search">Search Students</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    id="search"
                    placeholder="Search by name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Quick Actions</Label>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      filteredStudents.forEach(student => {
                        if (!getAttendanceStatus(student.id)) {
                          markAttendance(student.id, 'present')
                        }
                      })
                    }}
                  >
                    Mark All Present
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Attendance Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Total Students</div>
                  <div className="text-lg font-semibold">{stats.total}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Present</div>
                  <div className="text-lg font-semibold">{stats.present}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Absent</div>
                  <div className="text-lg font-semibold">{stats.absent}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-orange-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Attendance Rate</div>
                  <div className="text-lg font-semibold">{stats.percentage}%</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Attendance Content */}
        { loading ? (
          <TableSkeleton rows={6} />
        ) : viewMode === 'mark' ? (
          <Card>
            <CardHeader>
              <CardTitle>Mark Attendance - {selectedDate}</CardTitle>
              <CardDescription>
                Click on the status buttons to mark attendance for each student
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredStudents.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No students found for the selected criteria
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredStudents.map((student) => {
                    const currentStatus = getAttendanceStatus(student.id)
                    return (
                      <div
                        key={student.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                        data-testid="student-attendance-row"
                      >
                        <div className="flex-1">
                          <div className="font-medium">{student.name}</div>
                          <div className="text-sm text-muted-foreground">{student.course_name}</div>
                        </div>
                        
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant={currentStatus === 'present' ? 'default' : 'outline'}
                            onClick={() => markAttendance(student.id, 'present')}
                            className="text-green-600"
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Present
                          </Button>
                          
                          <Button
                            size="sm"
                            variant={currentStatus === 'late' ? 'default' : 'outline'}
                            onClick={() => markAttendance(student.id, 'late')}
                            className="text-orange-600"
                          >
                            <Clock className="h-4 w-4 mr-1" />
                            Late
                          </Button>
                          
                          <Button
                            size="sm"
                            variant={currentStatus === 'absent' ? 'default' : 'outline'}
                            onClick={() => markAttendance(student.id, 'absent')}
                            className="text-red-600"
                          >
                            <XCircle className="h-4 w-4 mr-1" />
                            Absent
                          </Button>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Attendance Records</CardTitle>
              <CardDescription>
                View historical attendance data
              </CardDescription>
            </CardHeader>
            <CardContent>
              {attendanceRecords.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No attendance records found for the selected date and course
                </div>
              ) : (
                <div className="space-y-4">
                  {attendanceRecords.map((record) => (
                    <div
                      key={record.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                      data-testid="attendance-record"
                    >
                      <div className="flex-1">
                        <div className="font-medium">{record.student_name}</div>
                        <div className="text-sm text-muted-foreground">{record.course_name}</div>
                        {record.notes && (
                          <div className="text-sm text-muted-foreground mt-1">Note: {record.notes}</div>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <div className="text-sm text-muted-foreground">{record.date}</div>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                          record.status === 'present' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : record.status === 'late'
                            ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {record.status}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
