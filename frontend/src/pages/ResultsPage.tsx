import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Search, Award, Calendar, User, FileText, Download, Edit, Trash2 } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton, ListItemSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface ExamResult {
  id: number
  student_id: number
  student_name: string
  course_name: string
  exam_name: string
  exam_date: string
  total_marks: number
  obtained_marks: number
  percentage: number
  grade: string
  status: string
  remarks?: string
  created_at: string
}

interface Student {
  id: number
  name: string
  email: string
  course_name: string
}

export default function ResultsPage() {
  const [results, setResults] = useState<ExamResult[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [courseFilter, setCourseFilter] = useState("")
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingResult, setEditingResult] = useState<ExamResult | null>(null)
  const [formData, setFormData] = useState({
    student_id: "",
    exam_name: "",
    exam_date: "",
    total_marks: "",
    obtained_marks: "",
    remarks: ""
  })

  useEffect(() => {
    fetchResults()
    fetchStudents()
  }, [])

  const fetchResults = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/results', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setResults(data.results || [])
      }
    } catch (error) {
      console.error('Error fetching results:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStudents = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/students', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setStudents(data.students || [])
      }
    } catch (error) {
      console.error('Error fetching students:', error)
    }
  }

  const calculateGrade = (percentage: number) => {
    if (percentage >= 90) return 'A+'
    if (percentage >= 80) return 'A'
    if (percentage >= 70) return 'B+'
    if (percentage >= 60) return 'B'
    if (percentage >= 50) return 'C+'
    if (percentage >= 40) return 'C'
    return 'F'
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const totalMarks = parseFloat(formData.total_marks)
      const obtainedMarks = parseFloat(formData.obtained_marks)
      const percentage = (obtainedMarks / totalMarks) * 100
      const grade = calculateGrade(percentage)

      const url = editingResult 
        ? `http://localhost:3000/api/results/${editingResult.id}`
        : 'http://localhost:3000/api/results'
      
      const method = editingResult ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          total_marks: totalMarks,
          obtained_marks: obtainedMarks,
          percentage,
          grade
        }),
        credentials: 'include'
      })

      const data = await response.json()

      if (response.ok) {
        await fetchResults()
        setShowAddForm(false)
        setEditingResult(null)
        setFormData({ student_id: "", exam_name: "", exam_date: "", total_marks: "", obtained_marks: "", remarks: "" })
      } else {
        toast.error(data.error || 'Operation failed')
      }
    } catch (error) {
      console.error('Error saving result:', error)
      toast.error('Failed to save result')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (result: ExamResult) => {
    setEditingResult(result)
    setFormData({
      student_id: result.student_id.toString(),
      exam_name: result.exam_name,
      exam_date: result.exam_date,
      total_marks: result.total_marks.toString(),
      obtained_marks: result.obtained_marks.toString(),
      remarks: result.remarks || ""
    })
    setShowAddForm(true)
  }

  const handleDelete = async (resultId: number) => {
    if (!confirm('Are you sure you want to delete this result?')) {
      return
    }

    try {
      const response = await fetch(`http://localhost:3000/api/results/${resultId}`, {
        method: 'DELETE',
        credentials: 'include'
      })

      if (response.ok) {
        await fetchResults()
      } else {
        const data = await response.json()
        toast.error(data.error || 'Failed to delete result')
      }
    } catch (error) {
      console.error('Error deleting result:', error)
      toast.error('Failed to delete result')
    }
  }

  const generateReportCard = async (studentId: number) => {
    try {
      const response = await fetch(`http://localhost:3000/api/results/report-card/${studentId}`, {
        credentials: 'include'
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `report-card-${studentId}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        toast.error('Failed to generate report card')
      }
    } catch (error) {
      console.error('Error generating report card:', error)
      toast.error('Failed to generate report card')
    }
  }

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A+':
      case 'A':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'B+':
      case 'B':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'C+':
      case 'C':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      default:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    }
  }

  const filteredResults = results.filter(result => {
    const matchesSearch = result.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         result.exam_name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCourse = courseFilter === '' || result.course_name === courseFilter
    return matchesSearch && matchesCourse
  })

  const courses = [...new Set(results.map(r => r.course_name))].filter(Boolean)

  const getResultStats = () => {
    const totalResults = results.length
    const passedResults = results.filter(r => r.percentage >= 40).length
    const failedResults = totalResults - passedResults
    const averagePercentage = totalResults > 0 
      ? results.reduce((sum, r) => sum + r.percentage, 0) / totalResults 
      : 0

    return {
      total: totalResults,
      passed: passedResults,
      failed: failedResults,
      average: Math.round(averagePercentage * 100) / 100
    }
  }

  const stats = getResultStats()

  return (
    <DashboardLayout userRole="center_admin" title="Exam Results">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Exam Results</h1>
            <p className="text-muted-foreground">Manage student exam results and generate reports</p>
          </div>
          
          <Button onClick={() => {
            setShowAddForm(true)
            setEditingResult(null)
            setFormData({ student_id: "", exam_name: "", exam_date: "", total_marks: "", obtained_marks: "", remarks: "" })
          }}>
            <Plus className="mr-2 h-4 w-4" />
            Add Result
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Total Results</div>
                  <div className="text-lg font-semibold">{stats.total}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Award className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Passed</div>
                  <div className="text-lg font-semibold">{stats.passed}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-red-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                  <div className="text-lg font-semibold">{stats.failed}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Award className="h-4 w-4 text-purple-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Average %</div>
                  <div className="text-lg font-semibold">{stats.average}%</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    id="search"
                    placeholder="Search by student or exam..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="course">Course Filter</Label>
                <select
                  id="course"
                  value={courseFilter}
                  onChange={(e) => setCourseFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-input bg-background rounded-md"
                >
                  <option value="">All Courses</option>
                  {courses.map(course => (
                    <option key={course} value={course}>{course}</option>
                  ))}
                </select>
              </div>
              
              <div className="space-y-2">
                <Label>Quick Actions</Label>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Export Results
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Add/Edit Form */}
        {showAddForm && (
          <Card>
            <CardHeader>
              <CardTitle>{editingResult ? 'Edit Result' : 'Add Exam Result'}</CardTitle>
              <CardDescription>
                {editingResult ? 'Update exam result information' : 'Enter exam result details'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="student_id">Student *</Label>
                    <select
                      id="student_id"
                      name="student_id"
                      value={formData.student_id}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-input bg-background rounded-md"
                    >
                      <option value="">Select student</option>
                      {students.map(student => (
                        <option key={student.id} value={student.id}>
                          {student.name} - {student.course_name}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="exam_name">Exam Name *</Label>
                    <Input
                      id="exam_name"
                      name="exam_name"
                      placeholder="e.g., Mid Term Exam"
                      value={formData.exam_name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="exam_date">Exam Date *</Label>
                    <Input
                      id="exam_date"
                      name="exam_date"
                      type="date"
                      value={formData.exam_date}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="total_marks">Total Marks *</Label>
                    <Input
                      id="total_marks"
                      name="total_marks"
                      type="number"
                      placeholder="100"
                      value={formData.total_marks}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="obtained_marks">Obtained Marks *</Label>
                    <Input
                      id="obtained_marks"
                      name="obtained_marks"
                      type="number"
                      placeholder="85"
                      value={formData.obtained_marks}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="remarks">Remarks</Label>
                    <Input
                      id="remarks"
                      name="remarks"
                      placeholder="Additional comments..."
                      value={formData.remarks}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Saving...' : (editingResult ? 'Update Result' : 'Add Result')}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setShowAddForm(false)
                      setEditingResult(null)
                      setFormData({ student_id: "", exam_name: "", exam_date: "", total_marks: "", obtained_marks: "", remarks: "" })
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Results List */}
        { loading ? (
          <TableSkeleton rows={6} />
        ) : filteredResults.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Award className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No results found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || courseFilter ? 'No results match your filters.' : 'Get started by adding your first exam result.'}
              </p>
              {!searchTerm && !courseFilter && (
                <Button onClick={() => setShowAddForm(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Result
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {filteredResults.map((result) => (
              <Card key={result.id} data-testid="result-record">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                          <Award className="text-white h-6 w-6" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{result.student_name}</h3>
                          <p className="text-sm text-muted-foreground">{result.course_name}</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center text-muted-foreground">
                          <FileText className="h-4 w-4 mr-2" />
                          {result.exam_name}
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          <Calendar className="h-4 w-4 mr-2" />
                          {new Date(result.exam_date).toLocaleDateString()}
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          <Award className="h-4 w-4 mr-2" />
                          {result.obtained_marks}/{result.total_marks} ({result.percentage.toFixed(1)}%)
                        </div>
                        <div className={`text-xs px-2 py-1 rounded-full font-medium w-fit ${getGradeColor(result.grade)}`}>
                          Grade: {result.grade}
                        </div>
                      </div>
                      
                      {result.remarks && (
                        <div className="mt-2 text-sm text-muted-foreground">
                          Remarks: {result.remarks}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => generateReportCard(result.student_id)}
                      >
                        <Download className="h-4 w-4 mr-1" />
                        Report
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(result)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(result.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
