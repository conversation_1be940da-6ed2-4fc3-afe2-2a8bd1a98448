import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Calendar, User, AlertCircle } from "lucide-react"
import DOMPurify from 'dompurify'
import toast from 'react-hot-toast'

interface CustomPage {
  id: string
  title: string
  slug: string
  content: string
  metaDescription?: string
  createdAt: string
  updatedAt: string
}

export default function PublicCustomPage() {
  const { slug } = useParams<{ slug: string }>()
  const navigate = useNavigate()
  const [page, setPage] = useState<CustomPage | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (slug) {
      fetchPage(slug)
    }
  }, [slug])

  const fetchPage = async (pageSlug: string) => {
    try {
      const subdomain = window.location.hostname.split('.')[0]
      const tenantSlug = subdomain === 'localhost' ? 'abc' : subdomain
      
      const response = await fetch(`http://localhost:3004/api/coaching/public/pages/${pageSlug}`, {
        headers: {
          'x-tenant-slug': tenantSlug,
          'Content-Type': 'application/json'
        }
      })
      
      const data = await response.json()
      
      if (response.ok) {
        setPage(data.data.page)
        
        // Set page title and meta description for SEO
        document.title = data.data.page.title
        if (data.data.page.metaDescription) {
          const metaDescription = document.querySelector('meta[name="description"]')
          if (metaDescription) {
            metaDescription.setAttribute('content', data.data.page.metaDescription)
          } else {
            const meta = document.createElement('meta')
            meta.name = 'description'
            meta.content = data.data.page.metaDescription
            document.head.appendChild(meta)
          }
        }
      } else {
        setError(data.message || 'Page not found')
      }
    } catch (error) {
      console.error('Error fetching page:', error)
      setError('Failed to load page')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const goBack = () => {
    if (window.history.length > 1) {
      navigate(-1)
    } else {
      // Navigate to home page if no history
      const subdomain = window.location.hostname.split('.')[0]
      const tenantSlug = subdomain === 'localhost' ? 'abc' : subdomain
      window.location.href = `http://${tenantSlug}.localhost:5175`
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-12 bg-gray-200 rounded w-3/4 mb-6"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                <div className="h-4 bg-gray-200 rounded w-4/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !page) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardContent className="text-center py-12">
                <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 mb-2">Page Not Found</h1>
                <p className="text-gray-600 mb-6">
                  {error || 'The page you are looking for does not exist or has been removed.'}
                </p>
                <Button onClick={goBack}>
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Go Back
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="max-w-4xl mx-auto">
            <Button 
              variant="ghost" 
              onClick={goBack}
              className="mb-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-8">
              {/* Page Header */}
              <header className="mb-8">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">
                  {page.title}
                </h1>
                
                <div className="flex items-center gap-6 text-sm text-gray-500 border-b pb-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>Published {formatDate(page.createdAt)}</span>
                  </div>
                  {page.updatedAt !== page.createdAt && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>Updated {formatDate(page.updatedAt)}</span>
                    </div>
                  )}
                </div>
              </header>

              {/* Page Content */}
              <article className="prose prose-lg max-w-none">
                <div 
                  dangerouslySetInnerHTML={{ 
                    __html: DOMPurify.sanitize(page.content, {
                      ALLOWED_TAGS: [
                        'p', 'br', 'strong', 'em', 'u', 's', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                        'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'table', 'thead', 'tbody', 
                        'tr', 'th', 'td', 'div', 'span', 'pre', 'code'
                      ],
                      ALLOWED_ATTR: [
                        'href', 'src', 'alt', 'title', 'class', 'id', 'target', 'rel'
                      ]
                    })
                  }}
                />
              </article>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-12">
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-4xl mx-auto text-center text-sm text-gray-500">
            <p>© 2024 Coaching Center. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
