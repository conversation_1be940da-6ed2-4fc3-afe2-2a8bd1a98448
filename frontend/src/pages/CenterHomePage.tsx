import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

interface CenterInfo {
  id: number
  name: string
  subdomain: string
  email: string
  phone: string
  address: string
  description?: string
  logo?: string
}

export default function CenterHomePage() {
  const { getTenantSlug } = useAuth()
  const [centerInfo, setCenterInfo] = useState<CenterInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const tenantSlug = getTenantSlug()

  useEffect(() => {
    fetchCenterInfo()
  }, [tenantSlug])

  const fetchCenterInfo = async () => {
    try {
      if (!tenantSlug) {
        setLoading(false)
        return
      }

      const response = await fetch(`http://localhost:3000/api/center/info?tenant_slug=${tenantSlug}`)
      if (response.ok) {
        const data = await response.json()
        setCenterInfo(data.center)
      }
    } catch (error) {
      console.error('Error fetching center info:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!centerInfo) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Center Not Found</h1>
          <p className="text-gray-600">The coaching center you're looking for doesn't exist.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              {centerInfo.logo && (
                <img
                  src={centerInfo.logo}
                  alt={centerInfo.name}
                  className="h-10 w-10 rounded-full mr-3"
                />
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{centerInfo.name}</h1>
                <p className="text-sm text-gray-600">Coaching Center</p>
              </div>
            </div>
            <div className="flex space-x-4">
              <Link
                to="/login"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Student Login
              </Link>
              <Link
                to="/admin-login"
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Staff Login
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Welcome to {centerInfo.name}
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            {centerInfo.description || 'Excellence in education, nurturing future leaders through quality coaching and personalized attention.'}
          </p>
          <div className="flex justify-center space-x-4">
            <Link
              to="/courses"
              className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              View Courses
            </Link>
            <Link
              to="/contact"
              className="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Why Choose Us?</h3>
            <p className="text-lg text-gray-600">We provide comprehensive coaching with modern teaching methods</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 bg-white rounded-lg shadow-md">
              <div className="text-4xl mb-4">👨‍🏫</div>
              <h4 className="text-xl font-semibold mb-2">Expert Faculty</h4>
              <p className="text-gray-600">Experienced teachers with proven track records</p>
            </div>
            
            <div className="text-center p-6 bg-white rounded-lg shadow-md">
              <div className="text-4xl mb-4">📚</div>
              <h4 className="text-xl font-semibold mb-2">Comprehensive Study Material</h4>
              <p className="text-gray-600">Well-structured curriculum and study resources</p>
            </div>
            
            <div className="text-center p-6 bg-white rounded-lg shadow-md">
              <div className="text-4xl mb-4">🎯</div>
              <h4 className="text-xl font-semibold mb-2">Personalized Attention</h4>
              <p className="text-gray-600">Individual focus on each student's progress</p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="bg-gray-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Get In Touch</h3>
            <p className="text-lg text-gray-600">Ready to start your learning journey?</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-2xl mb-2">📧</div>
              <h4 className="font-semibold mb-1">Email</h4>
              <p className="text-gray-600">{centerInfo.email}</p>
            </div>
            
            <div>
              <div className="text-2xl mb-2">📞</div>
              <h4 className="font-semibold mb-1">Phone</h4>
              <p className="text-gray-600">{centerInfo.phone}</p>
            </div>
            
            <div>
              <div className="text-2xl mb-2">📍</div>
              <h4 className="font-semibold mb-1">Address</h4>
              <p className="text-gray-600">{centerInfo.address}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p>&copy; 2024 {centerInfo.name}. All rights reserved.</p>
          <p className="text-sm text-gray-400 mt-2">
            Powered by TeachingCenter Platform
          </p>
        </div>
      </footer>
    </div>
  )
}
