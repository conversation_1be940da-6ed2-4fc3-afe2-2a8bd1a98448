import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Building2, Users, DollarSign, BarChart3, Settings, Crown, Globe } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import { DashboardSkeleton, CardGridSkeleton } from "@/components/skeletons/DashboardSkeleton"
import toast from 'react-hot-toast'

interface CoachingCenter {
  id: number
  name: string
  slug: string
  email: string
  phone: string
  address: string
  subscription_plan: string
  status: 'active' | 'inactive' | 'suspended'
  created_at: string
  admin_name: string
  admin_email: string
}

interface SystemStats {
  totalCenters: number
  activeCenters: number
  totalUsers: number
  totalRevenue: number
  monthlyGrowth: number
}

export default function SuperAdminDashboard() {
  const [centers, setCenters] = useState<CoachingCenter[]>([])
  const [stats, setStats] = useState<SystemStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    email: "",
    phone: "",
    address: "",
    admin_name: "",
    admin_email: "",
    admin_password: "",
    subscription_plan: "basic"
  })

  useEffect(() => {
    fetchCenters()
    fetchSystemStats()
  }, [])

  const fetchCenters = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/admin/centers', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setCenters(data.centers || [])
      }
    } catch (error) {
      console.error('Error fetching centers:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchSystemStats = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/admin/stats', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Auto-generate slug from name
    if (name === 'name') {
      const slug = value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
      setFormData(prev => ({ ...prev, slug }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('http://localhost:3000/api/admin/centers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
        credentials: 'include'
      })

      const data = await response.json()

      if (response.ok) {
        await fetchCenters()
        await fetchSystemStats()
        setShowAddForm(false)
        setFormData({
          name: "", slug: "", email: "", phone: "", address: "",
          admin_name: "", admin_email: "", admin_password: "", subscription_plan: "basic"
        })
        toast.success('Coaching center created successfully!')
      } else {
        toast.error(data.error || 'Failed to create center')
      }
    } catch (error) {
      console.error('Error creating center:', error)
      toast.error('Failed to create center')
    } finally {
      setLoading(false)
    }
  }

  const toggleCenterStatus = async (centerId: number, newStatus: string) => {
    try {
      const response = await fetch(`http://localhost:3000/api/admin/centers/${centerId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
        credentials: 'include'
      })

      if (response.ok) {
        await fetchCenters()
        await fetchSystemStats()
      } else {
        const data = await response.json()
        toast.error(data.error || 'Failed to update center status')
      }
    } catch (error) {
      console.error('Error updating center status:', error)
      toast.error('Failed to update center status')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'suspended':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  return (
    <DashboardLayout userRole="super_admin" title="Super Admin Dashboard">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center">
              <Crown className="text-white h-6 w-6" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Super Admin Dashboard</h1>
              <p className="text-muted-foreground">Manage coaching centers and system-wide operations</p>
            </div>
          </div>
          
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Coaching Center
          </Button>
        </div>

        {/* System Statistics */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-blue-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Total Centers</div>
                    <div className="text-2xl font-bold">{stats.totalCenters}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-green-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Active Centers</div>
                    <div className="text-2xl font-bold">{stats.activeCenters}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-purple-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Total Users</div>
                    <div className="text-2xl font-bold">{stats.totalUsers}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-yellow-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Total Revenue</div>
                    <div className="text-2xl font-bold">${stats.totalRevenue.toFixed(0)}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-orange-500" />
                  <div>
                    <div className="text-sm text-muted-foreground">Monthly Growth</div>
                    <div className="text-2xl font-bold">{stats.monthlyGrowth.toFixed(1)}%</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Add Center Form */}
        {showAddForm && (
          <Card>
            <CardHeader>
              <CardTitle>Add New Coaching Center</CardTitle>
              <CardDescription>Create a new coaching center with admin account</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Center Name *</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="ABC Coaching Center"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="slug">URL Slug *</Label>
                    <Input
                      id="slug"
                      name="slug"
                      placeholder="abc-coaching"
                      value={formData.slug}
                      onChange={handleInputChange}
                      required
                    />
                    <p className="text-xs text-muted-foreground">
                      Will be accessible at: {formData.slug}.localhost:5173
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email">Center Email *</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      name="phone"
                      placeholder="+1234567890"
                      value={formData.phone}
                      onChange={handleInputChange}
                    />
                  </div>
                  
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      name="address"
                      placeholder="123 Main St, City, State"
                      value={formData.address}
                      onChange={handleInputChange}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="admin_name">Admin Name *</Label>
                    <Input
                      id="admin_name"
                      name="admin_name"
                      placeholder="John Doe"
                      value={formData.admin_name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="admin_email">Admin Email *</Label>
                    <Input
                      id="admin_email"
                      name="admin_email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.admin_email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="admin_password">Admin Password *</Label>
                    <Input
                      id="admin_password"
                      name="admin_password"
                      type="password"
                      placeholder="Strong password"
                      value={formData.admin_password}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="subscription_plan">Subscription Plan</Label>
                    <select
                      id="subscription_plan"
                      name="subscription_plan"
                      value={formData.subscription_plan}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md"
                    >
                      <option value="basic">Basic Plan</option>
                      <option value="premium">Premium Plan</option>
                      <option value="enterprise">Enterprise Plan</option>
                    </select>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Creating...' : 'Create Center'}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setShowAddForm(false)
                      setFormData({
                        name: "", slug: "", email: "", phone: "", address: "",
                        admin_name: "", admin_email: "", admin_password: "", subscription_plan: "basic"
                      })
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Centers List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Coaching Centers
            </CardTitle>
            <CardDescription>Manage all coaching centers in the system</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <CardGridSkeleton count={3} />
            ) : centers.length === 0 ? (
              <div className="text-center py-8">
                <Building2 className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No coaching centers found</h3>
                <p className="text-muted-foreground mb-4">Get started by adding your first coaching center.</p>
                <Button onClick={() => setShowAddForm(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Center
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {centers.map((center) => (
                  <div key={center.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-2">
                          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                            <Building2 className="text-white h-5 w-5" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg">{center.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {center.slug}.localhost:5173
                            </p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Admin:</span> {center.admin_name}
                          </div>
                          <div>
                            <span className="font-medium">Email:</span> {center.admin_email}
                          </div>
                          <div>
                            <span className="font-medium">Plan:</span> {center.subscription_plan}
                          </div>
                        </div>
                        
                        {center.address && (
                          <div className="mt-2 text-sm text-muted-foreground">
                            <span className="font-medium">Address:</span> {center.address}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(center.status)}`}>
                          {center.status}
                        </div>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => toggleCenterStatus(
                            center.id, 
                            center.status === 'active' ? 'suspended' : 'active'
                          )}
                        >
                          {center.status === 'active' ? 'Suspend' : 'Activate'}
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(`http://${center.slug}.localhost:5173/center/dashboard`, '_blank')}
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
