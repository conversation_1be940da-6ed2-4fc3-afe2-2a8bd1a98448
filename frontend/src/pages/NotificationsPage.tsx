import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Search, MessageSquare, Send, Bell, Users, Calendar, CheckCircle } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton, PageHeaderSkeleton, ListItemSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface Notification {
  id: number
  type: 'sms' | 'push' | 'whatsapp' | 'email'
  title: string
  message: string
  recipients: string[]
  status: 'pending' | 'sent' | 'failed'
  sent_at?: string
  created_at: string
}

interface NotificationTemplate {
  id: number
  name: string
  type: string
  subject: string
  content: string
  variables: string[]
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [templates, setTemplates] = useState<NotificationTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'send' | 'history' | 'templates'>('send')
  const [searchTerm, setSearchTerm] = useState("")
  const [showSendForm, setShowSendForm] = useState(false)
  const [formData, setFormData] = useState({
    type: "sms",
    title: "",
    message: "",
    recipients: "",
    schedule_date: "",
    template_id: ""
  })

  useEffect(() => {
    fetchNotifications()
    fetchTemplates()
  }, [])

  const fetchNotifications = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/notifications', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setNotifications(data.notifications || [])
      }
    } catch (error) {
      console.error('Error fetching notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchTemplates = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/notification-templates', {
        credentials: 'include'
      })
      const data = await response.json()
      if (response.ok) {
        setTemplates(data.templates || [])
      }
    } catch (error) {
      console.error('Error fetching templates:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSendNotification = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('http://localhost:3000/api/notifications/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          recipients: formData.recipients.split(',').map(r => r.trim())
        }),
        credentials: 'include'
      })

      const data = await response.json()

      if (response.ok) {
        await fetchNotifications()
        setShowSendForm(false)
        setFormData({ type: "sms", title: "", message: "", recipients: "", schedule_date: "", template_id: "" })
        toast.success('Notification sent successfully!')
      } else {
        toast.error(data.error || 'Failed to send notification')
      }
    } catch (error) {
      console.error('Error sending notification:', error)
      toast.error('Failed to send notification')
    } finally {
      setLoading(false)
    }
  }

  const sendAbsenceNotifications = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/notifications/absence-alerts', {
        method: 'POST',
        credentials: 'include'
      })

      if (response.ok) {
        await fetchNotifications()
        toast.success('Absence notifications sent successfully!')
      } else {
        const data = await response.json()
        toast.error(data.error || 'Failed to send absence notifications')
      }
    } catch (error) {
      console.error('Error sending absence notifications:', error)
      toast.error('Failed to send absence notifications')
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'sms':
        return <MessageSquare className="h-4 w-4" />
      case 'push':
        return <Bell className="h-4 w-4" />
      case 'whatsapp':
        return <MessageSquare className="h-4 w-4" />
      case 'email':
        return <Send className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    }
  }

  const filteredNotifications = notifications.filter(notification =>
    notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    notification.message.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getNotificationStats = () => {
    const totalSent = notifications.filter(n => n.status === 'sent').length
    const totalFailed = notifications.filter(n => n.status === 'failed').length
    const totalPending = notifications.filter(n => n.status === 'pending').length
    const totalNotifications = notifications.length

    return {
      totalSent,
      totalFailed,
      totalPending,
      totalNotifications
    }
  }

  const stats = getNotificationStats()

  return (
    <DashboardLayout userRole="center_admin" title="Notifications">
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Notification System</h1>
            <p className="text-muted-foreground">Send SMS, push notifications, and manage communication</p>
          </div>
          
          <div className="flex gap-2">
            <Button onClick={() => setShowSendForm(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Send Notification
            </Button>
            <Button variant="outline" onClick={sendAbsenceNotifications}>
              <Bell className="mr-2 h-4 w-4" />
              Send Absence Alerts
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Bell className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Total Sent</div>
                  <div className="text-lg font-semibold">{stats.totalNotifications}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Delivered</div>
                  <div className="text-lg font-semibold">{stats.totalSent}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-yellow-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Pending</div>
                  <div className="text-lg font-semibold">{stats.totalPending}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-red-500" />
                <div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                  <div className="text-lg font-semibold">{stats.totalFailed}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
          <button
            onClick={() => setActiveTab('send')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'send'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Send Notification
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'history'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            History
          </button>
          <button
            onClick={() => setActiveTab('templates')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'templates'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Templates
          </button>
        </div>

        {/* Send Notification Form */}
        {(showSendForm || activeTab === 'send') && (
          <Card>
            <CardHeader>
              <CardTitle>Send Notification</CardTitle>
              <CardDescription>Send SMS, push notifications, or WhatsApp messages</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSendNotification} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">Notification Type *</Label>
                    <select
                      id="type"
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-input bg-background rounded-md"
                    >
                      <option value="sms">SMS</option>
                      <option value="push">Push Notification</option>
                      <option value="whatsapp">WhatsApp</option>
                      <option value="email">Email</option>
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="template_id">Use Template (Optional)</Label>
                    <select
                      id="template_id"
                      name="template_id"
                      value={formData.template_id}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md"
                    >
                      <option value="">Select template</option>
                      {templates.map(template => (
                        <option key={template.id} value={template.id}>
                          {template.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="title">Title *</Label>
                    <Input
                      id="title"
                      name="title"
                      placeholder="Notification title"
                      value={formData.title}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="recipients">Recipients *</Label>
                    <Input
                      id="recipients"
                      name="recipients"
                      placeholder="phone1,phone2,email1,email2"
                      value={formData.recipients}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="message">Message *</Label>
                    <textarea
                      id="message"
                      name="message"
                      placeholder="Your notification message..."
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={4}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="schedule_date">Schedule (Optional)</Label>
                    <Input
                      id="schedule_date"
                      name="schedule_date"
                      type="datetime-local"
                      value={formData.schedule_date}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Sending...' : 'Send Notification'}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setShowSendForm(false)
                      setFormData({ type: "sms", title: "", message: "", recipients: "", schedule_date: "", template_id: "" })
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Notification History */}
        {activeTab === 'history' && (
          <>
            <Card>
              <CardContent className="p-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardContent>
            </Card>

            { loading ? (
          <TableSkeleton rows={6} />
        ) : filteredNotifications.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <Bell className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No notifications found</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm ? 'No notifications match your search.' : 'No notifications have been sent yet.'}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4">
                {filteredNotifications.map((notification) => (
                  <Card key={notification.id} data-testid="notification-record">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-4 mb-4">
                            <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                              {getNotificationIcon(notification.type)}
                            </div>
                            <div>
                              <h3 className="font-semibold text-lg">{notification.title}</h3>
                              <p className="text-sm text-muted-foreground">{notification.type.toUpperCase()}</p>
                            </div>
                          </div>
                          
                          <div className="mb-4">
                            <p className="text-sm">{notification.message}</p>
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center">
                              <Users className="h-4 w-4 mr-1" />
                              {notification.recipients.length} recipients
                            </div>
                            {notification.sent_at && (
                              <div className="flex items-center">
                                <Calendar className="h-4 w-4 mr-1" />
                                {new Date(notification.sent_at).toLocaleString()}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(notification.status)}`}>
                          {notification.status}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  )
}
