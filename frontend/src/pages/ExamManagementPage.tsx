import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Plus, Calendar, Clock, FileText, Users, Award, TrendingUp } from "lucide-react"
import DashboardLayout from "@/components/DashboardLayout"
import toast from 'react-hot-toast'
import { TableSkeleton } from "@/components/skeletons/DashboardSkeleton"

interface Exam {
  id: string
  name: string
  description?: string
  examDate: string
  duration: number
  totalMarks: number
  passingMarks: number
  examType: string
  status: string
  resultCount: number
  createdAt: string
}

const EXAM_TYPES = [
  { value: 'WRITTEN', label: 'Written' },
  { value: 'ORAL', label: 'Oral' },
  { value: 'PRACTICAL', label: 'Practical' },
  { value: 'ONLINE', label: 'Online' }
]

const EXAM_STATUS_COLORS = {
  'SCHEDULED': 'bg-blue-100 text-blue-800',
  'ONGOING': 'bg-yellow-100 text-yellow-800',
  'COMPLETED': 'bg-green-100 text-green-800',
  'CANCELLED': 'bg-red-100 text-red-800'
}

export default function ExamManagementPage() {
  const [exams, setExams] = useState<Exam[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    examDate: "",
    duration: "",
    totalMarks: "",
    passingMarks: "",
    examType: "WRITTEN"
  })

  useEffect(() => {
    fetchExams()
  }, [])

  const getAuthHeaders = () => {
    const token = localStorage.getItem('auth_token')
    const subdomain = window.location.hostname.split('.')[0]
    return {
      'Authorization': `Bearer ${token}`,
      'x-tenant-slug': subdomain === 'localhost' ? 'abc' : subdomain,
      'Content-Type': 'application/json'
    }
  }

  const fetchExams = async () => {
    try {
      const response = await fetch('http://localhost:3004/api/coaching/exams', {
        headers: getAuthHeaders()
      })
      const data = await response.json()
      if (response.ok) {
        setExams(data.data.exams || [])
      } else {
        toast.error(data.message || 'Failed to fetch exams')
      }
    } catch (error) {
      console.error('Error fetching exams:', error)
      toast.error('Failed to fetch exams')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('http://localhost:3004/api/coaching/exams', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          examDate: formData.examDate,
          duration: parseInt(formData.duration),
          totalMarks: parseFloat(formData.totalMarks),
          passingMarks: parseFloat(formData.passingMarks),
          examType: formData.examType
        })
      })

      const data = await response.json()

      if (response.ok) {
        await fetchExams()
        setShowAddForm(false)
        setFormData({
          name: "",
          description: "",
          examDate: "",
          duration: "",
          totalMarks: "",
          passingMarks: "",
          examType: "WRITTEN"
        })
        toast.success('Exam created successfully')
      } else {
        toast.error(data.message || 'Failed to create exam')
      }
    } catch (error) {
      console.error('Error creating exam:', error)
      toast.error('Failed to create exam')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  if (loading && exams.length === 0) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <TableSkeleton />
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Exam Management</h1>
            <p className="text-muted-foreground">
              Create and manage exams and assessments
            </p>
          </div>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Exam
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Exams</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{exams.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {exams.filter(e => e.status === 'SCHEDULED').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {exams.filter(e => e.status === 'COMPLETED').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Results Entered</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {exams.reduce((sum, exam) => sum + exam.resultCount, 0)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Add Exam Form */}
        {showAddForm && (
          <Card>
            <CardHeader>
              <CardTitle>Create New Exam</CardTitle>
              <CardDescription>
                Set up a new exam with details and grading criteria
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Exam Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="e.g., Mathematics Midterm"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="examType">Exam Type</Label>
                    <Select value={formData.examType} onValueChange={(value) => setFormData({...formData, examType: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select exam type" />
                      </SelectTrigger>
                      <SelectContent>
                        {EXAM_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="examDate">Exam Date</Label>
                    <Input
                      id="examDate"
                      type="datetime-local"
                      value={formData.examDate}
                      onChange={(e) => setFormData({...formData, examDate: e.target.value})}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="duration">Duration (minutes)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={formData.duration}
                      onChange={(e) => setFormData({...formData, duration: e.target.value})}
                      placeholder="e.g., 180"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="totalMarks">Total Marks</Label>
                    <Input
                      id="totalMarks"
                      type="number"
                      step="0.01"
                      value={formData.totalMarks}
                      onChange={(e) => setFormData({...formData, totalMarks: e.target.value})}
                      placeholder="e.g., 100"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="passingMarks">Passing Marks</Label>
                    <Input
                      id="passingMarks"
                      type="number"
                      step="0.01"
                      value={formData.passingMarks}
                      onChange={(e) => setFormData({...formData, passingMarks: e.target.value})}
                      placeholder="e.g., 40"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="Exam description and instructions..."
                    rows={3}
                  />
                </div>

                <div className="flex gap-2">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Creating...' : 'Create Exam'}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Exams List */}
        <div className="grid gap-4">
          {exams.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No exams found</h3>
                <p className="text-muted-foreground mb-4">Get started by creating your first exam</p>
                <Button onClick={() => setShowAddForm(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Exam
                </Button>
              </CardContent>
            </Card>
          ) : (
            exams.map((exam) => (
              <Card key={exam.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {exam.name}
                        <Badge className={EXAM_STATUS_COLORS[exam.status as keyof typeof EXAM_STATUS_COLORS]}>
                          {exam.status}
                        </Badge>
                      </CardTitle>
                      <CardDescription>{exam.description}</CardDescription>
                    </div>
                    <div className="text-right text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {formatDate(exam.examDate)}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{formatDuration(exam.duration)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{exam.totalMarks} marks</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Pass: {exam.passingMarks}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{exam.resultCount} results</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2 mt-4">
                    <Button size="sm" variant="outline">
                      View Details
                    </Button>
                    <Button size="sm" variant="outline">
                      Add Results
                    </Button>
                    <Button size="sm" variant="outline">
                      View Results
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
