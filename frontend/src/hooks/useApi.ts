import { useAuth } from '../contexts/AuthContext'

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  body?: any
  headers?: Record<string, string>
  includeTenant?: boolean
}

export const useApi = () => {
  const { getTenantSlug } = useAuth()

  // Helper function for authenticated admin requests
  const makeAuthenticatedRequest = async (endpoint: string, options: ApiOptions = {}) => {
    const token = localStorage.getItem('auth_token')

    if (!token) {
      throw new Error('No authentication token found. Please login again.')
    }

    const {
      method = 'GET',
      body,
      headers = {},
      includeTenant = true
    } = options

    const tenantSlug = getTenantSlug()

    // Build URL with tenant context if needed
    let url = `http://localhost:3004${endpoint}`

    if (includeTenant && tenantSlug) {
      const separator = endpoint.includes('?') ? '&' : '?'
      url += `${separator}tenant_slug=${tenantSlug}`
    }

    // Branch ID will be handled by individual components when needed

    // Prepare request options with guaranteed Authorization header
    const requestOptions: RequestInit = {
      method,
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...headers
      }
    }

    // Add body for non-GET requests
    if (body && method !== 'GET') {
      requestOptions.body = JSON.stringify({
        ...body,
        ...(includeTenant && tenantSlug ? { tenant_slug: tenantSlug } : {})
      })
    }

    console.log('🔍 Making authenticated request:', {
      url,
      method,
      hasToken: !!token,
      headers: requestOptions.headers
    })

    const response = await fetch(url, requestOptions)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Request failed' }))
      console.error('🔍 Request failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      })
      throw new Error(errorData.message || errorData.error || `Request failed with status ${response.status}`)
    }

    return response.json()
  }

  const apiCall = async (endpoint: string, options: ApiOptions = {}) => {
    const {
      method = 'GET',
      body,
      headers = {},
      includeTenant = true
    } = options

    const tenantSlug = getTenantSlug()
    const token = localStorage.getItem('auth_token')

    // Build URL with tenant context if needed
    let url = `http://localhost:3004${endpoint}`

    if (includeTenant && tenantSlug) {
      const separator = endpoint.includes('?') ? '&' : '?'
      url += `${separator}tenant_slug=${tenantSlug}`
    }

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
        ...headers
      }
    }

    // Add body for non-GET requests
    if (body && method !== 'GET') {
      requestOptions.body = JSON.stringify({
        ...body,
        ...(includeTenant && tenantSlug ? { tenant_slug: tenantSlug } : {})
      })
    }

    try {
      const response = await fetch(url, requestOptions)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`)
      }

      return { data, response }
    } catch (error) {
      console.error('API call failed:', error)
      throw error
    }
  }

  // Convenience methods
  const get = (endpoint: string, includeTenant = true) => 
    apiCall(endpoint, { method: 'GET', includeTenant })

  const post = (endpoint: string, body: any, includeTenant = true) => 
    apiCall(endpoint, { method: 'POST', body, includeTenant })

  const put = (endpoint: string, body: any, includeTenant = true) => 
    apiCall(endpoint, { method: 'PUT', body, includeTenant })

  const del = (endpoint: string, includeTenant = true) => 
    apiCall(endpoint, { method: 'DELETE', includeTenant })

  const patch = (endpoint: string, body: any, includeTenant = true) =>
    apiCall(endpoint, { method: 'PATCH', body, includeTenant })

  // Authenticated admin methods
  const adminGet = (endpoint: string, includeTenant = false) =>
    makeAuthenticatedRequest(endpoint, { method: 'GET', includeTenant })

  const adminPost = (endpoint: string, body: any, includeTenant = false) =>
    makeAuthenticatedRequest(endpoint, { method: 'POST', body, includeTenant })

  const adminPut = (endpoint: string, body: any, includeTenant = false) =>
    makeAuthenticatedRequest(endpoint, { method: 'PUT', body, includeTenant })

  const adminDel = (endpoint: string, includeTenant = false) =>
    makeAuthenticatedRequest(endpoint, { method: 'DELETE', includeTenant })

  return {
    apiCall,
    get,
    post,
    put,
    delete: del,
    patch,
    adminGet,
    adminPost,
    adminPut,
    adminDel
  }
}

export default useApi
