// Admin API utility functions
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3004'

// Get auth token from localStorage
const getAuthToken = (): string | null => {
  return localStorage.getItem('auth_token')
}

// Get tenant from subdomain
const getTenant = (): string => {
  const hostname = window.location.hostname
  if (hostname.includes('localhost')) {
    const subdomain = hostname.split('.')[0]
    return subdomain === 'localhost' ? '' : subdomain
  }
  return hostname.split('.')[0]
}

// Generic API request function
const apiRequest = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<any> => {
  const token = getAuthToken()
  const tenant = getTenant()
  
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...options.headers,
  }
  
  if (token) {
    headers.Authorization = `Bearer ${token}`
  }
  
  if (tenant) {
    headers['X-Tenant'] = tenant
  }
  
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`

  // Debug logging
  console.log('🔍 Making authenticated request:', {
    url,
    method: options.method || 'GET',
    hasToken: !!token,
    tokenPreview: token ? `${token.substring(0, 10)}...` : 'null',
    tenant,
    headers: Object.keys(headers)
  })

  const response = await fetch(url, {
    ...options,
    headers,
  })
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Network error' }))
    throw new Error(errorData.error || `HTTP ${response.status}`)
  }
  
  return response.json()
}

// GET request
export const adminGet = async (endpoint: string): Promise<any> => {
  return apiRequest(endpoint, { method: 'GET' })
}

// POST request
export const adminPost = async (endpoint: string, data?: any): Promise<any> => {
  return apiRequest(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  })
}

// PUT request
export const adminPut = async (endpoint: string, data?: any): Promise<any> => {
  return apiRequest(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  })
}

// DELETE request
export const adminDelete = async (endpoint: string): Promise<any> => {
  return apiRequest(endpoint, { method: 'DELETE' })
}

// PATCH request
export const adminPatch = async (endpoint: string, data?: any): Promise<any> => {
  return apiRequest(endpoint, {
    method: 'PATCH',
    body: data ? JSON.stringify(data) : undefined,
  })
}

export default {
  get: adminGet,
  post: adminPost,
  put: adminPut,
  delete: adminDelete,
  patch: adminPatch,
}
