import React, { useState } from 'react'
import { useBranch, useMultiBranchAccess } from '../contexts/BranchContext'
import { useLanguage } from '../contexts/LanguageContext'

export default function BranchSelector() {
  const { currentBranch, availableBranches, switchBranch, loading } = useBranch()
  const { t } = useLanguage()
  const hasMultipleBranches = useMultiBranchAccess()
  const [isOpen, setIsOpen] = useState(false)

  // Show current branch with switch option even for single branch
  if (loading) {
    return (
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {t('branches.currentBranch')}:
        </span>
        <span className="text-sm font-medium text-gray-900 dark:text-white">
          Loading...
        </span>
      </div>
    )
  }

  // Always show switch option, even for single branch (for future expansion)
  if (!hasMultipleBranches) {
    return (
      <div className="relative">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {t('branches.currentBranch')}:
          </span>
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {currentBranch?.branch_name || 'No Branch'}
          </span>
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
          >
            {t('branches.switchBranch')}
          </button>
        </div>

        {isOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-10"
              onClick={() => setIsOpen(false)}
            />

            {/* Dropdown */}
            <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-20">
              <div className="p-2">
                <div className="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700">
                  {t('branches.selectBranch')}
                </div>

                <div className="mt-2 space-y-1">
                  {availableBranches.length > 0 ? availableBranches.map((branch) => (
                    <button
                      key={branch.id}
                      onClick={() => {
                        switchBranch(branch.id)
                        setIsOpen(false)
                      }}
                      className={`w-full text-left px-3 py-2 text-sm rounded-md transition-colors ${
                        currentBranch?.id === branch.id
                          ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{branch.branch_name}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {branch.branch_code}
                          </div>
                        </div>
                        {currentBranch?.id === branch.id && (
                          <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>

                      {branch.address && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                          {branch.address}
                        </div>
                      )}
                    </button>
                  )) : (
                    <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                      No branches available
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    )
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {t('branches.currentBranch')}:
        </span>
        <span className="font-semibold">
          {currentBranch?.branch_name || 'Select Branch'}
        </span>
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-20">
            <div className="p-2">
              <div className="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700">
                {t('branches.selectBranch')}
              </div>

              <div className="mt-2 space-y-1">
                {availableBranches.length > 0 ? availableBranches.map((branch) => (
                  <button
                    key={branch.id}
                    onClick={() => {
                      switchBranch(branch.id)
                      setIsOpen(false)
                    }}
                    className={`w-full text-left px-3 py-2 text-sm rounded-md transition-colors ${
                      currentBranch?.id === branch.id
                        ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{branch.branch_name}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {branch.branch_code}
                        </div>
                      </div>
                      {currentBranch?.id === branch.id && (
                        <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                    
                    {branch.address && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                        {branch.address}
                      </div>
                    )}
                  </button>
                )) : (
                  <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                    No branches available
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

// Branch indicator component for mobile
export function BranchIndicator() {
  const { currentBranch } = useBranch()
  const { t } = useLanguage()

  if (!currentBranch) return null

  return (
    <div className="flex items-center space-x-2 px-3 py-1 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
      <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
        {currentBranch.branch_name}
      </span>
    </div>
  )
}

// Compact branch selector for mobile
export function CompactBranchSelector() {
  const { currentBranch, availableBranches, switchBranch } = useBranch()
  const { t } = useLanguage()
  const hasMultipleBranches = useMultiBranchAccess()

  if (!hasMultipleBranches) {
    return <BranchIndicator />
  }

  return (
    <select
      value={currentBranch?.id || ''}
      onChange={(e) => switchBranch(parseInt(e.target.value))}
      className="text-xs bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md px-2 py-1 text-blue-700 dark:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
    >
      {availableBranches.map((branch) => (
        <option key={branch.id} value={branch.id}>
          {branch.branch_name}
        </option>
      ))}
    </select>
  )
}
