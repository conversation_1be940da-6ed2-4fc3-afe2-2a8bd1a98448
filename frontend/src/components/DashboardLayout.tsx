import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useTheme } from "@/components/theme-provider"
import { Moon, Sun, Globe, LogOut, Menu, X } from "lucide-react"
import Sidebar from "./Sidebar"
import { cn } from "@/lib/utils"
import { useAuth } from "../contexts/AuthContext"

interface DashboardLayoutProps {
  children: React.ReactNode
  userRole?: 'super_admin' | 'center_admin' | 'teacher' | 'student'
  title?: string
}

export default function DashboardLayout({
  children,
  userRole = 'center_admin',
  title = "Dashboard"
}: DashboardLayoutProps) {
  const { theme, setTheme } = useTheme()
  const { user, logout, getTenantSlug } = useAuth()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const tenantSlug = getTenantSlug()

  const handleLogout = async () => {
    await logout()
    // Redirect based on user role and tenant context
    if (tenantSlug) {
      window.location.href = '/login'
    } else {
      window.location.href = '/login'
    }
  }

  return (
    <div className="min-h-screen bg-background flex">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
        sidebarOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <Sidebar userRole={userRole} />
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Top header */}
        <header className="sticky top-0 z-30 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex h-16 items-center justify-between px-4 lg:px-6">
            <div className="flex items-center space-x-4">
              {/* Mobile menu button */}
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                {sidebarOpen ? (
                  <X className="h-5 w-5" />
                ) : (
                  <Menu className="h-5 w-5" />
                )}
                <span className="sr-only">Toggle sidebar</span>
              </Button>

              {/* Page title */}
              <div className="flex items-center space-x-2">
                <h1 className="text-lg font-semibold">{title}</h1>
              </div>
            </div>

            {/* Right side actions */}
            <div className="flex items-center space-x-4">
              {/* Theme toggle */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setTheme(theme === "light" ? "dark" : "light")}
              >
                <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                <span className="sr-only">Toggle theme</span>
              </Button>
              
              {/* Language toggle */}
              <Button variant="ghost" size="icon">
                <Globe className="h-[1.2rem] w-[1.2rem]" />
                <span className="sr-only">Switch language</span>
              </Button>

              {/* User menu */}
              <div className="flex items-center space-x-3">
                <div className="hidden md:block text-right">
                  <div className="text-sm font-medium">
                    {user?.name || (userRole === 'super_admin' ? 'Super Admin' : 'Center Admin')}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {user?.email || (userRole === 'super_admin' ? 'Platform Administrator' : 'ABC Coaching Center')}
                  </div>
                </div>
                
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user?.name?.charAt(0).toUpperCase() || (userRole === 'super_admin' ? 'SA' : 'CA')}
                  </span>
                </div>
                
                <Button variant="ghost" size="icon" onClick={handleLogout}>
                  <LogOut className="h-[1.2rem] w-[1.2rem]" />
                  <span className="sr-only">Sign out</span>
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto">
          <div className="p-4 lg:p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
