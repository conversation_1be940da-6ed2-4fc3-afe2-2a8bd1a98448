import React from 'react'
import { useAuth } from '../contexts/AuthContext'
import HomePage from '../pages/HomePage'
import CenterHomePage from '../pages/CenterHomePage'

export default function SmartHomePage() {
  const { getTenantSlug } = useAuth()
  const tenantSlug = getTenantSlug()

  // If there's a tenant slug (subdomain), show center homepage
  // Otherwise show the main SaaS homepage
  return tenantSlug ? <CenterHomePage /> : <HomePage />
}
