import { useState } from "react"
import { Link, useLocation } from "react-router-dom"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  ChevronDown, 
  ChevronRight,
  Home,
  Users,
  GraduationCap,
  BookOpen,
  Building2,
  Calendar,
  ClipboardCheck,
  DollarSign,
  FileText,
  Settings,
  BarChart3,
  UserCheck,
  Package,
  Bell,
  Shield,
  Award,
  Upload,
  CreditCard,
  Briefcase
} from "lucide-react"
import { cn } from "@/lib/utils"

interface MenuItem {
  title: string
  icon: React.ReactNode
  href?: string
  children?: MenuItem[]
}

interface SidebarProps {
  userRole: 'super_admin' | 'center_admin' | 'teacher' | 'student'
}

const superAdminMenuItems: MenuItem[] = [
  {
    title: "Dashboard",
    icon: <Home className="h-4 w-4" />,
    href: "/dashboard"
  },
  {
    title: "Coaching Centers",
    icon: <Building2 className="h-4 w-4" />,
    children: [
      { title: "All Centers", icon: <Building2 className="h-4 w-4" />, href: "/admin/centers" },
      { title: "Subscriptions", icon: <CreditCard className="h-4 w-4" />, href: "/admin/subscriptions" },
      { title: "Plans", icon: <Package className="h-4 w-4" />, href: "/admin/plans" }
    ]
  },
  {
    title: "User Management",
    icon: <Users className="h-4 w-4" />,
    children: [
      { title: "All Users", icon: <Users className="h-4 w-4" />, href: "/admin/users" },
      { title: "Roles & Permissions", icon: <Shield className="h-4 w-4" />, href: "/admin/roles" }
    ]
  },
  {
    title: "Analytics & Reports",
    icon: <BarChart3 className="h-4 w-4" />,
    children: [
      { title: "Platform Analytics", icon: <BarChart3 className="h-4 w-4" />, href: "/admin/analytics" },
      { title: "Revenue Reports", icon: <DollarSign className="h-4 w-4" />, href: "/admin/revenue" },
      { title: "Usage Reports", icon: <FileText className="h-4 w-4" />, href: "/admin/usage" }
    ]
  },
  {
    title: "System",
    icon: <Settings className="h-4 w-4" />,
    children: [
      { title: "Settings", icon: <Settings className="h-4 w-4" />, href: "/admin/settings" },
      { title: "Notifications", icon: <Bell className="h-4 w-4" />, href: "/admin/notifications" },
      { title: "Backup & Recovery", icon: <Upload className="h-4 w-4" />, href: "/admin/backup" }
    ]
  }
]

const centerMenuItems: MenuItem[] = [
  {
    title: "Dashboard",
    icon: <Home className="h-4 w-4" />,
    href: "/center/dashboard"
  },
  {
    title: "Student Management",
    icon: <GraduationCap className="h-4 w-4" />,
    children: [
      { title: "All Students", icon: <GraduationCap className="h-4 w-4" />, href: "/students" },
      { title: "Admissions", icon: <UserCheck className="h-4 w-4" />, href: "/admissions" },
      { title: "Student Reports", icon: <FileText className="h-4 w-4" />, href: "/student-reports" }
    ]
  },
  {
    title: "Academic Management",
    icon: <BookOpen className="h-4 w-4" />,
    children: [
      { title: "Courses", icon: <BookOpen className="h-4 w-4" />, href: "/courses" },
      { title: "Batches", icon: <Users className="h-4 w-4" />, href: "/batches" },
      { title: "Schedule Management", icon: <Calendar className="h-4 w-4" />, href: "/schedule-management" },
      { title: "Attendance", icon: <ClipboardCheck className="h-4 w-4" />, href: "/attendance" },
      { title: "Exam Management", icon: <FileText className="h-4 w-4" />, href: "/exam-management" },
      { title: "Results", icon: <Award className="h-4 w-4" />, href: "/results" }
    ]
  },
  {
    title: "Staff Management",
    icon: <Users className="h-4 w-4" />,
    children: [
      { title: "Teachers", icon: <Users className="h-4 w-4" />, href: "/coaching/users" },
      { title: "HR & Payroll", icon: <Briefcase className="h-4 w-4" />, href: "/hr" },
      { title: "Staff Reports", icon: <FileText className="h-4 w-4" />, href: "/staff-reports" }
    ]
  },
  {
    title: "Operations",
    icon: <Building2 className="h-4 w-4" />,
    children: [
      { title: "Branches", icon: <Building2 className="h-4 w-4" />, href: "/branches" },
      { title: "Asset & Expense Management", icon: <Package className="h-4 w-4" />, href: "/asset-expense-management" },
      { title: "Requisitions", icon: <ClipboardCheck className="h-4 w-4" />, href: "/requisitions" }
    ]
  },
  {
    title: "Financial",
    icon: <DollarSign className="h-4 w-4" />,
    children: [
      { title: "Accounting", icon: <DollarSign className="h-4 w-4" />, href: "/accounting" },
      { title: "Invoicing", icon: <FileText className="h-4 w-4" />, href: "/invoicing" },
      { title: "Fee Collection", icon: <CreditCard className="h-4 w-4" />, href: "/fees" }
    ]
  },
  {
    title: "Documents & Reports",
    icon: <FileText className="h-4 w-4" />,
    children: [
      { title: "Document Manager", icon: <Upload className="h-4 w-4" />, href: "/documents" },
      { title: "ID Cards", icon: <Award className="h-4 w-4" />, href: "/coaching/id-cards" },
      { title: "Certificates", icon: <Award className="h-4 w-4" />, href: "/coaching/certificates" }
    ]
  },
  {
    title: "Settings",
    icon: <Settings className="h-4 w-4" />,
    children: [
      { title: "Center Settings", icon: <Settings className="h-4 w-4" />, href: "/settings" },
      { title: "Notifications", icon: <Bell className="h-4 w-4" />, href: "/notifications" }
    ]
  }
]

function MenuItem({ item, level = 0 }: { item: MenuItem; level?: number }) {
  const [isOpen, setIsOpen] = useState(false)
  const location = useLocation()
  const isActive = item.href === location.pathname
  const hasChildren = item.children && item.children.length > 0

  const toggleOpen = () => {
    if (hasChildren) {
      setIsOpen(!isOpen)
    }
  }

  return (
    <div>
      {item.href ? (
        <Link to={item.href}>
          <Button
            variant={isActive ? "secondary" : "ghost"}
            className={cn(
              "w-full justify-start h-9 px-3",
              level > 0 && "ml-4 w-[calc(100%-1rem)]"
            )}
          >
            {item.icon}
            <span className="ml-2 text-sm">{item.title}</span>
          </Button>
        </Link>
      ) : (
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-between h-9 px-3",
            level > 0 && "ml-4 w-[calc(100%-1rem)]"
          )}
          onClick={toggleOpen}
        >
          <div className="flex items-center">
            {item.icon}
            <span className="ml-2 text-sm">{item.title}</span>
          </div>
          {hasChildren && (
            isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      )}
      
      {hasChildren && isOpen && (
        <div className="mt-1 space-y-1">
          {item.children?.map((child, index) => (
            <MenuItem key={index} item={child} level={level + 1} />
          ))}
        </div>
      )}
    </div>
  )
}

export default function Sidebar({ userRole }: SidebarProps) {
  const menuItems = userRole === 'super_admin' ? superAdminMenuItems : centerMenuItems

  return (
    <div className="w-64 border-r bg-background h-screen flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
            <span className="text-white font-bold text-sm">TC</span>
          </div>
          <span className="text-xl font-bold">TeachingCenter</span>
        </div>
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <div className="space-y-1">
          {menuItems.map((item, index) => (
            <MenuItem key={index} item={item} />
          ))}
        </div>
      </ScrollArea>

      {/* User Info */}
      <div className="p-4 border-t">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
            <span className="text-white text-sm font-medium">
              {userRole === 'super_admin' ? 'SA' : 'CA'}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium truncate">
              {userRole === 'super_admin' ? 'Super Admin' : 'Center Admin'}
            </div>
            <div className="text-xs text-muted-foreground truncate">
              {userRole === 'super_admin' ? 'Platform Admin' : 'ABC Coaching Center'}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
