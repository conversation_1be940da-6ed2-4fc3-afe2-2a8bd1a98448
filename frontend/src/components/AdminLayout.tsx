import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useLanguage } from '../contexts/LanguageContext'
import { Link, useLocation } from 'react-router-dom'
import TopBar from './TopBar'

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { user } = useAuth()
  const { t } = useLanguage()
  const location = useLocation()

  // Load sidebar state from localStorage, default to true (open)
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    const saved = localStorage.getItem('sidebarOpen')
    return saved !== null ? JSON.parse(saved) : true
  })

  // Navigation based on user role
  const getNavigation = () => {
    if (user?.role === 'super_admin') {
      return [
        {
          name: 'Dashboard',
          href: '/dashboard',
          icon: '🏠',
          current: location.pathname === '/dashboard'
        },
        {
          name: 'Coaching Centers',
          href: '/admin/centers',
          icon: '🏢',
          current: location.pathname === '/admin/centers'
        },
        {
          name: 'Subscriptions',
          href: '/admin/subscriptions',
          icon: '💳',
          current: location.pathname === '/admin/subscriptions'
        },
        {
          name: 'Plans',
          href: '/admin/plans',
          icon: '📋',
          current: location.pathname === '/admin/plans'
        },
        {
          name: 'Analytics',
          href: '/admin/analytics',
          icon: '📊',
          current: location.pathname === '/admin/analytics'
        },
        {
          name: 'Settings',
          href: '/admin/settings',
          icon: '⚙️',
          current: location.pathname === '/admin/settings'
        }
      ]
    } else {
      // Center admin, teacher navigation
      return [
        {
          name: t('navigation.dashboard'),
          href: '/coaching/dashboard',
          icon: '🏠',
          current: location.pathname === '/coaching/dashboard'
        },
        {
          name: t('navigation.students'),
          href: '/coaching/students',
          icon: '👥',
          current: location.pathname === '/coaching/students'
        },
        {
          name: t('navigation.teachers'),
          href: '/coaching/teachers',
          icon: '👨‍🏫',
          current: location.pathname === '/coaching/teachers'
        },
        {
          name: t('navigation.courses'),
          href: '/coaching/courses',
          icon: '📚',
          current: location.pathname === '/coaching/courses'
        },
        {
          name: t('navigation.attendance'),
          href: '/coaching/attendance',
          icon: '📋',
          current: location.pathname === '/coaching/attendance'
        },
        {
          name: t('navigation.fees'),
          href: '/coaching/fees',
          icon: '💰',
          current: location.pathname === '/coaching/fees'
        },
        {
          name: t('navigation.expenses'),
          href: '/coaching/expenses',
          icon: '💸',
          current: location.pathname === '/coaching/expenses'
        },
        {
          name: t('navigation.transactions'),
          href: '/coaching/transactions',
          icon: '🧾',
          current: location.pathname === '/coaching/transactions'
        },
        {
          name: t('navigation.assignments'),
          href: '/coaching/assignments',
          icon: '📝',
          current: location.pathname === '/coaching/assignments'
        },
        {
          name: t('navigation.reports'),
          href: '/coaching/reports',
          icon: '📊',
          current: location.pathname === '/coaching/reports'
        },
        {
          name: t('navigation.hrm'),
          href: '/coaching/hrm',
          icon: '👥',
          current: location.pathname === '/coaching/hrm'
        },
        {
          name: t('navigation.userManagement'),
          href: '/coaching/users',
          icon: '👤',
          current: location.pathname === '/coaching/users'
        },
        {
          name: t('navigation.inventory'),
          href: '/coaching/inventory',
          icon: '📦',
          current: location.pathname === '/coaching/inventory'
        },
        {
          name: t('navigation.pos'),
          href: '/coaching/pos',
          icon: '🛒',
          current: location.pathname === '/coaching/pos'
        },
        {
          name: t('branches.title'),
          href: '/coaching/branches',
          icon: '🏢',
          current: location.pathname === '/coaching/branches'
        },
        {
          name: t('exams.title'),
          href: '/coaching/exams',
          icon: '📝',
          current: location.pathname === '/coaching/exams'
        },
        {
          name: 'ফলাফল ব্যবস্থাপনা',
          href: '/coaching/results',
          icon: '📊',
          current: location.pathname === '/coaching/results'
        },
        {
          name: 'Room Management',
          href: '/coaching/room-management',
          icon: '🏢',
          current: location.pathname === '/coaching/room-management'
        },
        {
          name: t('settings.title'),
          href: '/coaching/settings',
          icon: '⚙️',
          current: location.pathname === '/coaching/settings'
        }
      ]
    }
  }

  const navigation = getNavigation()

  const toggleSidebar = () => {
    const newState = !sidebarOpen
    setSidebarOpen(newState)
    localStorage.setItem('sidebarOpen', JSON.stringify(newState))
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Top Bar */}
      <TopBar onToggleSidebar={toggleSidebar} sidebarOpen={sidebarOpen} />

      <div className="flex flex-1 overflow-hidden">
        {/* Mobile sidebar overlay */}
        {sidebarOpen && (
          <div className="fixed inset-0 z-40 lg:hidden">
            <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
            <div className="relative flex w-full max-w-xs flex-1 flex-col bg-white dark:bg-gray-800">
            <div className="absolute top-0 right-0 -mr-12 pt-2">
              <button
                type="button"
                className="ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                onClick={() => setSidebarOpen(false)}
              >
                <span className="sr-only">Close sidebar</span>
                <span className="text-white">✕</span>
              </button>
            </div>
            <div className="h-0 flex-1 overflow-y-auto pt-5 pb-4">
              <div className="flex flex-shrink-0 items-center px-4">
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">{t('navigation.adminPanel')}</h1>
              </div>
              <nav className="mt-5 space-y-1 px-2">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`group flex items-center px-2 py-2 text-base font-medium rounded-md ${
                      item.current
                        ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white'
                    }`}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <span className="mr-3 text-lg">{item.icon}</span>
                    {item.name}
                  </Link>
                ))}
              </nav>
            </div>
          </div>
        </div>
      )}

        {/* Desktop sidebar */}
        <div className={`hidden lg:flex lg:flex-col lg:fixed lg:inset-y-0 lg:top-16 transition-all duration-300 ${
          sidebarOpen ? 'lg:w-64' : 'lg:w-16'
        }`}>
          <div className="flex min-h-0 flex-1 flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
            <div className="flex flex-1 flex-col overflow-y-auto pt-5 pb-4">
              {sidebarOpen && (
                <div className="flex flex-shrink-0 items-center px-4 mb-4">
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">{t('navigation.adminPanel')}</h1>
                </div>
              )}
            <nav className="mt-5 flex-1 space-y-1 px-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                    item.current
                      ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white'
                  } ${!sidebarOpen ? 'justify-center' : ''}`}
                  title={!sidebarOpen ? item.name : undefined}
                >
                  <span className={`text-lg ${sidebarOpen ? 'mr-3' : ''}`}>{item.icon}</span>
                  {sidebarOpen && <span className="truncate">{item.name}</span>}
                </Link>
              ))}
            </nav>
          </div>
          
          {/* User info moved to TopBar */}
        </div>
      </div>

        {/* Main content */}
        <div className={`flex flex-col flex-1 transition-all duration-300 ${
          sidebarOpen ? 'lg:pl-64' : 'lg:pl-16'
        }`}>
          <main className="flex-1 overflow-y-auto">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}
