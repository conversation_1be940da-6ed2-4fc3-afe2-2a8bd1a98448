import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { Skeleton } from './ui/skeleton'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'super_admin' | 'center_admin' | 'teacher' | 'student'
  requireTenant?: boolean
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRole,
  requireTenant = false 
}) => {
  const { user, loading, getTenantSlug } = useAuth()
  const location = useLocation()
  const tenantSlug = getTenantSlug()

  // Show loading skeleton while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="ml-4 flex-1">
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6">
                <Skeleton className="h-6 w-32 mb-4" />
                <div className="space-y-4">
                  {[...Array(3)].map((_, j) => (
                    <div key={j} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <Skeleton className="h-4 w-32 mb-1" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                      <Skeleton className="h-4 w-16" />
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // Check role requirements
  if (requiredRole && user.role !== requiredRole) {
    // Allow center_admin to access coaching routes even if requiredRole is different
    if (location.pathname.startsWith('/coaching/') && user.role === 'center_admin') {
      // Allow access to coaching routes for center_admin
    } else {
      // Redirect based on user role
      if (user.role === 'super_admin') {
        return <Navigate to="/dashboard" replace />
      } else if (user.role === 'center_admin') {
        // Redirect to coaching dashboard
        return <Navigate to="/coaching/dashboard" replace />
      } else {
        return <Navigate to="/login" replace />
      }
    }
  }

  // Check tenant requirements for center admin routes
  if (requireTenant && user.role === 'center_admin' && !tenantSlug) {
    return <Navigate to="/login" replace />
  }

  // Super admin should not access tenant-specific routes
  if (requireTenant && user.role === 'super_admin') {
    return <Navigate to="/dashboard" replace />
  }

  return <>{children}</>
}

export default ProtectedRoute
