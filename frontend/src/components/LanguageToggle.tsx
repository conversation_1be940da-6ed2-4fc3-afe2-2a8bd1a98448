import { Globe } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useLanguage } from "@/contexts/LanguageContext"

export function LanguageToggle() {
  const { language, setLanguage, t } = useLanguage()

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'bn' : 'en')
  }

  const getLanguageLabel = () => {
    return language === 'en' ? 'বাং' : 'EN'
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
      className="h-8 px-2 text-xs font-medium"
      title={t('language.switchLanguage')}
    >
      <Globe className="h-3 w-3 mr-1" />
      {getLanguageLabel()}
      <span className="sr-only">{t('language.switchLanguage')}</span>
    </Button>
  )
}
