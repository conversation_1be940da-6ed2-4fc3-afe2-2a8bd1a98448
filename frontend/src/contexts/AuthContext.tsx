import React, { createContext, useContext, useState, useEffect } from 'react'

interface User {
  id: number
  name: string
  email: string
  role: 'super_admin' | 'center_admin' | 'teacher' | 'student'
  tenant_id?: number
  tenant_slug?: string
  tenant_name?: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; error?: string }>
  logout: () => void
  checkAuth: () => Promise<void>
  getTenantSlug: () => string | null
  isAuthenticated: () => boolean
  isSuperAdmin: () => boolean
  isCenterAdmin: () => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  // Extract tenant slug from subdomain
  const getTenantSlug = (): string | null => {
    const hostname = window.location.hostname
    
    // For development: check for patterns like "center-slug.localhost"
    if (hostname.includes('localhost')) {
      const parts = hostname.split('.')
      if (parts.length > 1 && parts[0] !== 'localhost' && parts[0] !== 'www') {
        return parts[0]
      }
    }
    
    // For production: check for patterns like "center-slug.yourdomain.com"
    const parts = hostname.split('.')
    if (parts.length > 2) {
      return parts[0]
    }
    
    return null
  }

  const checkAuth = async () => {
    try {
      const tenantSlug = getTenantSlug()
      const token = localStorage.getItem('auth_token')

      if (!token) {
        setUser(null)
        setLoading(false)
        return
      }

      const url = 'http://localhost:3004/api/auth/me'

      const headers: Record<string, string> = {
        'Authorization': `Bearer ${token}`
      }

      // Add tenant slug header if available
      if (tenantSlug && tenantSlug !== 'localhost') {
        headers['x-tenant-slug'] = tenantSlug
      }

      const response = await fetch(url, {
        headers,
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      } else {
        setUser(null)
        localStorage.removeItem('auth_token')
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      setUser(null)
      localStorage.removeItem('auth_token')
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string, rememberMe: boolean = false) => {
    try {
      setLoading(true)
      const tenantSlug = getTenantSlug()

      // Determine which login endpoint to use based on domain
      const isSubdomain = tenantSlug && tenantSlug !== 'localhost'
      const loginEndpoint = 'http://localhost:3004/api/auth/login'

      const requestBody = isSubdomain
        ? { email, password, center_slug: tenantSlug, loginType: 'center_admin' }
        : { email, password, loginType: 'super_admin' }

      console.log('🔐 Login attempt:', {
        endpoint: loginEndpoint,
        isSubdomain,
        tenantSlug,
        email
      })

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      }

      // Add tenant slug header for subdomain requests
      if (isSubdomain && tenantSlug) {
        headers['x-tenant-slug'] = tenantSlug
      }

      const response = await fetch(loginEndpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        credentials: 'include'
      })

      const data = await response.json()

      if (response.ok) {
        setUser(data.data.user)

        // Store token in localStorage as backup for cross-port issues
        if (data.data.token) {
          localStorage.setItem('auth_token', data.data.token)
        }

        return { success: true, token: data.data.token, user: data.data.user }
      } else {
        return { success: false, error: data.error || 'Login failed' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Network error occurred' }
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await fetch('http://localhost:3004/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setUser(null)
      localStorage.removeItem('auth_token')
    }
  }

  const isAuthenticated = () => {
    return user !== null
  }

  const isSuperAdmin = () => {
    return user?.role === 'super_admin'
  }

  const isCenterAdmin = () => {
    return user?.role === 'center_admin'
  }

  useEffect(() => {
    // Development bypass - check for dev token in URL
    const urlParams = new URLSearchParams(window.location.search)
    const devToken = urlParams.get('dev_token')

    if (devToken === 'super_admin') {
      setUser({
        id: 14,
        name: 'Super Admin',
        email: '<EMAIL>',
        role: 'super_admin',
        tenant_id: null,
        tenant_slug: null,
        tenant_name: null
      })
      // Generate a fresh token for development
      const devTokenPayload = {
        userId: 14,
        email: '<EMAIL>',
        role: 'super_admin',
        tenantId: null,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
      }
      // For development, we'll use a simple base64 encoded token
      const devTokenString = btoa(JSON.stringify(devTokenPayload))
      localStorage.setItem('auth_token', `dev.${devTokenString}`)
      setLoading(false)
      return
    }

    if (devToken === 'center_admin') {
      setUser({
        id: 1,
        name: 'John Admin',
        email: '<EMAIL>',
        role: 'center_admin',
        tenant_id: 1,
        tenant_slug: 'abc',
        tenant_name: 'ABC Coaching Center'
      })
      // Generate a fresh token for development
      const devTokenPayload = {
        userId: 1,
        email: '<EMAIL>',
        role: 'center_admin',
        tenantId: 1,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
      }
      // For development, we'll use a simple base64 encoded token
      const devTokenString = btoa(JSON.stringify(devTokenPayload))
      localStorage.setItem('auth_token', `dev.${devTokenString}`)
      setLoading(false)
      return
    }

    checkAuth()
  }, [])

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    checkAuth,
    getTenantSlug,
    isAuthenticated,
    isSuperAdmin,
    isCenterAdmin
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
