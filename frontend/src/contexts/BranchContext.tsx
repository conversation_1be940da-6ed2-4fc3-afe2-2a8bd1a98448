import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useAuth } from './AuthContext'
import { useApi } from '../hooks/useApi'
import toast from 'react-hot-toast'

interface Branch {
  id: number
  branch_code: string
  branch_name: string
  address: string
  phone: string
  email: string
  manager_id: number
  parent_branch_id: number
  establishment_date: string
  status: 'active' | 'inactive'
  settings: any
  created_at: string
  updated_at: string
}

interface BranchContextType {
  currentBranch: Branch | null
  availableBranches: Branch[]
  switchBranch: (branchId: number) => void
  refreshBranches: () => void
  loading: boolean
  error: string | null
}

const BranchContext = createContext<BranchContextType | undefined>(undefined)

interface BranchProviderProps {
  children: ReactNode
}

export function BranchProvider({ children }: BranchProviderProps) {
  const { user } = useAuth()
  const { adminGet } = useApi()
  
  const [currentBranch, setCurrentBranch] = useState<Branch | null>(null)
  const [availableBranches, setAvailableBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load user's available branches
  const loadAvailableBranches = async () => {
    if (!user) return

    try {
      setLoading(true)
      setError(null)

      // Get user's branch assignments
      const response = await adminGet('/api/coaching/user-branches')
      const branches = response.branches || []
      
      setAvailableBranches(branches)

      // Set current branch from localStorage or default to first available
      const savedBranchId = localStorage.getItem('currentBranchId')
      let targetBranch = null

      if (savedBranchId) {
        targetBranch = branches.find((b: Branch) => b.id === parseInt(savedBranchId))
      }

      if (!targetBranch && branches.length > 0) {
        // Default to user's primary branch or first available
        targetBranch = branches.find((b: Branch) => b.id === user.branch_id) || branches[0]
      }

      if (targetBranch) {
        setCurrentBranch(targetBranch)
        localStorage.setItem('currentBranchId', targetBranch.id.toString())
      }

    } catch (error: any) {
      console.error('Error loading branches:', error)
      setError('Failed to load branches')
      toast.error('Failed to load branches')
    } finally {
      setLoading(false)
    }
  }

  // Switch to a different branch
  const switchBranch = (branchId: number) => {
    const branch = availableBranches.find(b => b.id === branchId)
    if (branch) {
      setCurrentBranch(branch)
      localStorage.setItem('currentBranchId', branchId.toString())
      toast.success(`Switched to ${branch.branch_name}`)
      
      // Reload the page to refresh all data for the new branch
      window.location.reload()
    }
  }

  // Refresh branches list
  const refreshBranches = () => {
    loadAvailableBranches()
  }

  // Load branches when user changes
  useEffect(() => {
    if (user) {
      loadAvailableBranches()
    } else {
      setCurrentBranch(null)
      setAvailableBranches([])
      localStorage.removeItem('currentBranchId')
    }
  }, [user])

  const value: BranchContextType = {
    currentBranch,
    availableBranches,
    switchBranch,
    refreshBranches,
    loading,
    error
  }

  return (
    <BranchContext.Provider value={value}>
      {children}
    </BranchContext.Provider>
  )
}

export function useBranch() {
  const context = useContext(BranchContext)
  if (context === undefined) {
    throw new Error('useBranch must be used within a BranchProvider')
  }
  return context
}

// Hook to get current branch ID for API calls
export function useCurrentBranchId(): number | null {
  const { currentBranch } = useBranch()
  return currentBranch?.id || null
}

// Hook to check if user has access to multiple branches
export function useMultiBranchAccess(): boolean {
  const { availableBranches } = useBranch()
  return availableBranches.length > 1
}
