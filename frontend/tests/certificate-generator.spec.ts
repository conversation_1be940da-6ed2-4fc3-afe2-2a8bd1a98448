import { test, expect } from '@playwright/test';

test.describe('Certificate Generator System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to coaching center login
    await page.goto('http://abc.localhost:5173/coaching/login');
    
    // Login as center admin
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/coaching/dashboard');
  });

  test('should have certificate generator menu item', async ({ page }) => {
    console.log('🧪 Testing certificate generator menu access...');
    
    // Look for certificate generator in sidebar or menu
    const certificateMenu = page.locator('a[href*="certificate"], a:has-text("সার্টিফিকেট"), a:has-text("Certificate")');
    await expect(certificateMenu).toBeVisible({ timeout: 5000 });
    
    console.log('✅ Certificate generator menu item is visible');
  });

  test('should navigate to certificate generator page', async ({ page }) => {
    console.log('🧪 Testing certificate generator page navigation...');
    
    // Click on certificate generator menu
    const certificateMenu = page.locator('a[href*="certificate"], a:has-text("সার্টিফিকেট"), a:has-text("Certificate")');
    await certificateMenu.click();
    
    // Wait for certificate page to load
    await page.waitForURL('**/certificate*');
    
    // Check if certificate generator page elements are present
    const pageTitle = page.locator('h1:has-text("সার্টিফিকেট"), h1:has-text("Certificate"), h2:has-text("সার্টিফিকেট"), h2:has-text("Certificate")');
    await expect(pageTitle).toBeVisible();
    
    console.log('✅ Certificate generator page loads successfully');
  });

  test('should display certificate template options', async ({ page }) => {
    console.log('🧪 Testing certificate template options...');
    
    // Navigate to certificate generator
    await page.goto('http://abc.localhost:5173/coaching/certificates');
    
    // Check for template selection
    const templateSection = page.locator('text="টেমপ্লেট", text="Template", text="নমুনা"');
    await expect(templateSection).toBeVisible({ timeout: 5000 });
    
    // Check for available templates
    const templates = page.locator('.template-card, .certificate-template, button:has-text("Template")');
    const templateCount = await templates.count();
    expect(templateCount).toBeGreaterThan(0);
    
    console.log('✅ Certificate templates are available');
  });

  test('should allow student selection for certificate', async ({ page }) => {
    console.log('🧪 Testing student selection for certificate...');
    
    // Navigate to certificate generator
    await page.goto('http://abc.localhost:5173/coaching/certificates');
    
    // Look for student selection dropdown or search
    const studentSelector = page.locator('select:has(option:text("Student")), input[placeholder*="student"], input[placeholder*="শিক্ষার্থী"]');
    await expect(studentSelector).toBeVisible({ timeout: 5000 });
    
    // If it's a dropdown, check for student options
    if (await page.locator('select').isVisible()) {
      const studentOptions = page.locator('option:text("Mike"), option:text("Student")');
      const optionCount = await studentOptions.count();
      expect(optionCount).toBeGreaterThan(0);
    }
    
    console.log('✅ Student selection is available');
  });

  test('should generate certificate preview', async ({ page }) => {
    console.log('🧪 Testing certificate preview generation...');
    
    // Navigate to certificate generator
    await page.goto('http://abc.localhost:5173/coaching/certificates');
    
    // Select a template
    const firstTemplate = page.locator('.template-card, .certificate-template, button:has-text("Template")').first();
    await firstTemplate.click();
    
    // Select a student
    const studentSelector = page.locator('select, input[placeholder*="student"]');
    if (await page.locator('select').isVisible()) {
      await page.selectOption('select', { index: 1 }); // Select first student
    } else {
      await studentSelector.fill('Mike Student');
    }
    
    // Click generate preview button
    const previewButton = page.locator('button:has-text("প্রিভিউ"), button:has-text("Preview"), button:has-text("Generate")');
    await previewButton.click();
    
    // Wait for preview to appear
    await page.waitForTimeout(3000);
    
    // Check if preview is displayed
    const preview = page.locator('.certificate-preview, .preview-container, canvas, iframe');
    await expect(preview).toBeVisible({ timeout: 10000 });
    
    console.log('✅ Certificate preview generates successfully');
  });

  test('should customize certificate content', async ({ page }) => {
    console.log('🧪 Testing certificate customization...');
    
    // Navigate to certificate generator
    await page.goto('http://abc.localhost:5173/coaching/certificates');
    
    // Look for customization options
    const customizationFields = page.locator('input[name*="title"], input[name*="course"], textarea[name*="description"], input[placeholder*="Title"], input[placeholder*="Course"]');
    
    if (await customizationFields.first().isVisible({ timeout: 5000 })) {
      // Fill in custom content
      const titleField = customizationFields.first();
      await titleField.fill('Certificate of Excellence');
      
      // Check if changes are reflected
      await page.waitForTimeout(1000);
      
      console.log('✅ Certificate customization is available');
    } else {
      console.log('⚠️ Certificate customization fields not found - may need implementation');
    }
  });

  test('should support Bengali and English languages', async ({ page }) => {
    console.log('🧪 Testing Bengali/English language support...');
    
    // Navigate to certificate generator
    await page.goto('http://abc.localhost:5173/coaching/certificates');
    
    // Look for language toggle
    const languageToggle = page.locator('button:has-text("বাংলা"), button:has-text("English"), select:has(option:text("Bengali")), select:has(option:text("বাংলা"))');
    
    if (await languageToggle.isVisible({ timeout: 5000 })) {
      // Test language switching
      await languageToggle.click();
      
      // Wait for language change
      await page.waitForTimeout(2000);
      
      // Check if content changed language
      const bengaliText = page.locator('text="সার্টিফিকেট", text="প্রত্যয়নপত্র"');
      const englishText = page.locator('text="Certificate", text="Achievement"');
      
      const hasBengali = await bengaliText.isVisible();
      const hasEnglish = await englishText.isVisible();
      
      expect(hasBengali || hasEnglish).toBeTruthy();
      
      console.log('✅ Language support is available');
    } else {
      console.log('⚠️ Language toggle not found - may need implementation');
    }
  });

  test('should generate PDF certificate', async ({ page }) => {
    console.log('🧪 Testing PDF certificate generation...');
    
    // Navigate to certificate generator
    await page.goto('http://abc.localhost:5173/coaching/certificates');
    
    // Select template and student
    const firstTemplate = page.locator('.template-card, .certificate-template, button:has-text("Template")').first();
    if (await firstTemplate.isVisible({ timeout: 5000 })) {
      await firstTemplate.click();
    }
    
    const studentSelector = page.locator('select');
    if (await studentSelector.isVisible({ timeout: 2000 })) {
      await page.selectOption('select', { index: 1 });
    }
    
    // Set up download listener
    const downloadPromise = page.waitForEvent('download', { timeout: 10000 });
    
    // Click download/generate PDF button
    const downloadButton = page.locator('button:has-text("ডাউনলোড"), button:has-text("Download"), button:has-text("PDF"), button:has-text("Generate PDF")');
    
    if (await downloadButton.isVisible({ timeout: 5000 })) {
      await downloadButton.click();
      
      // Wait for download
      try {
        const download = await downloadPromise;
        expect(download.suggestedFilename()).toMatch(/certificate.*\.pdf/i);
        console.log('✅ PDF certificate downloads successfully');
      } catch (error) {
        console.log('⚠️ PDF download not triggered - may need implementation');
      }
    } else {
      console.log('⚠️ Download button not found - may need implementation');
    }
  });

  test('should include center branding in certificate', async ({ page }) => {
    console.log('🧪 Testing center branding in certificate...');
    
    // Navigate to certificate generator
    await page.goto('http://abc.localhost:5173/coaching/certificates');
    
    // Generate a preview
    const firstTemplate = page.locator('.template-card, .certificate-template, button:has-text("Template")').first();
    if (await firstTemplate.isVisible({ timeout: 5000 })) {
      await firstTemplate.click();
    }
    
    const previewButton = page.locator('button:has-text("প্রিভিউ"), button:has-text("Preview"), button:has-text("Generate")');
    if (await previewButton.isVisible({ timeout: 5000 })) {
      await previewButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Check for center branding elements
    const centerName = page.locator('text="ABC Coaching", text="ABC", text="Coaching Center"');
    const logo = page.locator('img[alt*="logo"], img[src*="logo"], .logo');
    
    const hasCenterName = await centerName.isVisible();
    const hasLogo = await logo.isVisible();
    
    expect(hasCenterName || hasLogo).toBeTruthy();
    
    console.log('✅ Center branding is included in certificate');
  });

  test('should validate required fields before generation', async ({ page }) => {
    console.log('🧪 Testing certificate generation validation...');
    
    // Navigate to certificate generator
    await page.goto('http://abc.localhost:5173/coaching/certificates');
    
    // Try to generate without selecting student or template
    const generateButton = page.locator('button:has-text("তৈরি"), button:has-text("Generate"), button:has-text("Create")');
    
    if (await generateButton.isVisible({ timeout: 5000 })) {
      await generateButton.click();
      
      // Check for validation error
      const errorMessage = page.locator('.error, .alert-error, text="required", text="নির্বাচন", text="Please select"');
      
      const hasValidation = await errorMessage.isVisible({ timeout: 3000 });
      expect(hasValidation).toBeTruthy();
      
      console.log('✅ Certificate generation validation works');
    } else {
      console.log('⚠️ Generate button not found - may need implementation');
    }
  });

  test('should save certificate templates', async ({ page }) => {
    console.log('🧪 Testing certificate template saving...');
    
    // Navigate to certificate generator
    await page.goto('http://abc.localhost:5173/coaching/certificates');
    
    // Look for template creation/editing options
    const createTemplateButton = page.locator('button:has-text("নতুন টেমপ্লেট"), button:has-text("Create Template"), button:has-text("Add Template")');
    
    if (await createTemplateButton.isVisible({ timeout: 5000 })) {
      await createTemplateButton.click();
      
      // Fill template details
      const templateNameField = page.locator('input[name*="name"], input[placeholder*="name"], input[placeholder*="নাম"]');
      if (await templateNameField.isVisible({ timeout: 3000 })) {
        await templateNameField.fill('Custom Achievement Certificate');
      }
      
      // Save template
      const saveButton = page.locator('button:has-text("সংরক্ষণ"), button:has-text("Save")');
      if (await saveButton.isVisible({ timeout: 3000 })) {
        await saveButton.click();
        
        // Check for success message
        const successMessage = page.locator('.toast, .alert, .notification');
        await expect(successMessage).toBeVisible({ timeout: 5000 });
        
        console.log('✅ Certificate template saving works');
      }
    } else {
      console.log('⚠️ Template creation not found - may need implementation');
    }
  });
});
