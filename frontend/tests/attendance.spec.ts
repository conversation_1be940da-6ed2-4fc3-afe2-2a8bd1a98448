import { test, expect } from '@playwright/test';

test.describe('Attendance Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            role: 'center_admin'
          }
        })
      });
    });

    await page.goto('http://localhost:5173/attendance');
  });

  test('should display attendance page elements', async ({ page }) => {
    // Check page title and description
    await expect(page.locator('h1.text-3xl')).toContainText('Attendance');
    await expect(page.locator('text=Track and manage student attendance')).toBeVisible();
    
    // Check view mode buttons
    await expect(page.locator('button:has-text("Mark Attendance")')).toBeVisible();
    await expect(page.locator('button:has-text("View Records")')).toBeVisible();
    
    // Check filter elements
    await expect(page.locator('input[type="date"]')).toBeVisible();
    await expect(page.locator('select[id="course"]')).toBeVisible();
    await expect(page.locator('input[placeholder*="Search by name"]')).toBeVisible();
  });

  test('should display attendance statistics', async ({ page }) => {
    // Mock students and attendance data
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', course_name: 'Mathematics' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', course_name: 'Physics' }
          ]
        })
      });
    });

    await page.route('**/api/attendance*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          attendance: [
            {
              id: 1,
              student_id: 1,
              student_name: 'John Doe',
              course_name: 'Mathematics',
              date: '2024-01-15',
              status: 'present',
              marked_by: 'Test User',
              created_at: '2024-01-15'
            },
            {
              id: 2,
              student_id: 2,
              student_name: 'Jane Smith',
              course_name: 'Physics',
              date: '2024-01-15',
              status: 'absent',
              marked_by: 'Test User',
              created_at: '2024-01-15'
            }
          ]
        })
      });
    });

    await page.reload();

    // Check statistics cards
    await expect(page.locator('text=Total Students')).toBeVisible();
    await expect(page.locator('div:has-text("Present")').first()).toBeVisible();
    await expect(page.locator('text=Absent')).toBeVisible();
    await expect(page.locator('text=Attendance Rate')).toBeVisible();
  });

  test('should mark student attendance', async ({ page }) => {
    // Mock students data
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', course_name: 'Mathematics' }
          ]
        })
      });
    });

    // Mock attendance data (initially empty)
    await page.route('**/api/attendance*', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ attendance: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            attendance: {
              id: 1,
              student_id: 1,
              student_name: 'John Doe',
              course_name: 'Mathematics',
              date: '2024-01-15',
              status: 'present',
              marked_by: 'Test User',
              created_at: '2024-01-15'
            }
          })
        });
      }
    });

    await page.reload();

    // Should be in mark attendance mode by default
    await expect(page.locator('button:has-text("Mark Attendance")').first()).toBeVisible();

    // Check student row is displayed
    await expect(page.locator('[data-testid="student-attendance-row"]')).toBeVisible();
    await expect(page.locator('text=John Doe')).toBeVisible();

    // Mark student as present
    await page.locator('[data-testid="student-attendance-row"]').locator('button:has-text("Present")').click();

    // The button should be selected (this would be reflected in the UI state)
    await expect(page.locator('[data-testid="student-attendance-row"]')).toBeVisible();
  });

  test('should mark student as late', async ({ page }) => {
    // Mock students data
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', course_name: 'Mathematics' }
          ]
        })
      });
    });

    // Mock attendance data
    await page.route('**/api/attendance*', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ attendance: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            attendance: {
              id: 1,
              student_id: 1,
              status: 'late'
            }
          })
        });
      }
    });

    await page.reload();

    // Mark student as late
    await page.locator('[data-testid="student-attendance-row"]').locator('button:has-text("Late")').click();

    // Verify the action was performed
    await expect(page.locator('[data-testid="student-attendance-row"]')).toBeVisible();
  });

  test('should mark student as absent', async ({ page }) => {
    // Mock students data
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', course_name: 'Mathematics' }
          ]
        })
      });
    });

    // Mock attendance data
    await page.route('**/api/attendance*', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ attendance: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            attendance: {
              id: 1,
              student_id: 1,
              status: 'absent'
            }
          })
        });
      }
    });

    await page.reload();

    // Mark student as absent
    await page.locator('[data-testid="student-attendance-row"]').locator('button:has-text("Absent")').click();

    // Verify the action was performed
    await expect(page.locator('[data-testid="student-attendance-row"]')).toBeVisible();
  });

  test('should filter students by course', async ({ page }) => {
    // Mock students data with different courses
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', course_name: 'Mathematics' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', course_name: 'Physics' }
          ]
        })
      });
    });

    await page.route('**/api/attendance*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ attendance: [] })
      });
    });

    await page.reload();

    // Select Mathematics course
    await page.selectOption('select[id="course"]', 'Mathematics');

    // Should show only Mathematics students
    await expect(page.locator('text=John Doe')).toBeVisible();
    // Jane Smith should not be visible (this would depend on the filtering logic)
  });

  test('should search students by name', async ({ page }) => {
    // Mock students data
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', course_name: 'Mathematics' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', course_name: 'Physics' }
          ]
        })
      });
    });

    await page.route('**/api/attendance*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ attendance: [] })
      });
    });

    await page.reload();

    // Search for John
    await page.fill('input[placeholder*="Search by name"]', 'John');

    // Should show only John Doe
    await expect(page.locator('text=John Doe')).toBeVisible();
  });

  test('should view attendance records', async ({ page }) => {
    // Mock attendance records
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.route('**/api/attendance*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          attendance: [
            {
              id: 1,
              student_id: 1,
              student_name: 'John Doe',
              course_name: 'Mathematics',
              date: '2024-01-15',
              status: 'present',
              marked_by: 'Test User',
              notes: 'On time',
              created_at: '2024-01-15'
            },
            {
              id: 2,
              student_id: 2,
              student_name: 'Jane Smith',
              course_name: 'Physics',
              date: '2024-01-15',
              status: 'absent',
              marked_by: 'Test User',
              created_at: '2024-01-15'
            }
          ]
        })
      });
    });

    await page.reload();

    // Switch to view records mode
    await page.click('button:has-text("View Records")');

    // Check records are displayed
    await expect(page.locator('text=Attendance Records')).toBeVisible();
    await expect(page.locator('[data-testid="attendance-record"]')).toHaveCount(2);
    await expect(page.locator('text=John Doe')).toBeVisible();
    await expect(page.locator('text=Jane Smith')).toBeVisible();
  });

  test('should mark all students present', async ({ page }) => {
    // Mock students data
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', course_name: 'Mathematics' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', course_name: 'Mathematics' }
          ]
        })
      });
    });

    await page.route('**/api/attendance*', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ attendance: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ attendance: { status: 'present' } })
        });
      }
    });

    await page.reload();

    // Click Mark All Present button
    await page.click('button:has-text("Mark All Present")');

    // Verify the action was performed (students should be marked)
    await expect(page.locator('[data-testid="student-attendance-row"]')).toHaveCount(2);
  });

  test('should change date filter', async ({ page }) => {
    // Mock data
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.route('**/api/attendance*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ attendance: [] })
      });
    });

    await page.reload();

    // Change date
    await page.fill('input[type="date"]', '2024-01-20');

    // The date should be updated
    await expect(page.locator('input[type="date"]')).toHaveValue('2024-01-20');
  });
});
