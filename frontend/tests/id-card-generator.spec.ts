import { test, expect } from '@playwright/test';

test.describe('ID Card Generator System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to coaching center login
    await page.goto('http://abc.localhost:5173/coaching/login');
    
    // Login as center admin
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/coaching/dashboard');
  });

  test('should have ID card generator menu item', async ({ page }) => {
    console.log('🧪 Testing ID card generator menu access...');
    
    // Look for ID card generator in sidebar or menu
    const idCardMenu = page.locator('a[href*="id-card"], a:has-text("আইডি কার্ড"), a:has-text("ID Card")');
    await expect(idCardMenu).toBeVisible({ timeout: 5000 });
    
    console.log('✅ ID card generator menu item is visible');
  });

  test('should navigate to ID card generator page', async ({ page }) => {
    console.log('🧪 Testing ID card generator page navigation...');
    
    // Click on ID card generator menu
    const idCardMenu = page.locator('a[href*="id-card"], a:has-text("আইডি কার্ড"), a:has-text("ID Card")');
    await idCardMenu.click();
    
    // Wait for ID card page to load
    await page.waitForURL('**/id-card*');
    
    // Check if ID card generator page elements are present
    const pageTitle = page.locator('h1:has-text("আইডি কার্ড"), h1:has-text("ID Card"), h2:has-text("আইডি কার্ড"), h2:has-text("ID Card")');
    await expect(pageTitle).toBeVisible();
    
    console.log('✅ ID card generator page loads successfully');
  });

  test('should display student selection for ID card', async ({ page }) => {
    console.log('🧪 Testing student selection for ID card...');
    
    // Navigate to ID card generator
    await page.goto('http://abc.localhost:5173/coaching/id-cards');
    
    // Look for student selection dropdown or search
    const studentSelector = page.locator('select:has(option:text("Student")), input[placeholder*="student"], input[placeholder*="শিক্ষার্থী"]');
    await expect(studentSelector).toBeVisible({ timeout: 5000 });
    
    // If it's a dropdown, check for student options
    if (await page.locator('select').isVisible()) {
      const studentOptions = page.locator('option:text("Mike"), option:text("Student")');
      const optionCount = await studentOptions.count();
      expect(optionCount).toBeGreaterThan(0);
    }
    
    console.log('✅ Student selection is available for ID card');
  });

  test('should generate ID card preview with student photo', async ({ page }) => {
    console.log('🧪 Testing ID card preview with photo...');
    
    // Navigate to ID card generator
    await page.goto('http://abc.localhost:5173/coaching/id-cards');
    
    // Select a student
    const studentSelector = page.locator('select, input[placeholder*="student"]');
    if (await page.locator('select').isVisible()) {
      await page.selectOption('select', { index: 1 }); // Select first student
    } else {
      await studentSelector.fill('Mike Student');
    }
    
    // Click generate preview button
    const previewButton = page.locator('button:has-text("প্রিভিউ"), button:has-text("Preview"), button:has-text("Generate")');
    await previewButton.click();
    
    // Wait for preview to appear
    await page.waitForTimeout(3000);
    
    // Check if preview is displayed
    const preview = page.locator('.id-card-preview, .preview-container, canvas, .card-preview');
    await expect(preview).toBeVisible({ timeout: 10000 });
    
    // Check for student photo placeholder or actual photo
    const studentPhoto = page.locator('img[alt*="student"], img[alt*="photo"], .student-photo, .photo-placeholder');
    await expect(studentPhoto).toBeVisible({ timeout: 5000 });
    
    console.log('✅ ID card preview generates with student photo');
  });

  test('should include QR code in ID card', async ({ page }) => {
    console.log('🧪 Testing QR code inclusion in ID card...');
    
    // Navigate to ID card generator
    await page.goto('http://abc.localhost:5173/coaching/id-cards');
    
    // Select student and generate preview
    const studentSelector = page.locator('select');
    if (await studentSelector.isVisible({ timeout: 5000 })) {
      await page.selectOption('select', { index: 1 });
    }
    
    const previewButton = page.locator('button:has-text("প্রিভিউ"), button:has-text("Preview"), button:has-text("Generate")');
    if (await previewButton.isVisible({ timeout: 5000 })) {
      await previewButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Check for QR code in the preview
    const qrCode = page.locator('img[alt*="qr"], img[src*="qr"], .qr-code, canvas[data-qr]');
    await expect(qrCode).toBeVisible({ timeout: 5000 });
    
    console.log('✅ QR code is included in ID card');
  });

  test('should display center branding on ID card', async ({ page }) => {
    console.log('🧪 Testing center branding on ID card...');
    
    // Navigate to ID card generator
    await page.goto('http://abc.localhost:5173/coaching/id-cards');
    
    // Generate a preview
    const studentSelector = page.locator('select');
    if (await studentSelector.isVisible({ timeout: 5000 })) {
      await page.selectOption('select', { index: 1 });
    }
    
    const previewButton = page.locator('button:has-text("প্রিভিউ"), button:has-text("Preview"), button:has-text("Generate")');
    if (await previewButton.isVisible({ timeout: 5000 })) {
      await previewButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Check for center branding elements
    const centerName = page.locator('text="ABC Coaching", text="ABC", text="Coaching Center"');
    const logo = page.locator('img[alt*="logo"], img[src*="logo"], .logo');
    
    const hasCenterName = await centerName.isVisible();
    const hasLogo = await logo.isVisible();
    
    expect(hasCenterName || hasLogo).toBeTruthy();
    
    console.log('✅ Center branding is displayed on ID card');
  });

  test('should support both front and back card design', async ({ page }) => {
    console.log('🧪 Testing front and back card design...');
    
    // Navigate to ID card generator
    await page.goto('http://abc.localhost:5173/coaching/id-cards');
    
    // Look for front/back toggle or tabs
    const frontBackToggle = page.locator('button:has-text("সামনে"), button:has-text("Front"), button:has-text("পিছনে"), button:has-text("Back"), .card-side-toggle');
    
    if (await frontBackToggle.first().isVisible({ timeout: 5000 })) {
      // Test front side
      const frontButton = page.locator('button:has-text("সামনে"), button:has-text("Front")').first();
      if (await frontButton.isVisible()) {
        await frontButton.click();
        await page.waitForTimeout(1000);
      }
      
      // Test back side
      const backButton = page.locator('button:has-text("পিছনে"), button:has-text("Back")').first();
      if (await backButton.isVisible()) {
        await backButton.click();
        await page.waitForTimeout(1000);
      }
      
      console.log('✅ Front and back card design is supported');
    } else {
      console.log('⚠️ Front/back toggle not found - may be single-sided design');
    }
  });

  test('should allow ID card customization', async ({ page }) => {
    console.log('🧪 Testing ID card customization...');
    
    // Navigate to ID card generator
    await page.goto('http://abc.localhost:5173/coaching/id-cards');
    
    // Look for customization options
    const customizationFields = page.locator('input[name*="title"], input[name*="subtitle"], select[name*="template"], input[placeholder*="Title"]');
    
    if (await customizationFields.first().isVisible({ timeout: 5000 })) {
      // Fill in custom content
      const titleField = customizationFields.first();
      await titleField.fill('Student ID Card');
      
      // Check if changes are reflected
      await page.waitForTimeout(1000);
      
      console.log('✅ ID card customization is available');
    } else {
      console.log('⚠️ ID card customization fields not found - may need implementation');
    }
  });

  test('should generate printable ID card', async ({ page }) => {
    console.log('🧪 Testing printable ID card generation...');
    
    // Navigate to ID card generator
    await page.goto('http://abc.localhost:5173/coaching/id-cards');
    
    // Select student and generate preview
    const studentSelector = page.locator('select');
    if (await studentSelector.isVisible({ timeout: 5000 })) {
      await page.selectOption('select', { index: 1 });
    }
    
    // Set up download listener
    const downloadPromise = page.waitForEvent('download', { timeout: 10000 });
    
    // Click print/download button
    const printButton = page.locator('button:has-text("প্রিন্ট"), button:has-text("Print"), button:has-text("ডাউনলোড"), button:has-text("Download")');
    
    if (await printButton.isVisible({ timeout: 5000 })) {
      await printButton.click();
      
      // Wait for download or print dialog
      try {
        const download = await downloadPromise;
        expect(download.suggestedFilename()).toMatch(/id.*card.*\.(pdf|png|jpg)/i);
        console.log('✅ Printable ID card downloads successfully');
      } catch (error) {
        // Check if print dialog opened instead
        console.log('⚠️ Print dialog may have opened instead of download');
      }
    } else {
      console.log('⚠️ Print/download button not found - may need implementation');
    }
  });

  test('should validate required fields before generation', async ({ page }) => {
    console.log('🧪 Testing ID card generation validation...');
    
    // Navigate to ID card generator
    await page.goto('http://abc.localhost:5173/coaching/id-cards');
    
    // Try to generate without selecting student
    const generateButton = page.locator('button:has-text("তৈরি"), button:has-text("Generate"), button:has-text("Create")');
    
    if (await generateButton.isVisible({ timeout: 5000 })) {
      await generateButton.click();
      
      // Check for validation error
      const errorMessage = page.locator('.error, .alert-error, text="required", text="নির্বাচন", text="Please select"');
      
      const hasValidation = await errorMessage.isVisible({ timeout: 3000 });
      expect(hasValidation).toBeTruthy();
      
      console.log('✅ ID card generation validation works');
    } else {
      console.log('⚠️ Generate button not found - may need implementation');
    }
  });

  test('should display student information correctly', async ({ page }) => {
    console.log('🧪 Testing student information display...');
    
    // Navigate to ID card generator
    await page.goto('http://abc.localhost:5173/coaching/id-cards');
    
    // Select student and generate preview
    const studentSelector = page.locator('select');
    if (await studentSelector.isVisible({ timeout: 5000 })) {
      await page.selectOption('select', { index: 1 });
    }
    
    const previewButton = page.locator('button:has-text("প্রিভিউ"), button:has-text("Preview"), button:has-text("Generate")');
    if (await previewButton.isVisible({ timeout: 5000 })) {
      await previewButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Check for student information fields
    const studentName = page.locator('text="Mike Student", text="Student Name", .student-name');
    const studentId = page.locator('text="ID:", text="Student ID", .student-id');
    const course = page.locator('text="Course:", text="Class:", .course-info');
    
    const hasStudentInfo = await studentName.isVisible() || await studentId.isVisible() || await course.isVisible();
    expect(hasStudentInfo).toBeTruthy();
    
    console.log('✅ Student information is displayed correctly');
  });

  test('should support multiple ID card templates', async ({ page }) => {
    console.log('🧪 Testing multiple ID card templates...');
    
    // Navigate to ID card generator
    await page.goto('http://abc.localhost:5173/coaching/id-cards');
    
    // Look for template selection
    const templateSelector = page.locator('select:has(option:text("Template")), .template-card, .template-option');
    
    if (await templateSelector.isVisible({ timeout: 5000 })) {
      // Check for multiple templates
      const templates = page.locator('.template-card, option:text("Template"), .template-option');
      const templateCount = await templates.count();
      expect(templateCount).toBeGreaterThan(1);
      
      console.log('✅ Multiple ID card templates are available');
    } else {
      console.log('⚠️ Template selection not found - may have single template');
    }
  });
});
