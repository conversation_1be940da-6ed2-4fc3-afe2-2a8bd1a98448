import { test, expect } from '@playwright/test'

test.describe('API Upload Test', () => {
  test('should verify upload API endpoint exists', async ({ page }) => {
    // Create a simple test image buffer
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
      0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
      0x49, 0x48, 0x44, 0x52, // IHDR
      0x00, 0x00, 0x00, 0x01, // Width: 1
      0x00, 0x00, 0x00, 0x01, // Height: 1
      0x08, 0x02, 0x00, 0x00, 0x00, // Bit depth, color type, compression, filter, interlace
      0x90, 0x77, 0x53, 0xDE, // CRC
      0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
      0x49, 0x44, 0x41, 0x54, // IDAT
      0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // Image data
      0xE2, 0x21, 0xBC, 0x33, // CRC
      0x00, 0x00, 0x00, 0x00, // IEND chunk length
      0x49, 0x45, 0x4E, 0x44, // IEND
      0xAE, 0x42, 0x60, 0x82  // CRC
    ])

    // Test 1: Upload API without auth (should return 401)
    const unauthorizedResponse = await page.request.post('http://localhost:3000/api/coaching/settings/upload-image', {
      multipart: {
        image: {
          name: 'test.png',
          mimeType: 'image/png',
          buffer: testImageBuffer
        },
        type: 'logo'
      }
    })
    
    console.log('Unauthorized upload response status:', unauthorizedResponse.status())
    expect(unauthorizedResponse.status()).toBe(401) // Should be unauthorized
    
    // Test 2: Upload API with fake auth (should return 401 or 403)
    const fakeAuthResponse = await page.request.post('http://localhost:3000/api/coaching/settings/upload-image', {
      headers: {
        'Authorization': 'Bearer fake-token'
      },
      multipart: {
        image: {
          name: 'test.png',
          mimeType: 'image/png',
          buffer: testImageBuffer
        },
        type: 'logo'
      }
    })
    
    console.log('Fake auth upload response status:', fakeAuthResponse.status())
    expect(fakeAuthResponse.status()).toBeGreaterThanOrEqual(401) // Should be unauthorized/forbidden
    
    // Test 3: Upload API without image (should return 400)
    const noImageResponse = await page.request.post('http://localhost:3000/api/coaching/settings/upload-image', {
      headers: {
        'Authorization': 'Bearer fake-token'
      },
      multipart: {
        type: 'logo'
        // Missing image
      }
    })
    
    console.log('No image upload response status:', noImageResponse.status())
    // Should be 400 (bad request) or 401 (unauthorized), but not 404 (not found)
    expect(noImageResponse.status()).not.toBe(404)
  })

  test('should verify settings API endpoint exists', async ({ page }) => {
    // Test settings GET API without auth
    const response = await page.request.get('http://localhost:3000/api/coaching/settings')
    
    console.log('Settings API response status:', response.status())
    // Should be 401 (unauthorized), not 404 (not found)
    expect(response.status()).toBe(401)
  })

  test('should verify static file serving works', async ({ page }) => {
    // Test static file serving
    const response = await page.request.get('http://localhost:3000/uploads/images/nonexistent.jpg')
    
    console.log('Static file serving response status:', response.status())
    expect(response.status()).toBe(404) // Should be not found
  })

  test('should verify image processing utilities are available', async ({ page }) => {
    // Test that sharp is available by checking if the server can handle image processing
    const testResult = await page.evaluate(() => {
      // Test client-side image processing capabilities
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        return !!(canvas && ctx && canvas.toDataURL)
      } catch (error) {
        return false
      }
    })
    
    expect(testResult).toBe(true)
  })

  test('should verify uploads directory exists', async ({ page }) => {
    // Test that the uploads directory endpoint responds
    const response = await page.request.get('http://localhost:3000/uploads/')
    
    console.log('Uploads directory response status:', response.status())
    // Should get some response (404 or 403), not connection error
    expect(response.status()).toBeLessThan(500)
  })

  test('should test multipart form data handling', async ({ page }) => {
    // Test that the server can handle multipart form data
    const testData = 'test-data'
    
    const response = await page.request.post('http://localhost:3000/api/coaching/settings/upload-image', {
      multipart: {
        test: testData,
        type: 'logo'
      }
    })
    
    console.log('Multipart handling response status:', response.status())
    // Should handle multipart data (401 unauthorized, not 500 server error)
    expect(response.status()).toBeLessThan(500)
  })

  test('should verify WebP conversion capability', async ({ page }) => {
    // Test that the browser supports WebP
    const supportsWebP = await page.evaluate(() => {
      const canvas = document.createElement('canvas')
      canvas.width = 1
      canvas.height = 1
      
      try {
        const dataURL = canvas.toDataURL('image/webp')
        return dataURL.startsWith('data:image/webp')
      } catch (error) {
        return false
      }
    })
    
    console.log('WebP support:', supportsWebP)
    // Modern browsers should support WebP
    expect(supportsWebP).toBe(true)
  })

  test('should verify file size validation', async ({ page }) => {
    // Create a large buffer to test file size limits
    const largeBuffer = Buffer.alloc(1024 * 1024) // 1MB buffer
    
    const response = await page.request.post('http://localhost:3000/api/coaching/settings/upload-image', {
      multipart: {
        image: {
          name: 'large-test.png',
          mimeType: 'image/png',
          buffer: largeBuffer
        },
        type: 'logo'
      }
    })
    
    console.log('Large file upload response status:', response.status())
    // Should handle large files (401 unauthorized or 400 bad request, not 500 server error)
    expect(response.status()).toBeLessThan(500)
  })
})
