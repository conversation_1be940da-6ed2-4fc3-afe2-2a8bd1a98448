import { test, expect } from '@playwright/test';

test.describe('Super Admin Functionality Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set authentication token in localStorage
    await page.goto('http://localhost:5173');
    await page.evaluate(() => {
      localStorage.setItem('auth_token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************.TLt7bx_rLsyzVKQz3E1scR6Ide2JbIrmhNWZgCk5Nzs');
    });

    // Mock authentication endpoint
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 14,
            name: 'Super Admin',
            email: '<EMAIL>',
            role: 'super_admin',
            tenant_id: null
          }
        })
      });
    });

    // Mock admin stats endpoint
    await page.route('**/api/admin/stats', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            totalCenters: 5,
            activeCenters: 4,
            totalUsers: 150,
            totalRevenue: 50000,
            monthlyGrowth: 12.5
          }
        })
      });
    });

    // Mock admin centers endpoint
    await page.route('**/api/admin/centers', async route => {
      const method = route.request().method();
      
      if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            centers: [
              {
                id: 1,
                name: 'ABC Coaching Center',
                slug: 'abc-coaching',
                email: '<EMAIL>',
                phone: '1234567890',
                address: '123 Main St',
                status: 'active',
                created_at: '2024-01-01'
              },
              {
                id: 2,
                name: 'XYZ Academy',
                slug: 'xyz-academy',
                email: '<EMAIL>',
                phone: '0987654321',
                address: '456 Oak Ave',
                status: 'active',
                created_at: '2024-01-15'
              }
            ]
          })
        });
      } else if (method === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            center: {
              id: 3,
              name: 'New Test Center',
              slug: 'new-test-center',
              email: '<EMAIL>',
              phone: '5555555555',
              address: '789 Test St',
              status: 'active',
              created_at: '2024-01-20'
            }
          })
        });
      }
    });

    // Mock center status update endpoint
    await page.route('**/api/admin/centers/*/status', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Center status updated successfully'
        })
      });
    });
  });

  test('should access Super Admin dashboard successfully', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Should show Super Admin dashboard
    await expect(page.locator('h1')).toContainText('Super Admin Dashboard');
    await expect(page.locator('text=Manage coaching centers')).toBeVisible();
    await expect(page.locator('text=System Overview')).toBeVisible();
  });

  test('should display system statistics correctly', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Should show statistics cards
    await expect(page.locator('text=Total Centers')).toBeVisible();
    await expect(page.locator('text=5')).toBeVisible(); // Total centers count
    await expect(page.locator('text=Active Centers')).toBeVisible();
    await expect(page.locator('text=4')).toBeVisible(); // Active centers count
    await expect(page.locator('text=Total Users')).toBeVisible();
    await expect(page.locator('text=150')).toBeVisible(); // Total users count
  });

  test('should display coaching centers list', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Should show centers list
    await expect(page.locator('text=ABC Coaching Center')).toBeVisible();
    await expect(page.locator('text=XYZ Academy')).toBeVisible();
    await expect(page.locator('text=abc-coaching')).toBeVisible();
    await expect(page.locator('text=xyz-academy')).toBeVisible();
  });

  test('should open Add Coaching Center modal', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Click Add Coaching Center button
    await page.click('text=Add Coaching Center');

    // Should show modal
    await expect(page.locator('text=Create New Coaching Center')).toBeVisible();
    await expect(page.locator('input[name="name"]')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="phone"]')).toBeVisible();
    await expect(page.locator('textarea[name="address"]')).toBeVisible();
  });

  test('should create a new coaching center successfully', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Click Add Coaching Center button
    await page.click('text=Add Coaching Center');

    // Fill the form
    await page.fill('input[name="name"]', 'New Test Center');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '5555555555');
    await page.fill('textarea[name="address"]', '789 Test St');
    await page.fill('input[name="adminName"]', 'Test Admin');
    await page.fill('input[name="adminEmail"]', '<EMAIL>');
    await page.fill('input[name="adminPassword"]', 'admin123');

    // Submit the form
    await page.click('button[type="submit"]');

    // Should show success message
    await expect(page.locator('text=Center created successfully')).toBeVisible();
  });

  test('should handle center status changes', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Find a center and click status toggle
    const centerRow = page.locator('text=ABC Coaching Center').locator('..');
    await centerRow.locator('button').first().click(); // Assuming first button is status toggle

    // Should show confirmation or success message
    await expect(page.locator('text=Status updated')).toBeVisible();
  });

  test('should navigate to different sections', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Test sidebar navigation
    const sidebarItems = [
      'Dashboard',
      'Coaching Centers',
      'System Users',
      'Analytics',
      'Settings'
    ];

    for (const item of sidebarItems) {
      const link = page.locator(`text=${item}`).first();
      if (await link.isVisible()) {
        await link.click();
        await page.waitForTimeout(500); // Wait for navigation
        // Should not redirect to login
        expect(page.url()).not.toContain('/login');
      }
    }
  });

  test('should display user profile information', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Should show user info in header or sidebar
    await expect(page.locator('text=Super Admin')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
  });

  test('should handle logout functionality', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Look for logout button/link
    const logoutButton = page.locator('text=Logout').or(page.locator('text=Sign Out'));
    
    if (await logoutButton.isVisible()) {
      await logoutButton.click();
      
      // Should redirect to login page
      await expect(page).toHaveURL(/.*\/login/);
    }
  });

  test('should validate form inputs in center creation', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Click Add Coaching Center button
    await page.click('text=Add Coaching Center');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Should show validation errors
    await expect(page.locator('text=required').or(page.locator('text=This field is required'))).toBeVisible();
  });

  test('should search and filter coaching centers', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Look for search input
    const searchInput = page.locator('input[placeholder*="search"]').or(page.locator('input[type="search"]'));
    
    if (await searchInput.isVisible()) {
      await searchInput.fill('ABC');
      await page.waitForTimeout(500);
      
      // Should show filtered results
      await expect(page.locator('text=ABC Coaching Center')).toBeVisible();
    }
  });

  test('should display responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('http://localhost:5173/dashboard');

    // Should show mobile-friendly layout
    await expect(page.locator('h1')).toBeVisible();
    
    // Mobile menu should be accessible
    const mobileMenuButton = page.locator('button[aria-label*="menu"]').or(page.locator('.hamburger'));
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
      await expect(page.locator('nav')).toBeVisible();
    }
  });
});
