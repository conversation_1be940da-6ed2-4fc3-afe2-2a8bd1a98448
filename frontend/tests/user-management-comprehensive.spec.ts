import { test, expect } from '@playwright/test'

test.describe('User Management System - Comprehensive Test', () => {
  test.beforeEach(async ({ page }) => {
    // Login as center admin
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/coaching/dashboard')
  })

  test('should display user management page with all elements', async ({ page }) => {
    // Navigate to user management page
    await page.goto('http://abc.localhost:5174/coaching/users')
    
    // Wait for page to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Check page title and description
    await expect(page.locator('h1')).toContainText('User Management')
    await expect(page.getByText('Manage users and their roles')).toBeVisible()
    
    // Check Add User button
    await expect(page.getByRole('button', { name: /Add User/i })).toBeVisible()
    
    // Check stats cards
    await expect(page.getByText('Total Users')).toBeVisible()
    await expect(page.getByText('Active Users')).toBeVisible()
    await expect(page.getByText('Teachers')).toBeVisible()
    await expect(page.getByText('Students')).toBeVisible()
    
    // Check search and filter elements
    await expect(page.getByPlaceholder(/Search users/i)).toBeVisible()
    await expect(page.getByText('Role')).toBeVisible()
    await expect(page.getByText('Status')).toBeVisible()
    
    // Check table headers
    await expect(page.getByText('Name')).toBeVisible()
    await expect(page.getByText('Email')).toBeVisible()
    await expect(page.getByText('Phone')).toBeVisible()
    await expect(page.getByText('Role')).toBeVisible()
    await expect(page.getByText('Status')).toBeVisible()
    await expect(page.getByText('Actions')).toBeVisible()
  })

  test('should open add user dialog with all form fields', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/users')
    
    // Click Add User button
    await page.getByRole('button', { name: /Add User/i }).click()
    
    // Check dialog is open
    await expect(page.getByRole('dialog')).toBeVisible()
    await expect(page.getByText('Add User')).toBeVisible()
    await expect(page.getByText('Create a new user account')).toBeVisible()
    
    // Check all form fields are present
    await expect(page.getByLabel(/Full Name/i)).toBeVisible()
    await expect(page.getByLabel(/Email Address/i)).toBeVisible()
    await expect(page.getByLabel(/Phone Number/i)).toBeVisible()
    await expect(page.getByLabel(/Role/i)).toBeVisible()
    await expect(page.getByLabel(/Password/i)).toBeVisible()
    await expect(page.getByLabel(/Confirm Password/i)).toBeVisible()
    await expect(page.getByLabel(/Status/i)).toBeVisible()
    
    // Check role options
    await page.getByLabel(/Role/i).click()
    await expect(page.getByText('Center Admin')).toBeVisible()
    await expect(page.getByText('Teacher')).toBeVisible()
    await expect(page.getByText('Staff')).toBeVisible()
    await expect(page.getByText('Student')).toBeVisible()
    
    // Check status options
    await page.getByLabel(/Status/i).click()
    await expect(page.getByText('Active')).toBeVisible()
    await expect(page.getByText('Inactive')).toBeVisible()
    
    // Check action buttons
    await expect(page.getByRole('button', { name: /Cancel/i })).toBeVisible()
    await expect(page.getByRole('button', { name: /Create/i })).toBeVisible()
  })

  test('should validate form fields when creating user', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/users')
    
    // Open add user dialog
    await page.getByRole('button', { name: /Add User/i }).click()
    
    // Try to submit empty form
    await page.getByRole('button', { name: /Create/i }).click()
    
    // Should show validation error
    await expect(page.getByText(/Please fill in all required fields/i)).toBeVisible()
    
    // Fill partial form and test password validation
    await page.getByLabel(/Full Name/i).fill('Test User')
    await page.getByLabel(/Email Address/i).fill('<EMAIL>')
    await page.getByLabel(/Phone Number/i).fill('1234567890')
    await page.getByLabel(/Role/i).click()
    await page.getByText('Teacher').click()
    await page.getByLabel(/Password/i).fill('123')
    await page.getByLabel(/Confirm Password/i).fill('456')
    
    await page.getByRole('button', { name: /Create/i }).click()
    
    // Should show password validation errors
    await expect(page.getByText(/Password must be at least 6 characters/i)).toBeVisible()
  })

  test('should create new user successfully', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/users')
    
    // Open add user dialog
    await page.getByRole('button', { name: /Add User/i }).click()
    
    // Fill form with valid data
    const timestamp = Date.now()
    const testEmail = `testuser${timestamp}@example.com`
    
    await page.getByLabel(/Full Name/i).fill('Test User')
    await page.getByLabel(/Email Address/i).fill(testEmail)
    await page.getByLabel(/Phone Number/i).fill('1234567890')
    await page.getByLabel(/Role/i).click()
    await page.getByText('Teacher').click()
    await page.getByLabel(/Password/i).fill('password123')
    await page.getByLabel(/Confirm Password/i).fill('password123')
    
    // Submit form
    await page.getByRole('button', { name: /Create/i }).click()
    
    // Should show success message
    await expect(page.getByText(/User created successfully/i)).toBeVisible()
    
    // Dialog should close
    await expect(page.getByRole('dialog')).not.toBeVisible()
    
    // User should appear in the list
    await expect(page.getByText('Test User')).toBeVisible()
    await expect(page.getByText(testEmail)).toBeVisible()
  })

  test('should filter users by role and status', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/users')
    
    // Wait for users to load
    await page.waitForSelector('table', { timeout: 10000 })
    
    // Test role filter
    await page.getByLabel(/Role/i).click()
    await page.getByText('Teacher').click()
    
    // Should show only teachers
    await page.waitForTimeout(1000) // Wait for filter to apply
    
    // Test status filter
    await page.getByLabel(/Status/i).click()
    await page.getByText('Active').click()
    
    // Should show only active users
    await page.waitForTimeout(1000) // Wait for filter to apply
    
    // Reset filters
    await page.getByLabel(/Role/i).click()
    await page.getByText('All Roles').click()
    
    await page.getByLabel(/Status/i).click()
    await page.getByText('All Status').click()
  })

  test('should search users by name and email', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/users')
    
    // Wait for users to load
    await page.waitForSelector('table', { timeout: 10000 })
    
    // Search by name
    await page.getByPlaceholder(/Search users/i).fill('Admin')
    await page.waitForTimeout(1000) // Wait for search to apply
    
    // Should show matching users
    await expect(page.getByText(/Admin/i)).toBeVisible()
    
    // Clear search
    await page.getByPlaceholder(/Search users/i).clear()
    await page.waitForTimeout(1000)
  })

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API to return error
    await page.route('**/api/coaching/users', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      })
    })
    
    await page.goto('http://abc.localhost:5174/coaching/users')
    
    // Should show error message
    await expect(page.getByText(/Failed to fetch users/i)).toBeVisible()
  })

  test('should display proper role badges and status indicators', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/users')
    
    // Wait for users to load
    await page.waitForSelector('table', { timeout: 10000 })
    
    // Check for role badges (should have different colors)
    const roleBadges = page.locator('[class*="bg-purple-500"], [class*="bg-blue-500"], [class*="bg-orange-500"], [class*="bg-gray-500"]')
    await expect(roleBadges.first()).toBeVisible()
    
    // Check for status badges
    const statusBadges = page.locator('[class*="bg-green-500"]')
    await expect(statusBadges.first()).toBeVisible()
  })

  test('should support Bengali/English translations', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/users')
    
    // Check if language toggle exists
    const languageToggle = page.locator('button:has-text("EN"), button:has-text("BD")')
    if (await languageToggle.count() > 0) {
      // Test language switching
      await languageToggle.click()
      await page.waitForTimeout(1000)
      
      // Check if text changed (this will depend on current language)
      // The test should pass regardless of which language is active
      await expect(page.locator('h1')).toBeVisible()
    }
  })

  test('should be mobile responsive', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('http://abc.localhost:5174/coaching/users')
    
    // Check if page loads properly on mobile
    await expect(page.locator('h1')).toBeVisible()
    await expect(page.getByRole('button', { name: /Add User/i })).toBeVisible()
    
    // Check if table is responsive (might be scrollable or stacked)
    await expect(page.locator('table')).toBeVisible()
  })
})
