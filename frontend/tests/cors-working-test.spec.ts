import { test, expect } from '@playwright/test'

test.describe('CORS Working Verification', () => {
  test('should verify CORS is working - center login endpoint', async ({ page }) => {
    // Test the center-login endpoint directly with proper origin
    const response = await page.request.post('http://localhost:3000/api/auth/center-login', {
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://abc.localhost:5174'
      },
      data: {
        email: '<EMAIL>',
        password: 'password123'
      }
    })
    
    console.log('✅ Center-login API response status:', response.status())
    
    // Should get a successful response (200), not CORS blocked
    expect(response.status()).toBe(200)
    
    const responseBody = await response.json()
    console.log('✅ Response contains token:', !!responseBody.token)
    expect(responseBody.token).toBeDefined()
  })

  test('should verify CORS preflight works', async ({ page }) => {
    // Test preflight OPTIONS request
    const response = await page.request.fetch('http://localhost:3000/api/auth/center-login', {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://abc.localhost:5174',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    })
    
    console.log('✅ Preflight OPTIONS response status:', response.status())
    
    // Preflight should succeed with 204
    expect(response.status()).toBe(204)
  })

  test('should verify file upload endpoint is accessible', async ({ page }) => {
    // Test file upload endpoint (should return 401 unauthorized, not CORS blocked)
    const response = await page.request.post('http://localhost:3000/api/coaching/settings/upload-image', {
      headers: {
        'Origin': 'http://abc.localhost:5174'
      },
      multipart: {
        type: 'logo'
      }
    })
    
    console.log('✅ Upload endpoint response status:', response.status())
    
    // Should get unauthorized (401), not CORS error
    expect(response.status()).toBe(401)
  })

  test('should verify settings API is accessible', async ({ page }) => {
    // Test settings GET endpoint
    const response = await page.request.get('http://localhost:3000/api/coaching/settings', {
      headers: {
        'Origin': 'http://abc.localhost:5174'
      }
    })
    
    console.log('✅ Settings API response status:', response.status())
    
    // Should get unauthorized (401), not CORS error
    expect(response.status()).toBe(401)
  })

  test('should verify static file serving works', async ({ page }) => {
    // Test static file serving
    const response = await page.request.get('http://localhost:3000/uploads/images/test.jpg', {
      headers: {
        'Origin': 'http://abc.localhost:5174'
      }
    })
    
    console.log('✅ Static file response status:', response.status())
    
    // Should get 404 (not found), not CORS error
    expect(response.status()).toBe(404)
  })

  test('should verify complete login flow works in browser', async ({ page }) => {
    // Navigate to login page
    await page.goto('http://abc.localhost:5174/login')
    
    // Wait for page to load
    await page.waitForTimeout(3000)
    
    // Check if login form is visible
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('input[type="password"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toBeVisible()
    
    // Fill login form
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    
    // Submit login
    await page.click('button[type="submit"]')
    
    // Wait for response
    await page.waitForTimeout(5000)
    
    // Check if we're redirected to dashboard (successful login)
    const currentUrl = page.url()
    console.log('✅ Current URL after login:', currentUrl)
    
    // Should be redirected to dashboard or show success
    const isOnDashboard = currentUrl.includes('/coaching/dashboard')
    const hasSuccessIndicator = await page.locator('text=Dashboard').isVisible().catch(() => false)
    
    console.log('✅ Is on dashboard:', isOnDashboard)
    console.log('✅ Has dashboard content:', hasSuccessIndicator)
    
    // Login should work without CORS errors
    expect(isOnDashboard || hasSuccessIndicator).toBe(true)
  })

  test('should verify no CORS errors in browser console', async ({ page }) => {
    const consoleErrors: string[] = []
    
    // Capture console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })
    
    // Navigate to login page and perform login
    await page.goto('http://abc.localhost:5174/login')
    await page.waitForTimeout(2000)
    
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    // Wait for any console errors
    await page.waitForTimeout(3000)
    
    // Filter for CORS-related errors
    const corsErrors = consoleErrors.filter(error => 
      error.includes('CORS') || 
      error.includes('Access-Control-Allow-Origin') ||
      error.includes('blocked by CORS policy')
    )
    
    console.log('✅ Total console errors:', consoleErrors.length)
    console.log('✅ CORS-related errors:', corsErrors.length)
    
    if (corsErrors.length > 0) {
      console.log('❌ CORS errors found:', corsErrors)
    }
    
    // Should have no CORS errors
    expect(corsErrors.length).toBe(0)
  })
})
