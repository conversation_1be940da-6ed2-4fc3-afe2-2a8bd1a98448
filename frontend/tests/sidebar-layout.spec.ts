import { test, expect } from '@playwright/test';

test.describe('Sidebar Layout', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            role: 'center_admin'
          }
        })
      });
    });
  });

  test('should display sidebar with navigation items', async ({ page }) => {
    await page.goto('http://localhost:5173/students');

    // Check sidebar is visible
    await expect(page.locator('.w-64')).toBeVisible();

    // Check logo
    await expect(page.locator('text=TeachingCenter')).toBeVisible();

    // Check main navigation items in sidebar
    await expect(page.locator('.w-64 button:has-text("Dashboard")')).toBeVisible();
    await expect(page.locator('.w-64 button:has-text("Student Management")')).toBeVisible();
    await expect(page.locator('.w-64 button:has-text("Academic Management")')).toBeVisible();
    await expect(page.locator('.w-64 button:has-text("Staff Management")')).toBeVisible();
    await expect(page.locator('.w-64 button:has-text("Operations")')).toBeVisible();
    await expect(page.locator('.w-64 button:has-text("Financial")')).toBeVisible();
  });

  test('should expand and collapse menu items', async ({ page }) => {
    await page.goto('http://localhost:5173/students');

    // Click on Student Management to expand
    await page.click('button:has-text("Student Management")');

    // Check submenu items are visible
    await expect(page.locator('text=All Students')).toBeVisible();
    await expect(page.locator('text=Admissions')).toBeVisible();
    await expect(page.locator('text=Student Reports')).toBeVisible();

    // Click again to collapse
    await page.click('button:has-text("Student Management")');

    // Check submenu items are hidden
    await expect(page.locator('text=All Students')).not.toBeVisible();
  });

  test('should navigate to students page from sidebar', async ({ page }) => {
    // Mock students API
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.goto('http://localhost:5173/students');

    // Expand Student Management
    await page.click('button:has-text("Student Management")');
    
    // Click on All Students
    await page.click('text=All Students');
    
    // Should navigate to students page
    await expect(page).toHaveURL('http://localhost:5173/students');
    await expect(page.locator('text=Student Management')).toBeVisible();
  });

  test('should navigate to courses page from sidebar', async ({ page }) => {
    // Mock courses API
    await page.route('**/api/courses', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ courses: [] })
      });
    });

    await page.goto('http://localhost:5173/students');

    // Expand Academic Management
    await page.click('button:has-text("Academic Management")');
    
    // Click on Courses
    await page.click('text=Courses');
    
    // Should navigate to courses page
    await expect(page).toHaveURL('http://localhost:5173/courses');
    await expect(page.locator('text=Course Management')).toBeVisible();
  });

  test('should navigate to branches page from sidebar', async ({ page }) => {
    // Mock branches API
    await page.route('**/api/branches', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ branches: [] })
      });
    });

    await page.goto('http://localhost:5173/students');

    // Expand Operations
    await page.click('button:has-text("Operations")');
    
    // Click on Branches
    await page.click('text=Branches');
    
    // Should navigate to branches page
    await expect(page).toHaveURL('http://localhost:5173/branches');
    await expect(page.locator('text=Branch Management')).toBeVisible();
  });

  test('should show mobile menu button on small screens', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('http://localhost:5173/dashboard');

    // Mobile menu button should be visible
    await expect(page.locator('button:has([data-testid="menu-icon"])').or(page.locator('button:has(.lucide-menu)'))).toBeVisible();
    
    // Sidebar should be hidden initially on mobile
    await expect(page.locator('.w-64')).not.toBeVisible();
  });

  test('should toggle mobile sidebar', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('http://localhost:5173/dashboard');

    // Click mobile menu button
    await page.click('button:has(.lucide-menu)');
    
    // Sidebar should become visible
    await expect(page.locator('.w-64')).toBeVisible();
    
    // Click close button or overlay
    await page.click('button:has(.lucide-x)');
    
    // Sidebar should be hidden again
    await expect(page.locator('.w-64')).not.toBeVisible();
  });

  test('should highlight active menu item', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Dashboard should be highlighted as active
    await expect(page.locator('a[href="/dashboard"]')).toHaveClass(/secondary/);
  });

  test('should display user info in sidebar footer', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Check user info is displayed
    await expect(page.locator('text=Center Admin')).toBeVisible();
    await expect(page.locator('text=ABC Coaching Center')).toBeVisible();
  });

  test('should toggle theme from header', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Check initial theme
    await expect(page.locator('html')).not.toHaveClass(/dark/);
    
    // Click theme toggle
    await page.click('button:has(.sr-only:text("Toggle theme"))');
    
    // Check dark theme is applied
    await expect(page.locator('html')).toHaveClass(/dark/);
  });

  test('should logout from header', async ({ page }) => {
    // Mock logout response
    await page.route('**/api/auth/logout', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });

    await page.goto('http://localhost:5173/dashboard');

    // Click logout button
    await page.click('button:has(.sr-only:text("Sign out"))');

    // Should redirect to login page
    await expect(page).toHaveURL('http://localhost:5173/login');
  });

  test('should display correct title in header', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Check page title
    await expect(page.locator('h1:has-text("Dashboard")')).toBeVisible();
  });

  test('should show language toggle button', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard');

    // Check language toggle is visible
    await expect(page.locator('button:has(.sr-only:text("Switch language"))').or(page.locator('button:has(.lucide-globe)'))).toBeVisible();
  });
});

test.describe('Admin Sidebar Layout', () => {
  test.beforeEach(async ({ page }) => {
    // Mock admin authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'super_admin'
          }
        })
      });
    });
  });

  test('should display admin sidebar with different navigation items', async ({ page }) => {
    await page.goto('http://localhost:5173/admin/dashboard');

    // Check admin-specific navigation items
    await expect(page.locator('text=Centers Management')).toBeVisible();
    await expect(page.locator('text=User Management')).toBeVisible();
    await expect(page.locator('text=Analytics & Reports')).toBeVisible();
    await expect(page.locator('text=System')).toBeVisible();
  });

  test('should expand admin menu items', async ({ page }) => {
    await page.goto('http://localhost:5173/admin/dashboard');

    // Click on Centers Management to expand
    await page.click('button:has-text("Centers Management")');
    
    // Check admin submenu items are visible
    await expect(page.locator('text=All Centers')).toBeVisible();
    await expect(page.locator('text=Subscriptions')).toBeVisible();
    await expect(page.locator('text=Plans')).toBeVisible();
  });

  test('should show admin user info', async ({ page }) => {
    await page.goto('http://localhost:5173/admin/dashboard');

    // Check admin user info
    await expect(page.locator('text=Super Admin')).toBeVisible();
    await expect(page.locator('text=Platform Administrator')).toBeVisible();
  });
});
