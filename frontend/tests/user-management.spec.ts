import { test, expect } from '@playwright/test';

test.describe('User Management System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to coaching center login
    await page.goto('http://abc.localhost:5173/coaching/login');
    
    // Login as center admin
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/coaching/dashboard');
  });

  test('should have user management menu item', async ({ page }) => {
    console.log('🧪 Testing user management menu access...');
    
    // Look for user management in sidebar or menu
    const userMenu = page.locator('a[href*="user"], a:has-text("ব্যবহারকারী"), a:has-text("User"), a:has-text("Staff")');
    await expect(userMenu).toBeVisible({ timeout: 5000 });
    
    console.log('✅ User management menu item is visible');
  });

  test('should navigate to user management page', async ({ page }) => {
    console.log('🧪 Testing user management page navigation...');
    
    // Click on user management menu
    const userMenu = page.locator('a[href*="user"], a:has-text("ব্যবহারকারী"), a:has-text("User"), a:has-text("Staff")');
    await userMenu.click();
    
    // Wait for user management page to load
    await page.waitForURL('**/user*');
    
    // Check if user management page elements are present
    const pageTitle = page.locator('h1:has-text("ব্যবহারকারী"), h1:has-text("User"), h2:has-text("ব্যবহারকারী"), h2:has-text("User")');
    await expect(pageTitle).toBeVisible();
    
    console.log('✅ User management page loads successfully');
  });

  test('should display list of users', async ({ page }) => {
    console.log('🧪 Testing user list display...');
    
    // Navigate to user management
    await page.goto('http://abc.localhost:5173/coaching/users');
    
    // Check if user table/list is displayed
    const userTable = page.locator('table, .user-list, .user-grid');
    await expect(userTable).toBeVisible({ timeout: 5000 });
    
    // Check for user entries
    const userEntries = page.locator('tr, .user-card, .user-item');
    const userCount = await userEntries.count();
    expect(userCount).toBeGreaterThan(1); // At least header + 1 user
    
    console.log('✅ User list is displayed');
  });

  test('should allow creating new user', async ({ page }) => {
    console.log('🧪 Testing new user creation...');
    
    // Navigate to user management
    await page.goto('http://abc.localhost:5173/coaching/users');
    
    // Click add user button
    const addButton = page.locator('button:has-text("নতুন"), button:has-text("Add"), button:has-text("Create"), button:has-text("যোগ")');
    await addButton.click();
    
    // Fill user form
    const nameField = page.locator('input[name="name"], input[placeholder*="name"], input[placeholder*="নাম"]');
    if (await nameField.isVisible({ timeout: 5000 })) {
      await nameField.fill('Test Teacher');
    }
    
    const emailField = page.locator('input[name="email"], input[type="email"]');
    if (await emailField.isVisible()) {
      await emailField.fill('<EMAIL>');
    }
    
    const roleField = page.locator('select[name="role"], select:has(option:text("Teacher"))');
    if (await roleField.isVisible()) {
      await roleField.selectOption('teacher');
    }
    
    // Submit form
    const submitButton = page.locator('button[type="submit"], button:has-text("সংরক্ষণ"), button:has-text("Save")');
    if (await submitButton.isVisible()) {
      await submitButton.click();
      
      // Check for success message
      const successMessage = page.locator('.toast, .alert, .notification');
      await expect(successMessage).toBeVisible({ timeout: 5000 });
      
      console.log('✅ New user created successfully');
    } else {
      console.log('⚠️ Submit button not found - form may need completion');
    }
  });

  test('should support role-based permissions', async ({ page }) => {
    console.log('🧪 Testing role-based permissions...');
    
    // Navigate to user management
    await page.goto('http://abc.localhost:5173/coaching/users');
    
    // Look for role selection or display
    const roleElements = page.locator('select:has(option:text("Teacher")), select:has(option:text("Admin")), text="teacher", text="admin", .role-badge');
    
    const roleCount = await roleElements.count();
    expect(roleCount).toBeGreaterThan(0);
    
    // Check for different role types
    const teacherRole = page.locator('text="teacher", text="Teacher", option:text("Teacher")');
    const adminRole = page.locator('text="admin", text="Admin", option:text("Admin")');
    
    const hasTeacherRole = await teacherRole.isVisible();
    const hasAdminRole = await adminRole.isVisible();
    
    expect(hasTeacherRole || hasAdminRole).toBeTruthy();
    
    console.log('✅ Role-based permissions are supported');
  });

  test('should allow editing user information', async ({ page }) => {
    console.log('🧪 Testing user editing functionality...');
    
    // Navigate to user management
    await page.goto('http://abc.localhost:5173/coaching/users');
    
    // Click edit button for first user
    const editButton = page.locator('button:has-text("সম্পাদনা"), button:has-text("Edit"), .edit-btn').first();
    
    if (await editButton.isVisible({ timeout: 5000 })) {
      await editButton.click();
      
      // Wait for edit form
      await page.waitForTimeout(2000);
      
      // Modify user information
      const nameField = page.locator('input[name="name"], input[value*="Teacher"], input[value*="Admin"]');
      if (await nameField.isVisible()) {
        await nameField.fill('Updated Teacher Name');
      }
      
      // Save changes
      const saveButton = page.locator('button:has-text("সংরক্ষণ"), button:has-text("Save"), button:has-text("Update")');
      if (await saveButton.isVisible()) {
        await saveButton.click();
        
        // Check for success message
        const successMessage = page.locator('.toast, .alert, .notification');
        await expect(successMessage).toBeVisible({ timeout: 5000 });
        
        console.log('✅ User information updated successfully');
      }
    } else {
      console.log('⚠️ Edit button not found - may need implementation');
    }
  });

  test('should support user enable/disable functionality', async ({ page }) => {
    console.log('🧪 Testing user enable/disable functionality...');
    
    // Navigate to user management
    await page.goto('http://abc.localhost:5173/coaching/users');
    
    // Look for enable/disable buttons
    const toggleButton = page.locator('button:has-text("নিষ্ক্রিয়"), button:has-text("সক্রিয়"), button:has-text("Disable"), button:has-text("Enable")');
    
    if (await toggleButton.first().isVisible({ timeout: 5000 })) {
      await toggleButton.first().click();
      
      // Wait for confirmation if needed
      const confirmButton = page.locator('button:has-text("নিশ্চিত"), button:has-text("Confirm"), button:has-text("Yes")');
      if (await confirmButton.isVisible({ timeout: 2000 })) {
        await confirmButton.click();
      }
      
      // Check for success message
      const successMessage = page.locator('.toast, .alert, .notification');
      await expect(successMessage).toBeVisible({ timeout: 5000 });
      
      console.log('✅ User enable/disable functionality works');
    } else {
      console.log('⚠️ Enable/disable buttons not found - may need implementation');
    }
  });

  test('should support password reset functionality', async ({ page }) => {
    console.log('🧪 Testing password reset functionality...');
    
    // Navigate to user management
    await page.goto('http://abc.localhost:5173/coaching/users');
    
    // Look for password reset button
    const resetButton = page.locator('button:has-text("পাসওয়ার্ড"), button:has-text("Password"), button:has-text("Reset")');
    
    if (await resetButton.first().isVisible({ timeout: 5000 })) {
      await resetButton.first().click();
      
      // Check for password reset dialog or form
      const resetDialog = page.locator('.modal, .dialog, [role="dialog"]');
      await expect(resetDialog).toBeVisible({ timeout: 3000 });
      
      // Fill new password if form is present
      const newPasswordField = page.locator('input[type="password"], input[name*="password"]');
      if (await newPasswordField.first().isVisible({ timeout: 2000 })) {
        await newPasswordField.first().fill('newpassword123');
        
        if (await newPasswordField.nth(1).isVisible()) {
          await newPasswordField.nth(1).fill('newpassword123');
        }
        
        // Submit password reset
        const submitButton = page.locator('button:has-text("রিসেট"), button:has-text("Reset"), button:has-text("Update")');
        if (await submitButton.isVisible()) {
          await submitButton.click();
          
          // Check for success message
          const successMessage = page.locator('.toast, .alert, .notification');
          await expect(successMessage).toBeVisible({ timeout: 5000 });
        }
      }
      
      console.log('✅ Password reset functionality is available');
    } else {
      console.log('⚠️ Password reset button not found - may need implementation');
    }
  });

  test('should display user activity logs', async ({ page }) => {
    console.log('🧪 Testing user activity logging...');
    
    // Navigate to user management
    await page.goto('http://abc.localhost:5173/coaching/users');
    
    // Look for activity logs or history
    const activitySection = page.locator('text="কার্যকলাপ", text="Activity", text="Log", text="History"');
    
    if (await activitySection.isVisible({ timeout: 5000 })) {
      // Click on activity section
      await activitySection.click();
      
      // Check for activity entries
      const activityEntries = page.locator('.activity-item, .log-entry, .history-item');
      const entryCount = await activityEntries.count();
      expect(entryCount).toBeGreaterThan(0);
      
      console.log('✅ User activity logs are displayed');
    } else {
      // Look for activity in user details
      const userRow = page.locator('tr, .user-card').first();
      if (await userRow.isVisible()) {
        await userRow.click();
        
        // Check for activity in user details
        const activityInDetails = page.locator('text="কার্যকলাপ", text="Activity", text="Log"');
        const hasActivity = await activityInDetails.isVisible({ timeout: 3000 });
        expect(hasActivity).toBeTruthy();
        
        console.log('✅ User activity is tracked in user details');
      }
    }
  });

  test('should filter users by role', async ({ page }) => {
    console.log('🧪 Testing user filtering by role...');
    
    // Navigate to user management
    await page.goto('http://abc.localhost:5173/coaching/users');
    
    // Look for role filter
    const roleFilter = page.locator('select:has(option:text("Teacher")), select:has(option:text("Admin")), button:has-text("Filter")');
    
    if (await roleFilter.isVisible({ timeout: 5000 })) {
      // Filter by teacher role
      if (await page.locator('select').isVisible()) {
        await page.selectOption('select', 'teacher');
      } else {
        await roleFilter.click();
        const teacherOption = page.locator('text="Teacher", text="শিক্ষক"');
        if (await teacherOption.isVisible()) {
          await teacherOption.click();
        }
      }
      
      // Wait for filter to apply
      await page.waitForTimeout(2000);
      
      // Check if only teachers are shown
      const adminUsers = page.locator('text="admin", text="Admin"');
      const adminCount = await adminUsers.count();
      
      // After filtering for teachers, admin count should be reduced
      expect(adminCount).toBeLessThanOrEqual(1); // May still show in headers
      
      console.log('✅ User filtering by role works');
    } else {
      console.log('⚠️ Role filter not found - may need implementation');
    }
  });

  test('should validate user creation form', async ({ page }) => {
    console.log('🧪 Testing user creation validation...');
    
    // Navigate to user management
    await page.goto('http://abc.localhost:5173/coaching/users');
    
    // Click add user button
    const addButton = page.locator('button:has-text("নতুন"), button:has-text("Add"), button:has-text("Create")');
    
    if (await addButton.isVisible({ timeout: 5000 })) {
      await addButton.click();
      
      // Try to submit empty form
      const submitButton = page.locator('button[type="submit"], button:has-text("সংরক্ষণ"), button:has-text("Save")');
      if (await submitButton.isVisible({ timeout: 3000 })) {
        await submitButton.click();
        
        // Check for validation errors
        const errorMessage = page.locator('.error, .alert-error, text="required", text="প্রয়োজন"');
        const hasValidation = await errorMessage.isVisible({ timeout: 3000 });
        expect(hasValidation).toBeTruthy();
        
        console.log('✅ User creation form validation works');
      }
    } else {
      console.log('⚠️ Add user button not found - may need implementation');
    }
  });

  test('should show user permissions and access levels', async ({ page }) => {
    console.log('🧪 Testing user permissions display...');
    
    // Navigate to user management
    await page.goto('http://abc.localhost:5173/coaching/users');
    
    // Look for permissions or access level information
    const permissionElements = page.locator('text="Permission", text="Access", text="অনুমতি", text="অ্যাক্সেস", .permission-badge, .access-level');
    
    const permissionCount = await permissionElements.count();
    
    if (permissionCount > 0) {
      console.log('✅ User permissions are displayed');
    } else {
      // Check in user details
      const userRow = page.locator('tr, .user-card').first();
      if (await userRow.isVisible()) {
        await userRow.click();
        
        const permissionInDetails = page.locator('text="Permission", text="অনুমতি", text="Role", text="ভূমিকা"');
        const hasPermissionInfo = await permissionInDetails.isVisible({ timeout: 3000 });
        expect(hasPermissionInfo).toBeTruthy();
        
        console.log('✅ User permissions are shown in user details');
      }
    }
  });
});
