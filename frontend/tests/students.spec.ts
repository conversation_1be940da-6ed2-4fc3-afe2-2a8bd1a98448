import { test, expect } from '@playwright/test';

test.describe('Students Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            role: 'center_admin'
          }
        })
      });
    });

    await page.goto('http://localhost:5173/students');
  });

  test('should display students page elements', async ({ page }) => {
    // Check page title and description
    await expect(page.locator('h1.text-3xl')).toContainText('Students');
    await expect(page.locator('text=Manage your student database')).toBeVisible();
    
    // Check Add Student button (use first one)
    await expect(page.locator('button:has-text("Add Student")').first()).toBeVisible();
    
    // Check search bar
    await expect(page.locator('input[placeholder*="Search students"]')).toBeVisible();
  });

  test('should show empty state when no students', async ({ page }) => {
    // Mock empty students response
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.reload();

    // Check empty state
    await expect(page.locator('text=No students found')).toBeVisible();
    await expect(page.locator('text=Get started by adding your first student')).toBeVisible();
  });

  test('should display students list', async ({ page }) => {
    // Mock students response
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            {
              id: 1,
              name: 'John Doe',
              email: '<EMAIL>',
              phone: '************',
              enrollment_date: '2024-01-15',
              status: 'active'
            },
            {
              id: 2,
              name: 'Jane Smith',
              email: '<EMAIL>',
              phone: '************',
              enrollment_date: '2024-02-01',
              status: 'active'
            }
          ]
        })
      });
    });

    await page.reload();

    // Check students are displayed
    await expect(page.locator('text=John Doe')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    await expect(page.locator('text=Jane Smith')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
  });

  test('should filter students by search term', async ({ page }) => {
    // Mock students response
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            {
              id: 1,
              name: 'John Doe',
              email: '<EMAIL>',
              phone: '************',
              enrollment_date: '2024-01-15',
              status: 'active'
            },
            {
              id: 2,
              name: 'Jane Smith',
              email: '<EMAIL>',
              phone: '************',
              enrollment_date: '2024-02-01',
              status: 'active'
            }
          ]
        })
      });
    });

    await page.reload();

    // Search for John
    await page.fill('input[placeholder*="Search students"]', 'John');
    
    // Should show John but not Jane
    await expect(page.locator('text=John Doe')).toBeVisible();
    await expect(page.locator('text=Jane Smith')).not.toBeVisible();
  });

  test('should open add student form', async ({ page }) => {
    // Mock empty students response
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.reload();

    // Click Add Student button
    await page.click('button:has-text("Add Student")');

    // Check form is visible
    await expect(page.locator('text=Add New Student')).toBeVisible();
    await expect(page.locator('input[id="name"]')).toBeVisible();
    await expect(page.locator('input[id="email"]')).toBeVisible();
    await expect(page.locator('input[id="phone"]')).toBeVisible();
    await expect(page.locator('input[id="date_of_birth"]')).toBeVisible();
  });

  test('should submit new student form', async ({ page }) => {
    // Mock students response
    await page.route('**/api/students', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ students: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            student: {
              id: 1,
              name: 'New Student',
              email: '<EMAIL>',
              phone: '555-0123',
              enrollment_date: '2024-01-01',
              status: 'active'
            }
          })
        });
      }
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Student")');

    // Fill form
    await page.fill('input[id="name"]', 'New Student');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="phone"]', '555-0123');
    await page.fill('input[id="date_of_birth"]', '2000-01-01');

    // Submit form
    await page.click('button[type="submit"]');

    // Form should close
    await expect(page.locator('text=Add New Student')).not.toBeVisible();
  });

  test('should handle form validation', async ({ page }) => {
    // Mock empty students response
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Student")');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Check HTML5 validation
    const nameInput = page.locator('input[id="name"]');
    const emailInput = page.locator('input[id="email"]');
    
    await expect(nameInput).toHaveAttribute('required');
    await expect(emailInput).toHaveAttribute('required');
  });

  test('should cancel form', async ({ page }) => {
    // Mock empty students response
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Student")');

    // Fill some data
    await page.fill('input[id="name"]', 'Test Student');

    // Cancel form
    await page.click('button:has-text("Cancel")');

    // Form should close and data should be cleared
    await expect(page.locator('text=Add New Student')).not.toBeVisible();
  });

  test('should edit student', async ({ page }) => {
    // Mock students response
    await page.route('**/api/students', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            students: [
              {
                id: 1,
                name: 'John Doe',
                email: '<EMAIL>',
                phone: '************',
                enrollment_date: '2024-01-15',
                status: 'active'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/students/1', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            student: {
              id: 1,
              name: 'John Updated',
              email: '<EMAIL>',
              phone: '************',
              enrollment_date: '2024-01-15',
              status: 'active'
            }
          })
        });
      }
    });

    await page.reload();

    // Click edit button for first student (first icon button in the student card)
    await page.locator('[data-testid="student-card"]').first().locator('button').first().click();

    // Check edit form is visible with pre-filled data
    await expect(page.locator('text=Edit Student')).toBeVisible();
    await expect(page.locator('input[id="name"]')).toHaveValue('John Doe');
    await expect(page.locator('input[id="email"]')).toHaveValue('<EMAIL>');

    // Update name
    await page.fill('input[id="name"]', 'John Updated');
    await page.fill('input[id="email"]', '<EMAIL>');

    // Submit form
    await page.click('button:has-text("Update Student")');

    // Form should close
    await expect(page.locator('text=Edit Student')).not.toBeVisible();
  });

  test('should delete student', async ({ page }) => {
    // Mock students response
    await page.route('**/api/students', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            students: [
              {
                id: 1,
                name: 'John Doe',
                email: '<EMAIL>',
                phone: '************',
                enrollment_date: '2024-01-15',
                status: 'active'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/students/1', async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        });
      }
    });

    await page.reload();

    // Set up dialog handler for confirmation
    let dialogMessage = '';
    page.on('dialog', async dialog => {
      dialogMessage = dialog.message();
      await dialog.accept();
    });

    // Click delete button (second icon button in the student card)
    await page.locator('[data-testid="student-card"]').first().locator('button').nth(1).click();

    // Wait for dialog
    await page.waitForTimeout(500);

    // Check confirmation dialog appeared
    expect(dialogMessage).toContain('Are you sure you want to delete');
  });

  test('should toggle theme', async ({ page }) => {
    // Check initial theme
    await expect(page.locator('html')).not.toHaveClass(/dark/);
    
    // Click theme toggle
    await page.click('button:has(.sr-only:text("Toggle theme"))');
    
    // Check dark theme is applied
    await expect(page.locator('html')).toHaveClass(/dark/);
  });

  test('should logout', async ({ page }) => {
    // Mock logout response
    await page.route('**/api/auth/logout', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });

    // Click logout button
    await page.click('button:has(.sr-only:text("Sign out"))');

    // Should redirect to login page
    await expect(page).toHaveURL('http://localhost:5173/login');
  });
});
