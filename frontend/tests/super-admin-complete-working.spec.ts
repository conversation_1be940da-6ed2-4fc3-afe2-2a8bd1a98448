import { test, expect } from '@playwright/test';

test.describe('Super Admin Complete Working System', () => {
  test.beforeEach(async ({ page }) => {
    // Set authentication token in localStorage
    await page.goto('http://localhost:5173');
    await page.evaluate(() => {
      localStorage.setItem('auth_token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************.TLt7bx_rLsyzVKQz3E1scR6Ide2JbIrmhNWZgCk5Nzs');
    });

    // Mock all API endpoints to simulate working backend
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 14,
            name: 'Super Admin',
            email: '<EMAIL>',
            role: 'super_admin',
            tenant_id: null
          }
        })
      });
    });

    await page.route('**/api/admin/stats', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            totalCenters: 12,
            activeCenters: 10,
            suspendedCenters: 2,
            totalUsers: 450,
            totalRevenue: 125000,
            monthlyGrowth: 15.8,
            newCentersThisMonth: 3,
            activeUsersToday: 89
          }
        })
      });
    });

    await page.route('**/api/admin/centers', async route => {
      const method = route.request().method();
      
      if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            centers: [
              {
                id: 1,
                name: 'ABC Coaching Center',
                slug: 'abc-coaching',
                email: '<EMAIL>',
                phone: '1234567890',
                address: '123 Main St, City',
                status: 'active',
                subscription_plan: 'premium',
                created_at: '2024-01-01T00:00:00Z',
                admin_name: 'John Doe',
                total_students: 45,
                total_teachers: 8
              },
              {
                id: 2,
                name: 'XYZ Learning Academy',
                slug: 'xyz-academy',
                email: '<EMAIL>',
                phone: '0987654321',
                address: '456 Oak Ave, Town',
                status: 'active',
                subscription_plan: 'basic',
                created_at: '2024-01-15T00:00:00Z',
                admin_name: 'Jane Smith',
                total_students: 32,
                total_teachers: 5
              },
              {
                id: 3,
                name: 'Elite Education Hub',
                slug: 'elite-education',
                email: '<EMAIL>',
                phone: '5555555555',
                address: '789 Pine St, Village',
                status: 'suspended',
                subscription_plan: 'enterprise',
                created_at: '2024-02-01T00:00:00Z',
                admin_name: 'Mike Johnson',
                total_students: 78,
                total_teachers: 12
              }
            ]
          })
        });
      } else if (method === 'POST') {
        const body = await route.request().postData();
        const data = JSON.parse(body || '{}');
        
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: 'Coaching center created successfully',
            center: {
              id: 4,
              name: data.name,
              slug: data.slug,
              email: data.email,
              phone: data.phone,
              address: data.address,
              status: 'active',
              subscription_plan: data.subscription_plan || 'basic',
              created_at: new Date().toISOString(),
              admin_name: data.adminName,
              total_students: 0,
              total_teachers: 0
            }
          })
        });
      }
    });

    await page.route('**/api/admin/centers/*/status', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Center status updated successfully'
        })
      });
    });
  });

  test('✅ Super Admin Dashboard - Complete System Overview', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard?dev_token=super_admin');

    // Verify dashboard loads
    await expect(page.locator('h1')).toContainText('Super Admin Dashboard');
    
    // Verify user authentication
    await expect(page.locator('text=Super Admin')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();

    // Verify system statistics
    await expect(page.locator('text=12')).toBeVisible(); // Total centers
    await expect(page.locator('text=450')).toBeVisible(); // Total users
    await expect(page.locator('text=₹1,25,000')).toBeVisible(); // Revenue
    await expect(page.locator('text=15.8%')).toBeVisible(); // Growth
  });

  test('✅ Coaching Centers Management - Full CRUD Operations', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard?dev_token=super_admin');

    // Verify centers list displays
    await expect(page.locator('text=ABC Coaching Center')).toBeVisible();
    await expect(page.locator('text=XYZ Learning Academy')).toBeVisible();
    await expect(page.locator('text=Elite Education Hub')).toBeVisible();

    // Verify center details
    await expect(page.locator('text=abc-coaching')).toBeVisible();
    await expect(page.locator('text=45 students')).toBeVisible();
    await expect(page.locator('text=8 teachers')).toBeVisible();

    // Verify status indicators
    await expect(page.locator('text=Active').first()).toBeVisible();
    await expect(page.locator('text=Suspended')).toBeVisible();
  });

  test('✅ Add New Coaching Center - Complete Form Workflow', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard?dev_token=super_admin');

    // Open add center modal
    await page.click('text=Add Coaching Center');
    await expect(page.locator('text=Add New Coaching Center')).toBeVisible();

    // Fill center information
    await page.fill('input[name="name"]', 'Test Academy Pro');
    await expect(page.locator('input[name="slug"]')).toHaveValue('test-academy-pro');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '******-TEST-PRO');
    await page.fill('textarea[name="address"]', '123 Test Street, Test City, TC 12345');

    // Fill admin information
    await page.fill('input[name="adminName"]', 'Test Admin');
    await page.fill('input[name="adminEmail"]', '<EMAIL>');
    await page.fill('input[name="adminPassword"]', 'securepass123');

    // Select subscription plan
    await page.selectOption('select[name="subscription_plan"]', 'premium');

    // Verify form validation
    await expect(page.locator('text=Will be accessible at: test-academy-pro.localhost:5173')).toBeVisible();

    // Submit form
    await page.click('button[type="submit"]');
    await expect(page.locator('text=Center created successfully')).toBeVisible();
  });

  test('✅ Center Status Management - Activate/Suspend Operations', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard?dev_token=super_admin');

    // Find suspended center and activate it
    const suspendedCenter = page.locator('text=Elite Education Hub').locator('..');
    await suspendedCenter.locator('button').first().click();
    await expect(page.locator('text=Status updated successfully')).toBeVisible();

    // Find active center and suspend it
    const activeCenter = page.locator('text=ABC Coaching Center').locator('..');
    await activeCenter.locator('button').first().click();
    await expect(page.locator('text=Status updated successfully')).toBeVisible();
  });

  test('✅ Navigation & Sidebar - Complete Menu System', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard?dev_token=super_admin');

    // Test main navigation items
    const navItems = [
      'Dashboard',
      'Coaching Centers', 
      'User Management',
      'Analytics & Reports',
      'System'
    ];

    for (const item of navItems) {
      await expect(page.locator(`text=${item}`)).toBeVisible();
    }

    // Test theme toggle
    await page.click('button[aria-label*="theme"]');
    
    // Test language switch
    await page.click('button[aria-label*="language"]');

    // Test user menu
    await expect(page.locator('text=Super Admin')).toBeVisible();
    await expect(page.locator('text=Platform Admin')).toBeVisible();
  });

  test('✅ Search & Filter - Center Management Tools', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard?dev_token=super_admin');

    // Test search functionality (if available)
    const searchInput = page.locator('input[placeholder*="search"]');
    if (await searchInput.isVisible()) {
      await searchInput.fill('ABC');
      await expect(page.locator('text=ABC Coaching Center')).toBeVisible();
    }

    // Test filter by status (if available)
    const statusFilter = page.locator('select[name*="status"]');
    if (await statusFilter.isVisible()) {
      await statusFilter.selectOption('active');
      await expect(page.locator('text=ABC Coaching Center')).toBeVisible();
    }
  });

  test('✅ Responsive Design - Mobile & Desktop Views', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto('http://localhost:5173/dashboard?dev_token=super_admin');
    await expect(page.locator('h1')).toBeVisible();

    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('h1')).toBeVisible();

    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('h1')).toBeVisible();

    // Test mobile menu (if available)
    const mobileMenuButton = page.locator('button[aria-label*="menu"]');
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
      await expect(page.locator('nav')).toBeVisible();
    }
  });

  test('✅ Data Export & Reports - Analytics Features', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard?dev_token=super_admin');

    // Test analytics cards
    await expect(page.locator('text=Total Centers')).toBeVisible();
    await expect(page.locator('text=Active Centers')).toBeVisible();
    await expect(page.locator('text=Total Users')).toBeVisible();
    await expect(page.locator('text=Monthly Revenue')).toBeVisible();

    // Test growth indicators
    await expect(page.locator('text=15.8%')).toBeVisible();
    await expect(page.locator('text=growth')).toBeVisible();
  });

  test('✅ Security & Permissions - Access Control', async ({ page }) => {
    await page.goto('http://localhost:5173/dashboard?dev_token=super_admin');

    // Verify super admin specific features
    await expect(page.locator('text=Add Coaching Center')).toBeVisible();
    await expect(page.locator('text=System Overview')).toBeVisible();
    await expect(page.locator('text=Manage all coaching centers')).toBeVisible();

    // Verify user role display
    await expect(page.locator('text=Platform Admin')).toBeVisible();
    await expect(page.locator('text=Super Admin')).toBeVisible();
  });

  test('✅ Performance & Loading - System Responsiveness', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('http://localhost:5173/dashboard?dev_token=super_admin');
    await expect(page.locator('h1')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(5000); // Should load within 5 seconds

    // Test modal performance
    const modalStartTime = Date.now();
    await page.click('text=Add Coaching Center');
    await expect(page.locator('text=Add New Coaching Center')).toBeVisible();
    const modalLoadTime = Date.now() - modalStartTime;
    expect(modalLoadTime).toBeLessThan(1000); // Modal should open within 1 second
  });
});
