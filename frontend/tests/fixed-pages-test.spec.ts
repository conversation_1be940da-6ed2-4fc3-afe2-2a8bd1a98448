import { test, expect } from '@playwright/test';

test.describe('Fixed Pages Routing Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page and login
    await page.goto('http://abc.localhost:5174/login');

    // Fill login form
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');

    // Click login button
    await page.click('button[type="submit"]');

    // Wait for navigation to dashboard
    await page.waitForURL('**/coaching/dashboard');

    // Wait for page to be fully loaded
    await page.waitForLoadState('networkidle');
  });

  test('Fees page routing and API calls work correctly', async ({ page }) => {
    console.log('🧪 Testing Fees Page Routing and API...');

    // Navigate to fees page
    await page.goto('http://abc.localhost:5174/coaching/fees');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Check that the URL is correct
    expect(page.url()).toContain('/coaching/fees');

    // Check that API calls are being made
    const apiCalls = [];
    page.on('request', request => {
      if (request.url().includes('/api/coaching/fees') || request.url().includes('/api/coaching/students')) {
        apiCalls.push(request.url());
      }
    });

    // Refresh to trigger API calls
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Verify API calls were made
    expect(apiCalls.length).toBeGreaterThan(0);
    console.log('✅ Fees page routing works and API calls are being made');
  });

  test('Exam Results page routing and API calls work correctly', async ({ page }) => {
    console.log('🧪 Testing Exam Results Page Routing and API...');

    // Navigate to exam results page
    await page.goto('http://abc.localhost:5174/coaching/exam-results');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Check that the URL is correct
    expect(page.url()).toContain('/coaching/exam-results');

    // Check that API calls are being made
    const apiCalls = [];
    page.on('request', request => {
      if (request.url().includes('/api/coaching/exam-results') ||
          request.url().includes('/api/coaching/exams')) {
        apiCalls.push(request.url());
      }
    });

    // Refresh to trigger API calls
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Verify API calls were made
    expect(apiCalls.length).toBeGreaterThan(0);
    console.log('✅ Exam Results page routing works and API calls are being made');
  });

  test('Reports page should load and display analytics', async ({ page }) => {
    console.log('🧪 Testing Reports Page...');

    // Navigate to reports page
    await page.goto('http://abc.localhost:5174/coaching/reports');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Check that the URL is correct
    expect(page.url()).toContain('/coaching/reports');

    // Check if the page title is present
    await expect(page.locator('h1')).toContainText('Reports & Analytics');

    // Check if the description is present
    await expect(page.locator('text=Comprehensive reports and analytics')).toBeVisible();

    // Check if export buttons are present
    await expect(page.locator('button:has-text("Export Excel")')).toBeVisible();
    await expect(page.locator('button:has-text("Export PDF")')).toBeVisible();

    // Check if date range filters are present
    await expect(page.locator('text=Start Date')).toBeVisible();
    await expect(page.locator('text=End Date')).toBeVisible();
    await expect(page.locator('text=Report Type')).toBeVisible();

    // Check if stats cards are present
    await expect(page.locator('text=Total Students')).toBeVisible();
    await expect(page.locator('text=Total Revenue')).toBeVisible();
    await expect(page.locator('text=Active Courses')).toBeVisible();
    await expect(page.locator('text=Attendance Rate')).toBeVisible();

    // Check if analytics sections are present
    await expect(page.locator('text=Popular Courses')).toBeVisible();
    await expect(page.locator('text=Exam Performance')).toBeVisible();

    console.log('✅ Reports page loaded successfully with all analytics components');
  });

  test('Navigation between fixed pages should work', async ({ page }) => {
    console.log('🧪 Testing Navigation Between Fixed Pages...');
    
    // Start from dashboard
    await page.goto('http://abc.localhost:5174/coaching/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Navigate to fees page via sidebar
    await page.click('a[href="/coaching/fees"]');
    await page.waitForURL('**/coaching/fees');
    await expect(page.locator('h1')).toContainText('Fee Management');
    
    // Navigate to reports page via sidebar
    await page.click('a[href="/coaching/reports"]');
    await page.waitForURL('**/coaching/reports');
    await expect(page.locator('h1')).toContainText('Reports & Analytics');
    
    // Navigate to exam results page via sidebar
    await page.click('a[href="/coaching/results"]');
    await page.waitForURL('**/coaching/results');
    // This should load the ResultManagementPage
    await expect(page.locator('body')).toBeVisible();
    
    console.log('✅ Navigation between all fixed pages works correctly');
  });

  test('Pages should handle API errors gracefully', async ({ page }) => {
    console.log('🧪 Testing Error Handling...');
    
    // Test fees page with potential API errors
    await page.goto('http://abc.localhost:5174/coaching/fees');
    await page.waitForLoadState('networkidle');
    
    // Page should still show UI even if API fails
    await expect(page.locator('h1')).toContainText('Fee Management');
    await expect(page.locator('table')).toBeVisible();
    
    // Test reports page with API errors (we know this has a 500 error)
    await page.goto('http://abc.localhost:5174/coaching/reports');
    await page.waitForLoadState('networkidle');
    
    // Page should show default data when API fails
    await expect(page.locator('h1')).toContainText('Reports & Analytics');
    await expect(page.locator('text=Total Students')).toBeVisible();
    await expect(page.locator('text=0')).toBeVisible(); // Should show default values
    
    console.log('✅ Pages handle API errors gracefully with fallback UI');
  });

  test('UI components should be responsive and functional', async ({ page }) => {
    console.log('🧪 Testing UI Components...');
    
    // Test fees page components
    await page.goto('http://abc.localhost:5174/coaching/fees');
    await page.waitForLoadState('networkidle');
    
    // Test if Add Fee Record dialog opens
    await page.click('button:has-text("Add Fee Record")');
    await expect(page.locator('text=Create a new fee record')).toBeVisible();
    
    // Close dialog
    await page.click('button:has-text("Cancel")');
    
    // Test reports page components
    await page.goto('http://abc.localhost:5174/coaching/reports');
    await page.waitForLoadState('networkidle');
    
    // Test date inputs
    await expect(page.locator('input[type="date"]').first()).toBeVisible();
    await expect(page.locator('input[type="date"]').last()).toBeVisible();
    
    // Test report type selector
    await page.click('div:has-text("Overview")');
    // Should show dropdown options
    
    console.log('✅ UI components are functional and responsive');
  });
});

test.describe('Page Performance and Loading', () => {
  test('All fixed pages should load within reasonable time', async ({ page }) => {
    console.log('🧪 Testing Page Load Performance...');
    
    // Login first
    await page.goto('http://abc.localhost:5174/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/coaching/dashboard');
    
    const pages = [
      { url: 'http://abc.localhost:5174/coaching/fees', name: 'Fees' },
      { url: 'http://abc.localhost:5174/coaching/exam-results', name: 'Exam Results' },
      { url: 'http://abc.localhost:5174/coaching/reports', name: 'Reports' }
    ];
    
    for (const pageInfo of pages) {
      const startTime = Date.now();
      
      await page.goto(pageInfo.url);
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      console.log(`📊 ${pageInfo.name} page loaded in ${loadTime}ms`);
      
      // Ensure page loads within 10 seconds
      expect(loadTime).toBeLessThan(10000);
      
      // Ensure page has meaningful content
      const content = await page.textContent('body');
      expect(content.length).toBeGreaterThan(500);
    }
    
    console.log('✅ All pages load within acceptable time limits');
  });
});
