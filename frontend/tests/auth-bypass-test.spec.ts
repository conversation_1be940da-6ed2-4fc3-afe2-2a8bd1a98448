import { test, expect } from '@playwright/test';

test.describe('Authentication Bypass Test', () => {
  test('should access Super Admin dashboard with manual auth', async ({ page }) => {
    // Mock the auth endpoints to bypass authentication issues
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Super Admin',
            email: '<EMAIL>',
            role: 'super_admin'
          }
        })
      });
    });

    await page.route('**/api/admin/centers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ centers: [] })
      });
    });

    await page.route('**/api/admin/stats', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            totalCenters: 5,
            activeCenters: 4,
            totalUsers: 150,
            totalRevenue: 50000,
            monthlyGrowth: 12.5
          }
        })
      });
    });

    // Navigate directly to dashboard
    await page.goto('http://localhost:5173/dashboard');

    // Should show Super Admin dashboard
    await expect(page.locator('h1')).toContainText('Super Admin Dashboard');
    await expect(page.locator('text=Manage coaching centers')).toBeVisible();
    await expect(page.locator('text=Add Coaching Center')).toBeVisible();
  });

  test('should access Center Admin dashboard with manual auth', async ({ page }) => {
    // Mock the auth endpoints for center admin
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 2,
            name: 'Center Admin',
            email: '<EMAIL>',
            role: 'center_admin',
            tenant_id: 1,
            tenant_slug: 'abc-coaching',
            tenant_name: 'ABC Coaching Center'
          }
        })
      });
    });

    await page.route('**/api/dashboard/stats*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            students: { total: 50, active: 48, newThisMonth: 5 },
            courses: { total: 8, active: 6, enrollments: 50 },
            attendance: { todayRate: 85.5, weeklyAverage: 82.3, totalSessions: 150 },
            financial: { monthlyRevenue: 15000, pendingFees: 2500, collectionRate: 92.5 },
            recentActivities: []
          }
        })
      });
    });

    // Navigate directly to center dashboard
    await page.goto('http://localhost:5173/center/dashboard');

    // Should show Center Admin dashboard
    await expect(page.locator('h1')).toContainText('Welcome back, Center Admin!');
    await expect(page.locator('text=ABC Coaching Center')).toBeVisible();
    await expect(page.locator('text=Quick Actions')).toBeVisible();
  });

  test('should test subdomain-based routing', async ({ page }) => {
    // Mock the auth endpoints for center admin
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 2,
            name: 'Center Admin',
            email: '<EMAIL>',
            role: 'center_admin',
            tenant_id: 1,
            tenant_slug: 'abc-coaching',
            tenant_name: 'ABC Coaching Center'
          }
        })
      });
    });

    await page.route('**/api/dashboard/stats*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            students: { total: 50, active: 48, newThisMonth: 5 },
            courses: { total: 8, active: 6, enrollments: 50 },
            attendance: { todayRate: 85.5, weeklyAverage: 82.3, totalSessions: 150 },
            financial: { monthlyRevenue: 15000, pendingFees: 2500, collectionRate: 92.5 },
            recentActivities: []
          }
        })
      });
    });

    // Test subdomain routing (simulated)
    await page.goto('http://localhost:5173/center/dashboard');

    // Verify tenant context is working
    await expect(page.locator('text=ABC Coaching Center')).toBeVisible();
    await expect(page.locator('text=Welcome back, Center Admin!')).toBeVisible();
  });
});
