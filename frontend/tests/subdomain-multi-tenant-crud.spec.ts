import { test, expect } from '@playwright/test';

test.describe('Subdomain-based Multi-tenant CRUD Operations', () => {
  // Mock authentication for all tests
  test.beforeEach(async ({ page }) => {
    // Mock center admin authentication
    await page.route('**/api/auth/me*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 2,
            name: 'Center Admin',
            email: '<EMAIL>',
            role: 'center_admin',
            tenant_id: 1,
            tenant_slug: 'abc-coaching',
            tenant_name: 'ABC Coaching Center'
          }
        })
      });
    });

    // Mock dashboard stats
    await page.route('**/api/dashboard/stats*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            students: { total: 50, active: 48, newThisMonth: 5 },
            courses: { total: 8, active: 6, enrollments: 50 },
            attendance: { todayRate: 85.5, weeklyAverage: 82.3, totalSessions: 150 },
            financial: { monthlyRevenue: 15000, pendingFees: 2500, collectionRate: 92.5 },
            recentActivities: []
          }
        })
      });
    });
  });

  test('should access center dashboard with tenant context', async ({ page }) => {
    // Navigate to center dashboard
    await page.goto('http://localhost:5173/center/dashboard');

    // Should show center-specific content
    await expect(page.locator('text=Welcome back')).toBeVisible();
    await expect(page.locator('text=ABC Coaching Center')).toBeVisible();
    await expect(page.locator('text=Quick Actions')).toBeVisible();
  });

  test('should perform Students CRUD operations with tenant isolation', async ({ page }) => {
    // Mock students API
    await page.route('**/api/students*', async route => {
      const url = route.request().url();
      const method = route.request().method();
      
      if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            students: [
              {
                id: 1,
                name: 'John Doe',
                email: '<EMAIL>',
                phone: '1234567890',
                course: 'Mathematics',
                status: 'active',
                tenant_id: 1
              }
            ]
          })
        });
      } else if (method === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            student: {
              id: 2,
              name: 'Jane Smith',
              email: '<EMAIL>',
              phone: '0987654321',
              course: 'Physics',
              status: 'active',
              tenant_id: 1
            }
          })
        });
      }
    });

    // Navigate to students page
    await page.goto('http://localhost:5173/students');

    // Should show students list
    await expect(page.locator('h1')).toContainText('Students');
    await expect(page.locator('text=John Doe')).toBeVisible();

    // Test adding a new student
    await page.click('text=Add Student');
    await page.fill('input[name="name"]', 'Jane Smith');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '0987654321');
    await page.click('button[type="submit"]');

    // Should show success message
    await expect(page.locator('text=Student added successfully')).toBeVisible();
  });

  test('should perform Courses CRUD operations with tenant isolation', async ({ page }) => {
    // Mock courses API
    await page.route('**/api/courses*', async route => {
      const method = route.request().method();
      
      if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            courses: [
              {
                id: 1,
                name: 'Advanced Mathematics',
                description: 'Advanced math course',
                duration: '6 months',
                fee: 5000,
                status: 'active',
                tenant_id: 1
              }
            ]
          })
        });
      } else if (method === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            course: {
              id: 2,
              name: 'Physics Fundamentals',
              description: 'Basic physics course',
              duration: '4 months',
              fee: 4000,
              status: 'active',
              tenant_id: 1
            }
          })
        });
      }
    });

    // Navigate to courses page
    await page.goto('http://localhost:5173/courses');

    // Should show courses list
    await expect(page.locator('h1')).toContainText('Courses');
    await expect(page.locator('text=Advanced Mathematics')).toBeVisible();

    // Test adding a new course
    await page.click('text=Add Course');
    await page.fill('input[name="name"]', 'Physics Fundamentals');
    await page.fill('textarea[name="description"]', 'Basic physics course');
    await page.fill('input[name="duration"]', '4 months');
    await page.fill('input[name="fee"]', '4000');
    await page.click('button[type="submit"]');

    // Should show success message
    await expect(page.locator('text=Course added successfully')).toBeVisible();
  });

  test('should perform Teachers CRUD operations with tenant isolation', async ({ page }) => {
    // Mock teachers API
    await page.route('**/api/teachers*', async route => {
      const method = route.request().method();
      
      if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            teachers: [
              {
                id: 1,
                name: 'Dr. Smith',
                email: '<EMAIL>',
                phone: '1234567890',
                subject: 'Mathematics',
                qualification: 'PhD in Mathematics',
                status: 'active',
                tenant_id: 1
              }
            ]
          })
        });
      } else if (method === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            teacher: {
              id: 2,
              name: 'Dr. Johnson',
              email: '<EMAIL>',
              phone: '0987654321',
              subject: 'Physics',
              qualification: 'PhD in Physics',
              status: 'active',
              tenant_id: 1
            }
          })
        });
      }
    });

    // Navigate to teachers page
    await page.goto('http://localhost:5173/teachers');

    // Should show teachers list
    await expect(page.locator('h1')).toContainText('Teachers');
    await expect(page.locator('text=Dr. Smith')).toBeVisible();

    // Test adding a new teacher
    await page.click('text=Add Teacher');
    await page.fill('input[name="name"]', 'Dr. Johnson');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '0987654321');
    await page.fill('input[name="subject"]', 'Physics');
    await page.fill('input[name="qualification"]', 'PhD in Physics');
    await page.click('button[type="submit"]');

    // Should show success message
    await expect(page.locator('text=Teacher added successfully')).toBeVisible();
  });

  test('should perform Attendance management with tenant isolation', async ({ page }) => {
    // Mock attendance API
    await page.route('**/api/attendance*', async route => {
      const method = route.request().method();
      
      if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            attendance: [
              {
                id: 1,
                student_name: 'John Doe',
                course_name: 'Mathematics',
                date: '2024-01-15',
                status: 'present',
                tenant_id: 1
              }
            ]
          })
        });
      } else if (method === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: 'Attendance marked successfully'
          })
        });
      }
    });

    // Navigate to attendance page
    await page.goto('http://localhost:5173/attendance');

    // Should show attendance interface
    await expect(page.locator('h1')).toContainText('Attendance');
    await expect(page.locator('text=John Doe')).toBeVisible();

    // Test marking attendance
    await page.click('text=Mark Attendance');
    await page.selectOption('select[name="course"]', 'Mathematics');
    await page.click('input[type="checkbox"][data-student="1"]');
    await page.click('button[type="submit"]');

    // Should show success message
    await expect(page.locator('text=Attendance marked successfully')).toBeVisible();
  });

  test('should perform Fee management with tenant isolation', async ({ page }) => {
    // Mock fees API
    await page.route('**/api/fees*', async route => {
      const method = route.request().method();
      
      if (method === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            fees: [
              {
                id: 1,
                student_name: 'John Doe',
                course_name: 'Mathematics',
                amount: 5000,
                paid_amount: 3000,
                due_amount: 2000,
                status: 'partial',
                tenant_id: 1
              }
            ]
          })
        });
      } else if (method === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: 'Payment recorded successfully'
          })
        });
      }
    });

    // Navigate to fees page
    await page.goto('http://localhost:5173/fees');

    // Should show fees interface
    await expect(page.locator('h1')).toContainText('Fee Management');
    await expect(page.locator('text=John Doe')).toBeVisible();
    await expect(page.locator('text=₹2,000')).toBeVisible(); // Due amount

    // Test recording payment
    await page.click('text=Record Payment');
    await page.fill('input[name="amount"]', '2000');
    await page.selectOption('select[name="payment_method"]', 'cash');
    await page.click('button[type="submit"]');

    // Should show success message
    await expect(page.locator('text=Payment recorded successfully')).toBeVisible();
  });

  test('should verify tenant data isolation', async ({ page }) => {
    // Mock API to verify tenant_slug is sent in requests
    let tenantSlugSent = false;
    
    await page.route('**/api/students*', async route => {
      const url = route.request().url();
      if (url.includes('tenant_slug=abc-coaching')) {
        tenantSlugSent = true;
      }
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    // Navigate to students page
    await page.goto('http://localhost:5173/students');

    // Wait for API call
    await page.waitForTimeout(1000);

    // Verify tenant context was sent
    expect(tenantSlugSent).toBe(true);
  });

  test('should handle navigation between different modules', async ({ page }) => {
    // Mock all module APIs
    const modules = ['students', 'courses', 'teachers', 'attendance', 'fees', 'results', 'hrm', 'notifications', 'reports'];
    
    for (const module of modules) {
      await page.route(`**/api/${module}*`, async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ [module]: [] })
        });
      });
    }

    // Start from dashboard
    await page.goto('http://localhost:5173/center/dashboard');

    // Test navigation to each module
    for (const module of modules) {
      await page.goto(`http://localhost:5173/${module}`);
      
      // Should load without errors
      await expect(page.locator('h1')).toBeVisible();
      
      // Should maintain tenant context
      await expect(page.locator('text=ABC Coaching Center')).toBeVisible();
    }
  });
});
