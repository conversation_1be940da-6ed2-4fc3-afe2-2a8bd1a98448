import { test, expect } from '@playwright/test'

test.describe('Complete Additional Features Integration Test', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the coaching center login page
    await page.goto('http://abc.localhost:5175/login')
    
    // Login as center admin
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'Admin123!')
    await page.click('button[type="submit"]')
    
    // Wait for dashboard to load
    await page.waitForURL('**/center/dashboard')
    await expect(page.locator('h1')).toContainText('Dashboard')
  })

  test('should navigate to and test Schedule Management page', async ({ page }) => {
    // Navigate to Schedule Management
    await page.click('text=Academic Management')
    await page.click('text=Schedule Management')
    
    // Wait for page to load
    await page.waitForURL('**/schedule-management')
    await expect(page.locator('h1')).toContainText('Schedule Management')
    
    // Check if the page has the expected elements
    await expect(page.locator('text=Add Schedule')).toBeVisible()
    await expect(page.locator('text=Manage class schedules and room bookings')).toBeVisible()
    
    // Test adding a schedule form
    await page.click('text=Add Schedule')
    await expect(page.locator('text=Create New Schedule')).toBeVisible()
    
    // Check form fields
    await expect(page.locator('label:has-text("Batch")')).toBeVisible()
    await expect(page.locator('label:has-text("Room")')).toBeVisible()
    await expect(page.locator('label:has-text("Day of Week")')).toBeVisible()
    await expect(page.locator('label:has-text("Start Time")')).toBeVisible()
    await expect(page.locator('label:has-text("End Time")')).toBeVisible()
    
    // Cancel form
    await page.click('text=Cancel')
    await expect(page.locator('text=Create New Schedule')).not.toBeVisible()
  })

  test('should navigate to and test Exam Management page', async ({ page }) => {
    // Navigate to Exam Management
    await page.click('text=Academic Management')
    await page.click('text=Exam Management')
    
    // Wait for page to load
    await page.waitForURL('**/exam-management')
    await expect(page.locator('h1')).toContainText('Exam Management')
    
    // Check if the page has the expected elements
    await expect(page.locator('text=Create Exam')).toBeVisible()
    await expect(page.locator('text=Create and manage exams and assessments')).toBeVisible()
    
    // Check stats cards
    await expect(page.locator('text=Total Exams')).toBeVisible()
    await expect(page.locator('text=Scheduled')).toBeVisible()
    await expect(page.locator('text=Completed')).toBeVisible()
    await expect(page.locator('text=Results Entered')).toBeVisible()
    
    // Test adding an exam form
    await page.click('text=Create Exam')
    await expect(page.locator('text=Create New Exam')).toBeVisible()
    
    // Check form fields
    await expect(page.locator('label:has-text("Exam Name")')).toBeVisible()
    await expect(page.locator('label:has-text("Exam Type")')).toBeVisible()
    await expect(page.locator('label:has-text("Exam Date")')).toBeVisible()
    await expect(page.locator('label:has-text("Duration (minutes)")')).toBeVisible()
    await expect(page.locator('label:has-text("Total Marks")')).toBeVisible()
    await expect(page.locator('label:has-text("Passing Marks")')).toBeVisible()
    
    // Cancel form
    await page.click('text=Cancel')
    await expect(page.locator('text=Create New Exam')).not.toBeVisible()
  })

  test('should navigate to and test Asset & Expense Management page', async ({ page }) => {
    // Navigate to Asset & Expense Management
    await page.click('text=Operations')
    await page.click('text=Asset & Expense Management')
    
    // Wait for page to load
    await page.waitForURL('**/asset-expense-management')
    await expect(page.locator('h1')).toContainText('Asset & Expense Management')
    
    // Check if the page has the expected elements
    await expect(page.locator('text=Manage inventory assets and track expenses')).toBeVisible()
    
    // Check stats cards
    await expect(page.locator('text=Total Inventory Value')).toBeVisible()
    await expect(page.locator('text=Total Expenses')).toBeVisible()
    await expect(page.locator('text=Inventory Items')).toBeVisible()
    await expect(page.locator('text=Expense Records')).toBeVisible()
    
    // Check tabs
    await expect(page.locator('text=Inventory Management')).toBeVisible()
    await expect(page.locator('text=Expense Management')).toBeVisible()
    
    // Test Inventory tab
    await page.click('text=Inventory Management')
    await expect(page.locator('text=Add Item')).toBeVisible()
    
    // Test adding inventory item form
    await page.click('text=Add Item')
    await expect(page.locator('text=Add Inventory Item')).toBeVisible()
    
    // Check inventory form fields
    await expect(page.locator('label:has-text("Item Name")')).toBeVisible()
    await expect(page.locator('label:has-text("Category")')).toBeVisible()
    await expect(page.locator('label:has-text("Quantity")')).toBeVisible()
    await expect(page.locator('label:has-text("Unit Price")')).toBeVisible()
    
    // Cancel form
    await page.click('text=Cancel')
    await expect(page.locator('text=Add Inventory Item')).not.toBeVisible()
    
    // Test Expense tab
    await page.click('text=Expense Management')
    await expect(page.locator('text=Add Expense')).toBeVisible()
    
    // Test adding expense form
    await page.click('text=Add Expense')
    await expect(page.locator('text=Add Expense')).toBeVisible()
    
    // Check expense form fields
    await expect(page.locator('label:has-text("Expense Title")')).toBeVisible()
    await expect(page.locator('label:has-text("Amount")')).toBeVisible()
    await expect(page.locator('label:has-text("Expense Date")')).toBeVisible()
    await expect(page.locator('label:has-text("Payment Method")')).toBeVisible()
    
    // Cancel form
    await page.click('text=Cancel')
    await expect(page.locator('text=Add Expense')).not.toBeVisible()
  })

  test('should test enhanced Courses page functionality', async ({ page }) => {
    // Navigate to Courses
    await page.click('text=Academic Management')
    await page.click('text=Courses')
    
    // Wait for page to load
    await page.waitForURL('**/courses')
    await expect(page.locator('h1')).toContainText('Courses')
    
    // Check if the page has the expected elements
    await expect(page.locator('text=Add Course')).toBeVisible()
    
    // Test adding a course form
    await page.click('text=Add Course')
    await expect(page.locator('text=Add New Course')).toBeVisible()
    
    // Check enhanced form fields
    await expect(page.locator('label:has-text("Course Name")')).toBeVisible()
    await expect(page.locator('label:has-text("Course Code")')).toBeVisible()
    await expect(page.locator('label:has-text("Subject")')).toBeVisible()
    await expect(page.locator('label:has-text("Branch")')).toBeVisible()
    await expect(page.locator('label:has-text("Duration (Months)")')).toBeVisible()
    await expect(page.locator('label:has-text("Fee")')).toBeVisible()
    
    // Cancel form
    await page.click('text=Cancel')
    await expect(page.locator('text=Add New Course')).not.toBeVisible()
  })

  test('should test navigation and sidebar updates', async ({ page }) => {
    // Test that all new menu items are present in the sidebar
    
    // Academic Management section
    await page.click('text=Academic Management')
    await expect(page.locator('text=Courses')).toBeVisible()
    await expect(page.locator('text=Batches')).toBeVisible()
    await expect(page.locator('text=Schedule Management')).toBeVisible()
    await expect(page.locator('text=Attendance')).toBeVisible()
    await expect(page.locator('text=Exam Management')).toBeVisible()
    await expect(page.locator('text=Results')).toBeVisible()
    
    // Operations section
    await page.click('text=Operations')
    await expect(page.locator('text=Branches')).toBeVisible()
    await expect(page.locator('text=Asset & Expense Management')).toBeVisible()
    await expect(page.locator('text=Requisitions')).toBeVisible()
  })

  test('should test responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Test mobile navigation
    await expect(page.locator('button[aria-label="Toggle sidebar"]')).toBeVisible()
    
    // Open mobile menu
    await page.click('button[aria-label="Toggle sidebar"]')
    
    // Test that menu items are accessible on mobile
    await page.click('text=Academic Management')
    await expect(page.locator('text=Schedule Management')).toBeVisible()
    await expect(page.locator('text=Exam Management')).toBeVisible()
    
    // Navigate to a page on mobile
    await page.click('text=Schedule Management')
    await page.waitForURL('**/schedule-management')
    await expect(page.locator('h1')).toContainText('Schedule Management')
    
    // Test that the page is responsive
    await expect(page.locator('text=Add Schedule')).toBeVisible()
  })

  test('should test API integration and error handling', async ({ page }) => {
    // Navigate to Schedule Management to test API calls
    await page.click('text=Academic Management')
    await page.click('text=Schedule Management')
    
    // Wait for page to load and API calls to complete
    await page.waitForURL('**/schedule-management')
    await page.waitForTimeout(2000) // Wait for API calls
    
    // Check that the page loads even if some APIs fail
    await expect(page.locator('h1')).toContainText('Schedule Management')
    
    // Navigate to Exam Management
    await page.click('text=Academic Management')
    await page.click('text=Exam Management')
    await page.waitForURL('**/exam-management')
    await page.waitForTimeout(2000)
    
    // Check that the page loads
    await expect(page.locator('h1')).toContainText('Exam Management')
    
    // Navigate to Asset & Expense Management
    await page.click('text=Operations')
    await page.click('text=Asset & Expense Management')
    await page.waitForURL('**/asset-expense-management')
    await page.waitForTimeout(2000)
    
    // Check that the page loads
    await expect(page.locator('h1')).toContainText('Asset & Expense Management')
  })

  test('should test theme and language consistency', async ({ page }) => {
    // Test that new pages respect theme settings
    await page.click('text=Academic Management')
    await page.click('text=Schedule Management')
    
    // Check that the page has proper theme classes
    const body = page.locator('body')
    await expect(body).toHaveClass(/light|dark/)
    
    // Navigate to other new pages and check consistency
    await page.click('text=Academic Management')
    await page.click('text=Exam Management')
    await expect(body).toHaveClass(/light|dark/)
    
    await page.click('text=Operations')
    await page.click('text=Asset & Expense Management')
    await expect(body).toHaveClass(/light|dark/)
  })
})
