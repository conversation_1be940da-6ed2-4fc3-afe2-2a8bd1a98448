import { test, expect } from '@playwright/test'

test.describe('Settings Page - Base64 Image Upload', () => {
  test.beforeEach(async ({ page }) => {
    // Login as center admin
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/coaching/dashboard')
  })

  test('should display settings page with image upload functionality', async ({ page }) => {
    // Navigate to settings page
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Wait for page to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Check page title
    await expect(page.locator('h1')).toContainText('Settings')
    
    // Check basic settings fields
    await expect(page.getByLabel(/Coaching Center Name/i)).toBeVisible()
    await expect(page.getByLabel(/Address/i)).toBeVisible()
    await expect(page.getByLabel(/Phone/i)).toBeVisible()
    await expect(page.getByLabel(/Email/i)).toBeVisible()
    
    // Check image upload fields
    await expect(page.getByText(/Invoice Logo/i)).toBeVisible()
    await expect(page.getByText(/Invoice Watermark/i)).toBeVisible()
    
    // Check file input fields
    const logoInput = page.locator('input[type="file"]').first()
    const watermarkInput = page.locator('input[type="file"]').last()
    
    await expect(logoInput).toBeVisible()
    await expect(watermarkInput).toBeVisible()
    
    // Check that inputs accept images
    await expect(logoInput).toHaveAttribute('accept', 'image/*')
    await expect(watermarkInput).toHaveAttribute('accept', 'image/*')
    
    // Check save button
    await expect(page.getByRole('button', { name: /Save Settings/i })).toBeVisible()
  })

  test('should save basic settings successfully', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Wait for page to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Fill in basic settings
    await page.getByLabel(/Coaching Center Name/i).fill('ABC Coaching Center Updated')
    await page.getByLabel(/Address/i).fill('123 Updated Street, City')
    await page.getByLabel(/Phone/i).fill('+1234567890')
    await page.getByLabel(/Email/i).fill('<EMAIL>')
    
    // Save settings
    await page.getByRole('button', { name: /Save Settings/i }).click()
    
    // Should show success message
    await expect(page.getByText(/Settings updated successfully/i)).toBeVisible()
  })

  test('should handle image upload and convert to base64', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Wait for page to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Create a simple test image (1x1 pixel PNG)
    const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    
    // Mock file input to simulate image upload
    await page.evaluate((base64) => {
      // Create a mock file from base64
      const byteCharacters = atob(base64.split(',')[1])
      const byteNumbers = new Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      const file = new File([byteArray], 'test-logo.png', { type: 'image/png' })
      
      // Get the file input and trigger change event
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      if (fileInput) {
        const dataTransfer = new DataTransfer()
        dataTransfer.items.add(file)
        fileInput.files = dataTransfer.files
        fileInput.dispatchEvent(new Event('change', { bubbles: true }))
      }
    }, testImageBase64)
    
    // Wait for image processing
    await page.waitForTimeout(2000)
    
    // Should show success message for image upload
    await expect(page.getByText(/Image uploaded and compressed successfully/i)).toBeVisible()
    
    // Should show image preview
    await expect(page.locator('img[alt="Invoice Logo"]')).toBeVisible()
  })

  test('should persist settings after page reload', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Wait for page to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Fill in a unique value
    const uniqueName = `Test Center ${Date.now()}`
    await page.getByLabel(/Coaching Center Name/i).fill(uniqueName)
    
    // Save settings
    await page.getByRole('button', { name: /Save Settings/i }).click()
    
    // Wait for success message
    await expect(page.getByText(/Settings updated successfully/i)).toBeVisible()
    
    // Reload page
    await page.reload()
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Check that the value persisted
    await expect(page.getByLabel(/Coaching Center Name/i)).toHaveValue(uniqueName)
  })

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API to return error
    await page.route('**/api/coaching/settings', route => {
      if (route.request().method() === 'PUT') {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        })
      } else {
        route.continue()
      }
    })
    
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Wait for page to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Try to save settings
    await page.getByRole('button', { name: /Save Settings/i }).click()
    
    // Should show error message
    await expect(page.getByText(/Failed to save settings/i)).toBeVisible()
  })

  test('should be mobile responsive', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Check if page loads properly on mobile
    await expect(page.locator('h1')).toBeVisible()
    await expect(page.getByLabel(/Coaching Center Name/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /Save Settings/i })).toBeVisible()
  })

  test('should support Bengali/English translations', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Check if language toggle exists
    const languageToggle = page.locator('button:has-text("EN"), button:has-text("BD")')
    if (await languageToggle.count() > 0) {
      // Test language switching
      await languageToggle.click()
      await page.waitForTimeout(1000)
      
      // Check if text changed (this will depend on current language)
      // The test should pass regardless of which language is active
      await expect(page.locator('h1')).toBeVisible()
    }
  })

  test('should validate image file types', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Wait for page to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Check that file inputs only accept images
    const fileInputs = page.locator('input[type="file"]')
    const count = await fileInputs.count()
    
    for (let i = 0; i < count; i++) {
      await expect(fileInputs.nth(i)).toHaveAttribute('accept', 'image/*')
    }
  })

  test('should compress and convert images to WebP format', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Wait for page to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Test that the image processing function exists and works
    const imageProcessingWorks = await page.evaluate(() => {
      // Check if canvas and image processing APIs are available
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      return !!(canvas && ctx && canvas.toDataURL)
    })
    
    expect(imageProcessingWorks).toBe(true)
  })
})
