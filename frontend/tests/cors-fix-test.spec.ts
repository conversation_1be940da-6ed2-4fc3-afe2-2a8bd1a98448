import { test, expect } from '@playwright/test'

test.describe('CORS Fix Verification', () => {
  test('should verify CORS is working for center-login endpoint', async ({ page }) => {
    // Navigate to the login page
    await page.goto('http://abc.localhost:5174/login')
    
    // Wait for page to load
    await page.waitForTimeout(3000)
    
    // Check if page loads without CORS errors
    const pageTitle = await page.title()
    console.log('Page title:', pageTitle)
    
    // Check if login form is visible
    const emailInput = page.locator('input[type="email"]')
    const passwordInput = page.locator('input[type="password"]')
    const loginButton = page.locator('button[type="submit"]')
    
    await expect(emailInput).toBeVisible()
    await expect(passwordInput).toBeVisible()
    await expect(loginButton).toBeVisible()
    
    // Fill login form
    await emailInput.fill('<EMAIL>')
    await passwordInput.fill('password123')
    
    // Monitor network requests to check for CORS errors
    const requests: any[] = []
    const responses: any[] = []
    
    page.on('request', request => {
      if (request.url().includes('/api/auth/center-login')) {
        requests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers()
        })
        console.log('🔍 Login request:', request.method(), request.url())
      }
    })
    
    page.on('response', response => {
      if (response.url().includes('/api/auth/center-login')) {
        responses.push({
          url: response.url(),
          status: response.status(),
          headers: response.headers()
        })
        console.log('✅ Login response:', response.status(), response.url())
      }
    })
    
    // Submit login form
    await loginButton.click()
    
    // Wait for response
    await page.waitForTimeout(5000)
    
    // Check if we got a response (success or error, but not CORS blocked)
    expect(responses.length).toBeGreaterThan(0)
    
    // Check if the response has proper CORS headers
    if (responses.length > 0) {
      const response = responses[0]
      console.log('Response status:', response.status)
      console.log('Response headers:', response.headers)
      
      // Should not be a CORS error (status would be 0 or network error)
      expect(response.status).toBeGreaterThan(0)
      expect(response.status).toBeLessThan(600)
    }
    
    // Check for CORS error in console
    const consoleLogs: string[] = []
    page.on('console', msg => {
      const text = msg.text()
      consoleLogs.push(text)
      if (text.includes('CORS') || text.includes('blocked')) {
        console.log('❌ Console error:', text)
      }
    })
    
    // Wait a bit more to catch any console errors
    await page.waitForTimeout(2000)
    
    // Check if there are any CORS-related errors
    const corsErrors = consoleLogs.filter(log => 
      log.includes('CORS') || 
      log.includes('blocked') || 
      log.includes('Access-Control-Allow-Origin')
    )
    
    console.log('CORS errors found:', corsErrors.length)
    corsErrors.forEach(error => console.log('CORS Error:', error))
    
    // The test passes if we don't have CORS errors
    expect(corsErrors.length).toBe(0)
  })

  test('should verify API endpoints are accessible', async ({ page }) => {
    // Test direct API calls
    const apiTests = [
      {
        name: 'Settings API',
        url: 'http://localhost:3000/api/coaching/settings',
        expectedStatus: 401 // Should be unauthorized, not CORS blocked
      },
      {
        name: 'Upload API',
        url: 'http://localhost:3000/api/coaching/settings/upload-image',
        expectedStatus: 401 // Should be unauthorized, not CORS blocked
      },
      {
        name: 'Auth API',
        url: 'http://localhost:3000/api/auth/me',
        expectedStatus: 401 // Should be unauthorized, not CORS blocked
      }
    ]

    for (const apiTest of apiTests) {
      console.log(`Testing ${apiTest.name}...`)
      
      const response = await page.request.get(apiTest.url, {
        headers: {
          'Origin': 'http://abc.localhost:5174'
        }
      })
      
      console.log(`${apiTest.name} response status:`, response.status())
      
      // Should get expected status, not CORS error
      expect(response.status()).toBe(apiTest.expectedStatus)
    }
  })

  test('should verify center-login API specifically', async ({ page }) => {
    // Test the center-login endpoint directly
    const response = await page.request.post('http://localhost:3000/api/auth/center-login', {
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://abc.localhost:5174'
      },
      data: {
        email: '<EMAIL>',
        password: 'password123'
      }
    })
    
    console.log('Center-login API response status:', response.status())
    console.log('Center-login API response headers:', await response.allHeaders())
    
    // Should get a response (success or error), not CORS blocked
    expect(response.status()).toBeGreaterThan(0)
    expect(response.status()).toBeLessThan(600)
    
    // Check for CORS headers
    const headers = await response.allHeaders()
    console.log('CORS headers present:', {
      'access-control-allow-origin': headers['access-control-allow-origin'],
      'access-control-allow-credentials': headers['access-control-allow-credentials'],
      'access-control-allow-methods': headers['access-control-allow-methods']
    })
  })

  test('should verify preflight OPTIONS request works', async ({ page }) => {
    // Test preflight request
    const response = await page.request.fetch('http://localhost:3000/api/auth/center-login', {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://abc.localhost:5174',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    })
    
    console.log('Preflight OPTIONS response status:', response.status())
    console.log('Preflight OPTIONS response headers:', await response.allHeaders())
    
    // Preflight should succeed
    expect(response.status()).toBe(204)
    
    // Check CORS headers in preflight response
    const headers = await response.allHeaders()
    expect(headers['access-control-allow-origin']).toBeDefined()
    expect(headers['access-control-allow-methods']).toBeDefined()
  })

  test('should verify file upload endpoint CORS', async ({ page }) => {
    // Test file upload endpoint CORS
    const formData = new FormData()
    formData.append('type', 'logo')
    
    const response = await page.request.post('http://localhost:3000/api/coaching/settings/upload-image', {
      headers: {
        'Origin': 'http://abc.localhost:5174'
      },
      multipart: {
        type: 'logo'
      }
    })
    
    console.log('Upload endpoint response status:', response.status())
    
    // Should get unauthorized (401), not CORS error
    expect(response.status()).toBe(401)
  })

  test('should verify complete login flow works without CORS errors', async ({ page }) => {
    // Navigate to login page
    await page.goto('http://abc.localhost:5174/login')
    
    // Monitor all network requests for CORS issues
    const networkErrors: string[] = []
    
    page.on('requestfailed', request => {
      const failure = request.failure()
      if (failure && failure.errorText.includes('net::ERR_FAILED')) {
        networkErrors.push(`Request failed: ${request.url()} - ${failure.errorText}`)
      }
    })
    
    // Fill and submit login form
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    // Wait for login attempt
    await page.waitForTimeout(5000)
    
    // Check for network failures
    console.log('Network errors:', networkErrors)
    
    // Should not have network failures due to CORS
    const corsNetworkErrors = networkErrors.filter(error => 
      error.includes('CORS') || error.includes('Access-Control')
    )
    
    expect(corsNetworkErrors.length).toBe(0)
  })
})
