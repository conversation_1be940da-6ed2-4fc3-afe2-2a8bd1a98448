import { test, expect } from '@playwright/test';

test.describe('Student Blood Group Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to coaching center login
    await page.goto('http://abc.localhost:5174/coaching/login');
    
    // Login as center admin
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/coaching/dashboard');
    
    // Navigate to students page
    await page.click('a[href="/coaching/students"]');
    await page.waitForURL('**/coaching/students');
  });

  test('should display blood group field in student creation form', async ({ page }) => {
    console.log('🧪 Testing blood group field in student creation form...');
    
    // Click "Add New Student" button
    await page.click('button:has-text("নতুন শিক্ষার্থী যোগ করুন")');
    
    // Wait for form to appear
    await page.waitForSelector('form');
    
    // Check if blood group field exists
    const bloodGroupField = page.locator('select[name="blood_group"], input[name="blood_group"]');
    await expect(bloodGroupField).toBeVisible();
    
    // Check if blood group label exists
    const bloodGroupLabel = page.locator('label:has-text("রক্তের গ্রুপ"), label:has-text("Blood Group")');
    await expect(bloodGroupLabel).toBeVisible();
    
    console.log('✅ Blood group field is visible in student creation form');
  });

  test('should have all blood group options available', async ({ page }) => {
    console.log('🧪 Testing blood group options...');
    
    // Click "Add New Student" button
    await page.click('button:has-text("নতুন শিক্ষার্থী যোগ করুন")');
    
    // Wait for form to appear
    await page.waitForSelector('form');
    
    // Check if blood group select field exists
    const bloodGroupSelect = page.locator('select[name="blood_group"]');
    await expect(bloodGroupSelect).toBeVisible();
    
    // Check all blood group options
    const expectedOptions = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
    
    for (const option of expectedOptions) {
      const optionElement = page.locator(`select[name="blood_group"] option[value="${option}"]`);
      await expect(optionElement).toBeVisible();
    }
    
    console.log('✅ All blood group options are available');
  });

  test('should create student with blood group information', async ({ page }) => {
    console.log('🧪 Testing student creation with blood group...');
    
    // Click "Add New Student" button
    await page.click('button:has-text("নতুন শিক্ষার্থী যোগ করুন")');
    
    // Wait for form to appear
    await page.waitForSelector('form');
    
    // Fill student information
    await page.fill('input[name="name"]', 'Test Student Blood Group');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '+8801234567890');
    await page.fill('input[name="guardian_name"]', 'Test Guardian');
    await page.fill('input[name="guardian_phone"]', '+8801234567891');
    
    // Select blood group
    await page.selectOption('select[name="blood_group"]', 'A+');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for success message or redirect
    await page.waitForTimeout(2000);
    
    // Check if student was created successfully
    const successMessage = page.locator('.toast, .alert, .notification');
    await expect(successMessage).toBeVisible({ timeout: 5000 });
    
    console.log('✅ Student created successfully with blood group');
  });

  test('should display blood group in student list', async ({ page }) => {
    console.log('🧪 Testing blood group display in student list...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Check if blood group column exists in table
    const bloodGroupHeader = page.locator('th:has-text("রক্তের গ্রুপ"), th:has-text("Blood Group")');
    await expect(bloodGroupHeader).toBeVisible();
    
    // Check if blood group data is displayed for students
    const bloodGroupCells = page.locator('td:has-text("A+"), td:has-text("B+"), td:has-text("O+"), td:has-text("AB+")');
    
    // At least one student should have blood group displayed
    const count = await bloodGroupCells.count();
    expect(count).toBeGreaterThan(0);
    
    console.log('✅ Blood group is displayed in student list');
  });

  test('should edit student blood group information', async ({ page }) => {
    console.log('🧪 Testing blood group editing...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Click edit button for first student
    const editButton = page.locator('button:has-text("সম্পাদনা"), button:has-text("Edit")').first();
    await editButton.click();
    
    // Wait for edit form to appear
    await page.waitForSelector('form');
    
    // Check if blood group field exists in edit form
    const bloodGroupField = page.locator('select[name="blood_group"]');
    await expect(bloodGroupField).toBeVisible();
    
    // Change blood group
    await page.selectOption('select[name="blood_group"]', 'B+');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for success message
    await page.waitForTimeout(2000);
    
    // Check if update was successful
    const successMessage = page.locator('.toast, .alert, .notification');
    await expect(successMessage).toBeVisible({ timeout: 5000 });
    
    console.log('✅ Student blood group updated successfully');
  });

  test('should validate blood group field', async ({ page }) => {
    console.log('🧪 Testing blood group validation...');
    
    // Click "Add New Student" button
    await page.click('button:has-text("নতুন শিক্ষার্থী যোগ করুন")');
    
    // Wait for form to appear
    await page.waitForSelector('form');
    
    // Fill required fields but leave blood group empty
    await page.fill('input[name="name"]', 'Test Validation Student');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '+8801234567892');
    
    // Try to submit without blood group (should still work as it's optional)
    await page.click('button[type="submit"]');
    
    // Wait for response
    await page.waitForTimeout(2000);
    
    // Blood group should be optional, so form should submit successfully
    const successMessage = page.locator('.toast, .alert, .notification');
    await expect(successMessage).toBeVisible({ timeout: 5000 });
    
    console.log('✅ Blood group validation works correctly (optional field)');
  });
});
