import { test, expect } from '@playwright/test';

test.describe('Student Disable/Enable Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to coaching center login
    await page.goto('http://abc.localhost:5173/coaching/login');
    
    // Login as center admin
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/coaching/dashboard');
    
    // Navigate to students page
    await page.click('a[href="/coaching/students"]');
    await page.waitForURL('**/coaching/students');
  });

  test('should display student status in student list', async ({ page }) => {
    console.log('🧪 Testing student status display...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Check if status column exists in table
    const statusHeader = page.locator('th:has-text("স্ট্যাটাস"), th:has-text("Status")');
    await expect(statusHeader).toBeVisible();
    
    // Check if status badges are displayed for students
    const statusBadges = page.locator('.status-badge, .badge');
    const count = await statusBadges.count();
    expect(count).toBeGreaterThan(0);
    
    console.log('✅ Student status is displayed in student list');
  });

  test('should show enable/disable toggle button for each student', async ({ page }) => {
    console.log('🧪 Testing enable/disable toggle buttons...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Check if toggle buttons exist for students
    const toggleButtons = page.locator('button:has-text("নিষ্ক্রিয়"), button:has-text("সক্রিয়"), button:has-text("Disable"), button:has-text("Enable")');
    const count = await toggleButtons.count();
    expect(count).toBeGreaterThan(0);
    
    console.log('✅ Enable/disable toggle buttons are visible');
  });

  test('should disable a student and update status', async ({ page }) => {
    console.log('🧪 Testing student disable functionality...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Find an active student and click disable button
    const disableButton = page.locator('button:has-text("নিষ্ক্রিয়"), button:has-text("Disable")').first();
    await disableButton.click();
    
    // Wait for confirmation dialog if it appears
    const confirmButton = page.locator('button:has-text("নিশ্চিত"), button:has-text("Confirm"), button:has-text("Yes")');
    if (await confirmButton.isVisible({ timeout: 2000 })) {
      await confirmButton.click();
    }
    
    // Wait for success message
    await page.waitForTimeout(2000);
    
    // Check if success message appears
    const successMessage = page.locator('.toast, .alert, .notification');
    await expect(successMessage).toBeVisible({ timeout: 5000 });
    
    // Check if status changed to inactive
    const inactiveStatus = page.locator('text="নিষ্ক্রিয়", text="Inactive"').first();
    await expect(inactiveStatus).toBeVisible({ timeout: 3000 });
    
    console.log('✅ Student disabled successfully');
  });

  test('should enable a disabled student', async ({ page }) => {
    console.log('🧪 Testing student enable functionality...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Find a disabled student and click enable button
    const enableButton = page.locator('button:has-text("সক্রিয়"), button:has-text("Enable")').first();
    
    // If no disabled students exist, first disable one
    if (!(await enableButton.isVisible({ timeout: 2000 }))) {
      const disableButton = page.locator('button:has-text("নিষ্ক্রিয়"), button:has-text("Disable")').first();
      await disableButton.click();
      
      // Wait for confirmation if needed
      const confirmButton = page.locator('button:has-text("নিশ্চিত"), button:has-text("Confirm"), button:has-text("Yes")');
      if (await confirmButton.isVisible({ timeout: 2000 })) {
        await confirmButton.click();
      }
      
      await page.waitForTimeout(2000);
    }
    
    // Now enable the student
    const enableButtonAfter = page.locator('button:has-text("সক্রিয়"), button:has-text("Enable")').first();
    await enableButtonAfter.click();
    
    // Wait for confirmation dialog if it appears
    const confirmButton = page.locator('button:has-text("নিশ্চিত"), button:has-text("Confirm"), button:has-text("Yes")');
    if (await confirmButton.isVisible({ timeout: 2000 })) {
      await confirmButton.click();
    }
    
    // Wait for success message
    await page.waitForTimeout(2000);
    
    // Check if success message appears
    const successMessage = page.locator('.toast, .alert, .notification');
    await expect(successMessage).toBeVisible({ timeout: 5000 });
    
    // Check if status changed to active
    const activeStatus = page.locator('text="সক্রিয়", text="Active"').first();
    await expect(activeStatus).toBeVisible({ timeout: 3000 });
    
    console.log('✅ Student enabled successfully');
  });

  test('should show audit trail for status changes', async ({ page }) => {
    console.log('🧪 Testing audit trail functionality...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Click on a student to view details
    const studentRow = page.locator('tr').nth(1); // First student row
    await studentRow.click();
    
    // Look for audit trail or history section
    const auditSection = page.locator('text="অডিট ট্রেইল", text="Audit Trail", text="History", text="ইতিহাস"');
    
    // If audit trail is not immediately visible, look for a tab or button to show it
    if (!(await auditSection.isVisible({ timeout: 2000 }))) {
      const historyTab = page.locator('button:has-text("ইতিহাস"), button:has-text("History"), a:has-text("ইতিহাস"), a:has-text("History")');
      if (await historyTab.isVisible({ timeout: 2000 })) {
        await historyTab.click();
      }
    }
    
    // Check if audit trail information is displayed
    const auditEntries = page.locator('.audit-entry, .history-item, .log-entry');
    
    // At minimum, there should be some audit information
    const hasAuditInfo = await auditSection.isVisible() || (await auditEntries.count()) > 0;
    expect(hasAuditInfo).toBeTruthy();
    
    console.log('✅ Audit trail functionality is available');
  });

  test('should filter students by status', async ({ page }) => {
    console.log('🧪 Testing student status filtering...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Look for status filter dropdown or buttons
    const statusFilter = page.locator('select:has(option:text("Active")), select:has(option:text("সক্রিয়")), button:has-text("Filter"), button:has-text("ফিল্টার")');
    
    if (await statusFilter.isVisible({ timeout: 2000 })) {
      // If it's a select dropdown
      if (await page.locator('select').isVisible()) {
        await page.selectOption('select', { label: /Active|সক্রিয়/ });
      } else {
        // If it's a filter button, click it
        await statusFilter.click();
        
        // Look for active status option
        const activeOption = page.locator('text="Active", text="সক্রিয়"').first();
        if (await activeOption.isVisible({ timeout: 2000 })) {
          await activeOption.click();
        }
      }
      
      // Wait for filter to apply
      await page.waitForTimeout(2000);
      
      // Check if only active students are shown
      const inactiveStudents = page.locator('text="Inactive", text="নিষ্ক্রিয়"');
      const inactiveCount = await inactiveStudents.count();
      
      // After filtering for active, there should be no inactive students visible
      expect(inactiveCount).toBe(0);
      
      console.log('✅ Student status filtering works correctly');
    } else {
      console.log('⚠️ Status filter not found - may need to be implemented');
    }
  });

  test('should prevent actions on disabled students', async ({ page }) => {
    console.log('🧪 Testing disabled student restrictions...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // First, disable a student if none are disabled
    const enableButton = page.locator('button:has-text("সক্রিয়"), button:has-text("Enable")').first();
    
    if (!(await enableButton.isVisible({ timeout: 2000 }))) {
      const disableButton = page.locator('button:has-text("নিষ্ক্রিয়"), button:has-text("Disable")').first();
      await disableButton.click();
      
      const confirmButton = page.locator('button:has-text("নিশ্চিত"), button:has-text("Confirm"), button:has-text("Yes")');
      if (await confirmButton.isVisible({ timeout: 2000 })) {
        await confirmButton.click();
      }
      
      await page.waitForTimeout(2000);
    }
    
    // Find a disabled student row
    const disabledStudentRow = page.locator('tr:has(text="Inactive"), tr:has(text="নিষ্ক্রিয়")').first();
    
    // Check if certain action buttons are disabled or hidden for inactive students
    const actionButtons = disabledStudentRow.locator('button:has-text("Edit"), button:has-text("সম্পাদনা"), button:has-text("Assign"), button:has-text("নিয়োগ")');
    
    const buttonCount = await actionButtons.count();
    
    if (buttonCount > 0) {
      // Check if buttons are disabled
      const firstButton = actionButtons.first();
      const isDisabled = await firstButton.isDisabled();
      
      // Either buttons should be disabled or have restricted functionality
      expect(isDisabled || buttonCount === 0).toBeTruthy();
    }
    
    console.log('✅ Disabled student restrictions are in place');
  });
});
