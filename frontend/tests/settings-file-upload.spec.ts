import { test, expect } from '@playwright/test'
import path from 'path'

test.describe('Settings Page - File Upload Implementation', () => {
  test.beforeEach(async ({ page }) => {
    // Login as center admin
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/coaching/dashboard')
  })

  test('should display settings page with file upload functionality', async ({ page }) => {
    // Navigate to settings page
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Wait for page to load
    await page.waitForTimeout(3000)
    
    // Check that we're on the settings page
    await expect(page.getByText('Basic Information')).toBeVisible()
    await expect(page.getByText('Invoice Settings')).toBeVisible()
    
    // Check for file input fields
    const fileInputs = page.locator('input[type="file"]')
    const count = await fileInputs.count()
    
    expect(count).toBeGreaterThanOrEqual(2) // Should have at least logo and watermark inputs
    
    // Check that they accept images
    for (let i = 0; i < count; i++) {
      await expect(fileInputs.nth(i)).toHaveAttribute('accept', 'image/*')
    }
  })

  test('should upload logo image successfully', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(3000)
    
    // Create a test image file (1x1 pixel PNG)
    const testImagePath = path.join(__dirname, 'test-logo.png')
    
    // Create a simple PNG file for testing
    const pngBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
      0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
      0x49, 0x48, 0x44, 0x52, // IHDR
      0x00, 0x00, 0x00, 0x01, // Width: 1
      0x00, 0x00, 0x00, 0x01, // Height: 1
      0x08, 0x02, 0x00, 0x00, 0x00, // Bit depth, color type, compression, filter, interlace
      0x90, 0x77, 0x53, 0xDE, // CRC
      0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
      0x49, 0x44, 0x41, 0x54, // IDAT
      0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // Image data
      0xE2, 0x21, 0xBC, 0x33, // CRC
      0x00, 0x00, 0x00, 0x00, // IEND chunk length
      0x49, 0x45, 0x4E, 0x44, // IEND
      0xAE, 0x42, 0x60, 0x82  // CRC
    ])
    
    // Write test image to filesystem
    await page.evaluate(async (buffer) => {
      const fs = require('fs')
      const path = require('path')
      const testPath = path.join(__dirname, 'test-logo.png')
      fs.writeFileSync(testPath, Buffer.from(buffer))
    }, Array.from(pngBuffer))
    
    // Find logo file input
    const logoInput = page.locator('input[type="file"]').first()
    
    // Upload the test image
    await logoInput.setInputFiles(testImagePath)
    
    // Wait for upload to complete
    await page.waitForTimeout(3000)
    
    // Should show success message
    await expect(page.getByText(/Logo uploaded successfully/i)).toBeVisible()
    
    // Should show image preview
    await expect(page.locator('img[alt*="Logo"]')).toBeVisible()
  })

  test('should validate file types', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(3000)
    
    // Try to upload a non-image file
    const textContent = 'This is not an image file'
    const textFile = new File([textContent], 'test.txt', { type: 'text/plain' })
    
    // Mock file input with text file
    await page.evaluate((fileContent) => {
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      if (fileInput) {
        const dataTransfer = new DataTransfer()
        const file = new File([fileContent], 'test.txt', { type: 'text/plain' })
        dataTransfer.items.add(file)
        fileInput.files = dataTransfer.files
        fileInput.dispatchEvent(new Event('change', { bubbles: true }))
      }
    }, textContent)
    
    // Should show error message
    await expect(page.getByText(/Please select a valid image file/i)).toBeVisible()
  })

  test('should validate file size', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(3000)
    
    // Create a large file (simulate > 10MB)
    const largeContent = 'x'.repeat(11 * 1024 * 1024) // 11MB of 'x'
    
    await page.evaluate((content) => {
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      if (fileInput) {
        const dataTransfer = new DataTransfer()
        const file = new File([content], 'large-image.jpg', { type: 'image/jpeg' })
        dataTransfer.items.add(file)
        fileInput.files = dataTransfer.files
        fileInput.dispatchEvent(new Event('change', { bubbles: true }))
      }
    }, largeContent)
    
    // Should show error message
    await expect(page.getByText(/Image file must be less than 10MB/i)).toBeVisible()
  })

  test('should handle upload errors gracefully', async ({ page }) => {
    // Mock API to return error
    await page.route('**/api/coaching/settings/upload-image', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Upload failed' })
      })
    })
    
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(3000)
    
    // Try to upload an image
    const imageContent = 'fake-image-data'
    await page.evaluate((content) => {
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      if (fileInput) {
        const dataTransfer = new DataTransfer()
        const file = new File([content], 'test.jpg', { type: 'image/jpeg' })
        dataTransfer.items.add(file)
        fileInput.files = dataTransfer.files
        fileInput.dispatchEvent(new Event('change', { bubbles: true }))
      }
    }, imageContent)
    
    // Should show error message
    await expect(page.getByText(/Failed to upload image/i)).toBeVisible()
  })

  test('should save settings with image URLs', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(3000)
    
    // Fill in basic settings
    const nameInput = page.locator('input[type="text"]').first()
    await nameInput.fill('Test Coaching Center')
    
    // Save settings
    await page.getByRole('button', { name: /Save Settings/i }).click()
    
    // Should show success message
    await expect(page.getByText(/Settings updated successfully/i)).toBeVisible()
  })

  test('should display existing images from URLs', async ({ page }) => {
    // Mock settings API to return image URLs
    await page.route('**/api/coaching/settings', route => {
      if (route.request().method() === 'GET') {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            settings: {
              coaching_center_name: 'Test Center',
              invoice_logo: '/uploads/images/logo_123456789_abcdef.webp',
              invoice_watermark: '/uploads/images/watermark_123456789_abcdef.webp'
            }
          })
        })
      } else {
        route.continue()
      }
    })
    
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(3000)
    
    // Should show image previews
    await expect(page.locator('img[src*="/uploads/images/logo_"]')).toBeVisible()
    await expect(page.locator('img[src*="/uploads/images/watermark_"]')).toBeVisible()
  })

  test('should be mobile responsive', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(3000)
    
    // Check if page loads properly on mobile
    await expect(page.getByText('Basic Information')).toBeVisible()
    await expect(page.locator('input[type="file"]').first()).toBeVisible()
    await expect(page.getByRole('button', { name: /Save Settings/i })).toBeVisible()
  })

  test('should support Bengali/English translations', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(3000)
    
    // Check if language toggle exists
    const languageToggle = page.locator('button:has-text("EN"), button:has-text("BD")')
    if (await languageToggle.count() > 0) {
      // Test language switching
      await languageToggle.click()
      await page.waitForTimeout(1000)
      
      // Check if text changed (this will depend on current language)
      // The test should pass regardless of which language is active
      await expect(page.getByText('Basic Information')).toBeVisible()
    }
  })
})
