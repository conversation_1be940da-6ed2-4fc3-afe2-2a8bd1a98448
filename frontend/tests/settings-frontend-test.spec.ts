import { test, expect } from '@playwright/test'

test.describe('Settings Frontend Tests', () => {
  test('should load settings page and populate form fields', async ({ page }) => {
    // Step 1: Login to get auth token
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    // Wait for login to complete
    await page.waitForTimeout(3000)
    
    // Step 2: Navigate to settings page
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Wait for page to load
    await page.waitForTimeout(5000)
    
    // Step 3: Check if the page loaded without errors
    const pageTitle = await page.title()
    console.log('Page title:', pageTitle)
    
    // Step 4: Check if form is present
    const form = page.locator('form')
    await expect(form).toBeVisible()
    console.log('✅ Form is visible')
    
    // Step 5: Check if input fields are present
    const nameInput = page.locator('input[name="coaching_center_name"], input[value*="ABC"]')
    const emailInput = page.locator('input[name="email"], input[value*="<EMAIL>"]')
    const phoneInput = page.locator('input[name="phone"], input[value*="******"]')
    const addressInput = page.locator('input[name="address"], textarea[name="address"]')
    
    // Check if at least some inputs are visible
    const nameVisible = await nameInput.isVisible().catch(() => false)
    const emailVisible = await emailInput.isVisible().catch(() => false)
    const phoneVisible = await phoneInput.isVisible().catch(() => false)
    const addressVisible = await addressInput.isVisible().catch(() => false)
    
    console.log('Input visibility:', {
      name: nameVisible,
      email: emailVisible,
      phone: phoneVisible,
      address: addressVisible
    })
    
    // At least one input should be visible
    expect(nameVisible || emailVisible || phoneVisible || addressVisible).toBe(true)
    
    // Step 6: Check if data is populated by looking for specific values
    const pageContent = await page.content()
    const hasABCCoaching = pageContent.includes('ABC Coaching Center')
    const hasEmail = pageContent.includes('<EMAIL>')
    const hasPhone = pageContent.includes('******')
    
    console.log('Data presence:', {
      hasABCCoaching,
      hasEmail,
      hasPhone
    })
    
    // At least some data should be present
    expect(hasABCCoaching || hasEmail || hasPhone).toBe(true)
    
    console.log('✅ Settings page loaded with data')
  })

  test('should test form submission', async ({ page }) => {
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    // Step 2: Go to settings
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(5000)
    
    // Step 3: Find and update a field
    const inputs = await page.locator('input[type="text"], input[type="email"], input[type="tel"], textarea').all()
    
    if (inputs.length > 0) {
      // Try to update the first editable input
      const firstInput = inputs[0]
      const currentValue = await firstInput.inputValue()
      console.log('Current value:', currentValue)
      
      // Update with a test value
      await firstInput.clear()
      await firstInput.fill('Test Value Updated')
      
      // Submit form
      const submitButton = page.locator('button[type="submit"], button:has-text("Save"), button:has-text("Update")')
      if (await submitButton.isVisible()) {
        await submitButton.click()
        await page.waitForTimeout(3000)
        console.log('✅ Form submitted')
      } else {
        console.log('⚠️ Submit button not found')
      }
    } else {
      console.log('⚠️ No editable inputs found')
    }
  })

  test('should test file upload functionality', async ({ page }) => {
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    // Step 2: Go to settings
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(5000)
    
    // Step 3: Check for file upload inputs
    const fileInputs = await page.locator('input[type="file"]').all()
    console.log('File inputs found:', fileInputs.length)
    
    if (fileInputs.length > 0) {
      console.log('✅ File upload inputs are present')
      
      // Check if upload sections have labels
      const logoLabel = page.locator('text=Logo, text=Invoice Logo')
      const watermarkLabel = page.locator('text=Watermark, text=Invoice Watermark')
      
      const logoVisible = await logoLabel.isVisible().catch(() => false)
      const watermarkVisible = await watermarkLabel.isVisible().catch(() => false)
      
      console.log('Upload labels:', {
        logo: logoVisible,
        watermark: watermarkVisible
      })
      
      expect(logoVisible || watermarkVisible).toBe(true)
    } else {
      console.log('⚠️ No file upload inputs found')
    }
  })

  test('should check network requests for settings API', async ({ page }) => {
    // Monitor network requests
    const requests: any[] = []
    const responses: any[] = []
    
    page.on('request', request => {
      if (request.url().includes('/api/coaching/settings')) {
        requests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers()
        })
        console.log('🔍 Settings request:', request.method(), request.url())
      }
    })
    
    page.on('response', response => {
      if (response.url().includes('/api/coaching/settings')) {
        responses.push({
          url: response.url(),
          status: response.status()
        })
        console.log('✅ Settings response:', response.status(), response.url())
      }
    })
    
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    // Step 2: Go to settings (this should trigger API call)
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(5000)
    
    // Step 3: Check if API calls were made
    console.log('Requests made:', requests.length)
    console.log('Responses received:', responses.length)
    
    if (requests.length > 0) {
      console.log('✅ Settings API was called')
      console.log('Request details:', requests[0])
    } else {
      console.log('❌ No settings API calls detected')
    }
    
    if (responses.length > 0) {
      console.log('✅ Settings API responded')
      console.log('Response details:', responses[0])
      expect(responses[0].status).toBe(200)
    } else {
      console.log('❌ No settings API responses detected')
    }
  })
})
