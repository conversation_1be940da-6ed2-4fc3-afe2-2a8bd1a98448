import { test, expect } from '@playwright/test';

test.describe('Multi-Tenant Routing System', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing auth
    await page.context().clearCookies();
  });

  test('should redirect unauthenticated users to login', async ({ page }) => {
    // Try to access protected dashboard route
    await page.goto('http://localhost:5173/dashboard');
    
    // Should be redirected to login
    await expect(page).toHaveURL(/.*\/login/);
  });

  test('should redirect unauthenticated users from center routes to login', async ({ page }) => {
    // Try to access protected center route
    await page.goto('http://localhost:5173/center/dashboard');
    
    // Should be redirected to login
    await expect(page).toHaveURL(/.*\/login/);
  });

  test('should allow Super Admin access to dashboard', async ({ page }) => {
    // Mock Super Admin authentication
    await page.route('**/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: {
            id: 1,
            name: 'Super Admin',
            email: '<EMAIL>',
            role: 'super_admin'
          }
        })
      });
    });

    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Super Admin',
            email: '<EMAIL>',
            role: 'super_admin'
          }
        })
      });
    });

    await page.route('**/api/admin/centers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ centers: [] })
      });
    });

    await page.route('**/api/admin/stats', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            totalCenters: 5,
            activeCenters: 4,
            totalUsers: 150,
            totalRevenue: 50000,
            monthlyGrowth: 12.5
          }
        })
      });
    });

    // Login as Super Admin
    await page.goto('http://localhost:5173/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');

    // Should be redirected to Super Admin dashboard
    await expect(page).toHaveURL('http://localhost:5173/dashboard');
    await expect(page.locator('h1')).toContainText('Super Admin Dashboard');
  });

  test('should allow Center Admin access to tenant-specific dashboard', async ({ page }) => {
    // Mock Center Admin authentication
    await page.route('**/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: {
            id: 2,
            name: 'Center Admin',
            email: '<EMAIL>',
            role: 'center_admin',
            tenant_id: 1,
            tenant_slug: 'abc-coaching',
            tenant_name: 'ABC Coaching Center'
          }
        })
      });
    });

    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 2,
            name: 'Center Admin',
            email: '<EMAIL>',
            role: 'center_admin',
            tenant_id: 1,
            tenant_slug: 'abc-coaching',
            tenant_name: 'ABC Coaching Center'
          }
        })
      });
    });

    await page.route('**/api/dashboard/stats*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            students: { total: 50, active: 48, newThisMonth: 5 },
            courses: { total: 8, active: 6, enrollments: 50 },
            attendance: { todayRate: 85.5, weeklyAverage: 82.3, totalSessions: 150 },
            financial: { monthlyRevenue: 15000, pendingFees: 2500, collectionRate: 92.5 },
            recentActivities: []
          }
        })
      });
    });

    // Login as Center Admin
    await page.goto('http://localhost:5173/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');

    // Should be redirected to Center Admin dashboard
    await expect(page).toHaveURL('http://localhost:5173/center/dashboard');
    await expect(page.locator('h1')).toContainText('Welcome back, Center Admin!');
  });

  test('should prevent Super Admin from accessing center routes', async ({ page }) => {
    // Mock Super Admin authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Super Admin',
            email: '<EMAIL>',
            role: 'super_admin'
          }
        })
      });
    });

    await page.route('**/api/admin/centers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ centers: [] })
      });
    });

    await page.route('**/api/admin/stats', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            totalCenters: 5,
            activeCenters: 4,
            totalUsers: 150,
            totalRevenue: 50000,
            monthlyGrowth: 12.5
          }
        })
      });
    });

    // Set auth cookie to simulate logged in Super Admin
    await page.context().addCookies([{
      name: 'auth_token',
      value: 'mock-super-admin-token',
      domain: 'localhost',
      path: '/'
    }]);

    // Try to access center route
    await page.goto('http://localhost:5173/center/dashboard');

    // Should be redirected to Super Admin dashboard
    await expect(page).toHaveURL('http://localhost:5173/dashboard');
  });

  test('should prevent Center Admin from accessing super admin routes', async ({ page }) => {
    // Mock Center Admin authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 2,
            name: 'Center Admin',
            email: '<EMAIL>',
            role: 'center_admin',
            tenant_id: 1,
            tenant_slug: 'abc-coaching',
            tenant_name: 'ABC Coaching Center'
          }
        })
      });
    });

    await page.route('**/api/dashboard/stats*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            students: { total: 50, active: 48, newThisMonth: 5 },
            courses: { total: 8, active: 6, enrollments: 50 },
            attendance: { todayRate: 85.5, weeklyAverage: 82.3, totalSessions: 150 },
            financial: { monthlyRevenue: 15000, pendingFees: 2500, collectionRate: 92.5 },
            recentActivities: []
          }
        })
      });
    });

    // Set auth cookie to simulate logged in Center Admin
    await page.context().addCookies([{
      name: 'auth_token',
      value: 'mock-center-admin-token',
      domain: 'localhost',
      path: '/'
    }]);

    // Try to access super admin route
    await page.goto('http://localhost:5173/dashboard');

    // Should be redirected to Center Admin dashboard
    await expect(page).toHaveURL('http://localhost:5173/center/dashboard');
  });

  test('should protect center-specific routes with tenant requirement', async ({ page }) => {
    // Mock Center Admin authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 2,
            name: 'Center Admin',
            email: '<EMAIL>',
            role: 'center_admin',
            tenant_id: 1,
            tenant_slug: 'abc-coaching',
            tenant_name: 'ABC Coaching Center'
          }
        })
      });
    });

    await page.route('**/api/students*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    // Set auth cookie
    await page.context().addCookies([{
      name: 'auth_token',
      value: 'mock-center-admin-token',
      domain: 'localhost',
      path: '/'
    }]);

    // Access students page (tenant-specific route)
    await page.goto('http://localhost:5173/students');

    // Should be accessible for center admin
    await expect(page).toHaveURL('http://localhost:5173/students');
    await expect(page.locator('h1')).toContainText('Students');
  });

  test('should display correct navigation for Super Admin', async ({ page }) => {
    // Mock Super Admin authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Super Admin',
            email: '<EMAIL>',
            role: 'super_admin'
          }
        })
      });
    });

    await page.route('**/api/admin/centers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ centers: [] })
      });
    });

    await page.route('**/api/admin/stats', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            totalCenters: 5,
            activeCenters: 4,
            totalUsers: 150,
            totalRevenue: 50000,
            monthlyGrowth: 12.5
          }
        })
      });
    });

    // Set auth cookie
    await page.context().addCookies([{
      name: 'auth_token',
      value: 'mock-super-admin-token',
      domain: 'localhost',
      path: '/'
    }]);

    await page.goto('http://localhost:5173/dashboard');

    // Should show Super Admin specific navigation
    await expect(page.locator('text=Coaching Centers')).toBeVisible();
    await expect(page.locator('text=System Settings')).toBeVisible();
  });

  test('should display correct navigation for Center Admin', async ({ page }) => {
    // Mock Center Admin authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 2,
            name: 'Center Admin',
            email: '<EMAIL>',
            role: 'center_admin',
            tenant_id: 1,
            tenant_slug: 'abc-coaching',
            tenant_name: 'ABC Coaching Center'
          }
        })
      });
    });

    await page.route('**/api/dashboard/stats*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          stats: {
            students: { total: 50, active: 48, newThisMonth: 5 },
            courses: { total: 8, active: 6, enrollments: 50 },
            attendance: { todayRate: 85.5, weeklyAverage: 82.3, totalSessions: 150 },
            financial: { monthlyRevenue: 15000, pendingFees: 2500, collectionRate: 92.5 },
            recentActivities: []
          }
        })
      });
    });

    // Set auth cookie
    await page.context().addCookies([{
      name: 'auth_token',
      value: 'mock-center-admin-token',
      domain: 'localhost',
      path: '/'
    }]);

    await page.goto('http://localhost:5173/center/dashboard');

    // Should show Center Admin specific navigation
    await expect(page.locator('text=Students')).toBeVisible();
    await expect(page.locator('text=Courses')).toBeVisible();
    await expect(page.locator('text=Teachers')).toBeVisible();
    await expect(page.locator('text=Attendance')).toBeVisible();
  });
});
