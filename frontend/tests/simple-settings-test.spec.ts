import { test, expect } from '@playwright/test'

test.describe('Simple Settings Tests', () => {
  test('should call settings API and get response', async ({ page }) => {
    // Monitor network requests
    const requests: any[] = []
    const responses: any[] = []
    
    page.on('request', request => {
      if (request.url().includes('/api/coaching/settings')) {
        requests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers()
        })
        console.log('🔍 Settings request:', request.method(), request.url())
      }
    })
    
    page.on('response', response => {
      if (response.url().includes('/api/coaching/settings')) {
        responses.push({
          url: response.url(),
          status: response.status()
        })
        console.log('✅ Settings response:', response.status(), response.url())
      }
    })
    
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    // Step 2: Go to settings page
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(10000) // Wait longer for API calls
    
    // Step 3: Check results
    console.log('Total requests made:', requests.length)
    console.log('Total responses received:', responses.length)
    
    if (requests.length > 0) {
      console.log('✅ Settings API was called')
      console.log('First request:', requests[0])
    } else {
      console.log('❌ No settings API calls detected')
    }
    
    if (responses.length > 0) {
      console.log('✅ Settings API responded')
      console.log('First response:', responses[0])
      expect(responses[0].status).toBe(200)
    } else {
      console.log('❌ No settings API responses detected')
    }
    
    // Step 4: Check page content
    const pageContent = await page.content()
    const hasForm = pageContent.includes('<form')
    const hasInput = pageContent.includes('<input')
    const hasSettings = pageContent.includes('Settings') || pageContent.includes('settings')
    
    console.log('Page content check:', {
      hasForm,
      hasInput,
      hasSettings,
      contentLength: pageContent.length
    })
    
    expect(hasForm || hasInput || hasSettings).toBe(true)
  })

  test('should manually test settings API', async ({ page }) => {
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    // Step 2: Get auth token
    const token = await page.evaluate(() => localStorage.getItem('auth_token'))
    console.log('Auth token present:', !!token)
    expect(token).toBeTruthy()
    
    // Step 3: Make direct API call
    const response = await page.request.get('http://localhost:3000/api/coaching/settings', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-Tenant': 'abc',
        'Content-Type': 'application/json'
      }
    })
    
    console.log('Direct API call status:', response.status())
    expect(response.status()).toBe(200)
    
    const data = await response.json()
    console.log('Settings data received:', data)
    expect(data.success).toBe(true)
    expect(data.settings).toBeDefined()
    
    console.log('✅ Direct API call works perfectly')
  })
})
