import { test, expect } from '@playwright/test'

test.describe('Settings Page Tests', () => {
  test('should load settings page and test upload functionality', async ({ page }) => {
    // Step 1: Navigate to center login page
    await page.goto('http://abc.localhost:5174/login')

    // Step 2: Login as center admin
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')

    // Wait for login to complete
    await page.waitForTimeout(3000)

    // Step 3: Navigate to settings page
    await page.goto('http://abc.localhost:5174/coaching/settings')

    // Wait for page to load
    await page.waitForTimeout(3000)

    // Step 4: Check if settings form is visible
    await expect(page.locator('form')).toBeVisible()

    // Check if basic form fields are present
    await expect(page.locator('input[value*="ABC Coaching Center"]')).toBeVisible()
    await expect(page.locator('input[value*="<EMAIL>"]')).toBeVisible()

    console.log('✅ Settings page loaded successfully')

    // Step 5: Test settings API call
    const settingsResponse = await page.request.get('http://localhost:3000/api/coaching/settings', {
      headers: {
        'Authorization': `Bearer ${await page.evaluate(() => localStorage.getItem('auth_token'))}`,
        'X-Tenant': 'abc'
      }
    })

    console.log('Settings API response status:', settingsResponse.status())
    expect(settingsResponse.status()).toBe(200)

    const settingsData = await settingsResponse.json()
    console.log('Settings data:', settingsData)
    expect(settingsData.success).toBe(true)
    expect(settingsData.settings).toBeDefined()

    // Step 6: Test upload endpoint (without actual file)
    const uploadResponse = await page.request.post('http://localhost:3000/api/coaching/settings/upload-image', {
      headers: {
        'Authorization': `Bearer ${await page.evaluate(() => localStorage.getItem('auth_token'))}`,
        'X-Tenant': 'abc'
      },
      multipart: {
        type: 'logo'
        // Missing image file intentionally to test endpoint exists
      }
    })

    console.log('Upload API response status:', uploadResponse.status())
    // Should get 400 (bad request) not 404 (not found) or 401 (unauthorized)
    expect(uploadResponse.status()).toBe(400)

    const uploadData = await uploadResponse.json()
    console.log('Upload error response:', uploadData)
    expect(uploadData.error).toContain('No image file provided')

    console.log('✅ Settings page and API endpoints working correctly')
  })

  test('should test settings form submission', async ({ page }) => {
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)

    // Step 2: Go to settings
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(3000)

    // Step 3: Update a setting
    const phoneInput = page.locator('input[value*="******-567-8900"]')
    await expect(phoneInput).toBeVisible()

    // Clear and update phone number
    await phoneInput.clear()
    await phoneInput.fill('******-123-4567')

    // Submit form
    await page.click('button[type="submit"]')

    // Wait for submission
    await page.waitForTimeout(3000)

    // Check for success message (toast)
    // Note: This might need adjustment based on actual toast implementation
    console.log('✅ Settings form submission test completed')
  })

  test('should test file upload UI interaction', async ({ page }) => {
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)

    // Step 2: Go to settings
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(3000)

    // Step 3: Check if file upload inputs are present
    const logoUpload = page.locator('input[type="file"]').first()
    const watermarkUpload = page.locator('input[type="file"]').last()

    await expect(logoUpload).toBeVisible()
    await expect(watermarkUpload).toBeVisible()

    console.log('✅ File upload inputs are present and visible')

    // Step 4: Check if upload sections have proper labels
    await expect(page.locator('text=Invoice Logo')).toBeVisible()
    await expect(page.locator('text=Invoice Watermark')).toBeVisible()

    console.log('✅ Upload sections properly labeled')
  })

  test('should verify CORS is working for settings endpoints', async ({ page }) => {
    // Test CORS for settings endpoints
    const endpoints = [
      '/api/coaching/settings',
      '/api/coaching/settings/upload-image'
    ]

    for (const endpoint of endpoints) {
      // Test OPTIONS preflight
      const optionsResponse = await page.request.fetch(`http://localhost:3000${endpoint}`, {
        method: 'OPTIONS',
        headers: {
          'Origin': 'http://abc.localhost:5174',
          'Access-Control-Request-Method': endpoint.includes('upload') ? 'POST' : 'GET',
          'Access-Control-Request-Headers': 'Content-Type,Authorization,X-Tenant'
        }
      })

      console.log(`OPTIONS ${endpoint} response status:`, optionsResponse.status())
      expect(optionsResponse.status()).toBe(204)
    }

    console.log('✅ CORS working for all settings endpoints')
  })
})