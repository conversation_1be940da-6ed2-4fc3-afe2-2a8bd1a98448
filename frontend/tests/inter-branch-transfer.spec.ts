import { test, expect } from '@playwright/test';

test.describe('Inter-Branch Student Transfer System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to coaching center login
    await page.goto('http://abc.localhost:5173/coaching/login');
    
    // Login as center admin
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/coaching/dashboard');
    
    // Navigate to students page
    await page.click('a[href="/coaching/students"]');
    await page.waitForURL('**/coaching/students');
  });

  test('should display transfer button for students', async ({ page }) => {
    console.log('🧪 Testing transfer button display...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Check if transfer buttons exist for students
    const transferButtons = page.locator('button:has-text("স্থানান্তর"), button:has-text("Transfer")');
    const count = await transferButtons.count();
    expect(count).toBeGreaterThan(0);
    
    console.log('✅ Transfer buttons are visible for students');
  });

  test('should open transfer modal with branch selection', async ({ page }) => {
    console.log('🧪 Testing transfer modal...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Click transfer button for first student
    const transferButton = page.locator('button:has-text("স্থানান্তর"), button:has-text("Transfer")').first();
    await transferButton.click();
    
    // Check if transfer modal opens
    const modal = page.locator('.modal, .dialog, [role="dialog"]');
    await expect(modal).toBeVisible({ timeout: 5000 });
    
    // Check if branch selection dropdown exists
    const branchSelect = page.locator('select:has(option:text("Branch")), select[name="branch"], select[name="branch_id"]');
    await expect(branchSelect).toBeVisible();
    
    // Check if available branches are listed
    const branchOptions = page.locator('option:text("Main Branch"), option:text("Downtown Branch"), option:text("Uptown Branch")');
    const optionCount = await branchOptions.count();
    expect(optionCount).toBeGreaterThan(0);
    
    console.log('✅ Transfer modal opens with branch selection');
  });

  test('should transfer student to different branch', async ({ page }) => {
    console.log('🧪 Testing student transfer functionality...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Click transfer button for first student
    const transferButton = page.locator('button:has-text("স্থানান্তর"), button:has-text("Transfer")').first();
    await transferButton.click();
    
    // Wait for modal to appear
    await page.waitForSelector('.modal, .dialog, [role="dialog"]', { timeout: 5000 });
    
    // Select target branch
    const branchSelect = page.locator('select[name="branch"], select[name="branch_id"]');
    await branchSelect.selectOption({ index: 1 }); // Select second branch
    
    // Add transfer reason
    const reasonField = page.locator('textarea[name="reason"], input[name="reason"], textarea[placeholder*="reason"], input[placeholder*="reason"]');
    if (await reasonField.isVisible({ timeout: 2000 })) {
      await reasonField.fill('Student requested transfer to downtown branch for convenience');
    }
    
    // Click confirm transfer button
    const confirmButton = page.locator('button:has-text("নিশ্চিত"), button:has-text("Confirm"), button:has-text("Transfer")');
    await confirmButton.click();
    
    // Wait for success message
    await page.waitForTimeout(2000);
    
    // Check if success message appears
    const successMessage = page.locator('.toast, .alert, .notification');
    await expect(successMessage).toBeVisible({ timeout: 5000 });
    
    console.log('✅ Student transfer completed successfully');
  });

  test('should show transfer history for students', async ({ page }) => {
    console.log('🧪 Testing transfer history...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Click on a student to view details
    const studentRow = page.locator('tr').nth(1); // First student row
    await studentRow.click();
    
    // Look for transfer history section
    const historySection = page.locator('text="স্থানান্তর ইতিহাস", text="Transfer History", text="Branch History"');
    
    // If history is not immediately visible, look for a tab or button
    if (!(await historySection.isVisible({ timeout: 2000 }))) {
      const historyTab = page.locator('button:has-text("ইতিহাস"), button:has-text("History"), a:has-text("Transfer")');
      if (await historyTab.isVisible({ timeout: 2000 })) {
        await historyTab.click();
      }
    }
    
    // Check if transfer history information is displayed
    const historyEntries = page.locator('.transfer-entry, .history-item, .branch-history');
    
    // At minimum, there should be some transfer information or indication
    const hasHistoryInfo = await historySection.isVisible() || (await historyEntries.count()) > 0;
    expect(hasHistoryInfo).toBeTruthy();
    
    console.log('✅ Transfer history functionality is available');
  });

  test('should validate transfer requirements', async ({ page }) => {
    console.log('🧪 Testing transfer validation...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Click transfer button for first student
    const transferButton = page.locator('button:has-text("স্থানান্তর"), button:has-text("Transfer")').first();
    await transferButton.click();
    
    // Wait for modal to appear
    await page.waitForSelector('.modal, .dialog, [role="dialog"]', { timeout: 5000 });
    
    // Try to transfer without selecting a branch
    const confirmButton = page.locator('button:has-text("নিশ্চিত"), button:has-text("Confirm"), button:has-text("Transfer")');
    await confirmButton.click();
    
    // Check if validation error appears
    const errorMessage = page.locator('.error, .alert-error, text="Please select", text="নির্বাচন করুন"');
    
    // Should show validation error
    const hasValidation = await errorMessage.isVisible({ timeout: 3000 });
    expect(hasValidation).toBeTruthy();
    
    console.log('✅ Transfer validation works correctly');
  });

  test('should update student branch information after transfer', async ({ page }) => {
    console.log('🧪 Testing branch information update...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Get current branch information for first student
    const firstStudentRow = page.locator('tbody tr').first();
    const currentBranchInfo = await firstStudentRow.locator('td').nth(3).textContent(); // Assuming branch is in 4th column
    
    // Click transfer button
    const transferButton = firstStudentRow.locator('button:has-text("স্থানান্তর"), button:has-text("Transfer")');
    await transferButton.click();
    
    // Wait for modal and complete transfer
    await page.waitForSelector('.modal, .dialog, [role="dialog"]', { timeout: 5000 });
    
    const branchSelect = page.locator('select[name="branch"], select[name="branch_id"]');
    await branchSelect.selectOption({ index: 1 });
    
    const confirmButton = page.locator('button:has-text("নিশ্চিত"), button:has-text("Confirm"), button:has-text("Transfer")');
    await confirmButton.click();
    
    // Wait for transfer to complete
    await page.waitForTimeout(3000);
    
    // Refresh the page to see updated information
    await page.reload();
    await page.waitForTimeout(2000);
    
    // Check if branch information has changed
    const updatedStudentRow = page.locator('tbody tr').first();
    const newBranchInfo = await updatedStudentRow.locator('td').nth(3).textContent();
    
    // Branch information should be different after transfer
    expect(newBranchInfo).not.toBe(currentBranchInfo);
    
    console.log('✅ Student branch information updated after transfer');
  });

  test('should prevent transfer to same branch', async ({ page }) => {
    console.log('🧪 Testing same branch transfer prevention...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Click transfer button for first student
    const transferButton = page.locator('button:has-text("স্থানান্তর"), button:has-text("Transfer")').first();
    await transferButton.click();
    
    // Wait for modal to appear
    await page.waitForSelector('.modal, .dialog, [role="dialog"]', { timeout: 5000 });
    
    // Try to select the same branch (current branch should be disabled or not selectable)
    const branchSelect = page.locator('select[name="branch"], select[name="branch_id"]');
    const currentBranchOption = page.locator('option[disabled], option:has-text("Current")');
    
    // Current branch should be disabled or marked as current
    const hasCurrentBranchRestriction = await currentBranchOption.isVisible({ timeout: 2000 });
    
    if (hasCurrentBranchRestriction) {
      expect(hasCurrentBranchRestriction).toBeTruthy();
      console.log('✅ Same branch transfer is properly restricted');
    } else {
      // If no visual restriction, try selecting same branch and check for error
      await branchSelect.selectOption({ index: 0 });
      
      const confirmButton = page.locator('button:has-text("নিশ্চিত"), button:has-text("Confirm"), button:has-text("Transfer")');
      await confirmButton.click();
      
      const errorMessage = page.locator('.error, .alert-error, text="same branch", text="একই শাখা"');
      const hasError = await errorMessage.isVisible({ timeout: 3000 });
      expect(hasError).toBeTruthy();
      
      console.log('✅ Same branch transfer shows appropriate error');
    }
  });

  test('should maintain data integrity during transfer', async ({ page }) => {
    console.log('🧪 Testing data integrity during transfer...');
    
    // Wait for students to load
    await page.waitForTimeout(2000);
    
    // Get student information before transfer
    const firstStudentRow = page.locator('tbody tr').first();
    const studentName = await firstStudentRow.locator('td').first().textContent();
    const studentEmail = await firstStudentRow.locator('td').nth(1).textContent();
    
    // Perform transfer
    const transferButton = firstStudentRow.locator('button:has-text("স্থানান্তর"), button:has-text("Transfer")');
    await transferButton.click();
    
    await page.waitForSelector('.modal, .dialog, [role="dialog"]', { timeout: 5000 });
    
    const branchSelect = page.locator('select[name="branch"], select[name="branch_id"]');
    await branchSelect.selectOption({ index: 1 });
    
    const confirmButton = page.locator('button:has-text("নিশ্চিত"), button:has-text("Confirm"), button:has-text("Transfer")');
    await confirmButton.click();
    
    // Wait for transfer to complete
    await page.waitForTimeout(3000);
    await page.reload();
    await page.waitForTimeout(2000);
    
    // Verify student data is preserved
    const transferredStudentRow = page.locator(`tr:has-text("${studentName}")`);
    await expect(transferredStudentRow).toBeVisible();
    
    // Student name and email should remain the same
    const preservedName = await transferredStudentRow.locator('td').first().textContent();
    const preservedEmail = await transferredStudentRow.locator('td').nth(1).textContent();
    
    expect(preservedName).toContain(studentName?.trim() || '');
    expect(preservedEmail).toContain(studentEmail?.trim() || '');
    
    console.log('✅ Student data integrity maintained during transfer');
  });
});
