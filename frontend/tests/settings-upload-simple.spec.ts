import { test, expect } from '@playwright/test'

test.describe('Settings File Upload - Simple Test', () => {
  test('should verify file upload functionality exists', async ({ page }) => {
    // Navigate directly to settings page
    await page.goto('http://abc.localhost:5174/coaching/settings')
    
    // Wait for page to load
    await page.waitForTimeout(5000)
    
    // Check if page loads (look for any content)
    const pageContent = await page.textContent('body')
    console.log('Page content preview:', pageContent?.substring(0, 200))
    
    // Check for file inputs
    const fileInputs = page.locator('input[type="file"]')
    const fileInputCount = await fileInputs.count()
    
    console.log('Number of file inputs found:', fileInputCount)
    
    if (fileInputCount > 0) {
      // Check if file inputs accept images
      for (let i = 0; i < fileInputCount; i++) {
        const accept = await fileInputs.nth(i).getAttribute('accept')
        console.log(`File input ${i} accept attribute:`, accept)
        expect(accept).toBe('image/*')
      }
    }
    
    // Check for basic form elements
    const textInputs = page.locator('input[type="text"]')
    const textInputCount = await textInputs.count()
    console.log('Number of text inputs found:', textInputCount)
    
    // Check for save button
    const saveButtons = page.locator('button[type="submit"], button:has-text("Save")')
    const saveButtonCount = await saveButtons.count()
    console.log('Number of save buttons found:', saveButtonCount)
    
    // The test passes if we can access the page and find expected elements
    expect(fileInputCount).toBeGreaterThan(0)
  })

  test('should test API endpoint exists', async ({ page }) => {
    // Test that the upload endpoint exists
    const response = await page.request.post('http://abc.localhost:5174/api/coaching/settings/upload-image', {
      multipart: {
        image: {
          name: 'test.jpg',
          mimeType: 'image/jpeg',
          buffer: Buffer.from('fake-image-data')
        },
        type: 'logo'
      },
      headers: {
        'Authorization': 'Bearer fake-token'
      }
    })
    
    // Should get a response (even if unauthorized)
    console.log('Upload API response status:', response.status())
    expect(response.status()).toBeLessThan(500) // Should not be server error
  })

  test('should verify backend image processing utilities exist', async ({ page }) => {
    // Test that sharp is available for image processing
    const testResult = await page.evaluate(async () => {
      try {
        // Test if we can create a canvas (client-side image processing fallback)
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        return !!(canvas && ctx)
      } catch (error) {
        return false
      }
    })
    
    expect(testResult).toBe(true)
  })

  test('should verify uploads directory structure', async ({ page }) => {
    // Test that static file serving works
    const response = await page.request.get('http://abc.localhost:5174/uploads/images/nonexistent.jpg')
    
    // Should get 404 for non-existent file (not 500)
    console.log('Static file serving response:', response.status())
    expect(response.status()).toBe(404)
  })

  test('should verify settings API works', async ({ page }) => {
    // Test settings GET endpoint
    const response = await page.request.get('http://abc.localhost:5174/api/coaching/settings', {
      headers: {
        'Authorization': 'Bearer fake-token'
      }
    })
    
    console.log('Settings API response status:', response.status())
    // Should get some response (even if unauthorized)
    expect(response.status()).toBeLessThan(500)
  })
})
