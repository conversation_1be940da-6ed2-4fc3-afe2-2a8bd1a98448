import { test, expect } from '@playwright/test'

test.describe('Multi-Branch Management System', () => {
  test.beforeEach(async ({ page }) => {
    // Login as center admin
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForURL('**/coaching/dashboard')
  })

  test('should display branch management page elements', async ({ page }) => {
    // Navigate to branch management
    await page.goto('http://abc.localhost:5174/coaching/branches')
    
    // Check page title and description
    await expect(page.locator('h1')).toContainText('শাখা ব্যবস্থাপনা')
    await expect(page.locator('text=আপনার কোচিং সেন্টারের শাখাসমূহ পরিচালনা করুন')).toBeVisible()
    
    // Check tab navigation
    await expect(page.locator('button:has-text("শাখা ব্যবস্থাপনা")')).toBeVisible()
    await expect(page.locator('button:has-text("রুম ব্যবস্থাপনা")')).toBeVisible()
    await expect(page.locator('button:has-text("সময়সূচী ব্যবস্থাপনা")')).toBeVisible()
  })

  test('should display branch selector in navigation', async ({ page }) => {
    // Check if branch selector is present in the sidebar
    await page.click('button[aria-label="Open sidebar"]')
    
    // Look for branch selector or current branch indicator
    const branchSelector = page.locator('text=বর্তমান শাখা').or(page.locator('text=Current Branch'))
    await expect(branchSelector).toBeVisible()
  })

  test('should display rooms tab with room management', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/branches')
    
    // Click on rooms tab
    await page.click('button:has-text("রুম ব্যবস্থাপনা")')
    
    // Check rooms tab content
    await expect(page.locator('h2:has-text("রুম ব্যবস্থাপনা")')).toBeVisible()
    await expect(page.locator('text=শাখার রুম এবং স্থান পরিচালনা করুন')).toBeVisible()
    await expect(page.locator('button:has-text("রুম যোগ করুন")')).toBeVisible()
  })

  test('should open add room modal', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/branches')
    
    // Click on rooms tab
    await page.click('button:has-text("রুম ব্যবস্থাপনা")')
    
    // Click add room button
    await page.click('button:has-text("রুম যোগ করুন")')
    
    // Check modal is open
    await expect(page.locator('text=রুম যোগ করুন')).toBeVisible()
    await expect(page.locator('input[placeholder*="রুম কোড"]').or(page.locator('label:has-text("রুম কোড")'))).toBeVisible()
    await expect(page.locator('input[placeholder*="রুমের নাম"]').or(page.locator('label:has-text("রুমের নাম")'))).toBeVisible()
    
    // Close modal
    await page.click('button:has-text("✕")')
  })

  test('should display schedules tab', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/branches')
    
    // Click on schedules tab
    await page.click('button:has-text("সময়সূচী ব্যবস্থাপনা")')
    
    // Check schedules tab content
    await expect(page.locator('h2:has-text("সময়সূচী ব্যবস্থাপনা")')).toBeVisible()
    await expect(page.locator('text=কোর্স এবং রুমের সময়সূচী পরিচালনা করুন')).toBeVisible()
    await expect(page.locator('button:has-text("সময়সূচী যোগ করুন")')).toBeVisible()
  })

  test('should have branch management in navigation menu', async ({ page }) => {
    // Open sidebar
    await page.click('button[aria-label="Open sidebar"]')
    
    // Check for branch management navigation item
    await expect(page.locator('text=শাখা ব্যবস্থাপনা').or(page.locator('text=Branch Management'))).toBeVisible()
    
    // Click on branch management link
    await page.click('a[href="/coaching/branches"]')
    
    // Verify navigation worked
    await expect(page).toHaveURL('**/coaching/branches')
  })

  test('should display branch statistics and data', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/branches')
    
    // Wait for data to load
    await page.waitForTimeout(2000)
    
    // Check if branch data is displayed (either in table or cards)
    const branchData = page.locator('text=Main Branch').or(page.locator('text=MAIN'))
    await expect(branchData).toBeVisible()
  })

  test('should handle dark mode in branch management', async ({ page }) => {
    await page.goto('http://abc.localhost:5174/coaching/branches')
    
    // Toggle dark mode
    await page.click('button[aria-label*="theme"]').or(page.click('button:has-text("🌙")'))
    
    // Check if dark mode classes are applied
    const darkElement = page.locator('.dark').or(page.locator('[class*="dark"]'))
    await expect(darkElement).toBeVisible()
  })

  test('should maintain responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('http://abc.localhost:5174/coaching/branches')
    
    // Check if mobile navigation works
    await page.click('button[aria-label="Open sidebar"]')
    await expect(page.locator('text=শাখা ব্যবস্থাপনা').or(page.locator('text=Branch Management'))).toBeVisible()
    
    // Check if tabs are scrollable on mobile
    await expect(page.locator('nav').first()).toBeVisible()
  })
})
