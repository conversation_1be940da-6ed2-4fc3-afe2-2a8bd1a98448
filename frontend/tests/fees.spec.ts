import { test, expect } from '@playwright/test';

test.describe('Fee Management Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            role: 'center_admin'
          }
        })
      });
    });

    await page.goto('http://localhost:5173/fees');
  });

  test('should display fee management page elements', async ({ page }) => {
    // Check page title and description
    await expect(page.locator('h1.text-3xl')).toContainText('Fee Management');
    await expect(page.locator('text=Track student fees and payments')).toBeVisible();
    
    // Check Add Fee Record button
    await expect(page.locator('button:has-text("Add Fee Record")').first()).toBeVisible();
    
    // Check statistics cards
    await expect(page.locator('text=Total Fees')).toBeVisible();
    await expect(page.locator('text=Paid')).toBeVisible();
    await expect(page.locator('text=Pending')).toBeVisible();
    await expect(page.locator('text=Overdue')).toBeVisible();
  });

  test('should display fee statistics', async ({ page }) => {
    // Mock fee records
    await page.route('**/api/fees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          fees: [
            {
              id: 1,
              student_id: 1,
              student_name: 'John Doe',
              course_name: 'Mathematics',
              amount: 500.00,
              due_date: '2024-01-15',
              status: 'paid',
              payment_method: 'cash',
              paid_date: '2024-01-10',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              student_id: 2,
              student_name: 'Jane Smith',
              course_name: 'Physics',
              amount: 600.00,
              due_date: '2024-01-20',
              status: 'pending',
              created_at: '2024-01-01'
            }
          ]
        })
      });
    });

    // Mock students
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.reload();

    // Check statistics are calculated correctly
    await expect(page.locator('text=$1,100.00')).toBeVisible(); // Total
    await expect(page.locator('text=$500.00')).toBeVisible(); // Paid
    await expect(page.locator('text=$600.00')).toBeVisible(); // Pending
  });

  test('should display fee records list', async ({ page }) => {
    // Mock fee records
    await page.route('**/api/fees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          fees: [
            {
              id: 1,
              student_id: 1,
              student_name: 'John Doe',
              course_name: 'Mathematics',
              amount: 500.00,
              due_date: '2024-01-15',
              status: 'paid',
              payment_method: 'cash',
              paid_date: '2024-01-10',
              created_at: '2024-01-01'
            }
          ]
        })
      });
    });

    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.reload();

    // Check fee record is displayed
    await expect(page.locator('[data-testid="fee-record"]')).toBeVisible();
    await expect(page.locator('text=John Doe')).toBeVisible();
    await expect(page.locator('text=Mathematics')).toBeVisible();
    await expect(page.locator('text=$500')).toBeVisible();
  });

  test('should filter fee records by status', async ({ page }) => {
    // Mock fee records with different statuses
    await page.route('**/api/fees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          fees: [
            {
              id: 1,
              student_id: 1,
              student_name: 'John Doe',
              course_name: 'Mathematics',
              amount: 500.00,
              due_date: '2024-01-15',
              status: 'paid',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              student_id: 2,
              student_name: 'Jane Smith',
              course_name: 'Physics',
              amount: 600.00,
              due_date: '2024-01-20',
              status: 'pending',
              created_at: '2024-01-01'
            }
          ]
        })
      });
    });

    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.reload();

    // Filter by paid status
    await page.selectOption('select[id="status"]', 'paid');

    // Should show only paid records
    await expect(page.locator('text=John Doe')).toBeVisible();
    await expect(page.locator('text=Jane Smith')).not.toBeVisible();
  });

  test('should search fee records', async ({ page }) => {
    // Mock fee records
    await page.route('**/api/fees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          fees: [
            {
              id: 1,
              student_id: 1,
              student_name: 'John Doe',
              course_name: 'Mathematics',
              amount: 500.00,
              due_date: '2024-01-15',
              status: 'paid',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              student_id: 2,
              student_name: 'Jane Smith',
              course_name: 'Physics',
              amount: 600.00,
              due_date: '2024-01-20',
              status: 'pending',
              created_at: '2024-01-01'
            }
          ]
        })
      });
    });

    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.reload();

    // Search for John
    await page.fill('input[placeholder*="Search by student"]', 'John');

    // Should show only John's record
    await expect(page.locator('text=John Doe')).toBeVisible();
    await expect(page.locator('text=Jane Smith')).not.toBeVisible();
  });

  test('should open add fee form', async ({ page }) => {
    // Mock students
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', course_name: 'Mathematics' }
          ]
        })
      });
    });

    await page.route('**/api/fees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ fees: [] })
      });
    });

    await page.reload();

    // Click Add Fee Record button
    await page.click('button:has-text("Add Fee Record")');

    // Check form is visible
    await expect(page.locator('text=Add Fee Record')).toBeVisible();
    await expect(page.locator('select[id="student_id"]')).toBeVisible();
    await expect(page.locator('input[id="amount"]')).toBeVisible();
    await expect(page.locator('input[id="due_date"]')).toBeVisible();
    await expect(page.locator('input[id="notes"]')).toBeVisible();
  });

  test('should submit new fee record', async ({ page }) => {
    // Mock students
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', course_name: 'Mathematics' }
          ]
        })
      });
    });

    // Mock fee records
    await page.route('**/api/fees', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ fees: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            fee: {
              id: 1,
              student_id: 1,
              student_name: 'John Doe',
              course_name: 'Mathematics',
              amount: 500.00,
              due_date: '2024-01-15',
              status: 'pending',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Fee Record")');

    // Fill form
    await page.selectOption('select[id="student_id"]', '1');
    await page.fill('input[id="amount"]', '500.00');
    await page.fill('input[id="due_date"]', '2024-01-15');
    await page.fill('input[id="notes"]', 'Monthly fee');

    // Submit form
    await page.click('button[type="submit"]');

    // Form should close
    await expect(page.locator('text=Add Fee Record')).not.toBeVisible();
  });

  test('should mark fee as paid', async ({ page }) => {
    // Mock fee records
    await page.route('**/api/fees', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            fees: [
              {
                id: 1,
                student_id: 1,
                student_name: 'John Doe',
                course_name: 'Mathematics',
                amount: 500.00,
                due_date: '2024-01-15',
                status: 'pending',
                created_at: '2024-01-01'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/fees/1/pay', async route => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            fee: {
              id: 1,
              student_id: 1,
              student_name: 'John Doe',
              course_name: 'Mathematics',
              amount: 500.00,
              due_date: '2024-01-15',
              status: 'paid',
              paid_date: '2024-01-10',
              payment_method: 'cash',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.reload();

    // Click Mark Paid button
    await page.click('button:has-text("Mark Paid")');

    // The fee should be marked as paid (this would trigger a refetch)
    await expect(page.locator('[data-testid="fee-record"]')).toBeVisible();
  });

  test('should generate invoice', async ({ page }) => {
    // Mock fee records
    await page.route('**/api/fees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          fees: [
            {
              id: 1,
              student_id: 1,
              student_name: 'John Doe',
              course_name: 'Mathematics',
              amount: 500.00,
              due_date: '2024-01-15',
              status: 'paid',
              created_at: '2024-01-01'
            }
          ]
        })
      });
    });

    await page.route('**/api/fees/1/invoice', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          invoice: {
            invoice_number: 'INV-1',
            date: '2024-01-15',
            student: 'John Doe',
            course: 'Mathematics',
            amount: 500.00
          }
        })
      });
    });

    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ students: [] })
      });
    });

    await page.reload();

    // Click Invoice button
    await page.click('button:has-text("Invoice")');

    // Invoice generation should be triggered
    await expect(page.locator('[data-testid="fee-record"]')).toBeVisible();
  });

  test('should handle form validation', async ({ page }) => {
    // Mock students
    await page.route('**/api/students', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          students: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', course_name: 'Mathematics' }
          ]
        })
      });
    });

    await page.route('**/api/fees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ fees: [] })
      });
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Fee Record")');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Check HTML5 validation
    const studentSelect = page.locator('select[id="student_id"]');
    const amountInput = page.locator('input[id="amount"]');
    const dueDateInput = page.locator('input[id="due_date"]');
    
    await expect(studentSelect).toHaveAttribute('required');
    await expect(amountInput).toHaveAttribute('required');
    await expect(dueDateInput).toHaveAttribute('required');
  });
});
