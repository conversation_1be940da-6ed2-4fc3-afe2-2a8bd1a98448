import { test, expect } from '@playwright/test'

test.describe('Settings Form Tests', () => {
  test('should load settings page and verify form is populated', async ({ page }) => {
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    // Step 2: Navigate to settings page
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(5000) // Wait for API calls to complete
    
    // Step 3: Check if form fields are populated with data
    const nameInput = page.locator('input[placeholder*="coaching center name"]')
    const emailInput = page.locator('input[type="email"]')
    const phoneInput = page.locator('input[type="tel"]').first()
    const addressInput = page.locator('textarea[placeholder*="address"]')

    // Check if inputs exist
    await expect(nameInput).toBeVisible()
    await expect(emailInput).toBeVisible()
    await expect(phoneInput).toBeVisible()
    await expect(addressInput).toBeVisible()
    
    // Check if inputs have the expected values
    const nameValue = await nameInput.inputValue()
    const emailValue = await emailInput.inputValue()
    const phoneValue = await phoneInput.inputValue()
    const addressValue = await addressInput.inputValue()
    
    console.log('Form values:', {
      name: nameValue,
      email: emailValue,
      phone: phoneValue,
      address: addressValue
    })
    
    // Verify the values match what we expect from the API
    expect(nameValue).toContain('ABC Coaching Center')
    expect(emailValue).toBe('<EMAIL>')
    expect(phoneValue).toBe('******-123-4567')
    expect(addressValue).toContain('123 Main Street')
    
    console.log('✅ Settings form is properly populated with data')
  })

  test('should test form submission and update', async ({ page }) => {
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    // Step 2: Go to settings
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(5000)
    
    // Step 3: Update a field
    const phoneInput = page.locator('input[type="tel"]').first()
    await expect(phoneInput).toBeVisible()
    
    // Clear and update phone number
    await phoneInput.clear()
    await phoneInput.fill('******-888-7777')
    
    // Submit form
    const submitButton = page.locator('button[type="submit"]')
    await expect(submitButton).toBeVisible()
    await submitButton.click()
    
    // Wait for submission
    await page.waitForTimeout(3000)
    
    // Check if the value was updated (reload page and check)
    await page.reload()
    await page.waitForTimeout(5000)
    
    const updatedPhoneValue = await phoneInput.inputValue()
    console.log('Updated phone value:', updatedPhoneValue)
    expect(updatedPhoneValue).toBe('******-888-7777')
    
    console.log('✅ Settings form submission and update working')
  })

  test('should test file upload sections', async ({ page }) => {
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    // Step 2: Go to settings
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(5000)
    
    // Step 3: Check file upload sections
    const logoSection = page.locator('text=Invoice Logo').locator('..')
    const watermarkSection = page.locator('text=Invoice Watermark').locator('..')
    
    await expect(logoSection).toBeVisible()
    await expect(watermarkSection).toBeVisible()
    
    // Check for file input elements
    const fileInputs = page.locator('input[type="file"]')
    const fileInputCount = await fileInputs.count()
    
    console.log('File inputs found:', fileInputCount)
    expect(fileInputCount).toBeGreaterThanOrEqual(2) // Should have at least 2 file inputs
    
    // Check if current images are displayed (if any)
    const logoImage = page.locator('img[alt*="logo"], img[src*="logo"]')
    const logoImageExists = await logoImage.isVisible().catch(() => false)
    
    console.log('Logo image displayed:', logoImageExists)
    
    console.log('✅ File upload sections are present and functional')
  })

  test('should verify all form fields are present', async ({ page }) => {
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    // Step 2: Go to settings
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(5000)
    
    // Step 3: Check all expected form fields
    const fields = [
      { name: 'coaching_center_name', selector: 'input[placeholder*="coaching center name"]' },
      { name: 'email', selector: 'input[type="email"]' },
      { name: 'phone', selector: 'input[type="tel"]' },
      { name: 'address', selector: 'textarea[placeholder*="address"]' },
      { name: 'facebook_link', selector: 'input[placeholder*="facebook"]' },
      { name: 'whatsapp_number', selector: 'input[placeholder*="WhatsApp"]' }
    ]

    for (const field of fields) {
      const element = page.locator(field.selector).first()
      await expect(element).toBeVisible()

      const value = await element.inputValue()
      console.log(`${field.name}:`, value)

      // Most fields should have some value (not empty), except optional ones
      if (['coaching_center_name', 'email', 'phone', 'address'].includes(field.name)) {
        expect(value.length).toBeGreaterThan(0)
      }
    }
    
    console.log('✅ All expected form fields are present and populated')
  })

  test('should test responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Step 1: Login
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    // Step 2: Go to settings
    await page.goto('http://abc.localhost:5174/coaching/settings')
    await page.waitForTimeout(5000)
    
    // Step 3: Check if form is still usable on mobile
    const form = page.locator('form')
    await expect(form).toBeVisible()
    
    const nameInput = page.locator('input[placeholder*="coaching center name"]')
    await expect(nameInput).toBeVisible()
    
    // Check if input is clickable and editable
    await nameInput.click()
    await nameInput.clear()
    await nameInput.fill('Mobile Test Center')
    
    const value = await nameInput.inputValue()
    expect(value).toBe('Mobile Test Center')
    
    console.log('✅ Settings form is responsive and works on mobile')
  })
})
