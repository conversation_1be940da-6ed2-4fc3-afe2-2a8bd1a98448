import { test, expect } from '@playwright/test';

test.describe('HRM & Payroll Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            role: 'center_admin'
          }
        })
      });
    });

    await page.goto('http://localhost:5173/hrm');
  });

  test('should display HRM page elements', async ({ page }) => {
    // Check page title and description
    await expect(page.locator('h1.text-3xl')).toContainText('HRM & Payroll');
    await expect(page.locator('text=Manage employees, leaves, and payroll')).toBeVisible();
    
    // Check Add Employee button
    await expect(page.locator('button:has-text("Add Employee")').first()).toBeVisible();
    
    // Check statistics cards
    await expect(page.locator('text=Total Employees')).toBeVisible();
    await expect(page.locator('text=Active')).toBeVisible();
    await expect(page.locator('text=Pending Leaves')).toBeVisible();
    await expect(page.locator('text=Total Payroll')).toBeVisible();
  });

  test('should display employee statistics', async ({ page }) => {
    // Mock employees data
    await page.route('**/api/employees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          employees: [
            {
              id: 1,
              name: 'John Doe',
              email: '<EMAIL>',
              phone: '+1234567890',
              position: 'Teacher',
              department: 'Mathematics',
              salary: 50000,
              join_date: '2024-01-01',
              status: 'active',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              name: 'Jane Smith',
              email: '<EMAIL>',
              phone: '+1234567891',
              position: 'Teacher',
              department: 'Physics',
              salary: 55000,
              join_date: '2024-01-15',
              status: 'active',
              created_at: '2024-01-15'
            }
          ]
        })
      });
    });

    // Mock other endpoints
    await page.route('**/api/leave-requests', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ leaveRequests: [] })
      });
    });

    await page.route('**/api/payroll', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ payrollRecords: [] })
      });
    });

    await page.reload();

    // Check statistics are calculated correctly
    await expect(page.locator('text=2').first()).toBeVisible(); // Total employees
  });

  test('should display employees list', async ({ page }) => {
    // Mock employees data
    await page.route('**/api/employees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          employees: [
            {
              id: 1,
              name: 'John Doe',
              email: '<EMAIL>',
              phone: '+1234567890',
              position: 'Teacher',
              department: 'Mathematics',
              salary: 50000,
              join_date: '2024-01-01',
              status: 'active',
              created_at: '2024-01-01'
            }
          ]
        })
      });
    });

    await page.route('**/api/leave-requests', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ leaveRequests: [] })
      });
    });

    await page.route('**/api/payroll', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ payrollRecords: [] })
      });
    });

    await page.reload();

    // Check employee record is displayed
    await expect(page.locator('[data-testid="employee-record"]')).toBeVisible();
    await expect(page.locator('text=John Doe')).toBeVisible();
    await expect(page.locator('text=Teacher')).toBeVisible();
    await expect(page.locator('text=Mathematics')).toBeVisible();
  });

  test('should search employees', async ({ page }) => {
    // Mock employees data
    await page.route('**/api/employees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          employees: [
            {
              id: 1,
              name: 'John Doe',
              email: '<EMAIL>',
              position: 'Teacher',
              department: 'Mathematics',
              salary: 50000,
              join_date: '2024-01-01',
              status: 'active',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              name: 'Jane Smith',
              email: '<EMAIL>',
              position: 'Teacher',
              department: 'Physics',
              salary: 55000,
              join_date: '2024-01-15',
              status: 'active',
              created_at: '2024-01-15'
            }
          ]
        })
      });
    });

    await page.route('**/api/leave-requests', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ leaveRequests: [] })
      });
    });

    await page.route('**/api/payroll', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ payrollRecords: [] })
      });
    });

    await page.reload();

    // Search for John
    await page.fill('input[placeholder*="Search employees"]', 'John');

    // Should show only John's record
    await expect(page.locator('text=John Doe')).toBeVisible();
    await expect(page.locator('text=Jane Smith')).not.toBeVisible();
  });

  test('should open add employee form', async ({ page }) => {
    // Mock empty data
    await page.route('**/api/employees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ employees: [] })
      });
    });

    await page.route('**/api/leave-requests', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ leaveRequests: [] })
      });
    });

    await page.route('**/api/payroll', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ payrollRecords: [] })
      });
    });

    await page.reload();

    // Click Add Employee button
    await page.click('button:has-text("Add Employee")');

    // Check form is visible
    await expect(page.locator('text=Add Employee').nth(1)).toBeVisible();
    await expect(page.locator('input[id="name"]')).toBeVisible();
    await expect(page.locator('input[id="email"]')).toBeVisible();
    await expect(page.locator('input[id="position"]')).toBeVisible();
    await expect(page.locator('input[id="salary"]')).toBeVisible();
  });

  test('should submit new employee', async ({ page }) => {
    // Mock employees data
    await page.route('**/api/employees', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ employees: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            employee: {
              id: 1,
              name: 'John Doe',
              email: '<EMAIL>',
              position: 'Teacher',
              department: 'Mathematics',
              salary: 50000,
              join_date: '2024-01-01',
              status: 'active',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.route('**/api/leave-requests', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ leaveRequests: [] })
      });
    });

    await page.route('**/api/payroll', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ payrollRecords: [] })
      });
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Employee")');

    // Fill form
    await page.fill('input[id="name"]', 'John Doe');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="position"]', 'Teacher');
    await page.fill('input[id="department"]', 'Mathematics');
    await page.fill('input[id="salary"]', '50000');
    await page.fill('input[id="join_date"]', '2024-01-01');

    // Submit form
    await page.click('button[type="submit"]');

    // Form should close
    await expect(page.locator('text=Add Employee').nth(1)).not.toBeVisible();
  });

  test('should switch between tabs', async ({ page }) => {
    // Mock data
    await page.route('**/api/employees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ employees: [] })
      });
    });

    await page.route('**/api/leave-requests', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ leaveRequests: [] })
      });
    });

    await page.route('**/api/payroll', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ payrollRecords: [] })
      });
    });

    await page.reload();

    // Check default tab is employees
    await expect(page.locator('button:has-text("Employees")')).toHaveClass(/bg-background/);

    // Switch to leaves tab
    await page.click('button:has-text("Leave Requests")');
    await expect(page.locator('button:has-text("Leave Requests")')).toHaveClass(/bg-background/);

    // Switch to payroll tab
    await page.click('button:has-text("Payroll")');
    await expect(page.locator('button:has-text("Payroll")')).toHaveClass(/bg-background/);
  });

  test('should handle form validation', async ({ page }) => {
    // Mock data
    await page.route('**/api/employees', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ employees: [] })
      });
    });

    await page.route('**/api/leave-requests', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ leaveRequests: [] })
      });
    });

    await page.route('**/api/payroll', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ payrollRecords: [] })
      });
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Employee")');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Check HTML5 validation
    const nameInput = page.locator('input[id="name"]');
    const emailInput = page.locator('input[id="email"]');
    const positionInput = page.locator('input[id="position"]');
    const salaryInput = page.locator('input[id="salary"]');
    const joinDateInput = page.locator('input[id="join_date"]');
    
    await expect(nameInput).toHaveAttribute('required');
    await expect(emailInput).toHaveAttribute('required');
    await expect(positionInput).toHaveAttribute('required');
    await expect(salaryInput).toHaveAttribute('required');
    await expect(joinDateInput).toHaveAttribute('required');
  });
});
