import { test, expect } from '@playwright/test';

test.describe('Login Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:5173/login');
  });

  test('should display login form elements', async ({ page }) => {
    // Test that all form elements are present
    await expect(page.locator('h2:has-text("Sign In")')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    await expect(page.locator('input[name="rememberMe"]')).toBeVisible();
  });

  test('should show validation errors for empty form', async ({ page }) => {
    // Click submit without filling form
    await page.click('button[type="submit"]');
    
    // Check for HTML5 validation (required fields)
    const emailInput = page.locator('input[name="email"]');
    const passwordInput = page.locator('input[name="password"]');
    
    await expect(emailInput).toHaveAttribute('required');
    await expect(passwordInput).toHaveAttribute('required');
  });

  test('should show loading state during login', async ({ page }) => {
    // Fill in the form
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    
    // Mock the API response to delay it
    await page.route('**/api/auth/login', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Invalid credentials' })
      });
    });
    
    // Click submit
    await page.click('button[type="submit"]');
    
    // Check loading state
    await expect(page.locator('button[type="submit"]')).toBeDisabled();
    await expect(page.locator('.animate-spin')).toBeVisible();
    await expect(page.locator('text=Signing in...')).toBeVisible();
  });

  test('should handle login failure', async ({ page }) => {
    // Mock failed login response
    await page.route('**/api/auth/login', async route => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Invalid credentials' })
      });
    });

    // Set up dialog handler
    let dialogMessage = '';
    page.on('dialog', async dialog => {
      dialogMessage = dialog.message();
      await dialog.accept();
    });

    // Fill and submit form
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');

    // Wait a bit for the dialog to appear and be handled
    await page.waitForTimeout(1000);

    // Check that the dialog appeared with the right message
    expect(dialogMessage).toContain('Invalid credentials');
  });

  test('should redirect admin user to admin dashboard on successful login', async ({ page }) => {
    // Mock successful admin login
    await page.route('**/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: {
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'super_admin'
          }
        })
      });
    });
    
    // Fill and submit form
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    // Should redirect to admin dashboard
    await expect(page).toHaveURL('http://localhost:5173/admin/dashboard');
  });

  test('should redirect regular user to tenant dashboard on successful login', async ({ page }) => {
    // Mock successful tenant user login
    await page.route('**/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: {
            id: 2,
            name: 'John Doe',
            email: '<EMAIL>',
            role: 'center_admin'
          }
        })
      });
    });
    
    // Fill and submit form
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Should redirect to tenant dashboard
    await expect(page).toHaveURL('http://localhost:5173/dashboard');
  });

  test('should toggle theme', async ({ page }) => {
    // Check initial theme (light)
    await expect(page.locator('html')).not.toHaveClass(/dark/);

    // Click theme toggle (using more specific selector)
    await page.click('button:has(.sr-only:text("Toggle theme"))');

    // Check dark theme is applied
    await expect(page.locator('html')).toHaveClass(/dark/);

    // Click again to go back to light
    await page.click('button:has(.sr-only:text("Toggle theme"))');
    await expect(page.locator('html')).not.toHaveClass(/dark/);
  });

  test('should remember form data when remember me is checked', async ({ page }) => {
    // Fill form and check remember me
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.check('input[name="rememberMe"]');
    
    // Verify checkbox is checked
    await expect(page.locator('input[name="rememberMe"]')).toBeChecked();
  });

  test('should navigate back to homepage', async ({ page }) => {
    // Click on logo/brand to go back to homepage
    await page.click('a[href="/"]');
    await expect(page).toHaveURL('http://localhost:5173/');
  });
});

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:5173/');
  });

  test('should display homepage elements', async ({ page }) => {
    // Check main heading
    await expect(page.locator('h1')).toContainText('Modern');
    
    // Check CTA buttons
    await expect(page.locator('text=Get Started Free')).toBeVisible();
    await expect(page.locator('text=Learn More')).toBeVisible();
    
    // Check stats section
    await expect(page.locator('.text-3xl:has-text("500+")')).toBeVisible();
    await expect(page.locator('.text-3xl:has-text("50K+")')).toBeVisible();
    await expect(page.locator('.text-3xl:has-text("99.9%")')).toBeVisible();
    await expect(page.locator('.text-3xl:has-text("24/7")')).toBeVisible();
  });

  test('should navigate to login page', async ({ page }) => {
    await page.click('text=Sign In');
    await expect(page).toHaveURL('http://localhost:5173/login');
  });

  test('should scroll to features section', async ({ page }) => {
    await page.click('text=Learn More');
    
    // Check that features section is visible
    await expect(page.locator('text=Powerful Features')).toBeVisible();
  });

  test('should display all feature cards', async ({ page }) => {
    // Scroll to features section
    await page.locator('#features').scrollIntoViewIfNeeded();
    
    // Check feature cards (using CardTitle selectors)
    await expect(page.getByText('Student Management').first()).toBeVisible();
    await expect(page.getByText('Course Management').first()).toBeVisible();
    await expect(page.getByText('Payment Processing').first()).toBeVisible();
    await expect(page.getByText('Analytics & Reports').first()).toBeVisible();
    await expect(page.getByText('Multi-Branch Support').first()).toBeVisible();
    await expect(page.getByText('Mobile Responsive').first()).toBeVisible();
  });

  test('should display pricing plans', async ({ page }) => {
    // Scroll to pricing section
    await page.locator('#pricing').scrollIntoViewIfNeeded();
    
    // Check pricing cards
    await expect(page.locator('text=Starter')).toBeVisible();
    await expect(page.locator('text=Professional')).toBeVisible();
    await expect(page.locator('text=Enterprise')).toBeVisible();
    
    // Check "Most Popular" badge
    await expect(page.locator('text=Most Popular')).toBeVisible();
  });

  test('should toggle theme on homepage', async ({ page }) => {
    // Check initial theme
    await expect(page.locator('html')).not.toHaveClass(/dark/);

    // Toggle theme
    await page.click('button:has(.sr-only:text("Toggle theme"))');
    await expect(page.locator('html')).toHaveClass(/dark/);
  });
});
