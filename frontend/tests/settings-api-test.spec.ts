import { test, expect } from '@playwright/test'

test.describe('Settings API Tests', () => {
  test('should test settings API with proper authentication', async ({ page }) => {
    // Step 1: Login to get auth token
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    // Wait for login to complete
    await page.waitForTimeout(3000)
    
    // Step 2: Get the auth token from localStorage
    const token = await page.evaluate(() => localStorage.getItem('auth_token'))
    console.log('Auth token present:', !!token)
    console.log('Token preview:', token ? `${token.substring(0, 20)}...` : 'null')
    
    expect(token).toBeTruthy()
    
    // Step 3: Test settings GET API directly
    const settingsResponse = await page.request.get('http://localhost:3000/api/coaching/settings', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-Tenant': 'abc',
        'Content-Type': 'application/json'
      }
    })
    
    console.log('Settings API response status:', settingsResponse.status())
    console.log('Settings API response headers:', await settingsResponse.allHeaders())
    
    if (settingsResponse.status() === 200) {
      const settingsData = await settingsResponse.json()
      console.log('Settings data:', settingsData)
      expect(settingsData.success).toBe(true)
      expect(settingsData.settings).toBeDefined()
    } else {
      const errorData = await settingsResponse.json().catch(() => ({ error: 'No JSON response' }))
      console.log('Settings API error:', errorData)
    }
    
    // Should get 200 (success) not 401 (unauthorized)
    expect(settingsResponse.status()).toBe(200)
  })

  test('should test upload API with proper authentication', async ({ page }) => {
    // Step 1: Login to get auth token
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    // Wait for login to complete
    await page.waitForTimeout(3000)
    
    // Step 2: Get the auth token from localStorage
    const token = await page.evaluate(() => localStorage.getItem('auth_token'))
    expect(token).toBeTruthy()
    
    // Step 3: Test upload API (without file to test endpoint exists)
    const uploadResponse = await page.request.post('http://localhost:3000/api/coaching/settings/upload-image', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-Tenant': 'abc'
      },
      multipart: {
        type: 'logo'
        // Missing image file intentionally to test endpoint exists
      }
    })
    
    console.log('Upload API response status:', uploadResponse.status())
    
    if (uploadResponse.status() === 400) {
      const errorData = await uploadResponse.json()
      console.log('Upload API error (expected):', errorData)
      expect(errorData.error).toContain('No image file provided')
    } else {
      const responseData = await uploadResponse.json().catch(() => ({ error: 'No JSON response' }))
      console.log('Upload API response:', responseData)
    }
    
    // Should get 400 (bad request) not 401 (unauthorized)
    expect(uploadResponse.status()).toBe(400)
  })

  test('should test settings PUT API', async ({ page }) => {
    // Step 1: Login to get auth token
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    // Wait for login to complete
    await page.waitForTimeout(3000)
    
    // Step 2: Get the auth token from localStorage
    const token = await page.evaluate(() => localStorage.getItem('auth_token'))
    expect(token).toBeTruthy()
    
    // Step 3: Test settings PUT API
    const testSettings = {
      coaching_center_name: 'ABC Coaching Center - Updated',
      address: '123 Main Street, City, State 12345',
      phone: '******-123-4567',
      email: '<EMAIL>',
      facebook_link: 'https://facebook.com/abccoaching',
      whatsapp_number: '******-123-4567'
    }
    
    const updateResponse = await page.request.put('http://localhost:3000/api/coaching/settings', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-Tenant': 'abc',
        'Content-Type': 'application/json'
      },
      data: testSettings
    })
    
    console.log('Settings PUT API response status:', updateResponse.status())
    
    if (updateResponse.status() === 200) {
      const responseData = await updateResponse.json()
      console.log('Settings PUT response:', responseData)
      expect(responseData.success).toBe(true)
    } else {
      const errorData = await updateResponse.json().catch(() => ({ error: 'No JSON response' }))
      console.log('Settings PUT error:', errorData)
    }
    
    // Should get 200 (success) not 401 (unauthorized)
    expect(updateResponse.status()).toBe(200)
  })

  test('should verify database has settings data', async ({ page }) => {
    // Step 1: Login to get auth token
    await page.goto('http://abc.localhost:5174/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    // Wait for login to complete
    await page.waitForTimeout(3000)
    
    // Step 2: Get the auth token from localStorage
    const token = await page.evaluate(() => localStorage.getItem('auth_token'))
    expect(token).toBeTruthy()
    
    // Step 3: Get settings and verify data structure
    const settingsResponse = await page.request.get('http://localhost:3000/api/coaching/settings', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-Tenant': 'abc',
        'Content-Type': 'application/json'
      }
    })
    
    expect(settingsResponse.status()).toBe(200)
    
    const settingsData = await settingsResponse.json()
    console.log('Settings data structure:', settingsData)
    
    expect(settingsData.success).toBe(true)
    expect(settingsData.settings).toBeDefined()
    
    // Check if required settings exist
    const settings = settingsData.settings
    expect(settings.coaching_center_name).toBeDefined()
    expect(settings.email).toBeDefined()
    expect(settings.phone).toBeDefined()
    expect(settings.address).toBeDefined()
    
    console.log('✅ Settings data structure is correct')
    console.log('Settings values:', {
      name: settings.coaching_center_name,
      email: settings.email,
      phone: settings.phone,
      address: settings.address
    })
  })
})
