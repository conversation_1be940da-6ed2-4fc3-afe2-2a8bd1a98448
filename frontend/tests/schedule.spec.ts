import { test, expect } from '@playwright/test';

test.describe('Class Schedule Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            role: 'center_admin'
          }
        })
      });
    });

    await page.goto('http://localhost:5173/schedule');
  });

  test('should display schedule page elements', async ({ page }) => {
    // Check page title and description
    await expect(page.locator('h1.text-3xl')).toContainText('Class Schedule');
    await expect(page.locator('text=Manage class timetables and prevent conflicts')).toBeVisible();
    
    // Check view mode buttons
    await expect(page.locator('button:has-text("Week View")')).toBeVisible();
    await expect(page.locator('button:has-text("Month View")')).toBeVisible();
    
    // Check Add Schedule button
    await expect(page.locator('button:has-text("Add Schedule")')).toBeVisible();
  });

  test('should display weekly schedule view', async ({ page }) => {
    // Mock schedules response
    await page.route('**/api/schedules', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          schedules: [
            {
              id: 1,
              course_name: 'Mathematics',
              teacher_name: 'John Smith',
              room: 'Room 101',
              day_of_week: 'Monday',
              start_time: '09:00',
              end_time: '10:00',
              batch: 'Batch A',
              status: 'active',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              course_name: 'Physics',
              teacher_name: 'Sarah Johnson',
              room: 'Room 102',
              day_of_week: 'Tuesday',
              start_time: '10:00',
              end_time: '11:00',
              batch: 'Batch B',
              status: 'active',
              created_at: '2024-01-01'
            }
          ]
        })
      });
    });

    await page.reload();

    // Check weekly schedule is displayed
    await expect(page.locator('text=Weekly Schedule')).toBeVisible();
    
    // Check days of week are displayed
    await expect(page.locator('text=Monday')).toBeVisible();
    await expect(page.locator('text=Tuesday')).toBeVisible();
    
    // Check schedule items are displayed
    await expect(page.locator('[data-testid="schedule-item"]')).toHaveCount(2);
    await expect(page.locator('text=Mathematics')).toBeVisible();
    await expect(page.locator('text=Physics')).toBeVisible();
  });

  test('should switch to month view', async ({ page }) => {
    // Mock empty schedules response
    await page.route('**/api/schedules', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ schedules: [] })
      });
    });

    await page.reload();

    // Click Month View button
    await page.click('button:has-text("Month View")');

    // Check month view is displayed
    await expect(page.locator('text=Monthly Calendar View')).toBeVisible();
    await expect(page.locator('text=Full month schedule overview')).toBeVisible();
  });

  test('should open add schedule form', async ({ page }) => {
    // Mock empty schedules response
    await page.route('**/api/schedules', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ schedules: [] })
      });
    });

    await page.reload();

    // Click Add Schedule button
    await page.click('button:has-text("Add Schedule")');

    // Check form is visible
    await expect(page.locator('text=Create New Schedule')).toBeVisible();
    await expect(page.locator('input[id="course_id"]')).toBeVisible();
    await expect(page.locator('input[id="teacher_id"]')).toBeVisible();
    await expect(page.locator('input[id="room"]')).toBeVisible();
    await expect(page.locator('input[id="batch"]')).toBeVisible();
    await expect(page.locator('select[id="day_of_week"]')).toBeVisible();
    await expect(page.locator('select[id="start_time"]')).toBeVisible();
    await expect(page.locator('select[id="end_time"]')).toBeVisible();
  });

  test('should submit new schedule form', async ({ page }) => {
    // Mock schedules response
    await page.route('**/api/schedules', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ schedules: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            schedule: {
              id: 1,
              course_name: 'Mathematics',
              teacher_name: 'John Smith',
              room: 'Room 101',
              day_of_week: 'Monday',
              start_time: '09:00',
              end_time: '10:00',
              batch: 'Batch A',
              status: 'active',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Schedule")');

    // Fill form
    await page.fill('input[id="course_id"]', 'Mathematics');
    await page.fill('input[id="teacher_id"]', 'John Smith');
    await page.fill('input[id="room"]', 'Room 101');
    await page.fill('input[id="batch"]', 'Batch A');
    await page.selectOption('select[id="day_of_week"]', 'Monday');
    await page.selectOption('select[id="start_time"]', '09:00');
    await page.selectOption('select[id="end_time"]', '10:00');

    // Submit form
    await page.click('button[type="submit"]');

    // Form should close
    await expect(page.locator('text=Create New Schedule')).not.toBeVisible();
  });

  test('should handle form validation', async ({ page }) => {
    // Mock empty schedules response
    await page.route('**/api/schedules', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ schedules: [] })
      });
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Schedule")');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Check HTML5 validation
    const courseInput = page.locator('input[id="course_id"]');
    const teacherInput = page.locator('input[id="teacher_id"]');
    const roomInput = page.locator('input[id="room"]');
    const daySelect = page.locator('select[id="day_of_week"]');
    const startTimeSelect = page.locator('select[id="start_time"]');
    const endTimeSelect = page.locator('select[id="end_time"]');
    
    await expect(courseInput).toHaveAttribute('required');
    await expect(teacherInput).toHaveAttribute('required');
    await expect(roomInput).toHaveAttribute('required');
    await expect(daySelect).toHaveAttribute('required');
    await expect(startTimeSelect).toHaveAttribute('required');
    await expect(endTimeSelect).toHaveAttribute('required');
  });

  test('should edit schedule', async ({ page }) => {
    // Mock schedules response
    await page.route('**/api/schedules', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            schedules: [
              {
                id: 1,
                course_name: 'Mathematics',
                teacher_name: 'John Smith',
                room: 'Room 101',
                day_of_week: 'Monday',
                start_time: '09:00',
                end_time: '10:00',
                batch: 'Batch A',
                status: 'active',
                created_at: '2024-01-01'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/schedules/1', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            schedule: {
              id: 1,
              course_name: 'Mathematics Updated',
              teacher_name: 'John Smith',
              room: 'Room 101',
              day_of_week: 'Monday',
              start_time: '09:00',
              end_time: '10:00',
              batch: 'Batch A',
              status: 'active',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.reload();

    // Click edit button for first schedule
    await page.locator('[data-testid="schedule-item"]').first().locator('button').first().click();

    // Check edit form is visible with pre-filled data
    await expect(page.locator('text=Edit Schedule')).toBeVisible();
    await expect(page.locator('input[id="course_id"]')).toHaveValue('Mathematics');
    await expect(page.locator('input[id="room"]')).toHaveValue('Room 101');

    // Update course
    await page.fill('input[id="course_id"]', 'Mathematics Updated');

    // Submit form
    await page.click('button:has-text("Update Schedule")');

    // Form should close
    await expect(page.locator('text=Edit Schedule')).not.toBeVisible();
  });

  test('should delete schedule', async ({ page }) => {
    // Mock schedules response
    await page.route('**/api/schedules', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            schedules: [
              {
                id: 1,
                course_name: 'Mathematics',
                teacher_name: 'John Smith',
                room: 'Room 101',
                day_of_week: 'Monday',
                start_time: '09:00',
                end_time: '10:00',
                batch: 'Batch A',
                status: 'active',
                created_at: '2024-01-01'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/schedules/1', async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        });
      }
    });

    await page.reload();

    // Set up dialog handler for confirmation
    let dialogMessage = '';
    page.on('dialog', async dialog => {
      dialogMessage = dialog.message();
      await dialog.accept();
    });

    // Click delete button (second button in the schedule item)
    await page.locator('[data-testid="schedule-item"]').first().locator('button').nth(1).click();

    // Wait for dialog
    await page.waitForTimeout(500);

    // Check confirmation dialog appeared
    expect(dialogMessage).toContain('Are you sure you want to delete');
  });

  test('should detect schedule conflicts', async ({ page }) => {
    // Mock schedules response with conflict
    await page.route('**/api/schedules', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ schedules: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Teacher has a conflicting schedule at this time' })
        });
      }
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Schedule")');

    // Fill form with conflicting schedule
    await page.fill('input[id="course_id"]', 'Mathematics');
    await page.fill('input[id="teacher_id"]', 'John Smith');
    await page.fill('input[id="room"]', 'Room 101');
    await page.selectOption('select[id="day_of_week"]', 'Monday');
    await page.selectOption('select[id="start_time"]', '09:00');
    await page.selectOption('select[id="end_time"]', '10:00');

    // Set up alert handler
    let alertMessage = '';
    page.on('dialog', async dialog => {
      alertMessage = dialog.message();
      await dialog.accept();
    });

    // Submit form
    await page.click('button[type="submit"]');

    // Wait for alert
    await page.waitForTimeout(500);

    // Check conflict alert appeared
    expect(alertMessage).toContain('Teacher has a conflicting schedule');
  });

  test('should display schedule for different days', async ({ page }) => {
    // Mock schedules response with multiple days
    await page.route('**/api/schedules', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          schedules: [
            {
              id: 1,
              course_name: 'Mathematics',
              teacher_name: 'John Smith',
              room: 'Room 101',
              day_of_week: 'Monday',
              start_time: '09:00',
              end_time: '10:00',
              batch: 'Batch A',
              status: 'active',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              course_name: 'Physics',
              teacher_name: 'Sarah Johnson',
              room: 'Room 102',
              day_of_week: 'Wednesday',
              start_time: '11:00',
              end_time: '12:00',
              batch: 'Batch B',
              status: 'active',
              created_at: '2024-01-01'
            },
            {
              id: 3,
              course_name: 'Chemistry',
              teacher_name: 'Mike Wilson',
              room: 'Room 103',
              day_of_week: 'Friday',
              start_time: '14:00',
              end_time: '15:00',
              batch: 'Batch C',
              status: 'active',
              created_at: '2024-01-01'
            }
          ]
        })
      });
    });

    await page.reload();

    // Check schedules are displayed in correct days
    await expect(page.locator('text=Mathematics')).toBeVisible();
    await expect(page.locator('text=Physics')).toBeVisible();
    await expect(page.locator('text=Chemistry')).toBeVisible();
    
    // Check schedule items count
    await expect(page.locator('[data-testid="schedule-item"]')).toHaveCount(3);
  });
});
