import { test, expect } from '@playwright/test';

test.describe('Courses Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            role: 'center_admin'
          }
        })
      });
    });

    await page.goto('http://localhost:5173/courses');
  });

  test('should display courses page elements', async ({ page }) => {
    // Check page title and description
    await expect(page.locator('h1.text-3xl')).toContainText('Courses');
    await expect(page.locator('text=Manage your course catalog')).toBeVisible();
    
    // Check Add Course button
    await expect(page.locator('button:has-text("Add Course")').first()).toBeVisible();
    
    // Check search bar
    await expect(page.locator('input[placeholder*="Search courses"]')).toBeVisible();
  });

  test('should show empty state when no courses', async ({ page }) => {
    // Mock empty courses response
    await page.route('**/api/courses', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ courses: [] })
      });
    });

    await page.reload();

    // Check empty state
    await expect(page.locator('text=No courses found')).toBeVisible();
    await expect(page.locator('text=Get started by adding your first course')).toBeVisible();
  });

  test('should display courses list', async ({ page }) => {
    // Mock courses response
    await page.route('**/api/courses', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          courses: [
            {
              id: 1,
              name: 'Mathematics',
              description: 'Advanced mathematics course',
              duration: '6 months',
              price: 299.99,
              start_date: '2024-01-15',
              end_date: '2024-07-15',
              status: 'active',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              name: 'Physics',
              description: 'Physics fundamentals',
              duration: '4 months',
              price: 199.99,
              start_date: '2024-02-01',
              end_date: '2024-06-01',
              status: 'active',
              created_at: '2024-01-15'
            }
          ]
        })
      });
    });

    await page.reload();

    // Check courses are displayed
    await expect(page.locator('h3:has-text("Mathematics")')).toBeVisible();
    await expect(page.locator('text=Advanced mathematics course')).toBeVisible();
    await expect(page.locator('h3:has-text("Physics")')).toBeVisible();
    await expect(page.locator('text=Physics fundamentals')).toBeVisible();
  });

  test('should filter courses by search term', async ({ page }) => {
    // Mock courses response
    await page.route('**/api/courses', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          courses: [
            {
              id: 1,
              name: 'Mathematics',
              description: 'Advanced mathematics course',
              duration: '6 months',
              price: 299.99,
              status: 'active',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              name: 'Physics',
              description: 'Physics fundamentals',
              duration: '4 months',
              price: 199.99,
              status: 'active',
              created_at: '2024-01-15'
            }
          ]
        })
      });
    });

    await page.reload();

    // Search for Mathematics
    await page.fill('input[placeholder*="Search courses"]', 'Mathematics');
    
    // Should show Mathematics but not Physics
    await expect(page.locator('h3:has-text("Mathematics")')).toBeVisible();
    await expect(page.locator('h3:has-text("Physics")')).not.toBeVisible();
  });

  test('should open add course form', async ({ page }) => {
    // Mock empty courses response
    await page.route('**/api/courses', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ courses: [] })
      });
    });

    await page.reload();

    // Click Add Course button
    await page.click('button:has-text("Add Course")');

    // Check form is visible
    await expect(page.locator('text=Add New Course')).toBeVisible();
    await expect(page.locator('input[id="name"]')).toBeVisible();
    await expect(page.locator('input[id="description"]')).toBeVisible();
    await expect(page.locator('input[id="duration"]')).toBeVisible();
    await expect(page.locator('input[id="price"]')).toBeVisible();
    await expect(page.locator('input[id="start_date"]')).toBeVisible();
    await expect(page.locator('input[id="end_date"]')).toBeVisible();
  });

  test('should submit new course form', async ({ page }) => {
    // Mock courses response
    await page.route('**/api/courses', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ courses: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            course: {
              id: 1,
              name: 'New Course',
              description: 'New course description',
              duration: '3 months',
              price: 150.00,
              status: 'active',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Course")');

    // Fill form
    await page.fill('input[id="name"]', 'New Course');
    await page.fill('input[id="description"]', 'New course description');
    await page.fill('input[id="duration"]', '3 months');
    await page.fill('input[id="price"]', '150.00');
    await page.fill('input[id="start_date"]', '2024-01-01');
    await page.fill('input[id="end_date"]', '2024-04-01');

    // Submit form
    await page.click('button[type="submit"]');

    // Form should close
    await expect(page.locator('text=Add New Course')).not.toBeVisible();
  });

  test('should handle form validation', async ({ page }) => {
    // Mock empty courses response
    await page.route('**/api/courses', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ courses: [] })
      });
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Course")');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Check HTML5 validation
    const nameInput = page.locator('input[id="name"]');
    const durationInput = page.locator('input[id="duration"]');
    const priceInput = page.locator('input[id="price"]');
    
    await expect(nameInput).toHaveAttribute('required');
    await expect(durationInput).toHaveAttribute('required');
    await expect(priceInput).toHaveAttribute('required');
  });

  test('should edit course', async ({ page }) => {
    // Mock courses response
    await page.route('**/api/courses', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            courses: [
              {
                id: 1,
                name: 'Mathematics',
                description: 'Advanced mathematics course',
                duration: '6 months',
                price: 299.99,
                status: 'active',
                created_at: '2024-01-01'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/courses/1', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            course: {
              id: 1,
              name: 'Mathematics Updated',
              description: 'Advanced mathematics course',
              duration: '6 months',
              price: 299.99,
              status: 'active',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.reload();

    // Click edit button for first course
    await page.locator('[data-testid="course-card"]').first().locator('button').first().click();

    // Check edit form is visible with pre-filled data
    await expect(page.locator('text=Edit Course')).toBeVisible();
    await expect(page.locator('input[id="name"]')).toHaveValue('Mathematics');
    await expect(page.locator('input[id="duration"]')).toHaveValue('6 months');

    // Update name
    await page.fill('input[id="name"]', 'Mathematics Updated');

    // Submit form
    await page.click('button:has-text("Update Course")');

    // Form should close
    await expect(page.locator('text=Edit Course')).not.toBeVisible();
  });

  test('should delete course', async ({ page }) => {
    // Mock courses response
    await page.route('**/api/courses', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            courses: [
              {
                id: 1,
                name: 'Mathematics',
                description: 'Advanced mathematics course',
                duration: '6 months',
                price: 299.99,
                status: 'active',
                created_at: '2024-01-01'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/courses/1', async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        });
      }
    });

    await page.reload();

    // Set up dialog handler for confirmation
    let dialogMessage = '';
    page.on('dialog', async dialog => {
      dialogMessage = dialog.message();
      await dialog.accept();
    });

    // Click delete button (second icon button in the course card)
    await page.locator('[data-testid="course-card"]').first().locator('button').nth(1).click();

    // Wait for dialog
    await page.waitForTimeout(500);

    // Check confirmation dialog appeared
    expect(dialogMessage).toContain('Are you sure you want to delete');
  });

  test('should toggle theme', async ({ page }) => {
    // Check initial theme
    await expect(page.locator('html')).not.toHaveClass(/dark/);
    
    // Click theme toggle
    await page.click('button:has(.sr-only:text("Toggle theme"))');
    
    // Check dark theme is applied
    await expect(page.locator('html')).toHaveClass(/dark/);
  });

  test('should logout', async ({ page }) => {
    // Mock logout response
    await page.route('**/api/auth/logout', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });

    // Click logout button
    await page.click('button:has(.sr-only:text("Sign out"))');

    // Should redirect to login page
    await expect(page).toHaveURL('http://localhost:5173/login');
  });
});
