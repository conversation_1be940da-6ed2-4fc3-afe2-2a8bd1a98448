import { test, expect } from '@playwright/test';

test.describe('Teachers Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            role: 'center_admin'
          }
        })
      });
    });

    await page.goto('http://localhost:5173/teachers');
  });

  test('should display teachers page elements', async ({ page }) => {
    // Check page title and description
    await expect(page.locator('h1.text-3xl')).toContainText('Teachers');
    await expect(page.locator('text=Manage your teaching staff')).toBeVisible();
    
    // Check Add Teacher button
    await expect(page.locator('button:has-text("Add Teacher")').first()).toBeVisible();
    
    // Check search bar
    await expect(page.locator('input[placeholder*="Search teachers"]')).toBeVisible();
  });

  test('should show empty state when no teachers', async ({ page }) => {
    // Mock empty teachers response
    await page.route('**/api/teachers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ teachers: [] })
      });
    });

    await page.reload();

    // Check empty state
    await expect(page.locator('text=No teachers found')).toBeVisible();
    await expect(page.locator('text=Get started by adding your first teacher')).toBeVisible();
  });

  test('should display teachers list', async ({ page }) => {
    // Mock teachers response
    await page.route('**/api/teachers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          teachers: [
            {
              id: 1,
              name: 'John Smith',
              email: '<EMAIL>',
              phone: '+1234567890',
              subject: 'Mathematics',
              qualification: 'M.Sc Mathematics',
              experience: '5 years',
              salary: 3000,
              status: 'active',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              name: 'Sarah Johnson',
              email: '<EMAIL>',
              phone: '+1234567891',
              subject: 'Physics',
              qualification: 'M.Sc Physics',
              experience: '3 years',
              salary: 2800,
              status: 'active',
              created_at: '2024-01-15'
            }
          ]
        })
      });
    });

    await page.reload();

    // Check teachers are displayed
    await expect(page.locator('h3:has-text("John Smith")')).toBeVisible();
    await expect(page.locator('text=Mathematics')).toBeVisible();
    await expect(page.locator('h3:has-text("Sarah Johnson")')).toBeVisible();
    await expect(page.locator('text=Physics')).toBeVisible();
  });

  test('should filter teachers by search term', async ({ page }) => {
    // Mock teachers response
    await page.route('**/api/teachers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          teachers: [
            {
              id: 1,
              name: 'John Smith',
              email: '<EMAIL>',
              subject: 'Mathematics',
              qualification: 'M.Sc Mathematics',
              status: 'active',
              created_at: '2024-01-01'
            },
            {
              id: 2,
              name: 'Sarah Johnson',
              email: '<EMAIL>',
              subject: 'Physics',
              qualification: 'M.Sc Physics',
              status: 'active',
              created_at: '2024-01-15'
            }
          ]
        })
      });
    });

    await page.reload();

    // Search for Mathematics
    await page.fill('input[placeholder*="Search teachers"]', 'Mathematics');
    
    // Should show John but not Sarah
    await expect(page.locator('h3:has-text("John Smith")')).toBeVisible();
    await expect(page.locator('h3:has-text("Sarah Johnson")')).not.toBeVisible();
  });

  test('should open add teacher form', async ({ page }) => {
    // Mock empty teachers response
    await page.route('**/api/teachers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ teachers: [] })
      });
    });

    await page.reload();

    // Click Add Teacher button
    await page.click('button:has-text("Add Teacher")');

    // Check form is visible
    await expect(page.locator('text=Add New Teacher')).toBeVisible();
    await expect(page.locator('input[id="name"]')).toBeVisible();
    await expect(page.locator('input[id="email"]')).toBeVisible();
    await expect(page.locator('input[id="phone"]')).toBeVisible();
    await expect(page.locator('input[id="subject"]')).toBeVisible();
    await expect(page.locator('input[id="qualification"]')).toBeVisible();
    await expect(page.locator('input[id="experience"]')).toBeVisible();
    await expect(page.locator('input[id="salary"]')).toBeVisible();
  });

  test('should submit new teacher form', async ({ page }) => {
    // Mock teachers response
    await page.route('**/api/teachers', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ teachers: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            teacher: {
              id: 1,
              name: 'New Teacher',
              email: '<EMAIL>',
              subject: 'Chemistry',
              status: 'active',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Teacher")');

    // Fill form
    await page.fill('input[id="name"]', 'New Teacher');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="phone"]', '+1234567890');
    await page.fill('input[id="subject"]', 'Chemistry');
    await page.fill('input[id="qualification"]', 'M.Sc Chemistry');
    await page.fill('input[id="experience"]', '2 years');
    await page.fill('input[id="salary"]', '2500');

    // Submit form
    await page.click('button[type="submit"]');

    // Form should close
    await expect(page.locator('text=Add New Teacher')).not.toBeVisible();
  });

  test('should handle form validation', async ({ page }) => {
    // Mock empty teachers response
    await page.route('**/api/teachers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ teachers: [] })
      });
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Teacher")');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Check HTML5 validation
    const nameInput = page.locator('input[id="name"]');
    const emailInput = page.locator('input[id="email"]');
    const subjectInput = page.locator('input[id="subject"]');
    
    await expect(nameInput).toHaveAttribute('required');
    await expect(emailInput).toHaveAttribute('required');
    await expect(subjectInput).toHaveAttribute('required');
  });

  test('should edit teacher', async ({ page }) => {
    // Mock teachers response
    await page.route('**/api/teachers', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            teachers: [
              {
                id: 1,
                name: 'John Smith',
                email: '<EMAIL>',
                subject: 'Mathematics',
                qualification: 'M.Sc Mathematics',
                status: 'active',
                created_at: '2024-01-01'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/teachers/1', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            teacher: {
              id: 1,
              name: 'John Smith Updated',
              email: '<EMAIL>',
              subject: 'Mathematics',
              status: 'active',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.reload();

    // Click edit button for first teacher
    await page.locator('[data-testid="teacher-card"]').first().locator('button').first().click();

    // Check edit form is visible with pre-filled data
    await expect(page.locator('text=Edit Teacher')).toBeVisible();
    await expect(page.locator('input[id="name"]')).toHaveValue('John Smith');
    await expect(page.locator('input[id="subject"]')).toHaveValue('Mathematics');

    // Update name
    await page.fill('input[id="name"]', 'John Smith Updated');

    // Submit form
    await page.click('button:has-text("Update Teacher")');

    // Form should close
    await expect(page.locator('text=Edit Teacher')).not.toBeVisible();
  });

  test('should delete teacher', async ({ page }) => {
    // Mock teachers response
    await page.route('**/api/teachers', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            teachers: [
              {
                id: 1,
                name: 'John Smith',
                email: '<EMAIL>',
                subject: 'Mathematics',
                status: 'active',
                created_at: '2024-01-01'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/teachers/1', async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        });
      }
    });

    await page.reload();

    // Set up dialog handler for confirmation
    let dialogMessage = '';
    page.on('dialog', async dialog => {
      dialogMessage = dialog.message();
      await dialog.accept();
    });

    // Click delete button (second icon button in the teacher card)
    await page.locator('[data-testid="teacher-card"]').first().locator('button').nth(1).click();

    // Wait for dialog
    await page.waitForTimeout(500);

    // Check confirmation dialog appeared
    expect(dialogMessage).toContain('Are you sure you want to delete');
  });

  test('should assign subjects to teacher', async ({ page }) => {
    // Mock teachers response with subject assignment
    await page.route('**/api/teachers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          teachers: [
            {
              id: 1,
              name: 'John Smith',
              email: '<EMAIL>',
              subject: 'Mathematics, Physics',
              qualification: 'M.Sc Mathematics',
              status: 'active',
              created_at: '2024-01-01'
            }
          ]
        })
      });
    });

    await page.reload();

    // Check multiple subjects are displayed
    await expect(page.locator('text=Mathematics, Physics')).toBeVisible();
  });

  test('should display teacher dashboard access status', async ({ page }) => {
    // Mock teachers response
    await page.route('**/api/teachers', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          teachers: [
            {
              id: 1,
              name: 'John Smith',
              email: '<EMAIL>',
              subject: 'Mathematics',
              status: 'active',
              created_at: '2024-01-01'
            }
          ]
        })
      });
    });

    await page.reload();

    // Check teacher status is displayed
    await expect(page.locator('text=active')).toBeVisible();
  });

  test('should deactivate teacher', async ({ page }) => {
    // Mock teachers response
    await page.route('**/api/teachers', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            teachers: [
              {
                id: 1,
                name: 'John Smith',
                email: '<EMAIL>',
                subject: 'Mathematics',
                status: 'active',
                created_at: '2024-01-01'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/teachers/1', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            teacher: {
              id: 1,
              name: 'John Smith',
              email: '<EMAIL>',
              subject: 'Mathematics',
              status: 'inactive',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.reload();

    // Edit teacher to change status
    await page.locator('[data-testid="teacher-card"]').first().locator('button').first().click();

    // Form should be visible
    await expect(page.locator('text=Edit Teacher')).toBeVisible();
  });
});
