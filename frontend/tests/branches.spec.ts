import { test, expect } from '@playwright/test';

test.describe('Branches Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            role: 'center_admin'
          }
        })
      });
    });

    await page.goto('http://localhost:5173/branches');
  });

  test('should display branches page elements', async ({ page }) => {
    // Check page title and description
    await expect(page.locator('h1.text-3xl')).toContainText('Branches');
    await expect(page.locator('text=Manage your branch locations')).toBeVisible();
    
    // Check Add Branch button
    await expect(page.locator('button:has-text("Add Branch")').first()).toBeVisible();
    
    // Check search bar
    await expect(page.locator('input[placeholder*="Search branches"]')).toBeVisible();
  });

  test('should show empty state when no branches', async ({ page }) => {
    // Mock empty branches response
    await page.route('**/api/branches', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ branches: [] })
      });
    });

    await page.reload();

    // Check empty state
    await expect(page.locator('text=No branches found')).toBeVisible();
    await expect(page.locator('text=Get started by adding your first branch')).toBeVisible();
  });

  test('should display branches list', async ({ page }) => {
    // Mock branches response
    await page.route('**/api/branches', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          branches: [
            {
              id: 1,
              name: 'Main Branch',
              address: '123 Main St, City',
              phone: '************',
              email: '<EMAIL>',
              manager_name: 'John Manager',
              status: 'active',
              created_at: '2024-01-15'
            },
            {
              id: 2,
              name: 'Secondary Branch',
              address: '456 Oak Ave, Town',
              phone: '************',
              email: '<EMAIL>',
              manager_name: 'Jane Manager',
              status: 'active',
              created_at: '2024-02-01'
            }
          ]
        })
      });
    });

    await page.reload();

    // Check branches are displayed
    await expect(page.locator('text=Main Branch')).toBeVisible();
    await expect(page.locator('text=123 Main St, City')).toBeVisible();
    await expect(page.locator('text=Secondary Branch')).toBeVisible();
    await expect(page.locator('text=456 Oak Ave, Town')).toBeVisible();
  });

  test('should filter branches by search term', async ({ page }) => {
    // Mock branches response
    await page.route('**/api/branches', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          branches: [
            {
              id: 1,
              name: 'Main Branch',
              address: '123 Main St, City',
              phone: '************',
              email: '<EMAIL>',
              manager_name: 'John Manager',
              status: 'active',
              created_at: '2024-01-15'
            },
            {
              id: 2,
              name: 'Secondary Branch',
              address: '456 Oak Ave, Town',
              phone: '************',
              email: '<EMAIL>',
              manager_name: 'Jane Manager',
              status: 'active',
              created_at: '2024-02-01'
            }
          ]
        })
      });
    });

    await page.reload();

    // Search for Main
    await page.fill('input[placeholder*="Search branches"]', 'Main');
    
    // Should show Main Branch but not Secondary Branch
    await expect(page.locator('text=Main Branch')).toBeVisible();
    await expect(page.locator('text=Secondary Branch')).not.toBeVisible();
  });

  test('should open add branch form', async ({ page }) => {
    // Mock empty branches response
    await page.route('**/api/branches', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ branches: [] })
      });
    });

    await page.reload();

    // Click Add Branch button
    await page.click('button:has-text("Add Branch")');

    // Check form is visible
    await expect(page.locator('text=Add New Branch')).toBeVisible();
    await expect(page.locator('input[id="name"]')).toBeVisible();
    await expect(page.locator('input[id="address"]')).toBeVisible();
    await expect(page.locator('input[id="phone"]')).toBeVisible();
    await expect(page.locator('input[id="email"]')).toBeVisible();
    await expect(page.locator('input[id="manager_name"]')).toBeVisible();
  });

  test('should submit new branch form', async ({ page }) => {
    // Mock branches response
    await page.route('**/api/branches', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ branches: [] })
        });
      } else if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            branch: {
              id: 1,
              name: 'New Branch',
              address: '789 New St, City',
              phone: '555-0123',
              email: '<EMAIL>',
              manager_name: 'New Manager',
              status: 'active',
              created_at: '2024-01-01'
            }
          })
        });
      }
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Branch")');

    // Fill form
    await page.fill('input[id="name"]', 'New Branch');
    await page.fill('input[id="address"]', '789 New St, City');
    await page.fill('input[id="phone"]', '555-0123');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="manager_name"]', 'New Manager');

    // Submit form
    await page.click('button[type="submit"]');

    // Form should close
    await expect(page.locator('text=Add New Branch')).not.toBeVisible();
  });

  test('should handle form validation', async ({ page }) => {
    // Mock empty branches response
    await page.route('**/api/branches', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ branches: [] })
      });
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Branch")');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Check HTML5 validation
    const nameInput = page.locator('input[id="name"]');
    const addressInput = page.locator('input[id="address"]');
    
    await expect(nameInput).toHaveAttribute('required');
    await expect(addressInput).toHaveAttribute('required');
  });

  test('should cancel form', async ({ page }) => {
    // Mock empty branches response
    await page.route('**/api/branches', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ branches: [] })
      });
    });

    await page.reload();

    // Open add form
    await page.click('button:has-text("Add Branch")');

    // Fill some data
    await page.fill('input[id="name"]', 'Test Branch');

    // Cancel form
    await page.click('button:has-text("Cancel")');

    // Form should close and data should be cleared
    await expect(page.locator('text=Add New Branch')).not.toBeVisible();
  });

  test('should edit branch', async ({ page }) => {
    // Mock branches response
    await page.route('**/api/branches', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            branches: [
              {
                id: 1,
                name: 'Main Branch',
                address: '123 Main St, City',
                phone: '************',
                email: '<EMAIL>',
                manager_name: 'John Manager',
                status: 'active',
                created_at: '2024-01-15'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/branches/1', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            branch: {
              id: 1,
              name: 'Main Branch Updated',
              address: '123 Main St, City',
              phone: '************',
              email: '<EMAIL>',
              manager_name: 'John Manager',
              status: 'active',
              created_at: '2024-01-15'
            }
          })
        });
      }
    });

    await page.reload();

    // Click edit button for first branch (first icon button in the branch card)
    await page.locator('[data-testid="branch-card"]').first().locator('button').first().click();

    // Check edit form is visible with pre-filled data
    await expect(page.locator('text=Edit Branch')).toBeVisible();
    await expect(page.locator('input[id="name"]')).toHaveValue('Main Branch');
    await expect(page.locator('input[id="address"]')).toHaveValue('123 Main St, City');

    // Update name
    await page.fill('input[id="name"]', 'Main Branch Updated');

    // Submit form
    await page.click('button:has-text("Update Branch")');

    // Form should close
    await expect(page.locator('text=Edit Branch')).not.toBeVisible();
  });

  test('should delete branch', async ({ page }) => {
    // Mock branches response
    await page.route('**/api/branches', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            branches: [
              {
                id: 1,
                name: 'Main Branch',
                address: '123 Main St, City',
                phone: '************',
                email: '<EMAIL>',
                manager_name: 'John Manager',
                status: 'active',
                created_at: '2024-01-15'
              }
            ]
          })
        });
      }
    });

    await page.route('**/api/branches/1', async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        });
      }
    });

    await page.reload();

    // Set up dialog handler for confirmation
    let dialogMessage = '';
    page.on('dialog', async dialog => {
      dialogMessage = dialog.message();
      await dialog.accept();
    });

    // Click delete button (second icon button in the branch card)
    await page.locator('[data-testid="branch-card"]').first().locator('button').nth(1).click();

    // Wait for dialog
    await page.waitForTimeout(500);

    // Check confirmation dialog appeared
    expect(dialogMessage).toContain('Are you sure you want to delete');
  });

  test('should toggle theme', async ({ page }) => {
    // Check initial theme
    await expect(page.locator('html')).not.toHaveClass(/dark/);
    
    // Click theme toggle
    await page.click('button:has(.sr-only:text("Toggle theme"))');
    
    // Check dark theme is applied
    await expect(page.locator('html')).toHaveClass(/dark/);
  });

  test('should logout', async ({ page }) => {
    // Mock logout response
    await page.route('**/api/auth/logout', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });

    // Click logout button
    await page.click('button:has(.sr-only:text("Sign out"))');

    // Should redirect to login page
    await expect(page).toHaveURL('http://localhost:5173/login');
  });
});
