-- Fix Student Transfer System Schema
USE coaching;

-- Add missing columns to student_transfers table
ALTER TABLE student_transfers 
ADD COLUMN center_id INT NOT NULL DEFAULT 1 AFTER id,
ADD COLUMN transfer_reason TEXT AFTER to_branch_id,
ADD COLUMN effective_date DATE NOT NULL DEFAULT (CURDATE()) AFTER transfer_date,
ADD COLUMN data_migration_status ENUM('pending', 'in_progress', 'completed', 'failed') DEFAULT 'pending' AFTER notes,
ADD COLUMN migration_details JSON AFTER data_migration_status;

-- Add foreign key constraints
ALTER TABLE student_transfers 
ADD CONSTRAINT fk_transfers_center FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_transfers_student FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_transfers_from_branch FOREIGN KEY (from_branch_id) REFERENCES branches(id) ON DELETE RESTRICT,
ADD CONSTRAINT fk_transfers_to_branch FOREIGN KEY (to_branch_id) REFERENCES branches(id) ON DELETE RESTRICT,
ADD CONSTRAINT fk_transfers_requested_by FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE RESTRICT,
ADD CONSTRAINT fk_transfers_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL;

-- Create indexes
CREATE INDEX idx_student_transfers ON student_transfers(student_id, transfer_date DESC);
CREATE INDEX idx_branch_transfers ON student_transfers(from_branch_id, to_branch_id);
CREATE INDEX idx_transfer_status ON student_transfers(status);
CREATE INDEX idx_center_transfers ON student_transfers(center_id, transfer_date DESC);

-- Create transfer_data_backup table
CREATE TABLE IF NOT EXISTS transfer_data_backup (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transfer_id INT NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id INT NOT NULL,
    original_data JSON NOT NULL,
    backup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transfer_id) REFERENCES student_transfers(id) ON DELETE CASCADE,
    INDEX idx_transfer_backup (transfer_id, table_name)
);

-- Create triggers for transfer logging
DELIMITER //

CREATE TRIGGER IF NOT EXISTS log_transfer_request
AFTER INSERT ON student_transfers
FOR EACH ROW
BEGIN
    INSERT INTO student_audit_logs (
        center_id, student_id, action_type, old_value, new_value, 
        field_name, description, performed_by
    ) VALUES (
        NEW.center_id, 
        NEW.student_id, 
        'transfer_requested', 
        (SELECT branch_name FROM branches WHERE id = NEW.from_branch_id), 
        (SELECT branch_name FROM branches WHERE id = NEW.to_branch_id), 
        'branch_transfer',
        CONCAT('Transfer requested from ', 
               (SELECT branch_name FROM branches WHERE id = NEW.from_branch_id), 
               ' to ', 
               (SELECT branch_name FROM branches WHERE id = NEW.to_branch_id)),
        NEW.requested_by
    );
END//

CREATE TRIGGER IF NOT EXISTS log_transfer_completion
AFTER UPDATE ON student_transfers
FOR EACH ROW
BEGIN
    -- Log when transfer status changes to completed
    IF OLD.status != 'completed' AND NEW.status = 'completed' THEN
        INSERT INTO student_audit_logs (
            center_id, student_id, action_type, old_value, new_value, 
            field_name, description, performed_by
        ) VALUES (
            NEW.center_id, 
            NEW.student_id, 
            'transfer_completed', 
            (SELECT branch_name FROM branches WHERE id = NEW.from_branch_id), 
            (SELECT branch_name FROM branches WHERE id = NEW.to_branch_id), 
            'branch_transfer',
            CONCAT('Transfer completed from ', 
                   (SELECT branch_name FROM branches WHERE id = NEW.from_branch_id), 
                   ' to ', 
                   (SELECT branch_name FROM branches WHERE id = NEW.to_branch_id)),
            COALESCE(NEW.approved_by, NEW.requested_by)
        );
    END IF;
    
    -- Log when transfer is approved
    IF OLD.status != 'approved' AND NEW.status = 'approved' THEN
        INSERT INTO student_audit_logs (
            center_id, student_id, action_type, old_value, new_value, 
            field_name, description, performed_by
        ) VALUES (
            NEW.center_id, 
            NEW.student_id, 
            'transfer_approved', 
            'pending', 
            'approved', 
            'transfer_status',
            'Transfer request approved',
            NEW.approved_by
        );
    END IF;
END//

DELIMITER ;

-- Insert sample transfer data
INSERT IGNORE INTO student_transfers (
    center_id, student_id, from_branch_id, to_branch_id, 
    transfer_reason, transfer_date, effective_date, status, 
    requested_by, approved_by, approved_at
) VALUES
(1, 3, 1, 3, 'Student requested transfer for convenience', '2025-01-05', '2025-01-10', 'completed', 1, 1, '2025-01-06 10:00:00'),
(1, 3, 3, 4, 'Family relocated to Gulshan area', '2025-01-15', '2025-01-20', 'pending', 1, NULL, NULL);

-- Create a view for easy transfer history queries
CREATE OR REPLACE VIEW student_transfer_history AS
SELECT 
    st.id,
    st.student_id,
    u.name as student_name,
    u.email as student_email,
    fb.branch_name as from_branch,
    tb.branch_name as to_branch,
    st.transfer_reason,
    st.transfer_date,
    st.effective_date,
    st.status,
    requester.name as requested_by_name,
    approver.name as approved_by_name,
    st.approved_at,
    st.notes,
    st.data_migration_status,
    st.created_at
FROM student_transfers st
JOIN users u ON st.student_id = u.id
JOIN branches fb ON st.from_branch_id = fb.id
JOIN branches tb ON st.to_branch_id = tb.id
LEFT JOIN users requester ON st.requested_by = requester.id
LEFT JOIN users approver ON st.approved_by = approver.id
ORDER BY st.transfer_date DESC, st.created_at DESC;

-- Show the updated table structure
DESCRIBE student_transfers;
SELECT COUNT(*) as transfer_records FROM student_transfers;
