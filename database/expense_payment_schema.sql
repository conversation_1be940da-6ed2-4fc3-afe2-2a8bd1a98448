-- Additional tables for expense management and payment gateways
USE coaching;

-- Payment Gateways table
CREATE TABLE payment_gateways (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    type ENUM('razorpay', 'payu', 'stripe', 'paypal', 'phonepe', 'gpay', 'paytm', 'cash', 'bank_transfer', 'cheque', 'upi') NOT NULL,
    api_key VARCHAR(500),
    api_secret VARCHAR(500),
    webhook_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    configuration JSON, -- Store gateway-specific config
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE
);

-- Expense Types table
CREATE TABLE expense_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('infrastructure', 'utilities', 'salaries', 'marketing', 'supplies', 'maintenance', 'transport', 'other') DEFAULT 'other',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE
);

-- Expenses table
CREATE TABLE expenses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    expense_type_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    amount DECIMAL(10,2) NOT NULL,
    expense_date DATE NOT NULL,
    payment_method ENUM('cash', 'card', 'bank_transfer', 'upi', 'cheque', 'online') NOT NULL,
    payment_gateway_id INT,
    vendor_name VARCHAR(255),
    vendor_contact VARCHAR(255),
    receipt_number VARCHAR(100),
    receipt_url VARCHAR(500),
    notes TEXT,
    status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
    approved_by INT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (expense_type_id) REFERENCES expense_types(id) ON DELETE RESTRICT,
    FOREIGN KEY (payment_gateway_id) REFERENCES payment_gateways(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Comprehensive Transactions table (replaces the simple payments table)
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    transaction_id VARCHAR(255) UNIQUE NOT NULL, -- Unique transaction identifier
    type ENUM('fee_collection', 'expense', 'refund', 'adjustment') NOT NULL,
    
    -- Fee collection specific fields
    student_id INT,
    fee_id INT,
    
    -- Expense specific fields
    expense_id INT,
    
    -- Common transaction fields
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    payment_method ENUM('cash', 'card', 'bank_transfer', 'upi', 'cheque', 'online', 'razorpay', 'payu', 'stripe', 'paypal', 'phonepe', 'gpay', 'paytm') NOT NULL,
    payment_gateway_id INT,
    
    -- Gateway response fields
    gateway_transaction_id VARCHAR(255),
    gateway_payment_id VARCHAR(255),
    gateway_order_id VARCHAR(255),
    gateway_signature VARCHAR(500),
    gateway_response JSON,
    
    -- Transaction status and details
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    transaction_date DATETIME NOT NULL,
    description TEXT,
    receipt_number VARCHAR(100),
    receipt_url VARCHAR(500),
    
    -- Additional metadata
    ip_address VARCHAR(45),
    user_agent TEXT,
    notes TEXT,
    processed_by INT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE SET NULL,
    FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE SET NULL,
    FOREIGN KEY (payment_gateway_id) REFERENCES payment_gateways(id) ON DELETE SET NULL,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_center_type (center_id, type),
    INDEX idx_status_date (status, transaction_date),
    INDEX idx_gateway_transaction (gateway_transaction_id)
);

-- Insert default payment gateways for the sample center
INSERT INTO payment_gateways (center_id, name, type, is_active, is_default) VALUES
(1, 'Cash Payment', 'cash', TRUE, TRUE),
(1, 'Bank Transfer', 'bank_transfer', TRUE, FALSE),
(1, 'UPI Payment', 'upi', TRUE, FALSE),
(1, 'Cheque Payment', 'cheque', TRUE, FALSE),
(1, 'Razorpay', 'razorpay', FALSE, FALSE),
(1, 'PayU', 'payu', FALSE, FALSE),
(1, 'PhonePe', 'phonepe', FALSE, FALSE),
(1, 'Google Pay', 'gpay', FALSE, FALSE);

-- Insert default expense types for the sample center
INSERT INTO expense_types (center_id, name, description, category, is_active) VALUES
(1, 'Electricity Bill', 'Monthly electricity expenses', 'utilities', TRUE),
(1, 'Water Bill', 'Monthly water expenses', 'utilities', TRUE),
(1, 'Internet & Phone', 'Internet and telephone bills', 'utilities', TRUE),
(1, 'Teacher Salaries', 'Monthly salary payments to teachers', 'salaries', TRUE),
(1, 'Staff Salaries', 'Monthly salary payments to staff', 'salaries', TRUE),
(1, 'Rent', 'Monthly building rent', 'infrastructure', TRUE),
(1, 'Maintenance', 'Building and equipment maintenance', 'maintenance', TRUE),
(1, 'Stationery & Supplies', 'Office and teaching supplies', 'supplies', TRUE),
(1, 'Marketing & Advertising', 'Promotional activities and advertising', 'marketing', TRUE),
(1, 'Transport', 'Vehicle fuel and maintenance', 'transport', TRUE),
(1, 'Equipment Purchase', 'Computers, projectors, furniture', 'infrastructure', TRUE),
(1, 'Software Licenses', 'Educational software and licenses', 'infrastructure', TRUE);

-- Insert sample expenses
INSERT INTO expenses (center_id, expense_type_id, title, description, amount, expense_date, payment_method, vendor_name, vendor_contact, receipt_number, status, created_by) VALUES
(1, 1, 'January Electricity Bill', 'Monthly electricity bill payment', 8500.00, '2024-01-15', 'bank_transfer', 'State Electricity Board', '1800-123-456', 'EB001', 'paid', 1),
(1, 4, 'Teacher Salary - January', 'Monthly salary for Dr. Sarah Wilson', 50000.00, '2024-01-31', 'bank_transfer', 'Dr. Sarah Wilson', '<EMAIL>', 'SAL001', 'paid', 1),
(1, 6, 'January Rent', 'Monthly building rent payment', 25000.00, '2024-01-01', 'cheque', 'Property Owner', '**********', 'RENT001', 'paid', 1),
(1, 8, 'Office Supplies', 'Stationery and teaching materials', 3500.00, '2024-01-20', 'cash', 'Local Stationery Store', '**********', 'STAT001', 'paid', 1);

-- Insert sample transactions for fee collections
INSERT INTO transactions (center_id, transaction_id, type, student_id, fee_id, amount, payment_method, payment_gateway_id, status, transaction_date, description, receipt_number, processed_by) VALUES
(1, 'TXN001', 'fee_collection', 3, 1, 15000.00, 'upi', 3, 'completed', '2024-01-20 10:30:00', 'JEE Course Fee Payment', 'RCP001', 1),
(1, 'TXN002', 'fee_collection', 3, 1, 5000.00, 'cash', 1, 'completed', '2024-01-25 14:15:00', 'Partial Fee Payment', 'RCP002', 1);

-- Insert sample transactions for expenses
INSERT INTO transactions (center_id, transaction_id, type, expense_id, amount, payment_method, payment_gateway_id, status, transaction_date, description, receipt_number, processed_by) VALUES
(1, 'TXN003', 'expense', 1, 8500.00, 'bank_transfer', 2, 'completed', '2024-01-15 09:00:00', 'Electricity Bill Payment', 'EXP001', 1),
(1, 'TXN004', 'expense', 2, 50000.00, 'bank_transfer', 2, 'completed', '2024-01-31 16:00:00', 'Teacher Salary Payment', 'EXP002', 1),
(1, 'TXN005', 'expense', 3, 25000.00, 'cheque', 4, 'completed', '2024-01-01 11:00:00', 'Monthly Rent Payment', 'EXP003', 1);

-- Update the existing payments table to reference transactions
-- ALTER TABLE payments ADD COLUMN transaction_id INT;
-- ALTER TABLE payments ADD FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE SET NULL;
