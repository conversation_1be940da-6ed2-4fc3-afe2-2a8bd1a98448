-- Migration: Add blood_group field to users table
-- Description: Add blood group field to support student blood group information

USE coaching;

-- Add blood_group column to users table
ALTER TABLE users 
ADD COLUMN blood_group ENUM('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-') NULL 
AFTER gender;

-- Add index for blood_group for better query performance
CREATE INDEX idx_users_blood_group ON users(blood_group);

-- Update some existing students with sample blood group data for testing
UPDATE users SET blood_group = 'A+' WHERE id = 3 AND role = 'student';

-- Show the updated table structure
DESCRIBE users;
