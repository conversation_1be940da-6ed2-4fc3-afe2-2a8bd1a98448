-- Student Transfer System Schema
-- This file creates the inter-branch student transfer system

USE coaching;

-- Create student_transfers table for tracking branch transfers
CREATE TABLE IF NOT EXISTS student_transfers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    student_id INT NOT NULL,
    from_branch_id INT NOT NULL,
    to_branch_id INT NOT NULL,
    transfer_reason TEXT,
    transfer_date DATE NOT NULL,
    effective_date DATE NOT NULL,
    status ENUM('pending', 'approved', 'completed', 'cancelled') DEFAULT 'pending',
    requested_by INT NOT NULL,
    approved_by INT NULL,
    approved_at TIMESTAMP NULL,
    notes TEXT,
    data_migration_status ENUM('pending', 'in_progress', 'completed', 'failed') DEFAULT 'pending',
    migration_details JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    <PERSON>OREIG<PERSON> KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (from_branch_id) REFERENCES branches(id) ON DELETE RESTRICT,
    FOREIGN KEY (to_branch_id) REFERENCES branches(id) ON DELETE RESTRICT,
    FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_student_transfers (student_id, transfer_date DESC),
    INDEX idx_branch_transfers (from_branch_id, to_branch_id),
    INDEX idx_transfer_status (status),
    INDEX idx_center_transfers (center_id, transfer_date DESC)
);

-- Create transfer_data_backup table for storing original data before transfer
CREATE TABLE IF NOT EXISTS transfer_data_backup (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transfer_id INT NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id INT NOT NULL,
    original_data JSON NOT NULL,
    backup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transfer_id) REFERENCES student_transfers(id) ON DELETE CASCADE,
    INDEX idx_transfer_backup (transfer_id, table_name)
);

-- Add transfer-related audit log types
ALTER TABLE student_audit_logs 
MODIFY COLUMN action_type ENUM(
    'status_change', 'profile_update', 'course_assignment', 'fee_payment', 
    'created', 'deleted', 'transfer_requested', 'transfer_approved', 
    'transfer_completed', 'transfer_cancelled'
) NOT NULL;

-- Create trigger to log transfer requests
DELIMITER //

CREATE TRIGGER IF NOT EXISTS log_transfer_request
AFTER INSERT ON student_transfers
FOR EACH ROW
BEGIN
    INSERT INTO student_audit_logs (
        center_id, student_id, action_type, old_value, new_value, 
        field_name, description, performed_by
    ) VALUES (
        NEW.center_id, 
        NEW.student_id, 
        'transfer_requested', 
        (SELECT branch_name FROM branches WHERE id = NEW.from_branch_id), 
        (SELECT branch_name FROM branches WHERE id = NEW.to_branch_id), 
        'branch_transfer',
        CONCAT('Transfer requested from ', 
               (SELECT branch_name FROM branches WHERE id = NEW.from_branch_id), 
               ' to ', 
               (SELECT branch_name FROM branches WHERE id = NEW.to_branch_id)),
        NEW.requested_by
    );
END//

-- Create trigger to log transfer completion
CREATE TRIGGER IF NOT EXISTS log_transfer_completion
AFTER UPDATE ON student_transfers
FOR EACH ROW
BEGIN
    -- Log when transfer status changes to completed
    IF OLD.status != 'completed' AND NEW.status = 'completed' THEN
        INSERT INTO student_audit_logs (
            center_id, student_id, action_type, old_value, new_value, 
            field_name, description, performed_by
        ) VALUES (
            NEW.center_id, 
            NEW.student_id, 
            'transfer_completed', 
            (SELECT branch_name FROM branches WHERE id = NEW.from_branch_id), 
            (SELECT branch_name FROM branches WHERE id = NEW.to_branch_id), 
            'branch_transfer',
            CONCAT('Transfer completed from ', 
                   (SELECT branch_name FROM branches WHERE id = NEW.from_branch_id), 
                   ' to ', 
                   (SELECT branch_name FROM branches WHERE id = NEW.to_branch_id)),
            COALESCE(NEW.approved_by, NEW.requested_by)
        );
    END IF;
    
    -- Log when transfer is approved
    IF OLD.status != 'approved' AND NEW.status = 'approved' THEN
        INSERT INTO student_audit_logs (
            center_id, student_id, action_type, old_value, new_value, 
            field_name, description, performed_by
        ) VALUES (
            NEW.center_id, 
            NEW.student_id, 
            'transfer_approved', 
            'pending', 
            'approved', 
            'transfer_status',
            'Transfer request approved',
            NEW.approved_by
        );
    END IF;
END//

DELIMITER ;

-- Insert sample transfer data
INSERT IGNORE INTO student_transfers (
    center_id, student_id, from_branch_id, to_branch_id, 
    transfer_reason, transfer_date, effective_date, status, 
    requested_by, approved_by, approved_at
) VALUES
(1, 3, 1, 3, 'Student requested transfer for convenience', '2025-01-05', '2025-01-10', 'completed', 1, 1, '2025-01-06 10:00:00'),
(1, 3, 3, 4, 'Family relocated to Gulshan area', '2025-01-15', '2025-01-20', 'pending', 1, NULL, NULL);

-- Create a view for easy transfer history queries
CREATE OR REPLACE VIEW student_transfer_history AS
SELECT 
    st.id,
    st.student_id,
    u.name as student_name,
    u.email as student_email,
    fb.branch_name as from_branch,
    tb.branch_name as to_branch,
    st.transfer_reason,
    st.transfer_date,
    st.effective_date,
    st.status,
    requester.name as requested_by_name,
    approver.name as approved_by_name,
    st.approved_at,
    st.notes,
    st.data_migration_status,
    st.created_at
FROM student_transfers st
JOIN users u ON st.student_id = u.id
JOIN branches fb ON st.from_branch_id = fb.id
JOIN branches tb ON st.to_branch_id = tb.id
LEFT JOIN users requester ON st.requested_by = requester.id
LEFT JOIN users approver ON st.approved_by = approver.id
ORDER BY st.transfer_date DESC, st.created_at DESC;

-- Show the created tables and sample data
DESCRIBE student_transfers;
SELECT COUNT(*) as transfer_records FROM student_transfers;
SELECT * FROM student_transfer_history LIMIT 3;
