-- Insert sample data for exam results system
USE coaching;

-- Create subjects table if it doesn't exist
CREATE TABLE IF NOT EXISTS subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50),
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    INDEX idx_center (center_id),
    INDEX idx_status (status)
);

-- Insert sample subjects for ABC Coaching Center
INSERT IGNORE INTO subjects (center_id, name, code, description, status) VALUES
(1, 'Mathematics', 'MATH101', 'Advanced Mathematics for competitive exams', 'active'),
(1, 'Physics', 'PHY101', 'Physics fundamentals and problem solving', 'active'),
(1, 'Chemistry', 'CHEM101', 'Organic and Inorganic Chemistry', 'active'),
(1, 'Biology', 'BIO101', 'Biology for NEET preparation', 'active'),
(1, 'English', 'ENG101', 'English language and literature', 'active');

-- Insert sample exams for ABC Coaching Center
INSERT IGNORE INTO exams (
    center_id, exam_name, exam_description, exam_type, subject_id,
    exam_date, start_time, end_time, 
    total_marks, passing_marks, status, created_by
) VALUES
(1, 'Mathematics Midterm', 'Midterm examination for JEE Main Mathematics', 'midterm', 1, 
 '2025-07-20', '09:00:00', '12:00:00', 
 200.00, 80.00, 'draft', 1),
(1, 'JEE Main Mock Test 1', 'Full-length mock test for JEE Main', 'mock_test', 1, 
 '2025-07-15', '10:00:00', '13:00:00', 
 300.00, 120.00, 'scheduled', 1),
(1, 'NEET Biology Test', 'Biology quiz for NEET preparation', 'quiz', 4, 
 '2025-07-12', '14:00:00', '15:30:00', 
 100.00, 40.00, 'completed', 1);

-- Insert sample exam results
INSERT IGNORE INTO exam_results (
    exam_id, student_id, batch_id, total_marks, obtained_marks, percentage, grade, 
    is_absent, status, entered_by, entered_at
) VALUES
-- Results for JEE Main Mock Test 1 (assuming exam_id will be 2)
(2, 3, 1, 300.00, 245.00, 81.67, 'A', FALSE, 'published', 1, NOW()),
-- Results for NEET Biology Test (assuming exam_id will be 3)
(3, 3, 2, 100.00, 85.00, 85.00, 'A', FALSE, 'published', 1, NOW()),
-- Results for Mathematics Midterm (assuming exam_id will be 1)
(1, 3, 1, 200.00, 165.00, 82.50, 'A', FALSE, 'draft', 1, NOW());
