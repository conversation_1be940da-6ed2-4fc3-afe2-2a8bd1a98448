-- Fix exam_results table and add missing columns
USE coaching;

-- Add missing columns to exam_results table
ALTER TABLE exam_results 
ADD COLUMN IF NOT EXISTS total_marks DECIMAL(10,2) NOT NULL DEFAULT 100.00 AFTER student_id,
ADD COLUMN IF NOT EXISTS status ENUM('draft', 'published') DEFAULT 'draft' AFTER notes,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER entered_at;

-- Create subjects table if it doesn't exist
CREATE TABLE IF NOT EXISTS subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50),
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_subject_code_center (center_id, code),
    INDEX idx_center (center_id),
    INDEX idx_status (status)
);

-- Insert sample subjects for ABC Coaching Center
INSERT IGNORE INTO subjects (center_id, name, code, description, status) VALUES
(1, 'Mathematics', 'MATH101', 'Advanced Mathematics for competitive exams', 'active'),
(1, 'Physics', 'PHY101', 'Physics fundamentals and problem solving', 'active'),
(1, 'Chemistry', 'CHEM101', 'Organic and Inorganic Chemistry', 'active'),
(1, 'Biology', 'BIO101', 'Biology for NEET preparation', 'active'),
(1, 'English', 'ENG101', 'English language and literature', 'active');

-- Insert sample exams for ABC Coaching Center (without course_id)
INSERT IGNORE INTO exams (
    center_id, exam_name, exam_description, exam_type, subject_id,
    exam_date, start_time, end_time, 
    total_marks, passing_marks, status, created_by
) VALUES
(1, 'Mathematics Midterm', 'Midterm examination for JEE Main Mathematics', 'midterm', 1, 
 '2025-07-20', '09:00:00', '12:00:00', 
 200.00, 80.00, 'draft', 1),
(1, 'JEE Main Mock Test 1', 'Full-length mock test for JEE Main', 'mock_test', 1, 
 '2025-07-15', '10:00:00', '13:00:00', 
 300.00, 120.00, 'scheduled', 1),
(1, 'NEET Biology Test', 'Biology quiz for NEET preparation', 'quiz', 4, 
 '2025-07-12', '14:00:00', '15:30:00', 
 100.00, 40.00, 'completed', 1);

-- Get the exam IDs for inserting results
SET @math_exam_id = (SELECT id FROM exams WHERE exam_name = 'Mathematics Midterm' AND center_id = 1 LIMIT 1);
SET @jee_exam_id = (SELECT id FROM exams WHERE exam_name = 'JEE Main Mock Test 1' AND center_id = 1 LIMIT 1);
SET @neet_exam_id = (SELECT id FROM exams WHERE exam_name = 'NEET Biology Test' AND center_id = 1 LIMIT 1);

-- Insert sample exam results
INSERT IGNORE INTO exam_results (
    exam_id, student_id, batch_id, total_marks, obtained_marks, percentage, grade, 
    is_absent, status, entered_by, entered_at
) VALUES
-- Results for JEE Main Mock Test 1
(@jee_exam_id, 3, 1, 300.00, 245.00, 81.67, 'A', FALSE, 'published', 1, NOW()),
-- Results for NEET Biology Test  
(@neet_exam_id, 3, 2, 100.00, 85.00, 85.00, 'A', FALSE, 'published', 1, NOW()),
-- Results for Mathematics Midterm
(@math_exam_id, 3, 1, 200.00, 165.00, 82.50, 'A', FALSE, 'draft', 1, NOW());

-- Update exams table to add results_count for the summary
UPDATE exams e SET 
    e.status = CASE 
        WHEN e.exam_name = 'NEET Biology Test' THEN 'completed'
        WHEN e.exam_name = 'JEE Main Mock Test 1' THEN 'completed' 
        ELSE e.status 
    END
WHERE e.center_id = 1;

-- Create a view for easier result queries with course information
CREATE OR REPLACE VIEW exam_results_view AS
SELECT 
    er.id,
    er.exam_id,
    e.exam_name,
    e.exam_date,
    er.student_id,
    u.name as student_name,
    u.email as student_email,
    COALESCE(c.name, 'General') as course_name,
    COALESCE(b.name, 'No Batch') as batch_name,
    er.total_marks,
    er.obtained_marks,
    er.percentage,
    er.grade,
    er.position_in_batch,
    er.position_overall,
    er.is_absent,
    er.notes,
    er.status,
    er.created_at,
    er.updated_at
FROM exam_results er
JOIN exams e ON er.exam_id = e.id
JOIN users u ON er.student_id = u.id
LEFT JOIN batches b ON er.batch_id = b.id
LEFT JOIN courses c ON b.course_id = c.id;
