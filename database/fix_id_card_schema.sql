-- Fix ID Card Schema
USE coaching;

-- Create the tables first (without the problematic view)
CREATE TABLE IF NOT EXISTS id_card_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    card_type ENU<PERSON>('student', 'staff', 'visitor', 'temporary') DEFAULT 'student',
    orientation ENUM('portrait', 'landscape') DEFAULT 'portrait',
    background_color VARCHAR(7) DEFAULT '#ffffff',
    text_color VARCHAR(7) DEFAULT '#000000',
    layout ENUM('classic', 'modern', 'minimal', 'decorative') DEFAULT 'classic',
    include_photo BOOLEAN DEFAULT TRUE,
    include_qr BOOLEAN DEFAULT TRUE,
    include_barcode BOOLEAN DEFAULT FALSE,
    template_data JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_center_id_card_templates (center_id, is_active),
    INDEX idx_card_type (card_type)
);

CREATE TABLE IF NOT EXISTS id_cards (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    student_id INT NOT NULL,
    template_id INT NOT NULL,
    card_number VARCHAR(50) UNIQUE,
    card_title VARCHAR(255) NOT NULL,
    validity_date DATE,
    emergency_contact VARCHAR(20),
    custom_fields JSON,
    qr_code_data TEXT,
    barcode_data VARCHAR(100),
    card_data JSON,
    file_path VARCHAR(500),
    issued_by INT NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'expired', 'revoked', 'lost') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES id_card_templates(id) ON DELETE RESTRICT,
    FOREIGN KEY (issued_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_student_id_cards (student_id, issued_at DESC),
    INDEX idx_center_id_cards (center_id, issued_at DESC),
    INDEX idx_card_number (card_number),
    INDEX idx_card_status (status)
);

-- Insert default templates
INSERT IGNORE INTO id_card_templates (
    center_id, name, card_type, orientation, background_color, text_color,
    layout, include_photo, include_qr, include_barcode, created_by
) VALUES
(1, 'Classic Student ID', 'student', 'portrait', '#ffffff', '#000000', 'classic', TRUE, TRUE, FALSE, 1),
(1, 'Modern Student ID', 'student', 'portrait', '#f8f9fa', '#1e40af', 'modern', TRUE, TRUE, TRUE, 1),
(1, 'Minimal Student ID', 'student', 'portrait', '#ffffff', '#374151', 'minimal', TRUE, FALSE, FALSE, 1),
(1, 'Staff ID Card', 'staff', 'portrait', '#fef3c7', '#92400e', 'classic', TRUE, TRUE, TRUE, 1);

-- Insert sample ID cards
INSERT IGNORE INTO id_cards (
    center_id, student_id, template_id, card_title, validity_date,
    emergency_contact, issued_by
) VALUES
(1, 3, 1, 'Student ID Card', '2025-12-31', '+880 1234567890', 1),
(1, 3, 2, 'Student Identification', '2025-12-31', '+880 1234567890', 1);

-- Create the corrected view
CREATE OR REPLACE VIEW id_card_details AS
SELECT 
    ic.id,
    ic.card_number,
    ic.card_title,
    ic.validity_date,
    ic.emergency_contact,
    ic.status,
    ic.issued_at,
    s.name as student_name,
    s.email as student_email,
    s.id as student_roll,
    s.class_name,
    s.class_section,
    s.blood_group,
    s.profile_image_webp,
    ict.name as template_name,
    ict.card_type,
    ict.layout,
    issuer.name as issued_by_name,
    center.name as center_name
FROM id_cards ic
JOIN users s ON ic.student_id = s.id
JOIN id_card_templates ict ON ic.template_id = ict.id
JOIN users issuer ON ic.issued_by = issuer.id
JOIN centers center ON ic.center_id = center.id
ORDER BY ic.issued_at DESC;

-- Show results
SELECT COUNT(*) as template_count FROM id_card_templates;
SELECT COUNT(*) as id_card_count FROM id_cards;
SELECT * FROM id_card_details LIMIT 2;
