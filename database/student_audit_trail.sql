-- Student Audit Trail Schema
-- This file creates the audit trail system for tracking student status changes

USE coaching;

-- Create student_audit_logs table for tracking all student changes
CREATE TABLE IF NOT EXISTS student_audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    student_id INT NOT NULL,
    action_type ENUM('status_change', 'profile_update', 'course_assignment', 'fee_payment', 'created', 'deleted') NOT NULL,
    old_value TEXT,
    new_value TEXT,
    field_name VARCHAR(100),
    description TEXT,
    performed_by INT NOT NULL,
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIG<PERSON> KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_student_audit (student_id, performed_at DESC),
    INDEX idx_center_audit (center_id, performed_at DESC),
    INDEX idx_action_type (action_type),
    INDEX idx_performed_by (performed_by)
);

-- Create trigger to automatically log status changes
DELIMITER //

CREATE TRIGGER IF NOT EXISTS log_student_status_change
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    -- Only log for students and when status actually changes
    IF NEW.role = 'student' AND OLD.status != NEW.status THEN
        INSERT INTO student_audit_logs (
            center_id, student_id, action_type, old_value, new_value, 
            field_name, description, performed_by
        ) VALUES (
            NEW.center_id, 
            NEW.id, 
            'status_change', 
            OLD.status, 
            NEW.status, 
            'status',
            CONCAT('Student status changed from ', OLD.status, ' to ', NEW.status),
            COALESCE(@current_user_id, 1) -- Use session variable or default to admin
        );
    END IF;
    
    -- Log other important field changes
    IF NEW.role = 'student' THEN
        -- Log name changes
        IF OLD.name != NEW.name THEN
            INSERT INTO student_audit_logs (
                center_id, student_id, action_type, old_value, new_value, 
                field_name, description, performed_by
            ) VALUES (
                NEW.center_id, NEW.id, 'profile_update', OLD.name, NEW.name, 
                'name', 'Student name updated', COALESCE(@current_user_id, 1)
            );
        END IF;
        
        -- Log email changes
        IF OLD.email != NEW.email THEN
            INSERT INTO student_audit_logs (
                center_id, student_id, action_type, old_value, new_value, 
                field_name, description, performed_by
            ) VALUES (
                NEW.center_id, NEW.id, 'profile_update', OLD.email, NEW.email, 
                'email', 'Student email updated', COALESCE(@current_user_id, 1)
            );
        END IF;
        
        -- Log phone changes
        IF OLD.phone != NEW.phone THEN
            INSERT INTO student_audit_logs (
                center_id, student_id, action_type, old_value, new_value, 
                field_name, description, performed_by
            ) VALUES (
                NEW.center_id, NEW.id, 'profile_update', OLD.phone, NEW.phone, 
                'phone', 'Student phone updated', COALESCE(@current_user_id, 1)
            );
        END IF;
        
        -- Log blood group changes
        IF OLD.blood_group != NEW.blood_group THEN
            INSERT INTO student_audit_logs (
                center_id, student_id, action_type, old_value, new_value, 
                field_name, description, performed_by
            ) VALUES (
                NEW.center_id, NEW.id, 'profile_update', OLD.blood_group, NEW.blood_group, 
                'blood_group', 'Student blood group updated', COALESCE(@current_user_id, 1)
            );
        END IF;
    END IF;
END//

-- Create trigger for new student creation
CREATE TRIGGER IF NOT EXISTS log_student_creation
AFTER INSERT ON users
FOR EACH ROW
BEGIN
    -- Only log for students
    IF NEW.role = 'student' THEN
        INSERT INTO student_audit_logs (
            center_id, student_id, action_type, old_value, new_value, 
            field_name, description, performed_by
        ) VALUES (
            NEW.center_id, 
            NEW.id, 
            'created', 
            NULL, 
            'active', 
            'status',
            CONCAT('Student account created: ', NEW.name),
            COALESCE(@current_user_id, 1)
        );
    END IF;
END//

DELIMITER ;

-- Insert some sample audit logs for existing students
INSERT IGNORE INTO student_audit_logs (
    center_id, student_id, action_type, old_value, new_value, 
    field_name, description, performed_by, performed_at
) VALUES
(1, 3, 'created', NULL, 'active', 'status', 'Student account created: Mike Student', 1, '2025-01-10 10:00:00'),
(1, 3, 'profile_update', NULL, 'A+', 'blood_group', 'Blood group added during profile update', 1, '2025-01-10 14:30:00');

-- Create a view for easy audit trail queries
CREATE OR REPLACE VIEW student_audit_view AS
SELECT 
    sal.id,
    sal.student_id,
    u.name as student_name,
    sal.action_type,
    sal.old_value,
    sal.new_value,
    sal.field_name,
    sal.description,
    sal.performed_by,
    performer.name as performed_by_name,
    sal.performed_at,
    sal.ip_address
FROM student_audit_logs sal
JOIN users u ON sal.student_id = u.id
LEFT JOIN users performer ON sal.performed_by = performer.id
ORDER BY sal.performed_at DESC;

-- Show the created tables and sample data
DESCRIBE student_audit_logs;
SELECT COUNT(*) as audit_logs_count FROM student_audit_logs;
SELECT * FROM student_audit_view LIMIT 5;
