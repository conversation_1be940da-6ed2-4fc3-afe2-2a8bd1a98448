-- Create tenants table for multi-tenant support
CREATE TABLE IF NOT EXISTS tenants (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  slug VARCHAR(100) NOT NULL UNIQUE,
  email VARCHAR(255),
  phone VARCHAR(20),
  address TEXT,
  subscription_plan ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic',
  status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_slug (slug),
  INDEX idx_status (status)
);

-- Add tenant_id to existing tables
ALTER TABLE users ADD COLUMN tenant_id INT NULL AFTER id;
ALTER TABLE users ADD FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL;

ALTER TABLE courses ADD COLUMN tenant_id INT NULL AFTER id;
ALTER TABLE courses ADD FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL;

ALTER TABLE branches ADD COLUMN tenant_id INT NULL AFTER id;
ALTER TABLE branches ADD FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL;

ALTER TABLE schedules ADD COLUMN tenant_id INT NULL AFTER id;
ALTER TABLE schedules ADD FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL;

ALTER TABLE attendance ADD COLUMN tenant_id INT NULL AFTER id;
ALTER TABLE attendance ADD FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL;

ALTER TABLE fees ADD COLUMN tenant_id INT NULL AFTER id;
ALTER TABLE fees ADD FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL;

ALTER TABLE results ADD COLUMN tenant_id INT NULL AFTER id;
ALTER TABLE results ADD FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL;

ALTER TABLE employees ADD COLUMN tenant_id INT NULL AFTER id;
ALTER TABLE employees ADD FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL;

-- Insert a sample super admin user (no tenant)
INSERT INTO users (name, email, password_hash, role, status, created_at, updated_at) 
VALUES (
  'Super Admin', 
  '<EMAIL>', 
  '$2b$10$rQZ8kHWKtGKVQZ8kHWKtGOyQZ8kHWKtGKVQZ8kHWKtGOyQZ8kHWKtG', -- password: admin123
  'super_admin', 
  'active', 
  NOW(), 
  NOW()
) ON DUPLICATE KEY UPDATE email = email;

-- Insert a sample coaching center
INSERT INTO tenants (name, slug, email, phone, address, subscription_plan, status, created_at, updated_at) 
VALUES (
  'ABC Coaching Center', 
  'abc-coaching', 
  '<EMAIL>', 
  '+1234567890', 
  '123 Main St, City, State', 
  'premium', 
  'active', 
  NOW(), 
  NOW()
) ON DUPLICATE KEY UPDATE slug = slug;

-- Insert a sample center admin
INSERT INTO users (name, email, password_hash, role, tenant_id, status, created_at, updated_at) 
VALUES (
  'Center Admin', 
  '<EMAIL>', 
  '$2b$10$rQZ8kHWKtGKVQZ8kHWKtGOyQZ8kHWKtGKVQZ8kHWKtGOyQZ8kHWKtG', -- password: admin123
  'center_admin', 
  (SELECT id FROM tenants WHERE slug = 'abc-coaching' LIMIT 1),
  'active', 
  NOW(), 
  NOW()
) ON DUPLICATE KEY UPDATE email = email;
