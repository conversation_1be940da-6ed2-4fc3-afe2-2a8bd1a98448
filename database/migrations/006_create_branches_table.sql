-- Migration: Create branches table
-- Description: Create table for managing center branches

CREATE TABLE IF NOT EXISTS branches (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT NOT NULL,
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  address TEXT,
  phone VARCHAR(20),
  email VARCHAR(255),
  capacity INT DEFAULT 0,
  facilities JSON,
  status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
  manager_id INT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (tenant_id) REFERENCES centers(id) ON DELETE CASCADE,
  FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
  
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_status (status),
  INDEX idx_manager_id (manager_id)
);

-- Create staff_branches table for many-to-many relationship
CREATE TABLE IF NOT EXISTS staff_branches (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  branch_id INT NOT NULL,
  role VARCHAR(50) DEFAULT 'staff',
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
  
  UNIQUE KEY unique_user_branch (user_id, branch_id),
  INDEX idx_user_id (user_id),
  INDEX idx_branch_id (branch_id)
);

-- Add foreign key constraint for existing branch_id column
ALTER TABLE users
ADD CONSTRAINT fk_users_branch_id
FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL;
