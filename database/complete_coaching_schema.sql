-- Complete Coaching Center Database Schema
USE coaching;

-- Drop existing tables if they exist (in correct order to handle foreign keys)
DROP TABLE IF EXISTS attendance;
DROP TABLE IF EXISTS assignments;
DROP TABLE IF EXISTS enrollments;
DROP TABLE IF EXISTS batches;
DROP TABLE IF EXISTS courses;
DROP TABLE IF EXISTS subjects;
DROP TABLE IF EXISTS fees;
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS notifications;
DROP TABLE IF EXISTS subscriptions;
DROP TABLE IF EXISTS plans;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS centers;

-- Centers table (coaching centers)
CREATE TABLE centers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHA<PERSON>(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    description TEXT,
    logo_url VARCHAR(500),
    website VARCHAR(255),
    status ENUM('active', 'suspended', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Plans table (subscription plans)
CREATE TABLE plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration_months INT DEFAULT 12,
    max_students INT DEFAULT -1, -- -1 means unlimited
    max_teachers INT DEFAULT -1, -- -1 means unlimited
    max_courses INT DEFAULT -1,
    features JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    plan_id INT NOT NULL,
    status ENUM('active', 'expired', 'cancelled', 'pending') DEFAULT 'pending',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_status ENUM('paid', 'pending', 'failed', 'refunded') DEFAULT 'pending',
    auto_renewal BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE RESTRICT
);

-- Users table (students, teachers, admins)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    role ENUM('super_admin', 'center_admin', 'teacher', 'student') NOT NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    profile_image VARCHAR(500),
    emergency_contact VARCHAR(20),
    parent_name VARCHAR(255),
    parent_phone VARCHAR(20),
    admission_date DATE,
    employee_id VARCHAR(50),
    qualification TEXT,
    experience_years INT,
    salary DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_email_center (email, center_id)
);

-- Subjects table
CREATE TABLE subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50),
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE
);

-- Courses table
CREATE TABLE courses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50),
    description TEXT,
    duration_months INT,
    fee DECIMAL(10,2),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE
);

-- Batches table
CREATE TABLE batches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    course_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    teacher_id INT,
    start_date DATE,
    end_date DATE,
    schedule_days JSON, -- ["monday", "wednesday", "friday"]
    start_time TIME,
    end_time TIME,
    max_students INT DEFAULT 30,
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Enrollments table
CREATE TABLE enrollments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    student_id INT NOT NULL,
    batch_id INT NOT NULL,
    enrollment_date DATE NOT NULL,
    status ENUM('active', 'completed', 'dropped', 'transferred') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_batch (student_id, batch_id)
);

-- Fees table
CREATE TABLE fees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    student_id INT NOT NULL,
    batch_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    due_date DATE NOT NULL,
    status ENUM('pending', 'paid', 'overdue', 'waived') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE CASCADE
);

-- Payments table
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    fee_id INT NOT NULL,
    student_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'bank_transfer', 'upi', 'cheque') NOT NULL,
    payment_date DATE NOT NULL,
    transaction_id VARCHAR(255),
    receipt_number VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Attendance table
CREATE TABLE attendance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    student_id INT NOT NULL,
    batch_id INT NOT NULL,
    date DATE NOT NULL,
    status ENUM('present', 'absent', 'late', 'excused') NOT NULL,
    notes TEXT,
    marked_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE CASCADE,
    FOREIGN KEY (marked_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_student_date_batch (student_id, date, batch_id)
);

-- Assignments table
CREATE TABLE assignments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    batch_id INT NOT NULL,
    teacher_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    due_date DATE,
    max_marks INT DEFAULT 100,
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (batch_id) REFERENCES batches(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Notifications table
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    user_id INT,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'warning', 'success', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default plans
INSERT INTO plans (name, description, price, duration_months, max_students, max_teachers, max_courses, features, is_active) VALUES
('Basic Plan', 'Perfect for small coaching centers', 5000.00, 12, 100, 5, 10, '["Student Management", "Basic Reports", "Email Support", "Attendance Tracking"]', TRUE),
('Premium Plan', 'Ideal for growing coaching centers', 12000.00, 12, 500, 20, 50, '["All Basic Features", "Advanced Analytics", "SMS Integration", "Priority Support", "Fee Management", "Assignment System"]', TRUE),
('Enterprise Plan', 'For large coaching institutions', 25000.00, 12, -1, -1, -1, '["All Premium Features", "Custom Branding", "API Access", "Dedicated Support", "Multi-location Support", "Advanced Reporting"]', TRUE);

-- Insert sample center
INSERT INTO centers (name, subdomain, email, phone, address, description, status) VALUES
('ABC Coaching Center', 'abc', '<EMAIL>', '******-567-8900', '123 Main Street, City, State 12345', 'Excellence in education with personalized attention', 'active');

-- Insert sample subscription
INSERT INTO subscriptions (center_id, plan_id, status, start_date, end_date, amount, payment_status, auto_renewal) VALUES
(1, 2, 'active', '2024-01-01', '2024-12-31', 12000.00, 'paid', TRUE);

-- Insert sample users
INSERT INTO users (center_id, name, email, password_hash, role, status, phone, admission_date) VALUES
(1, 'John Admin', '<EMAIL>', '$2b$10$1PSRXLfx9TArSNeO0QoPse5kjHv2QHC/xb5wH0q05dZv8TQQCTkvG', 'center_admin', 'active', '******-567-8901', '2024-01-01'),
(1, 'Sarah Teacher', '<EMAIL>', '$2b$10$1PSRXLfx9TArSNeO0QoPse5kjHv2QHC/xb5wH0q05dZv8TQQCTkvG', 'teacher', 'active', '******-567-8902', '2024-01-01'),
(1, 'Mike Student', '<EMAIL>', '$2b$10$1PSRXLfx9TArSNeO0QoPse5kjHv2QHC/xb5wH0q05dZv8TQQCTkvG', 'student', 'active', '******-567-8903', '2024-01-15');

-- Insert sample subjects
INSERT INTO subjects (center_id, name, code, description, status) VALUES
(1, 'Mathematics', 'MATH101', 'Advanced Mathematics for competitive exams', 'active'),
(1, 'Physics', 'PHY101', 'Physics fundamentals and problem solving', 'active'),
(1, 'Chemistry', 'CHEM101', 'Organic and Inorganic Chemistry', 'active');

-- Insert sample courses
INSERT INTO courses (center_id, name, code, description, duration_months, fee, status) VALUES
(1, 'JEE Main Preparation', 'JEE001', 'Complete preparation for JEE Main examination', 12, 15000.00, 'active'),
(1, 'NEET Preparation', 'NEET001', 'Medical entrance exam preparation', 12, 18000.00, 'active');

-- Insert sample batches
INSERT INTO batches (center_id, course_id, name, teacher_id, start_date, end_date, schedule_days, start_time, end_time, max_students, status) VALUES
(1, 1, 'JEE Morning Batch', 2, '2024-01-01', '2024-12-31', '["monday", "wednesday", "friday"]', '09:00:00', '12:00:00', 30, 'active'),
(1, 2, 'NEET Evening Batch', 2, '2024-01-01', '2024-12-31', '["tuesday", "thursday", "saturday"]', '14:00:00', '17:00:00', 25, 'active');

-- Insert sample enrollment
INSERT INTO enrollments (center_id, student_id, batch_id, enrollment_date, status) VALUES
(1, 3, 1, '2024-01-15', 'active');

-- Insert sample fees
INSERT INTO fees (center_id, student_id, batch_id, amount, due_date, status) VALUES
(1, 3, 1, 15000.00, '2024-02-01', 'paid');

-- Insert sample payment
INSERT INTO payments (center_id, fee_id, student_id, amount, payment_method, payment_date, receipt_number) VALUES
(1, 1, 3, 15000.00, 'upi', '2024-01-20', 'RCP001');

-- Insert sample attendance
INSERT INTO attendance (center_id, student_id, batch_id, date, status, marked_by) VALUES
(1, 3, 1, '2024-01-15', 'present', 2),
(1, 3, 1, '2024-01-17', 'present', 2),
(1, 3, 1, '2024-01-19', 'absent', 2);

-- Insert sample assignment
INSERT INTO assignments (center_id, batch_id, teacher_id, title, description, due_date, max_marks, status) VALUES
(1, 1, 2, 'Calculus Problem Set 1', 'Solve the given calculus problems focusing on derivatives and integrals', '2024-02-01', 100, 'active');

-- Insert sample notifications
INSERT INTO notifications (center_id, user_id, title, message, type, is_read) VALUES
(1, 3, 'Welcome to ABC Coaching', 'Welcome to our coaching center! Your classes start from tomorrow.', 'info', FALSE),
(1, 3, 'Fee Payment Due', 'Your monthly fee payment is due on 1st February 2024.', 'warning', FALSE);
