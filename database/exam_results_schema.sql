-- <PERSON>am and Result Management Schema
-- This file creates the necessary tables for exam and result management

USE coaching;

-- Create exams table if it doesn't exist
CREATE TABLE IF NOT EXISTS exams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    exam_name VARCHAR(255) NOT NULL,
    exam_description TEXT,
    exam_type ENUM('quiz', 'midterm', 'final', 'mock_test', 'practice', 'assignment') DEFAULT 'quiz',
    course_id INT,
    batch_id INT,
    subject_id INT,
    exam_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    duration_minutes INT NOT NULL,
    room_id INT,
    total_marks DECIMAL(10,2) NOT NULL DEFAULT 100.00,
    passing_marks DECIMAL(10,2) NOT NULL DEFAULT 40.00,
    grading_scale JSON, -- Store grading criteria
    exam_instructions TEXT,
    status ENUM('draft', 'scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'draft',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_center_date (center_id, exam_date),
    INDEX idx_status (status),
    INDEX idx_course (course_id),
    INDEX idx_batch (batch_id)
);

-- Create exam_results table
CREATE TABLE IF NOT EXISTS exam_results (
    id INT PRIMARY KEY AUTO_INCREMENT,
    exam_id INT NOT NULL,
    student_id INT NOT NULL,
    total_marks DECIMAL(10,2) NOT NULL,
    obtained_marks DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    grade VARCHAR(5) NOT NULL DEFAULT 'F',
    position_in_batch INT,
    position_overall INT,
    is_absent BOOLEAN DEFAULT FALSE,
    notes TEXT,
    status ENUM('draft', 'published') DEFAULT 'draft',
    entered_by INT,
    entered_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (entered_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_exam_student (exam_id, student_id),
    INDEX idx_exam (exam_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status),
    INDEX idx_percentage (percentage)
);

-- Create subjects table if it doesn't exist
CREATE TABLE IF NOT EXISTS subjects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50),
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_subject_code_center (center_id, code),
    INDEX idx_center (center_id),
    INDEX idx_status (status)
);

-- Insert sample subjects for ABC Coaching Center
INSERT IGNORE INTO subjects (center_id, name, code, description, status) VALUES
(1, 'Mathematics', 'MATH101', 'Advanced Mathematics for competitive exams', 'active'),
(1, 'Physics', 'PHY101', 'Physics fundamentals and problem solving', 'active'),
(1, 'Chemistry', 'CHEM101', 'Organic and Inorganic Chemistry', 'active'),
(1, 'Biology', 'BIO101', 'Biology for NEET preparation', 'active'),
(1, 'English', 'ENG101', 'English language and literature', 'active');

-- Insert sample exams for ABC Coaching Center
INSERT IGNORE INTO exams (
    center_id, exam_name, exam_description, exam_type, course_id, 
    exam_date, start_time, end_time, duration_minutes, 
    total_marks, passing_marks, status, created_by
) VALUES
(1, 'Mathematics Midterm', 'Midterm examination for JEE Main Mathematics', 'midterm', 1, 
 '2025-07-20', '09:00:00', '12:00:00', 180, 
 200.00, 80.00, 'draft', 1),
(1, 'JEE Main Mock Test 1', 'Full-length mock test for JEE Main', 'mock_test', 1, 
 '2025-07-15', '10:00:00', '13:00:00', 180, 
 300.00, 120.00, 'scheduled', 1),
(1, 'NEET Biology Test', 'Biology quiz for NEET preparation', 'quiz', 2, 
 '2025-07-12', '14:00:00', '15:30:00', 90, 
 100.00, 40.00, 'completed', 1);

-- Insert sample exam results
INSERT IGNORE INTO exam_results (
    exam_id, student_id, total_marks, obtained_marks, percentage, grade, 
    is_absent, status, entered_by, entered_at
) VALUES
-- Results for JEE Main Mock Test 1 (exam_id = 2)
(2, 3, 300.00, 245.00, 81.67, 'A', FALSE, 'published', 1, NOW()),
-- Results for NEET Biology Test (exam_id = 3)  
(3, 3, 100.00, 85.00, 85.00, 'A', FALSE, 'published', 1, NOW());

-- Create triggers for automatic grade calculation
DELIMITER //

CREATE TRIGGER IF NOT EXISTS calculate_grade_before_insert
BEFORE INSERT ON exam_results
FOR EACH ROW
BEGIN
    -- Calculate percentage
    IF NEW.total_marks > 0 THEN
        SET NEW.percentage = (NEW.obtained_marks / NEW.total_marks) * 100;
    ELSE
        SET NEW.percentage = 0;
    END IF;
    
    -- Calculate grade based on percentage
    IF NEW.percentage >= 90 THEN
        SET NEW.grade = 'A+';
    ELSEIF NEW.percentage >= 80 THEN
        SET NEW.grade = 'A';
    ELSEIF NEW.percentage >= 70 THEN
        SET NEW.grade = 'B+';
    ELSEIF NEW.percentage >= 60 THEN
        SET NEW.grade = 'B';
    ELSEIF NEW.percentage >= 50 THEN
        SET NEW.grade = 'C+';
    ELSEIF NEW.percentage >= 40 THEN
        SET NEW.grade = 'C';
    ELSE
        SET NEW.grade = 'F';
    END IF;
END//

CREATE TRIGGER IF NOT EXISTS calculate_grade_before_update
BEFORE UPDATE ON exam_results
FOR EACH ROW
BEGIN
    -- Calculate percentage
    IF NEW.total_marks > 0 THEN
        SET NEW.percentage = (NEW.obtained_marks / NEW.total_marks) * 100;
    ELSE
        SET NEW.percentage = 0;
    END IF;
    
    -- Calculate grade based on percentage
    IF NEW.percentage >= 90 THEN
        SET NEW.grade = 'A+';
    ELSEIF NEW.percentage >= 80 THEN
        SET NEW.grade = 'A';
    ELSEIF NEW.percentage >= 70 THEN
        SET NEW.grade = 'B+';
    ELSEIF NEW.percentage >= 60 THEN
        SET NEW.grade = 'B';
    ELSEIF NEW.percentage >= 50 THEN
        SET NEW.grade = 'C+';
    ELSEIF NEW.percentage >= 40 THEN
        SET NEW.grade = 'C';
    ELSE
        SET NEW.grade = 'F';
    END IF;
END//

DELIMITER ;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_exam_results_exam_percentage ON exam_results(exam_id, percentage DESC);
CREATE INDEX IF NOT EXISTS idx_exam_results_student_exam ON exam_results(student_id, exam_id);
CREATE INDEX IF NOT EXISTS idx_exams_center_date ON exams(center_id, exam_date DESC);
CREATE INDEX IF NOT EXISTS idx_exams_status_date ON exams(status, exam_date);
