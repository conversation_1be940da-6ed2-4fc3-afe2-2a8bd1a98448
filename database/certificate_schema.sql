-- Certificate Generator System Schema
-- This file creates the certificate generation system

USE coaching;

-- Create certificate_templates table
CREATE TABLE IF NOT EXISTS certificate_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    template_type ENUM('completion', 'achievement', 'participation', 'excellence', 'custom') DEFAULT 'completion',
    background_color VARCHAR(7) DEFAULT '#ffffff',
    border_style ENUM('none', 'simple', 'elegant', 'modern', 'classic') DEFAULT 'elegant',
    font_family ENUM('serif', 'sans-serif', 'monospace') DEFAULT 'serif',
    language ENUM('bengali', 'english', 'both') DEFAULT 'both',
    layout ENUM('classic', 'modern', 'minimal', 'decorative') DEFAULT 'classic',
    template_data JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_center_templates (center_id, is_active),
    INDEX idx_template_type (template_type)
);

-- Create certificates table
CREATE TABLE IF NOT EXISTS certificates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    student_id INT NOT NULL,
    template_id INT NOT NULL,
    certificate_number VARCHAR(50) UNIQUE,
    certificate_title VARCHAR(255) NOT NULL,
    course_name VARCHAR(255),
    completion_date DATE,
    grade VARCHAR(10),
    percentage DECIMAL(5,2),
    custom_text TEXT,
    language ENUM('bengali', 'english') DEFAULT 'bengali',
    certificate_data JSON,
    file_path VARCHAR(500),
    issued_by INT NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('draft', 'issued', 'revoked') DEFAULT 'issued',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES certificate_templates(id) ON DELETE RESTRICT,
    FOREIGN KEY (issued_by) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_student_certificates (student_id, issued_at DESC),
    INDEX idx_center_certificates (center_id, issued_at DESC),
    INDEX idx_certificate_number (certificate_number),
    INDEX idx_certificate_status (status)
);

-- Create trigger to generate certificate number
DELIMITER //

CREATE TRIGGER IF NOT EXISTS generate_certificate_number
BEFORE INSERT ON certificates
FOR EACH ROW
BEGIN
    DECLARE cert_count INT;
    DECLARE center_code VARCHAR(10);
    
    -- Get center code or use center_id
    SELECT COALESCE(
        (SELECT branch_code FROM branches WHERE id = 1 LIMIT 1), 
        CONCAT('C', NEW.center_id)
    ) INTO center_code;
    
    -- Get count of certificates for this center
    SELECT COUNT(*) + 1 INTO cert_count 
    FROM certificates 
    WHERE center_id = NEW.center_id;
    
    -- Generate certificate number: CENTER-YEAR-SEQUENCE
    SET NEW.certificate_number = CONCAT(
        center_code, '-',
        YEAR(CURDATE()), '-',
        LPAD(cert_count, 4, '0')
    );
END//

DELIMITER ;

-- Insert default certificate templates
INSERT IGNORE INTO certificate_templates (
    center_id, name, template_type, background_color, border_style, 
    font_family, language, layout, created_by
) VALUES
(1, 'Course Completion Certificate', 'completion', '#ffffff', 'elegant', 'serif', 'both', 'classic', 1),
(1, 'Achievement Certificate', 'achievement', '#f8f9fa', 'modern', 'sans-serif', 'both', 'modern', 1),
(1, 'Excellence Award', 'excellence', '#fff8dc', 'decorative', 'serif', 'both', 'decorative', 1),
(1, 'Participation Certificate', 'participation', '#f0f8ff', 'simple', 'sans-serif', 'both', 'minimal', 1);

-- Insert sample certificates
INSERT IGNORE INTO certificates (
    center_id, student_id, template_id, certificate_title, course_name,
    completion_date, grade, percentage, language, issued_by
) VALUES
(1, 3, 1, 'Certificate of Course Completion', 'Advanced Mathematics', '2025-01-10', 'A+', 95.50, 'english', 1),
(1, 3, 2, 'Achievement Certificate', 'Science Olympiad', '2025-01-15', 'Gold', 98.00, 'bengali', 1);

-- Add certificate-related audit log types
ALTER TABLE student_audit_logs 
MODIFY COLUMN action_type ENUM(
    'status_change', 'profile_update', 'course_assignment', 'fee_payment', 
    'created', 'deleted', 'transfer_requested', 'transfer_approved', 
    'transfer_completed', 'transfer_cancelled', 'certificate_issued', 
    'certificate_revoked'
) NOT NULL;

-- Create trigger to log certificate issuance
DELIMITER //

CREATE TRIGGER IF NOT EXISTS log_certificate_issuance
AFTER INSERT ON certificates
FOR EACH ROW
BEGIN
    INSERT INTO student_audit_logs (
        center_id, student_id, action_type, old_value, new_value, 
        field_name, description, performed_by
    ) VALUES (
        NEW.center_id, 
        NEW.student_id, 
        'certificate_issued', 
        NULL, 
        NEW.certificate_number, 
        'certificate',
        CONCAT('Certificate issued: ', NEW.certificate_title, ' for ', NEW.course_name),
        NEW.issued_by
    );
END//

DELIMITER ;

-- Create a view for easy certificate queries
CREATE OR REPLACE VIEW certificate_details AS
SELECT 
    c.id,
    c.certificate_number,
    c.certificate_title,
    c.course_name,
    c.completion_date,
    c.grade,
    c.percentage,
    c.language,
    c.status,
    c.issued_at,
    s.name as student_name,
    s.email as student_email,
    ct.name as template_name,
    ct.template_type,
    issuer.name as issued_by_name,
    center.name as center_name
FROM certificates c
JOIN users s ON c.student_id = s.id
JOIN certificate_templates ct ON c.template_id = ct.id
JOIN users issuer ON c.issued_by = issuer.id
JOIN centers center ON c.center_id = center.id
ORDER BY c.issued_at DESC;

-- Show the created tables and sample data
DESCRIBE certificate_templates;
DESCRIBE certificates;
SELECT COUNT(*) as template_count FROM certificate_templates;
SELECT COUNT(*) as certificate_count FROM certificates;
SELECT * FROM certificate_details LIMIT 3;
