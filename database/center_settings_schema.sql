-- Center Settings Table Schema
USE coaching;

-- Create center_settings table for storing coaching center configuration
CREATE TABLE IF NOT EXISTS center_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    center_id INT NOT NULL,
    setting_key VARCHAR(255) NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (center_id) REFERENCES centers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_center_setting (center_id, setting_key),
    INDEX idx_center_id (center_id),
    INDEX idx_setting_key (setting_key)
);

-- Insert default settings for ABC Coaching Center
INSERT IGNORE INTO center_settings (center_id, setting_key, setting_value, setting_type) VALUES
(1, 'coaching_center_name', 'ABC Coaching Center', 'string'),
(1, 'address', '123 Main Street, City, State 12345', 'string'),
(1, 'phone', '******-567-8900', 'string'),
(1, 'email', '<EMAIL>', 'string'),
(1, 'facebook_link', 'https://facebook.com/abccoaching', 'string'),
(1, 'whatsapp_number', '******-567-8900', 'string'),
(1, 'invoice_logo', '', 'string'),
(1, 'invoice_watermark', '', 'string');
