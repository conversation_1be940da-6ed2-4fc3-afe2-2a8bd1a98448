-- Multi-Branch Setup Script

-- Create branches table if not exists
CREATE TABLE IF NOT EXISTS branches (
  id INT AUTO_INCREMENT PRIMARY KEY,
  branch_code VARCHAR(20) UNIQUE NOT NULL,
  branch_name VARCHAR(255) NOT NULL,
  address TEXT,
  phone VARCHAR(20),
  email VARCHAR(255),
  manager_id INT,
  parent_branch_id INT,
  establishment_date DATE,
  status ENUM('active', 'inactive') DEFAULT 'active',
  settings JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_branch_code (branch_code),
  INDEX idx_status (status)
);

-- Create rooms table
CREATE TABLE IF NOT EXISTS rooms (
  id INT AUTO_INCREMENT PRIMARY KEY,
  branch_id INT NOT NULL,
  room_code <PERSON><PERSON><PERSON>R(50) NOT NULL,
  room_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  room_type ENUM('classroom', 'lab', 'auditorium', 'library', 'office', 'other') DEFAULT 'classroom',
  capacity INT NOT NULL DEFAULT 0,
  equipment TEXT,
  status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_room_code_per_branch (branch_id, room_code),
  INDEX idx_branch_room (branch_id, status)
);

-- Create schedules table
CREATE TABLE IF NOT EXISTS schedules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  branch_id INT NOT NULL,
  course_id INT NOT NULL,
  batch_id INT,
  room_id INT NOT NULL,
  teacher_id INT NOT NULL,
  day_of_week ENUM('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday') NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  effective_from DATE NOT NULL,
  effective_to DATE,
  status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
  notes TEXT,
  created_by INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_branch_schedule (branch_id, day_of_week, start_time),
  INDEX idx_room_schedule (room_id, day_of_week, start_time),
  INDEX idx_teacher_schedule (teacher_id, day_of_week, start_time)
);

-- Create student transfers table
CREATE TABLE IF NOT EXISTS student_transfers (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_id INT NOT NULL,
  from_branch_id INT NOT NULL,
  to_branch_id INT NOT NULL,
  transfer_date DATE NOT NULL,
  reason TEXT NOT NULL,
  status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
  requested_by INT NOT NULL,
  approved_by INT,
  approved_at TIMESTAMP NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_student_transfers (student_id, transfer_date),
  INDEX idx_branch_transfers (from_branch_id, to_branch_id, status)
);

-- Create inventory transfers table
CREATE TABLE IF NOT EXISTS inventory_transfers (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transfer_number VARCHAR(50) UNIQUE NOT NULL,
  from_branch_id INT NOT NULL,
  to_branch_id INT NOT NULL,
  transfer_date DATE NOT NULL,
  status ENUM('pending', 'approved', 'in_transit', 'received', 'cancelled') DEFAULT 'pending',
  total_value DECIMAL(12,2) DEFAULT 0,
  notes TEXT,
  requested_by INT NOT NULL,
  approved_by INT,
  approved_at TIMESTAMP NULL,
  received_by INT,
  received_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_transfer_number (transfer_number),
  INDEX idx_branch_transfers (from_branch_id, to_branch_id, status)
);

-- Create inventory transfer items table
CREATE TABLE IF NOT EXISTS inventory_transfer_items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transfer_id INT NOT NULL,
  item_id INT NOT NULL,
  quantity_requested INT NOT NULL,
  quantity_sent INT DEFAULT 0,
  quantity_received INT DEFAULT 0,
  unit_price DECIMAL(10,2) NOT NULL,
  total_value DECIMAL(12,2) NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_transfer_item (transfer_id, item_id)
);

-- Create user branch assignments table
CREATE TABLE IF NOT EXISTS user_branch_assignments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  branch_id INT NOT NULL,
  role ENUM('admin', 'manager', 'teacher', 'staff', 'viewer') NOT NULL,
  is_primary BOOLEAN DEFAULT FALSE,
  assigned_by INT NOT NULL,
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('active', 'inactive') DEFAULT 'active',
  UNIQUE KEY unique_user_branch (user_id, branch_id),
  INDEX idx_user_branches (user_id, status),
  INDEX idx_branch_users (branch_id, role, status)
);

-- Insert default branches if they don't exist
INSERT IGNORE INTO branches (branch_code, branch_name, address, establishment_date, status) VALUES
('MAIN', 'Main Branch', 'Head Office Location', CURDATE(), 'active'),
('DHAN', 'Dhanmondi Branch', 'Road 27, Dhanmondi, Dhaka-1209', '2023-01-15', 'active'),
('GULS', 'Gulshan Branch', 'Road 11, Gulshan-2, Dhaka-1212', '2023-06-01', 'active'),
('UTTE', 'Uttara Branch', 'Sector 7, Uttara, Dhaka-1230', '2024-01-01', 'active');

-- Insert sample rooms
INSERT IGNORE INTO rooms (branch_id, room_code, room_name, room_type, capacity, equipment, status) VALUES
-- Main Branch Rooms
(1, 'R101', 'Classroom 101', 'classroom', 30, 'Whiteboard, Projector, AC', 'active'),
(1, 'R102', 'Classroom 102', 'classroom', 25, 'Whiteboard, AC', 'active'),
(1, 'LAB1', 'Physics Lab', 'lab', 20, 'Lab Equipment, Safety Kit', 'active'),
(1, 'AUD1', 'Main Auditorium', 'auditorium', 100, 'Sound System, Projector, AC', 'active'),

-- Dhanmondi Branch Rooms
(2, 'D101', 'Classroom D101', 'classroom', 35, 'Smart Board, AC', 'active'),
(2, 'D102', 'Classroom D102', 'classroom', 30, 'Whiteboard, Projector', 'active'),
(2, 'DLAB', 'Chemistry Lab', 'lab', 25, 'Lab Equipment, Fume Hood', 'active'),

-- Gulshan Branch Rooms
(3, 'G101', 'Classroom G101', 'classroom', 40, 'Interactive Board, AC', 'active'),
(3, 'G102', 'Classroom G102', 'classroom', 35, 'Whiteboard, Projector', 'active'),
(3, 'GLIB', 'Library', 'library', 50, 'Books, Reading Tables, AC', 'active'),

-- Uttara Branch Rooms
(4, 'U101', 'Classroom U101', 'classroom', 30, 'Whiteboard, AC', 'active'),
(4, 'U102', 'Classroom U102', 'classroom', 28, 'Smart Board, Projector', 'active');
